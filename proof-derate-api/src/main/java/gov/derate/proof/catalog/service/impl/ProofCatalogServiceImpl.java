package gov.derate.proof.catalog.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import gov.derate.proof.assist.enums.IssueProofLicenseTypeEnum;
import gov.derate.proof.assist.query.CatalogApiQuery;
import gov.derate.proof.catalog.bo.*;
import gov.derate.proof.catalog.dto.AuditRelationTemplateDto;
import gov.derate.proof.catalog.dto.ProofCatalogDto;
import gov.derate.proof.catalog.dto.ProofCatalogLicenseItemImplItemInfoDto;
import gov.derate.proof.catalog.entity.*;
import gov.derate.proof.catalog.exception.CatalogServiceException;
import gov.derate.proof.catalog.query.*;
import gov.derate.proof.catalog.repository.*;
import gov.derate.proof.catalog.req.ProofCatalogRequest;
import gov.derate.proof.catalog.resp.CatalogResponseCode;
import gov.derate.proof.catalog.resp.ProofCatalogResponse;
import gov.derate.proof.catalog.service.ProofCatalogService;
import gov.derate.proof.common.bo.AttachmentBo;
import gov.derate.proof.common.bo.UploadFileAttachmentBo;
import gov.derate.proof.common.entity.ExcelTablesModel;
import gov.derate.proof.common.enums.ProofProvideTypeEnum;
import gov.derate.proof.common.exception.DictionaryConfigException;
import gov.derate.proof.common.response.DictionaryResponseCode;
import gov.derate.proof.common.response.ResponseCode;
import gov.derate.proof.common.service.UploadFileAttachmentService;
import gov.derate.proof.common.utils.Base64EncoderUtils;
import gov.derate.proof.common.utils.BeanCopyUtils;
import gov.derate.proof.common.utils.ExcelUtils;
import gov.derate.proof.common.utils.UserUtils;
import gov.derate.proof.dictionary.entity.DictionaryDicTypeConstant;
import gov.derate.proof.item.service.ItemService;
import gov.derate.proof.license.dto.ImplementLicenseItemInfoDto;
import gov.derate.proof.license.service.LicenseFacadeService;
import gov.derate.proof.license.service.LicenseFacadeStrategyService;
import gov.derate.proof.list.bo.ProofListByPageViewBo;
import gov.derate.proof.list.query.ProofListByPageViewQuery;
import gov.derate.proof.list.service.ProofListService;
import gov.licc.func.api.amp.dto.DictDetailDto;
import gov.licc.func.api.amp.dto.DictDto;
import gov.licc.func.api.amp.dto.DivisionDto;
import gov.licc.func.api.amp.service.DictPublicService;
import gov.licc.func.api.amp.service.DivisionPublicService;
import gov.licc.func.api.auth.dto.OrganizationDto;
import gov.licc.func.api.auth.service.OrganizationPublicService;
import gov.license.common.api.entity.BaseDo;
import gov.license.common.api.exception.ApplicationServiceException;
import gov.license.common.api.resp.BaseResponseCode;
import gov.license.common.tools.jackson.JacksonUtil;
import gov.license.jpa.Specifications;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Nonnull;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * ProofCatalog service impl
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-02
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class ProofCatalogServiceImpl implements ProofCatalogService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProofCatalogServiceImpl.class);
    public static final String CATALOG_SER_NUMBER_PREFIX = "ZMML";
    /**
     * 证明目录仓库
     */
    @Autowired
    private ProofCatalogRepository proofCatalogRepository;
    /**
     * 证明目录-人工协查关系
     */
    @Autowired
    private ProofCatalogArtificialRelationRepository artificialRelationRepository;
    /**
     * 证明目录，数据共享关系
     */
    @Autowired
    private ProofCatalogDataSharedRelationRepository dataSharedRelationRepository;

    /**
     * 证明目录承诺书关系
     */
    @Autowired
    private ProofCatalogClerkCommitmentRepository proofCatalogClerkCommitmentRepository;
    /**
     * 证明目录部门部门关系
     */
    @Autowired
    private ProofCatalogDeptSurveyRepository proofCatalogDeptSurveyRepository;
    /**
     * 证明目录电子证照关心
     */
    @Autowired
    private ProofCatalogLicenseRelationRepository licenseRelationRepository;
    @Autowired
    private ProofCatalogLicenseItemRelationRepository proofCatalogLicenseItemRelationRepository;
    /**
     * 证明目录其他关系
     */
    @Autowired
    private ProofCatalogOtherRelationRepository proofCatalogOtherRelationRepository;
    /**
     * 证明目录更变日志仓库
     */
    @Autowired
    private ProofCatalogChangeLogRepository proofCatalogChangeLogRepository;

    /**
     * 数据字典
     */
    @Autowired
    private DictPublicService dictPublicService;
    /**
     * 事项清单服务
     */
    @Autowired
    @Lazy
    private ProofListService proofListService;
    /**
     * 证明目录电子证照关系
     */
    @Autowired
    private ProofCatalogLicenseRelationRepository proofCatalogLicenseRelationRepository;
    /**
     * 证明目录数据共享关系
     */
    @Autowired
    private ProofCatalogDataSharedRelationRepository proofCatalogDataSharedRelationRepository;
    /**
     * 证明目录人工协查
     */
    @Autowired
    private ProofCatalogArtificialRelationRepository proofCatalogArtificialRelationRepository;
    /**
     * 数据共享配置仓库
     */
    @Autowired
    private ProofCatalogDataSharedConfigRepository proofCatalogDataSharedConfigRepository;
    /**
     * 行政区划服务
     */
    @Autowired
    private DivisionPublicService divisionPublicService;
    /**
     * 组织机构服务
     */
    @Autowired
    private OrganizationPublicService organizationPublicService;

    /**
     * 证明目录-人工协查关系视图表 repository
     */
    @Autowired
    private ProofCatalogArtificialRelationViewDoRepository proofCatalogArtificialRelationViewDoRepository;
    /**
     * 审核模板仓库
     */
    @Autowired
    private AuditRelationTemplateRepository auditRelationTemplateRepository;
    /**
     * 证照策略服务
     */
    @Autowired
    private LicenseFacadeStrategyService licenseFacadeStrategyService;
    /**
     * 事项服务
     */
    @Autowired
    private ItemService itemService;
    /**
     * 文件上传处理。
     */
    @Autowired
    private UploadFileAttachmentService uploadFileAttachmentService;

    /**
     * 获取证明目录，派号
     * 证明目录编码由系统自动生成，不允许用户编辑，规则为ZMML + A~Z +
     * XXXXXX(000001~999999)，A~Z是证明开具单位类型，XXXXXX(000001~999999))是自然增长数
     *
     * @param proofProvideType 类型
     * @return 字符串
     */
    private synchronized String generateCodeByDict(ProofProvideTypeEnum proofProvideType)
            throws NumberFormatException {
        String seriaNumberPrefix = CATALOG_SER_NUMBER_PREFIX + proofProvideType.getEnTypeCode();
        long seriaNum = getIncrementBySerNumberPrefixKey(seriaNumberPrefix);
        int retry = 1;
        while (seriaNum == -1) {
            LOGGER.debug("更新失败重执行：" + seriaNum);
            if (retry >= 50) {
                throw new RuntimeException("获取自增流水号重试：" + retry + " 后依然无法获取自增值!");
            }
            seriaNum = getIncrementBySerNumberPrefixKey(seriaNumberPrefix);
            retry++;
        }
        String seriaNumStr = String.format("%06d", seriaNum);
        return seriaNumberPrefix + seriaNumStr;
    }

    /**
     * 获取自增的数据
     *
     * @param serNumberPrefix 证明目录编码前缀 ZMML + A~Z
     * @return 自增数据
     */
    private long getIncrementBySerNumberPrefixKey(String serNumberPrefix)
            throws NumberFormatException {
        Long serNumber = findBySerNumberPrefixKey(serNumberPrefix);
        if (Objects.isNull(serNumber)) {
            serNumber = updateSerialNumInit(serNumberPrefix);
            return serNumber;
        } else {
            serNumber = forUpdateIncremental(serNumberPrefix);
            return serNumber;
        }
    }

    /**
     * 更新序号，并且+1
     *
     * @param serNumberPrefix 序号前缀
     * @return 序号，serNumber+1
     */
    private Long forUpdateIncremental(String serNumberPrefix) {
        Optional<DictDto> appKeyOptional;
        try {
            appKeyOptional = dictPublicService.findDictAndDetailByName(DictionaryDicTypeConstant.CATALOG_SER_NUM.name());
        } catch (Exception e) {
            throw new CatalogServiceException(DictionaryResponseCode.SERVICE_DICTIONARY_ERROR);
        }
        if (!appKeyOptional.isPresent()) {
            throw new CatalogServiceException(DictionaryResponseCode.SERVICE_DICTIONARY_ERROR);
        }
        DictDto dictDto = appKeyOptional.get();
        Optional<String> detailValue = dictDto.getDetailValue(serNumberPrefix);
        if (!detailValue.isPresent()) {
            throw new CatalogServiceException(DictionaryResponseCode.SERVICE_DICTIONARY_ERROR);
        }
        long parseSerNum = Long.parseLong(detailValue.get());
        long result = parseSerNum + 1L;
        Optional<DictDetailDto> serNumberOptional = dictDto.getDictDetailList().stream().filter(item -> serNumberPrefix.equals(item.getKey())).findFirst();
        serNumberOptional.ifPresent(item -> item.setValue(String.valueOf(result)));
        dictPublicService.saveDictAndDetail(dictDto);
        return result;
    }

    /**
     * 查询字典该证明目录序号
     *
     * @param serNumberPrefix 序号前缀
     * @return 序号，因为是初始化，返回1L
     */
    private Long findBySerNumberPrefixKey(String serNumberPrefix) {
        Optional<String> appKeyOptional;
        try {
            // 没有配置该字典key，直接报错。
            appKeyOptional = dictPublicService.getDetailValue(DictionaryDicTypeConstant.CATALOG_SER_NUM.name(), serNumberPrefix);
        } catch (Exception e) {
            throw new CatalogServiceException(DictionaryResponseCode.SERVICE_DICTIONARY_ERROR);
        }
        return appKeyOptional.map(Long::valueOf).orElse(null);
    }

    /**
     * 初始化序列号
     *
     * @param serNumberPrefix key
     * @return 1
     */

    private Long updateSerialNumInit(String serNumberPrefix) {
        Optional<DictDto> appKeyOptional;
        try {
            // 没有配置该字典key，直接报错。
            appKeyOptional = dictPublicService.findDictAndDetailByName(DictionaryDicTypeConstant.CATALOG_SER_NUM.name());
        } catch (Exception e) {
            throw new CatalogServiceException(DictionaryResponseCode.SERVICE_DICTIONARY_ERROR);
        }
        if (!appKeyOptional.isPresent()) {
            throw new CatalogServiceException(DictionaryResponseCode.SERVICE_DICTIONARY_ERROR);
        }
        DictDto dictDto = appKeyOptional.get();
        long result = 1L;
        Optional<DictDetailDto> serNumberOptional = dictDto.getDictDetailList().stream().filter(item -> serNumberPrefix.equals(item.getKey())).findFirst();
        if (!serNumberOptional.isPresent()) {
            // 不存在该字典值新增，新增字典明细
            DictDetailDto dictDetailDto = new DictDetailDto();
            dictDetailDto.setDictId(dictDto.getId());
            dictDetailDto.setKey(serNumberPrefix);
            dictDetailDto.setValue(String.valueOf(result));
            dictDetailDto.setDescription("证明目录序列号");
            dictDetailDto.setSort(0);
            dictDetailDto.setHasEncrypt(false);
            if (CollectionUtils.isEmpty(dictDto.getDictDetailList())) {
                dictDto.setDictDetailList(Lists.newArrayList());
            }
            dictDto.getDictDetailList().add(dictDetailDto);
            dictPublicService.saveDictAndDetail(dictDto);
        } else {
            throw new CatalogServiceException(DictionaryResponseCode.SERVICE_DICTIONARY_ERROR);
        }
        return result;
    }

    /**
     * 判断证明目录是否存在
     *
     * @param proofCatalogDo 证明目录
     */
    private void validateProofCatalogExist(ProofCatalogDo proofCatalogDo) throws ApplicationServiceException {
        if (!Objects.isNull(proofCatalogDo)) {
            throw new CatalogServiceException(CatalogResponseCode.CATALOG_CREATE_NAME_EXISTS_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveProofCatalog(ProofCatalogCreateBo proofCatalogCreateBo) {
        validateProofCatalogExist(proofCatalogRepository.findByName(proofCatalogCreateBo.getName()));
        ProofCatalogDo proofCatalogDo = new ProofCatalogDo();
        String proofCatalogId = UUID.randomUUID().toString();
        // 生成证明目录编码
        BeanCopyUtils.copy(proofCatalogCreateBo, proofCatalogDo);
        proofCatalogDo.setId(proofCatalogId);
        proofCatalogDo.setAccountName(UserUtils.getUserName());
        proofCatalogDo.setCode(this.generateCodeByDict(proofCatalogCreateBo.getUnitType()));

        List<ProofCatalogArtificialRelationDo> artificialRelationList = Lists.newArrayList();
        List<ProofCatalogDataSharedRelationDo> dataSharedRelationList = Lists.newArrayList();
        List<ProofCatalogClerkCommitmentDo> proofCatalogClerkCommitmentDos = Lists.newArrayList();
        List<ProofCatalogDeptSurveyDo> proofCatalogDeptSurveyDos = Lists.newArrayList();
        List<ProofCatalogLicenseRelationDo> licenseRelationList = Lists.newArrayList();
        List<ProofCatalogOtherRelationDo> proofCatalogOtherRelationDos = Lists.newArrayList();
        List<AuditRelationTemplateDto> auditRelationTemplateDoList = Lists.newArrayList();

        // 证明目录关联的人工协查
        if (CollectionUtils.isNotEmpty(proofCatalogCreateBo.getProofCatalogArtificialList())) {
            proofCatalogCreateBo.getProofCatalogArtificialList()
                    .stream()
                    .filter(Objects::nonNull)
                    .forEach(item -> {
                        item.setProofCatalogId(proofCatalogDo.getId());
                        item.setProofCatalogCode(proofCatalogDo.getCode());
                        String onlyKey = item.buildAuditRelationTemplateKey();
                        item.setAuditRelationTemplateKey(onlyKey);
                        List<AuditRelationTemplateDto> auditRelationTemplateList = item.getAuditRelationTemplateList();
                        auditRelationTemplateList.forEach(auditDto -> auditDto.setOnlyKey(onlyKey));
                        auditRelationTemplateDoList.addAll(auditRelationTemplateList);
                        Optional<AuditRelationTemplateDto> first = auditRelationTemplateList.stream().findFirst();
                        first.ifPresent(tempDto -> {
                            item.setInvestigationDeptName(tempDto.getAuditOrgName());
                            item.setInvestigationDeptCode(tempDto.getAuditOrgCode());
                            item.setDivisionCode(tempDto.getAuditDivisionCode());
                            item.setDivisionName(tempDto.getAuditDivisionName());
                        });
                        // 设定开具才保存相关值
                        if (Objects.nonNull(item.getIssueProofLicense()) && IssueProofLicenseTypeEnum.isIssue(item.getIssueProofLicense())) {
                            // 设值开通目录信息
                            String licenseCode = item.getLicenseCode();
                            LicenseFacadeService licenseFacadeService = licenseFacadeStrategyService.settingStrategy(LicenseFacadeStrategyService.LicenseProcessStrategy.LICENSE_PROOF);
                            List<ImplementLicenseItemInfoDto> infoDtoList = licenseFacadeService.queryImplLicenseItemInfoByBasicCode(licenseCode);
                            if (CollectionUtils.isEmpty(infoDtoList)) {
                                item.setImplementItemInfoJson(JacksonUtil.toJsonStr(Lists.newArrayList()));
                            } else {
                                List<ProofCatalogLicenseItemImplItemInfoDto> collect = infoDtoList.stream().map(implInfo -> {
                                    ProofCatalogLicenseItemImplItemInfoDto copy = BeanCopyUtils.copy(item, ProofCatalogLicenseItemImplItemInfoDto.class);
                                    copy.setBasicCode(implInfo.buildBasicCode());
                                    String shortName = implInfo.getImplementOrg();
                                    List<OrganizationDto> organizationsByShortName = organizationPublicService.getOrganizationsByShortName(shortName);
                                    if (CollectionUtils.isNotEmpty(organizationsByShortName)) {
                                        OrganizationDto organizationDto = organizationsByShortName.get(0);
                                        copy.setImplementCreditCode(organizationDto.getCreditCode());
                                    }
                                    copy.setImplementOrgCode(implInfo.buildImplementOrgCode());
                                    return copy;
                                }).collect(Collectors.toList());
                                item.setImplementItemInfoJson(JacksonUtil.toJsonStr(collect));
                            }
                        } else {
                            item.setLicenseCode(null);
                            item.setLicenseName(null);
                            item.setIssueProofLicenseWay(null);
                            item.setImplementItemInfoJson(JacksonUtil.toJsonStr(Lists.newArrayList()));
                        }
                        ProofCatalogArtificialRelationDo entity = new ProofCatalogArtificialRelationDo();
                        BeanCopyUtils.copy(item, entity);
                        artificialRelationList.add(entity);
                    });
        }

        // 证明目录关联的数据共享
        if (CollectionUtils.isNotEmpty(proofCatalogCreateBo.getProofCatalogDataSharedList())) {
            proofCatalogCreateBo.getProofCatalogDataSharedList()
                    .stream()
                    .filter(Objects::nonNull)
                    .forEach(item -> {
                        ProofCatalogDataSharedRelationDo entity = new ProofCatalogDataSharedRelationDo();
                        BeanCopyUtils.copy(item, entity);
                        entity.setProofCatalogId(proofCatalogDo.getId());
                        entity.setCatalogCode(proofCatalogDo.getCode());
                        String dataThemeCode = item.getDataThemeCode();
                        Optional<ProofCatalogDataSharedConfigDo> byDataThemeCode = proofCatalogDataSharedConfigRepository.findByDataThemeCode(dataThemeCode);
                        ProofCatalogDataSharedConfigDo configDo = byDataThemeCode.orElseThrow(() -> new CatalogServiceException(new ResponseCode(ResponseCode.BIZ_ERROR.getCode(), "")));
                        entity.setDataSharedConfigId(configDo.getId());
                        dataSharedRelationList.add(entity);
                    });
        }

        // 证明目录关联的告知承诺
        if (CollectionUtils.isNotEmpty(proofCatalogCreateBo.getProofCatalogClerkCommitmentList())) {
            proofCatalogCreateBo.getProofCatalogClerkCommitmentList()
                    .stream()
                    .filter(Objects::nonNull)
                    .forEach(item -> {
                        ProofCatalogClerkCommitmentDo entity = new ProofCatalogClerkCommitmentDo();
                        BeanCopyUtils.copy(item, entity);
                        entity.setProofCatalogId(proofCatalogDo.getId());
                        entity.setProofCatalogCode(proofCatalogDo.getCode());
                        proofCatalogClerkCommitmentDos.add(entity);
                    });
        }

        // 证明目录关联的部门自行调查表
        if (CollectionUtils.isNotEmpty(proofCatalogCreateBo.getProofCatalogDeptSurveyList())) {
            proofCatalogCreateBo.getProofCatalogDeptSurveyList()
                    .stream()
                    .filter(Objects::nonNull)
                    .forEach(item -> {
                        ProofCatalogDeptSurveyDo entity = new ProofCatalogDeptSurveyDo();
                        BeanCopyUtils.copy(item, entity);
                        entity.setProofCatalogId(proofCatalogId);
                        entity.setProofCatalogCode(proofCatalogDo.getCode());
                        proofCatalogDeptSurveyDos.add(entity);
                    });
        }

        // 证明目录关联的电子证照
        if (CollectionUtils.isNotEmpty(proofCatalogCreateBo.getProofCatalogLicenseRelationList())) {
            proofCatalogCreateBo.getProofCatalogLicenseRelationList()
                    .stream()
                    .filter(Objects::nonNull)
                    .forEach(item -> {
                        ProofCatalogLicenseRelationDo entity = new ProofCatalogLicenseRelationDo();
                        BeanCopyUtils.copy(item, entity);
                        entity.setProofCatalogId(proofCatalogDo.getId());
                        entity.setProofCatalogCode(proofCatalogDo.getCode());
                        licenseRelationList.add(entity);
                    });
        }

        // 证明目录关联的电子证明
        if (CollectionUtils.isNotEmpty(proofCatalogCreateBo.getProofCatalogLicenseItemRelation())) {
            List<ProofCatalogLicenseItemRelationDo> licenseItemRelationDoList = proofCatalogCreateBo.getProofCatalogLicenseItemRelation()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(item -> {
                        item.setProofCatalogId(proofCatalogDo.getId());
                        item.setProofCatalogCode(proofCatalogDo.getCode());
                        // 设值开通目录信息
                        String licenseCode = item.getLicenseCode();
                        LicenseFacadeService licenseFacadeService = licenseFacadeStrategyService.settingStrategy(LicenseFacadeStrategyService.LicenseProcessStrategy.LICENSE_PROOF);
                        List<ImplementLicenseItemInfoDto> infoDtoList = licenseFacadeService.queryImplLicenseItemInfoByBasicCode(licenseCode);
                        if (CollectionUtils.isEmpty(infoDtoList)) {
                            item.setImplementItemInfoJson(JacksonUtil.toJsonStr(Lists.newArrayList()));
                        } else {
                            List<ProofCatalogLicenseItemImplItemInfoDto> collect = infoDtoList.stream().map(implInfo -> {
                                ProofCatalogLicenseItemImplItemInfoDto copy = BeanCopyUtils.copy(item, ProofCatalogLicenseItemImplItemInfoDto.class);
                                copy.setBasicCode(implInfo.buildBasicCode());
                                String shortName = implInfo.getImplementOrg();
                                List<OrganizationDto> organizationsByShortName = organizationPublicService.getOrganizationsByShortName(shortName);
                                if (CollectionUtils.isNotEmpty(organizationsByShortName)) {
                                    OrganizationDto organizationDto = organizationsByShortName.get(0);
                                    copy.setImplementCreditCode(organizationDto.getCreditCode());
                                }
                                copy.setImplementOrgCode(implInfo.buildImplementOrgCode());
                                return copy;
                            }).collect(Collectors.toList());
                            item.setImplementItemInfoJson(JacksonUtil.toJsonStr(collect));
                        }

                        return BeanCopyUtils.copy(item, ProofCatalogLicenseItemRelationDo.class);
                    }).collect(Collectors.toList());
            proofCatalogLicenseItemRelationRepository.saveAll(licenseItemRelationDoList);
        }

        // 证明目录关联的其它表
        if (CollectionUtils.isNotEmpty(proofCatalogCreateBo.getProofCatalogOtherRelationList())) {
            proofCatalogCreateBo.getProofCatalogOtherRelationList()
                    .stream()
                    .filter(Objects::nonNull)
                    .forEach(item -> {
                        ProofCatalogOtherRelationDo entity = new ProofCatalogOtherRelationDo();
                        BeanCopyUtils.copy(item, entity);
                        entity.setProofCatalogId(proofCatalogDo.getId());
                        entity.setProofCatalogId(proofCatalogDo.getCode());
                        proofCatalogOtherRelationDos.add(entity);
                    });
        }

        // 保存证明目录与关联的数据
        proofCatalogRepository.save(proofCatalogDo);
        artificialRelationRepository.saveAll(artificialRelationList);
        dataSharedRelationRepository.saveAll(dataSharedRelationList);
        proofCatalogClerkCommitmentRepository.saveAll(proofCatalogClerkCommitmentDos);
        proofCatalogDeptSurveyRepository.saveAll(proofCatalogDeptSurveyDos);
        licenseRelationRepository.saveAll(licenseRelationList);

        proofCatalogOtherRelationRepository.saveAll(proofCatalogOtherRelationDos);
        saveAuditRelationTemplateDoByDto(auditRelationTemplateDoList);

        // 生成证明目录创建日志
        ProofCatalogChangeLogDo logDo = new ProofCatalogChangeLogDo();
        logDo.setOperationName("创建证明目录");
        logDo.setProofCatalogId(proofCatalogId);
        logDo.setAccount(UserUtils.getUserName());
        logDo.setAccountName(UserUtils.getUserName());
        proofCatalogChangeLogRepository.save(logDo);
        return proofCatalogId;
    }

    /**
     * 保存多审核对象数据
     *
     * @param dtoList dto数据
     */

    private void saveAuditRelationTemplateDoByDto(List<AuditRelationTemplateDto> dtoList) {
        if (CollectionUtils.isNotEmpty(dtoList)) {
            Set<String> deleteOnlyKey = dtoList.stream().map(AuditRelationTemplateDto::getOnlyKey).collect(Collectors.toSet());
            auditRelationTemplateRepository.deleteAllByOnlyKeyIn(deleteOnlyKey);
            auditRelationTemplateRepository.saveAll(BeanCopyUtils.copyList(dtoList, AuditRelationTemplateDo.class));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void edit(ProofCatalogEditBo afterCatalog, String proofCatalogId) {
        ProofCatalogDetailDto beforeCatalog = findDetailById(proofCatalogId);
        Optional.ofNullable(beforeCatalog)
                .orElseThrow(() -> new CatalogServiceException(ResponseCode.FIND_PROOF_CATALOG_EMPTY));
        // 更变日志，记录统一的最后修改时间，前端多条就可以根据此时间进行记录的合并。
        long lastModificationTime = System.currentTimeMillis();

        // 生成证明目录创建日志
        afterCatalog.hasDiffName(beforeCatalog.getName(), lastModificationTime);
        afterCatalog.hasDiffProofProvideType(beforeCatalog.getUnitType(), lastModificationTime);
        afterCatalog.hasDiffIndustryDeptCode(beforeCatalog.getIndustryDeptCode(), lastModificationTime);
        afterCatalog.hasDiffIndustryDeptName(beforeCatalog.getIndustryDeptName(), lastModificationTime);
        String proofCatalogCode = afterCatalog.getCode();

        // 以下需要全部换成更变前的数据
        List<ProofCatalogArtificialRelationDo> beforeProofCatalogArtificialList = artificialRelationRepository
                .findAllByProofCatalogId(proofCatalogId);
        List<ProofCatalogClerkCommitmentDo> beforeProofCatalogClerkCommitmentList = proofCatalogClerkCommitmentRepository
                .findAllByProofCatalogId(proofCatalogId);
        List<ProofCatalogDataSharedRelationDo> beforeProofCatalogDataSharedList = dataSharedRelationRepository
                .findAllByProofCatalogId(proofCatalogId);
        List<ProofCatalogDeptSurveyDo> beforeProofCatalogDeptSurveyList = proofCatalogDeptSurveyRepository
                .findAllByProofCatalogId(proofCatalogId);
        List<ProofCatalogLicenseRelationDo> beforeProofCatalogLicenseRelationList = licenseRelationRepository
                .findAllByProofCatalogId(proofCatalogId);
        List<ProofCatalogOtherRelationDo> beforeProofCatalogOtherRelationList = proofCatalogOtherRelationRepository
                .findAllByProofCatalogId(proofCatalogId);
        List<ProofCatalogLicenseItemRelationDo> beforeProofCatalogLicenseItemRelation = proofCatalogLicenseItemRelationRepository
                .findAllByProofCatalogId(proofCatalogId);

        List<ProofCatalogArtificialRelationDto> proofCatalogArtificialList = BeanCopyUtils
                .copyList(beforeProofCatalogArtificialList, ProofCatalogArtificialRelationDto.class);
        List<ProofCatalogClerkCommitmentDto> proofCatalogClerkCommitmentList = BeanCopyUtils
                .copyList(beforeProofCatalogClerkCommitmentList, ProofCatalogClerkCommitmentDto.class);
        List<ProofCatalogDataSharedRelationDto> proofCatalogDataSharedList = BeanCopyUtils
                .copyList(beforeProofCatalogDataSharedList, ProofCatalogDataSharedRelationDto.class);
        List<ProofCatalogDeptSurveyDto> proofCatalogDeptSurveyList = BeanCopyUtils
                .copyList(beforeProofCatalogDeptSurveyList, ProofCatalogDeptSurveyDto.class);
        List<ProofCatalogLicenseRelationDto> proofCatalogLicenseRelationList = BeanCopyUtils
                .copyList(beforeProofCatalogLicenseRelationList, ProofCatalogLicenseRelationDto.class);
        List<ProofCatalogOtherRelationDto> proofCatalogOtherRelationList = BeanCopyUtils
                .copyList(beforeProofCatalogOtherRelationList, ProofCatalogOtherRelationDto.class);
        List<ProofCatalogLicenseItemRelationDto> proofCatalogLicenseItemRelation = BeanCopyUtils
                .copyList(beforeProofCatalogLicenseItemRelation, ProofCatalogLicenseItemRelationDto.class);

        afterCatalog.hasDiffProofCatalogArtificialList(proofCatalogArtificialList, lastModificationTime);
        afterCatalog.hasDiffProofCatalogClerkCommitmentList(proofCatalogClerkCommitmentList, lastModificationTime);
        afterCatalog.hasDiffProofCatalogDataSharedList(proofCatalogDataSharedList, lastModificationTime);
        afterCatalog.hasDiffProofCatalogDeptSurveyList(proofCatalogDeptSurveyList, lastModificationTime);
        afterCatalog.hasDiffProofCatalogLicenseRelationList(proofCatalogLicenseRelationList, lastModificationTime);
        afterCatalog.hasDiffProofCatalogLicenseItemRelation(proofCatalogLicenseItemRelation, lastModificationTime);
        afterCatalog.hasDiffProofCatalogOtherRelationList(proofCatalogOtherRelationList, lastModificationTime);

        // 根据id，找到所有的相关的证明目录数据
        // 根据editBo与找到的证明目录数据进行每个字段比对。
        // 如果有不一样，记录日志，修改过什么字段，修改前后值
        if (CollectionUtils.isNotEmpty(afterCatalog.getChangeLogBoList())) {
            List<ProofCatalogChangeLogDo> saveList = BeanCopyUtils.copyList(afterCatalog.getChangeLogBoList(),
                    ProofCatalogChangeLogDo.class);
            if (CollectionUtils.isNotEmpty(saveList)) {
                proofCatalogChangeLogRepository.saveAll(saveList);
            }
        }

        List<ProofCatalogArtificialRelationDto> proofCatalogArtificialDoList = afterCatalog.getProofCatalogArtificialList();
        List<ProofCatalogClerkCommitmentDto> proofCatalogClerkCommitmentDoList = afterCatalog.getProofCatalogClerkCommitmentList();
        List<ProofCatalogDataSharedRelationDto> proofCatalogDataSharedDoList = afterCatalog.getProofCatalogDataSharedList();
        List<ProofCatalogDeptSurveyDto> proofCatalogDeptSurveyDoList = afterCatalog.getProofCatalogDeptSurveyList();
        List<ProofCatalogLicenseRelationDto> proofCatalogLicenseRelationDoList = afterCatalog.getProofCatalogLicenseRelationList();
        List<ProofCatalogOtherRelationDto> proofCatalogOtherRelationDoList = afterCatalog.getProofCatalogOtherRelationList();
        List<ProofCatalogLicenseItemRelationDto> proofCatalogLicenseItemRelationDo = afterCatalog.getProofCatalogLicenseItemRelation();

        Optional<ProofCatalogDo> findById = proofCatalogRepository.findById(proofCatalogId);
        ProofCatalogDo entityCatalog = findById
                .orElseThrow(() -> new CatalogServiceException(ResponseCode.FIND_PROOF_CATALOG_EMPTY));
        entityCatalog.setName(afterCatalog.getName());
        entityCatalog.setUnitType(afterCatalog.getUnitType());
        entityCatalog.setIndustryDeptCode(afterCatalog.getIndustryDeptCode());
        entityCatalog.setIndustryDeptName(afterCatalog.getIndustryDeptName());
        proofCatalogRepository.save(entityCatalog);

        // 先清除所有之前的证明目录清理方式。
        if (CollectionUtils.isNotEmpty(beforeProofCatalogArtificialList)) {
            artificialRelationRepository.deleteAllInBatch(beforeProofCatalogArtificialList);
        }
        if (CollectionUtils.isNotEmpty(beforeProofCatalogClerkCommitmentList)) {
            proofCatalogClerkCommitmentRepository.deleteAllInBatch(beforeProofCatalogClerkCommitmentList);
        }
        if (CollectionUtils.isNotEmpty(beforeProofCatalogDataSharedList)) {
            dataSharedRelationRepository.deleteAllInBatch(beforeProofCatalogDataSharedList);
        }
        if (CollectionUtils.isNotEmpty(beforeProofCatalogDeptSurveyList)) {
            proofCatalogDeptSurveyRepository.deleteAllInBatch(beforeProofCatalogDeptSurveyList);
        }
        if (CollectionUtils.isNotEmpty(beforeProofCatalogLicenseRelationList)) {
            licenseRelationRepository.deleteAllInBatch(beforeProofCatalogLicenseRelationList);
        }
        if (CollectionUtils.isNotEmpty(beforeProofCatalogOtherRelationList)) {
            proofCatalogOtherRelationRepository.deleteAllInBatch(beforeProofCatalogOtherRelationList);
        }
        if (CollectionUtils.isNotEmpty(beforeProofCatalogLicenseItemRelation)) {
            proofCatalogLicenseItemRelationRepository.deleteAllInBatch(beforeProofCatalogLicenseItemRelation);
        }
        List<AuditRelationTemplateDto> auditRelationTemplateDtoList = Lists.newArrayList();
        // 以下防止beanCopy导致proofCatalogId无法copy，手动设值,注意 ： id值需要重新赋值。否则会报错。
        if (CollectionUtils.isNotEmpty(proofCatalogArtificialDoList)) {
            proofCatalogArtificialDoList
                    .stream().filter(Objects::nonNull)
                    .forEach(item -> {
                        item.setId(UUID.randomUUID().toString());
                        item.setProofCatalogId(proofCatalogId);
                        item.setProofCatalogCode(proofCatalogCode);
                        item.setAuditRelationTemplateKey(item.buildAuditRelationTemplateKey());
                        item.setAuditRelationTemplateKey(item.buildAuditRelationTemplateKey());
                        item.getAuditRelationTemplateList().forEach(templateDto -> templateDto.setOnlyKey(item.buildAuditRelationTemplateKey()));
                        auditRelationTemplateDtoList.addAll(item.getAuditRelationTemplateList());
                        Optional<AuditRelationTemplateDto> first = item.getAuditRelationTemplateList().stream().findFirst();
                        first.ifPresent(tempDto -> {
                            item.setInvestigationDeptName(tempDto.getAuditOrgName());
                            item.setInvestigationDeptCode(tempDto.getAuditOrgCode());
                            item.setDivisionName(tempDto.getAuditDivisionName());
                            item.setDivisionCode(tempDto.getAuditDivisionCode());
                        });
                        Optional<DivisionDto> divisionDtoOptional = divisionPublicService.findByCode(item.getDivisionCode());
                        divisionDtoOptional.ifPresent(divisionDto -> item.setDivisionName(divisionDto.getName()));
                        Optional<OrganizationDto> organizationDto = organizationPublicService.findByCreditCode(item.getInvestigationDeptCode());
                        organizationDto.ifPresent(org -> item.setInvestigationDeptName(org.getName()));
                        if (Objects.nonNull(item.getIssueProofLicense()) && IssueProofLicenseTypeEnum.isIssue(item.getIssueProofLicense())) {
                            // 设值开通目录信息
                            String licenseCode = item.getLicenseCode();
                            LicenseFacadeService licenseFacadeService = licenseFacadeStrategyService.settingStrategy(LicenseFacadeStrategyService.LicenseProcessStrategy.LICENSE_PROOF);
                            List<ImplementLicenseItemInfoDto> infoDtoList = licenseFacadeService.queryImplLicenseItemInfoByBasicCode(licenseCode);
                            if (CollectionUtils.isEmpty(infoDtoList)) {
                                item.setImplementItemInfoJson(JacksonUtil.toJsonStr(Lists.newArrayList()));
                            } else {
                                List<ProofCatalogLicenseItemImplItemInfoDto> collect = infoDtoList.stream().map(implInfo -> {
                                    ProofCatalogLicenseItemImplItemInfoDto copy = new ProofCatalogLicenseItemImplItemInfoDto();
                                    copy.setImplementCode(implInfo.getImplementCode());
                                    copy.setImplementOrgCode(implInfo.getImplementCode());
                                    copy.setImplementOrg(implInfo.getImplementOrg());
                                    copy.setImplementDivisionCode(implInfo.getImplementDivisionCode());
//                        copy.setImplementCreditCode(implInfo.buildImplementOrgCode());
                                    copy.setBasicCode(implInfo.buildBasicCode());
                                    String shortName = implInfo.getImplementOrg();
                                    List<OrganizationDto> organizationsByShortName = organizationPublicService.getOrganizationsByShortName(shortName);
                                    if (CollectionUtils.isNotEmpty(organizationsByShortName)) {
                                        OrganizationDto organizationDtoOrg = organizationsByShortName.get(0);
                                        copy.setImplementCreditCode(organizationDtoOrg.getCreditCode());
                                        copy.setImplementDivision(organizationDtoOrg.getDivisionName());
                                    }
                                    copy.setImplementOrgCode(implInfo.buildImplementOrgCode());
                                    return copy;
                                }).collect(Collectors.toList());
                                item.setImplementItemInfoJson(JacksonUtil.toJsonStr(collect));
                            }
                        } else {
                            item.setLicenseCode(null);
                            item.setLicenseName(null);
                            item.setIssueProofLicenseWay(null);
                            item.setImplementItemInfoJson(JacksonUtil.toJsonStr(Lists.newArrayList()));
                        }

                    });
            artificialRelationRepository.saveAll(BeanCopyUtils.copyList(proofCatalogArtificialDoList, ProofCatalogArtificialRelationDo.class));
        }
        if (CollectionUtils.isNotEmpty(proofCatalogClerkCommitmentDoList)) {
            proofCatalogClerkCommitmentDoList
                    .stream().filter(Objects::nonNull)
                    .forEach(item -> {
                        item.setId(UUID.randomUUID().toString());
                        item.setProofCatalogId(proofCatalogId);
                        item.setProofCatalogCode(proofCatalogCode);
                    });
            proofCatalogClerkCommitmentRepository.saveAll(BeanCopyUtils.copyList(proofCatalogClerkCommitmentDoList, ProofCatalogClerkCommitmentDo.class));
        }
        if (CollectionUtils.isNotEmpty(proofCatalogDataSharedDoList)) {
            proofCatalogDataSharedDoList
                    .stream().filter(Objects::nonNull)
                    .forEach(item -> {
                        item.setId(UUID.randomUUID().toString());
                        item.setProofCatalogId(proofCatalogId);
                        item.setProofCatalogCode(proofCatalogCode);
                        Optional<ProofCatalogDataSharedConfigDo> optionalConfig = proofCatalogDataSharedConfigRepository.findByDataThemeCode(item.getDataThemeCode());
                        ProofCatalogDataSharedConfigDo configDo = optionalConfig.orElseThrow(() -> new CatalogServiceException(new ResponseCode(ResponseCode.BIZ_ERROR.getCode(), "数据共享配置为空")));
                        item.setDataSharedConfigId(configDo.getId());
                        AtomicReference<String> systemName = getDataSharedSystemNameBySystemCode(item.getSystemCode());
                        item.setSystemName(systemName.get());
                    });
            dataSharedRelationRepository.saveAll(BeanCopyUtils.copyList(proofCatalogDataSharedDoList, ProofCatalogDataSharedRelationDo.class));
        }
        if (CollectionUtils.isNotEmpty(proofCatalogDeptSurveyDoList)) {
            proofCatalogDeptSurveyDoList
                    .stream().filter(Objects::nonNull)
                    .forEach(item -> {
                        item.setId(UUID.randomUUID().toString());
                        item.setProofCatalogId(proofCatalogId);
                        item.setProofCatalogCode(proofCatalogCode);
                    });
            proofCatalogDeptSurveyRepository.saveAll(BeanCopyUtils.copyList(proofCatalogDeptSurveyDoList, ProofCatalogDeptSurveyDo.class));
        }
        if (CollectionUtils.isNotEmpty(proofCatalogLicenseRelationDoList)) {
            proofCatalogLicenseRelationDoList
                    .stream().filter(Objects::nonNull)
                    .forEach(item -> {
                        item.setId(UUID.randomUUID().toString());
                        item.setProofCatalogId(proofCatalogId);
                        item.setProofCatalogCode(proofCatalogCode);
                    });
            licenseRelationRepository.saveAll(BeanCopyUtils.copyList(proofCatalogLicenseRelationDoList, ProofCatalogLicenseRelationDo.class));
        }
        if (CollectionUtils.isNotEmpty(proofCatalogOtherRelationDoList)) {
            proofCatalogOtherRelationDoList
                    .stream().filter(Objects::nonNull)
                    .forEach(item -> {
                        item.setId(UUID.randomUUID().toString());
                        item.setProofCatalogId(proofCatalogId);
                        item.setProofCatalogCode(proofCatalogCode);
                    });
            proofCatalogOtherRelationRepository.saveAll(BeanCopyUtils.copyList(proofCatalogOtherRelationDoList, ProofCatalogOtherRelationDo.class));
        }
        if (CollectionUtils.isNotEmpty(proofCatalogLicenseItemRelationDo)) {
            proofCatalogLicenseItemRelationDo
                    .stream().filter(Objects::nonNull)
                    .forEach(item -> {
                        item.setId(UUID.randomUUID().toString());
                        item.setProofCatalogId(proofCatalogId);
                        item.setProofCatalogCode(proofCatalogCode);
                        // 设值开通目录信息
                        String licenseCode = item.getLicenseCode();
                        LicenseFacadeService licenseFacadeService = licenseFacadeStrategyService.settingStrategy(LicenseFacadeStrategyService.LicenseProcessStrategy.LICENSE_PROOF);
                        List<ImplementLicenseItemInfoDto> infoDtoList = licenseFacadeService.queryImplLicenseItemInfoByBasicCode(licenseCode);
                        if (CollectionUtils.isEmpty(infoDtoList)) {
                            item.setImplementItemInfoJson(JacksonUtil.toJsonStr(Lists.newArrayList()));
                        } else {
                            List<ProofCatalogLicenseItemImplItemInfoDto> collect = infoDtoList.stream().map(implInfo -> {
                                ProofCatalogLicenseItemImplItemInfoDto copy = new ProofCatalogLicenseItemImplItemInfoDto();
                                copy.setImplementCode(implInfo.getImplementCode());
                                copy.setImplementOrgCode(implInfo.getImplementCode());
                                copy.setImplementOrg(implInfo.getImplementOrg());
                                copy.setImplementDivisionCode(implInfo.getImplementDivisionCode());
//                        copy.setImplementCreditCode(implInfo.buildImplementOrgCode());
                                copy.setBasicCode(implInfo.buildBasicCode());
                                String shortName = implInfo.getImplementOrg();
                                List<OrganizationDto> organizationsByShortName = organizationPublicService.getOrganizationsByShortName(shortName);
                                if (CollectionUtils.isNotEmpty(organizationsByShortName)) {
                                    OrganizationDto organizationDto = organizationsByShortName.get(0);
                                    copy.setImplementCreditCode(organizationDto.getCreditCode());
                                    copy.setImplementDivision(organizationDto.getDivisionName());
                                }
                                copy.setImplementOrgCode(implInfo.buildImplementOrgCode());
                                return copy;
                            }).collect(Collectors.toList());
                            item.setImplementItemInfoJson(JacksonUtil.toJsonStr(collect));
                        }
                    });
            List<ProofCatalogLicenseItemRelationDto> collect = proofCatalogLicenseItemRelationDo.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                List<ProofCatalogLicenseItemRelationDo> entities = BeanCopyUtils.copyList(collect, ProofCatalogLicenseItemRelationDo.class);
                proofCatalogLicenseItemRelationRepository.saveAll(entities);
            }
        }
        saveAuditRelationTemplateDoByDto(auditRelationTemplateDtoList);
    }

    @Override
    public Page<ProofCatalogPageBo> query(@Nonnull ProofCatalogQuery query, Pageable pageable) {
        Specification<ProofCatalogDo> spec = query.toSpec();
        Page<ProofCatalogDo> resultDo = proofCatalogRepository.findAll(spec, pageable);
        return BeanCopyUtils.copyPageItemToVo(resultDo, ProofCatalogPageBo.class);
    }

    @Override
    public ProofCatalogResponse findById(String id) {
        Optional<ProofCatalogDo> optionalDo = proofCatalogRepository.findById(id);
        return optionalDo.map(proofCatalogDo -> BeanCopyUtils.copy(proofCatalogDo, ProofCatalogResponse.class)).orElse(null);
    }

    @Override
    public List<ProofCatalogResponse> findByIds(Collection<String> ids) {
        List<ProofCatalogDo> resList = proofCatalogRepository.findAllById(ids);
        return BeanCopyUtils.copyList(resList, ProofCatalogResponse.class);
    }

    @Override
    public ProofCatalogDetailDto findDetailById(String id) {
        Optional<ProofCatalogDo> optionalProofCatalogDo = proofCatalogRepository.findById(id);
        return getProofCatalogDetailBo(optionalProofCatalogDo);
    }

    @Override
    public ProofCatalogDetailDto findDetailByCode(String code) {
        Optional<ProofCatalogDo> optionalProofCatalogDo = proofCatalogRepository.findByCode(code);
        return getProofCatalogDetailBo(optionalProofCatalogDo);
    }

    public List<ProofCatalogDto> findByLicenseBaseCode(List<String> basicCode) {
        List<ProofCatalogLicenseRelationDo> byLicenseCode = licenseRelationRepository.findAllByLicenseCodeIn(basicCode);
        if (CollectionUtils.isEmpty(byLicenseCode)) {
            return null;
        }
        List<String> catalogIdList = byLicenseCode.stream().map(ProofCatalogLicenseRelationDo::getProofCatalogId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<ProofCatalogDo> allById = proofCatalogRepository.findAllById(catalogIdList);
        return BeanCopyUtils.copyList(allById, ProofCatalogDto.class);
    }

    private ProofCatalogDetailDto getProofCatalogDetailBo(Optional<ProofCatalogDo> optionalProofCatalogDo) {
        ProofCatalogDo proofCatalogDo = optionalProofCatalogDo.orElseThrow(() -> new CatalogServiceException(ResponseCode.FIND_PROOF_CATALOG_EMPTY));
        String id = proofCatalogDo.getId();
        // 查询与证明目录关联绑定的方式
        List<ProofCatalogArtificialRelationDo> resultArtificialDo = artificialRelationRepository.findAllByProofCatalogId(id);
        List<ProofCatalogDataSharedRelationDo> resultDataSharedDo = dataSharedRelationRepository.findAllByProofCatalogId(id);
        List<ProofCatalogClerkCommitmentDo> resultClerkCommitmentDo = proofCatalogClerkCommitmentRepository.findAllByProofCatalogId(id);
        List<ProofCatalogDeptSurveyDo> resultDeptSurveyDo = proofCatalogDeptSurveyRepository.findAllByProofCatalogId(id);
        List<ProofCatalogLicenseRelationDo> resultLicenseDo = licenseRelationRepository.findAllByProofCatalogId(id);
        List<ProofCatalogLicenseItemRelationDo> resultLicenseItemDo = proofCatalogLicenseItemRelationRepository.findAllByProofCatalogId(id);
        List<ProofCatalogOtherRelationDo> resultOtherDo = proofCatalogOtherRelationRepository.findAllByProofCatalogId(id);

        // 转换bo
        ProofCatalogDetailDto proofCatalogDetailDto = BeanCopyUtils.copy(proofCatalogDo,
                ProofCatalogDetailDto.class);
        if (Objects.isNull(proofCatalogDetailDto)) {
            throw new CatalogServiceException(ResponseCode.BEAN_COPY_ERROR);
        }

        List<ProofCatalogArtificialRelationDto> proofCatalogArtificialRelationList = BeanCopyUtils
                .copyList(resultArtificialDo, ProofCatalogArtificialRelationDto.class);
        List<ProofCatalogDataSharedRelationDto> proofCatalogDataSharedRelationList = BeanCopyUtils
                .copyList(resultDataSharedDo, ProofCatalogDataSharedRelationDto.class);
        List<ProofCatalogClerkCommitmentDto> proofCatalogClerkCommitmentList = BeanCopyUtils
                .copyList(resultClerkCommitmentDo, ProofCatalogClerkCommitmentDto.class);
        List<ProofCatalogDeptSurveyDto> proofCatalogDeptSurveyList = BeanCopyUtils.copyList(resultDeptSurveyDo,
                ProofCatalogDeptSurveyDto.class);
        List<ProofCatalogLicenseRelationDto> proofCatalogLicenseRelationList = BeanCopyUtils
                .copyList(resultLicenseDo, ProofCatalogLicenseRelationDto.class);
        List<ProofCatalogLicenseItemRelationDto> proofCatalogLicenseItemRelation = BeanCopyUtils
                .copyList(resultLicenseItemDo, ProofCatalogLicenseItemRelationDto.class);
        List<ProofCatalogOtherRelationDto> proofCatalogOtherRelationList = BeanCopyUtils.copyList(resultOtherDo,
                ProofCatalogOtherRelationDto.class);

        if (CollectionUtils.isNotEmpty(proofCatalogDataSharedRelationList)) {
            // 重新处理系统名称，因为可能数据名称改变
            proofCatalogDataSharedRelationList.forEach(item -> {
                AtomicReference<String> dataSharedSystemNameBySystemCode = getDataSharedSystemNameBySystemCode(item.getSystemCode());
                item.setSystemName(dataSharedSystemNameBySystemCode.get());
            });
        }

        if (CollectionUtils.isNotEmpty(proofCatalogDataSharedRelationList)) {
            proofCatalogDataSharedRelationList
                    .stream()
                    .filter(item -> StringUtils.isNotBlank(item.getDataSharedConfigId()))
                    .forEach(item -> {
                        Optional<ProofCatalogDataSharedConfigDo> optionalConfig = proofCatalogDataSharedConfigRepository.findById(item.getDataSharedConfigId());
                        ProofCatalogDataSharedConfigDo configDo = optionalConfig.orElseThrow(() -> new CatalogServiceException(new ResponseCode(ResponseCode.BIZ_ERROR.getCode(), "数据共享配置不存在", true)));
                        item.setSystemCode(configDo.getSystemCode());
                        item.setSystemName(configDo.getSystemName());
                        item.setDataThemeName(configDo.getDataThemeName());
                        item.setDataThemeCode(configDo.getDataThemeCode());
                        item.setApiUrl(configDo.getApiUrl());
                        item.setConfigJson(JacksonUtil.toJsonStr(configDo));
                        item.setDataSharedConfigId(configDo.getId());
                    });
        }
        proofCatalogDetailDto.setProofCatalogArtificialList(proofCatalogArtificialRelationList);
        proofCatalogDetailDto.setProofCatalogDataSharedList(proofCatalogDataSharedRelationList);
        proofCatalogDetailDto.setProofCatalogClerkCommitmentList(proofCatalogClerkCommitmentList);
        proofCatalogDetailDto.setProofCatalogDeptSurveyList(proofCatalogDeptSurveyList);
        proofCatalogDetailDto.setProofCatalogLicenseRelationList(proofCatalogLicenseRelationList);
        proofCatalogDetailDto.setProofCatalogLicenseItemRelation(proofCatalogLicenseItemRelation);
        proofCatalogDetailDto.setProofCatalogOtherRelationList(proofCatalogOtherRelationList);
        // 构造电子证明，审核关系数据
        if (CollectionUtils.isNotEmpty(proofCatalogDetailDto.getProofCatalogArtificialList())) {
            proofCatalogDetailDto.getProofCatalogArtificialList().forEach(item -> {
                String auditRelationTemplateKey = item.getAuditRelationTemplateKey();
                List<AuditRelationTemplateDo> allByOnlyKey = auditRelationTemplateRepository.findAllByOnlyKey(auditRelationTemplateKey);
                item.setAuditRelationTemplateList(BeanCopyUtils.copyList(allByOnlyKey, AuditRelationTemplateDto.class));
                if (CollectionUtils.isNotEmpty(item.getAuditRelationTemplateList())) {
                    long count = item.getAuditRelationTemplateList().stream().mapToInt(AuditRelationTemplateDto::getAuditLevel).distinct().count();
                    item.setExamineLevel((int) count);
                }
            });

        }
        // 处理复检byte转base64Bo对象的处理。
        if (CollectionUtils.isNotEmpty(resultClerkCommitmentDo)) {
            Map<String, ProofCatalogClerkCommitmentDo> clerkCommitmentIdAndDoMap = resultClerkCommitmentDo.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(BaseDo::getId, (v) -> v, (before, after) -> after));
            proofCatalogDetailDto.getProofCatalogClerkCommitmentList().forEach(item -> {
                ProofCatalogClerkCommitmentDo proofCatalogClerkCommitmentDo = clerkCommitmentIdAndDoMap
                        .get(item.getId());
                if (StringUtils.isNotBlank(proofCatalogClerkCommitmentDo.getCommitAttachmentId())) {
                    UploadFileAttachmentBo byId = uploadFileAttachmentService.findById(proofCatalogClerkCommitmentDo.getCommitAttachmentId());
                    if (Objects.nonNull(byId)) {
                        item.setFileData(Base64EncoderUtils.transformBase64(byId.getFileData()));
                    }

                }
            });
        }

        return proofCatalogDetailDto;
    }

    @Override
    public List<ProofCatalogResponse> queryList(ProofCatalogRequest proofCatalogRequest) {
        List<ProofCatalogDo> query = proofCatalogRepository.query(proofCatalogRequest);
        return BeanCopyUtils.copyList(query, ProofCatalogResponse.class);
    }

    @Override
    public byte[] downAttachment(String attachmentId) {
        ProofCatalogClerkCommitmentDo entity = proofCatalogClerkCommitmentRepository
                .findByCommitAttachmentId(attachmentId);
        if (Objects.nonNull(entity)) {
            return entity.getFileData();
        }
        return new byte[0];
    }

    @Override
    public List<ProofCatalogChangeLogViewBo> findCatalogLogById(String id) {
        Specification<ProofCatalogChangeLogDo> build = Specifications.<ProofCatalogChangeLogDo>and()
                .eq("proofCatalogId", id)
                .build();
        // 按最新修改进行排序
        List<ProofCatalogChangeLogDo> changeLogList = proofCatalogChangeLogRepository.findAll(build,
                Sort.by(Sort.Direction.DESC, "lastModificationTime"));
        // 每一次操作的日志数据，进行归集到同一个对象中的list。
        List<ProofCatalogChangeLogViewBo> result = Lists.newArrayList();
        Map<String, ProofCatalogChangeLogDo> keyMap = changeLogList.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(k -> k.getProofCatalogId() + k.getLastModificationTime().getTime(), (v) -> v,
                        (before, after) -> after));
        Map<String, List<Map<String, String>>> valueMap = changeLogList.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(k -> k.getProofCatalogId() + k.getLastModificationTime().getTime(), (v) -> {
                    List<Map<String, String>> list = Lists.newArrayList();
                    Map<String, String> value = Maps.newHashMap();
                    value.put("change_prefix", v.getChangePrefix());
                    value.put("change_post", v.getChangePost());
                    list.add(value);
                    return list;
                }, (before, after) -> {
                    before.addAll(after);
                    return before;
                }));
        keyMap.values().forEach(item -> {
            ProofCatalogChangeLogViewBo bo = new ProofCatalogChangeLogViewBo();
            String valueKey = item.getProofCatalogId() + item.getLastModificationTime().getTime();
            List<Map<String, String>> values = valueMap.get(valueKey);
            bo.setChangePrefixAndPostList(values);
            bo.setOperationName(item.getOperationName());
            bo.setProofCatalogId(item.getProofCatalogId());
            bo.setAccount(item.getAccount());
            bo.setAccountName(item.getAccountName());
            bo.setId(item.getId());
            bo.setCreatorId(item.getCreatorId());
            bo.setCreationTime(item.getCreationTime());
            bo.setLastModificationTime(item.getLastModificationTime());
            result.add(bo);
        });

        return result;
    }

    @Override
    public List<ProofCatalogResponse> queryList(ProofCatalogQuery proofCatalogQuery) {
        List<ProofCatalogDo> query = proofCatalogRepository.findAll(proofCatalogQuery.toSpec());
        return BeanCopyUtils.copyList(query, ProofCatalogResponse.class);
    }

    @Override
    public long countProofCatalog() {
        return proofCatalogRepository.countProofCatalog();
    }

    @Override
    public List<ProofCatalogBO> findAllByIds(Collection<String> proofCatalogIdSet) {
        try {
            List<ProofCatalogDo> allById = proofCatalogRepository.findAllById(proofCatalogIdSet);
            return BeanCopyUtils.copyList(allById, ProofCatalogBO.class);
        } catch (Exception e) {
            LOGGER.error("findAllByIds error", e);
        }
        return null;
    }

    @Override
    public List<ProofCatalogBO> findAllByCatalogCode(Collection<String> proofCatalogCodeSet) {
        try {
            if (CollectionUtils.isNotEmpty(proofCatalogCodeSet)) {
                List<ProofCatalogDo> allById = proofCatalogRepository.findAllByCodeIn(proofCatalogCodeSet);
                return BeanCopyUtils.copyList(allById, ProofCatalogBO.class);
            }
        } catch (Exception e) {
            LOGGER.error("findAllByIds error", e);
        }
        return null;
    }

    @Override
    public Optional<List<ProofCatalogArtificialRelationDto>> findArtificialByCatalogIdIn(Collection<String> catalogIdSet) {
        List<ProofCatalogArtificialRelationDo> result = proofCatalogArtificialRelationRepository.findAllByProofCatalogIdIn(catalogIdSet);
        return Optional.ofNullable(BeanCopyUtils.copyList(result, ProofCatalogArtificialRelationDto.class));
    }

    @Override
    public Page<ProofCatalogDataSharedThemePageBo> getDataSharedThemePage(ProofCatalogDataSharedThemeQuery query, String dataSharedSystem) throws Exception {
        ProofCatalogDataSharedConfigPageQuery configQueryObj = new ProofCatalogDataSharedConfigPageQuery();
        configQueryObj.setPageable(query.getPageable());
        configQueryObj.setDataThemeCode(query.getDataSharedThemeCode());
        configQueryObj.setDataThemeName(query.getDataThemeName());
        configQueryObj.setSystemCode(dataSharedSystem);
        Page<ProofCatalogDataSharedConfigManagerDto> configPage = getDataSharedThemeConfigPage(configQueryObj);
        if (configPage.isEmpty()) {
            return new PageImpl<>(Lists.newArrayList(), PageRequest.of(query.getPageable().getPageNumber(), query.getPageable().getPageSize()), 0L);
        } else {
            List<ProofCatalogDataSharedThemePageBo> responseBoList = configPage.getContent().stream().map(item -> {
                ProofCatalogDataSharedThemePageBo responseBo = new ProofCatalogDataSharedThemePageBo();
                responseBo.setDataSharedThemeName(item.getDataThemeName());
                responseBo.setDataSharedThemeCode(item.getDataThemeCode());
                responseBo.setId(item.getId());
                return responseBo;
            }).collect(Collectors.toList());
            return new PageImpl<>(responseBoList, PageRequest.of(query.getPageable().getPageNumber(), query.getPageable().getPageSize()), 0L);
        }
    }

    @Override
    public ProofCatalogDataSharedConfigBo getDataSharedConfigDetail(String systemCode, String dataThemeCode, String dataThemeName) throws Exception {
        ProofCatalogDataSharedConfigPageQuery configQueryObj = new ProofCatalogDataSharedConfigPageQuery();
        configQueryObj.setDataThemeCode(dataThemeCode);
        configQueryObj.setDataThemeName(dataThemeName);
        configQueryObj.setSystemCode(systemCode);
        List<ProofCatalogDataSharedConfigDo> doList = proofCatalogDataSharedConfigRepository.findAll(configQueryObj.toSpec());
        if (CollectionUtils.isEmpty(doList)) {
            return new ProofCatalogDataSharedConfigBo();
        }
        gov.license.common.api.utils.Assert.isTrue(doList.size() > 1, ResponseCode.SERVICE_ERROR);
        ProofCatalogDataSharedConfigDo configDo = doList.get(0);
        return ProofCatalogDataSharedConfigBo.convert(configDo);
    }

    /**
     * 获取根据共享系统编码，获取共享系统名称
     *
     * @param systemCode 共享系统编码
     * @return 共享系统名称
     */
    private AtomicReference<String> getDataSharedSystemNameBySystemCode(String systemCode) {
        AtomicReference<String> systemName = new AtomicReference<>("");

        Optional<DictDto> optionalDict = dictPublicService.findDictAndDetailByName(DictionaryDicTypeConstant.PROOF_CATALOG_DATA_SHARED_SYSTEM.name());
        DictDto dictDto = optionalDict.orElseThrow(() -> new DictionaryConfigException(DictionaryResponseCode.PROOF_CATALOG_DATA_SHARED_SYSTEM_NOT_NULL));
        List<DictDetailDto> dictionaryBos = dictDto.getDictDetailList();

        try {
            if (CollectionUtils.isNotEmpty(dictionaryBos)) {
                String splitStr = ",";
                int systemNameIndex = 1;
                dictionaryBos.stream()
                        .filter(Objects::nonNull)
                        .filter(item -> StringUtils.isNotBlank(item.getValue()))
                        .map(item -> Lists.newArrayList(item.getValue().split(splitStr)))
                        .filter(systemKeyAndNameList -> StringUtils.equals(systemCode, systemKeyAndNameList.get(0)))
                        .forEach(systemKeyAndNameList -> {
                            systemName.set(systemKeyAndNameList.size() > systemNameIndex ? systemKeyAndNameList.get(systemNameIndex) : "未知系统名称");
                        });
            }
        } catch (Exception e) {
            LOGGER.warn("findDetailById getDataSharedSystemNameBySystemCode(item.getSystemCode())");
        }

        return systemName;
    }

    @Override
    public ProofCatalogDataSharedConfigBo getDataSharedConfigByCatalogId(String catalogId) {
        List<ProofCatalogDataSharedRelationDo> byProofCatalogId = dataSharedRelationRepository.findByProofCatalogId(catalogId);
        if (CollectionUtils.isEmpty(byProofCatalogId)) {
            return null;
        }
        ProofCatalogDataSharedRelationDo proofCatalogDataSharedRelationDo = byProofCatalogId.get(0);
        String configJson = proofCatalogDataSharedRelationDo.getConfigJson();
        if (StringUtils.isBlank(configJson)) {
            return null;
        }
        return JacksonUtil.toBean(configJson, new TypeReference<ProofCatalogDataSharedConfigBo>() {
        });
    }

    @Override
    public ProofCatalogDataSharedRelationDto findDataSharedById(String catalogId) {
        List<ProofCatalogDataSharedRelationDo> catalogDataSharedRelationDoList = proofCatalogDataSharedRelationRepository.findByProofCatalogId(catalogId);
        Assert.notEmpty(catalogDataSharedRelationDoList, "证明目录，数据配置为空");
        ProofCatalogDataSharedRelationDo proofCatalogDataSharedRelationDo = catalogDataSharedRelationDoList.get(0);
        return proofCatalogDataSharedRelationDo.clone(ProofCatalogDataSharedRelationDto.class);
    }


    @Override
    public Page<ProofCatalogDataSharedConfigManagerDto> getDataSharedThemeConfigPage(ProofCatalogDataSharedConfigPageQuery query) {
        Page<ProofCatalogDataSharedConfigDo> page = proofCatalogDataSharedConfigRepository.findAll(query.toSpec(), query.getPageable());
        if (page.isEmpty()) {
            return new PageImpl<>(Lists.newArrayList(), query.getPageable(), 0L);
        }
        List<ProofCatalogDataSharedConfigManagerDto> collect = page.getContent().stream().map(ProofCatalogDataSharedConfigManagerDto::convert).collect(Collectors.toList());
        return new PageImpl<>(collect, query.getPageable(), page.getTotalElements());
    }

    @Override
    public String createDataSharedThemeConfig(ProofCatalogDataSharedConfigManagerDto configBo) {
        ProofCatalogDataSharedConfigDo configDo = configBo.convertDo();
        gov.license.common.api.utils.Assert.isTrue(StringUtils.isBlank(configDo.getId()), BaseResponseCode.PARAM_ERROR);
        ProofCatalogDataSharedConfigDo entity = proofCatalogDataSharedConfigRepository.save(configDo);
        return entity.getId();
    }

    @Override
    public void editDataSharedThemeConfig(ProofCatalogDataSharedConfigManagerDto configBo) {
        ProofCatalogDataSharedConfigDo configDo = configBo.convertDo();
        Optional<ProofCatalogDataSharedConfigDo> byId = proofCatalogDataSharedConfigRepository.findById(configDo.getId());
        gov.license.common.api.utils.Assert.isTrue(byId.isPresent(), new ResponseCode(ResponseCode.SERVICE_ERROR.getCode(), "数据共享配置不存在"));
        ProofCatalogDataSharedConfigDo dbEntity = byId.get();
        configDo.setStrategyFlag(dbEntity.getStrategyFlag());
        proofCatalogDataSharedConfigRepository.save(configDo);
    }

    @Override
    public ProofCatalogDataSharedConfigManagerDto findDataSharedThemeConfig(String id) {
        Optional<ProofCatalogDataSharedConfigDo> byId = proofCatalogDataSharedConfigRepository.findById(id);
        gov.license.common.api.utils.Assert.isTrue(byId.isPresent(), new ResponseCode(ResponseCode.SERVICE_ERROR.getCode(), "数据共享配置不存在"));
        return ProofCatalogDataSharedConfigManagerDto.convert(byId.get());
    }

    @Override
    public void importDataSharedThemeConfigExcel(byte[] fileData, String fileName) {
        ExcelTablesModel excelTablesModel = ExcelUtils.readExcelTableByIndex(fileData, fileName, 0, 1, 0, 13);
        Table<Integer, Integer, Object> sheet = excelTablesModel.getSheet(0);
        sheet.rowMap().values().stream().map(columnValue -> {
            ProofCatalogDataSharedConfigDo configDo = new ProofCatalogDataSharedConfigDo();
            configDo.setSystemName(String.valueOf(columnValue.get(0)));
            configDo.setSystemCode(String.valueOf(columnValue.get(1)));
            configDo.setDataThemeName(String.valueOf(columnValue.get(2)));
            configDo.setDataThemeCode(String.valueOf(columnValue.get(3)));
            configDo.setApiUrl(String.valueOf(columnValue.get(4)));
            configDo.setDataItemList(String.valueOf(columnValue.get(5)));
            configDo.setSearchConditionList(String.valueOf(columnValue.get(6)));
            try {
                configDo.setDataThemeStatus(DataSharedStatusEnum.valueOf(String.valueOf(columnValue.get(7))));
            } catch (Exception e) {
                LOGGER.warn("importDataSharedThemeConfigExcel convert enums error,setting default enable", e);
                configDo.setDataThemeStatus(DataSharedStatusEnum.ENABLE);
            }
            configDo.setStrategyFlag(String.valueOf(columnValue.get(8)));
            configDo.setListSortType(DataSharedListSortTypeEnum.valueOf(String.valueOf(columnValue.get(9))));
            configDo.setListSortTypeDataItemName(String.valueOf(columnValue.get(10)));
            configDo.setListSortRule(DataSharedListSortRuleEnum.valueOf(String.valueOf(columnValue.get(11))));
            configDo.setSearchLimitCount(StringUtils.isBlank(String.valueOf(columnValue.get(12))) ? null : Integer.valueOf(String.valueOf(columnValue.get(12))));
            return configDo;
        }).forEach(item -> {
            String dataThemeCode = item.getDataThemeCode();
            Optional<ProofCatalogDataSharedConfigDo> byDataThemeCode = proofCatalogDataSharedConfigRepository.findByDataThemeCode(dataThemeCode);
            try {
                gov.license.common.api.utils.Assert.isTrue(!byDataThemeCode.isPresent(), new CatalogServiceException(new ResponseCode(ResponseCode.BIZ_ERROR.getCode(), "导入数据主题，已经存在库中。", true)));
                proofCatalogDataSharedConfigRepository.save(item);
            } catch (CatalogServiceException e) {
                LOGGER.warn("importDataSharedThemeConfigExcel exists dataSharedTheme,skip import data data is [{}]", item, e);
            }
        });
    }

    @Override
    public AttachmentBo downExcelDataSharedConfigById(String id) {
        Optional<ProofCatalogDataSharedConfigDo> byId = proofCatalogDataSharedConfigRepository.findById(id);
        gov.license.common.api.utils.Assert.isTrue(byId.isPresent(), new ResponseCode(ResponseCode.SERVICE_ERROR.getCode(), "数据共享配置不存在"));
        ProofCatalogDataSharedConfigDo configDo = byId.get();
        HashBasedTable<Integer, Integer, Object> header = ExcelUtils.getExcelDataTableInstance();
        header.put(0, 0, "systemName");
        header.put(0, 1, "systemCode");
        header.put(0, 2, "dataThemeName");
        header.put(0, 3, "dataThemeCode");
        header.put(0, 4, "apiUrl");
        header.put(0, 5, "dataItemList");
        header.put(0, 6, "searchConditionList");
        header.put(0, 7, "dataThemeStatus");
        header.put(0, 8, "strategyFlag");
        header.put(0, 9, "listSortType");
        header.put(0, 10, "listSortTypeDataItemName");
        header.put(0, 11, "listSortRule");
        header.put(0, 12, "searchLimitCount");

        HashBasedTable<Integer, Integer, Object> columnsMap = ExcelUtils.getExcelDataTableInstance();
        columnsMap.put(1, 0, StringUtils.isNotBlank(configDo.getSystemName()) ? configDo.getSystemName() : "");
        columnsMap.put(1, 1, StringUtils.isNotBlank(configDo.getSystemCode()) ? configDo.getSystemCode() : "");
        columnsMap.put(1, 2, StringUtils.isNotBlank(configDo.getDataThemeName()) ? configDo.getDataThemeName() : "");
        columnsMap.put(1, 3, StringUtils.isNotBlank(configDo.getDataThemeCode()) ? configDo.getDataThemeCode() : "");
        columnsMap.put(1, 4, StringUtils.isNotBlank(configDo.getApiUrl()) ? configDo.getApiUrl() : "");
        columnsMap.put(1, 5, StringUtils.isNotBlank(configDo.getDataItemList()) ? configDo.getDataItemList() : "");
        columnsMap.put(1, 6, StringUtils.isNotBlank(configDo.getSearchConditionList()) ? configDo.getSearchConditionList() : "");
        columnsMap.put(1, 7, Objects.nonNull(configDo.getDataThemeStatus()) ? configDo.getDataThemeStatus().name() : DataSharedStatusEnum.ENABLE.name());
        columnsMap.put(1, 8, StringUtils.isNotBlank(configDo.getStrategyFlag()) ? configDo.getStrategyFlag() : "");
        columnsMap.put(1, 9, Objects.nonNull(configDo.getListSortType()) ? configDo.getListSortType().name() : "");
        columnsMap.put(1, 10, StringUtils.isNotBlank(configDo.getListSortTypeDataItemName()) ? configDo.getListSortTypeDataItemName() : "");
        columnsMap.put(1, 11, Objects.nonNull(configDo.getListSortRule()) ? configDo.getListSortRule().name() : "");
        columnsMap.put(1, 12, Objects.nonNull(configDo.getSearchLimitCount()) ? configDo.getSearchLimitCount() : "");

        String fileName = "数据共享配置-" + configDo.getDataThemeName() + ".xls";
        try (Workbook workBook = ExcelUtils.createWorkBook(fileName);
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ) {
            Sheet sheet = ExcelUtils.createSheet(workBook, "Sheet1");
            ExcelUtils.writeExcelValue(workBook, sheet, header, columnsMap);
            workBook.write(outputStream);
            byte[] byteArray = outputStream.toByteArray();
            return new AttachmentBo(fileName, byteArray);
        } catch (Exception e) {
            LOGGER.error("downExcelDataSharedConfigById error", e);
            throw new CatalogServiceException(ResponseCode.BIZ_ERROR, e);
        }
    }

    @Override
    public void deleteDataSharedThemeConfig(String id) {
        proofCatalogDataSharedConfigRepository.deleteById(id);
    }

    @Override
    public List<ProofCatalogDataSharedConfigBo> findDataSharedThemeConfigByCatalogIdList(Collection<String> catalogIdList) {
        List<ProofCatalogDataSharedRelationDo> allInProofCatalogId = proofCatalogDataSharedRelationRepository.findAllInProofCatalogId(catalogIdList);
        if (CollectionUtils.isEmpty(allInProofCatalogId)) {
            LOGGER.warn("findDataSharedThemeConfigByCatalogIdList allInProofCatalogId is empty");
            return Lists.newArrayList();
        }
        Set<String> dataSharedIdSet = allInProofCatalogId.stream().map(ProofCatalogDataSharedRelationDo::getDataSharedConfigId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(dataSharedIdSet)) {
            return Lists.newArrayList();
        }
        List<ProofCatalogDataSharedConfigDo> allById = proofCatalogDataSharedConfigRepository.findAllById(dataSharedIdSet);
        if (CollectionUtils.isEmpty(allById)) {
            return Lists.newArrayList();
        } else {
            return allById.stream().map(ProofCatalogDataSharedConfigBo::convert).collect(Collectors.toList());
        }
    }

    @Override
    public Page<ProofCatalogPageBo> queryWaterMarkPage(ProofCatalogWaterMarkQuery query, Pageable pageable) {
        return BeanCopyUtils.copyPageItemToVo(proofCatalogRepository.findAll(query.build(), pageable), ProofCatalogPageBo.class);
    }

    @Override
    public List<ProofCatalogWaterMarkListBo> queryWaterMarkList(ProofCatalogWaterMarkQuery query) {
        return BeanCopyUtils.copyList(proofCatalogRepository.findAll(query.build()), ProofCatalogWaterMarkListBo.class);
    }

    @Override
    public void bindAssociationCatalog(List<String> proofCatalogCodeList, String waterMarkId) {
        // 逻辑1 。证明目录CodeList为空，删除所有数据。
        proofCatalogRepository.updateAllCatalogWaterMarkerIdIsNull();
        if (CollectionUtils.isNotEmpty(proofCatalogCodeList)) {
            // 逻辑2 。证明目录只传递code过来，只保证这些目录有水印id，其他都没有。
            List<ProofCatalogDo> allByCodeIn = proofCatalogRepository.findAllByCodeIn(proofCatalogCodeList);
            allByCodeIn.forEach(item -> item.setWaterMarkId(waterMarkId));
            proofCatalogRepository.saveAll(allByCodeIn);
        }
    }

    @Override
    public void deleteAssociationCatalog(List<String> proofCatalogCodeList) {
        List<ProofCatalogDo> allByCodeIn = proofCatalogRepository.findAllByCodeIn(proofCatalogCodeList);
        allByCodeIn.forEach(item -> item.setWaterMarkId(null));
        proofCatalogRepository.saveAll(allByCodeIn);
    }

    @Override
    public List<ProofCatalogWaterMarkListBo> queryHasWaterMarkCatalogList() {
        return BeanCopyUtils.copyList(proofCatalogRepository.queryHasWaterMarkCatalogList(), ProofCatalogWaterMarkListBo.class);
    }

    @Override
    public Optional<ProofCatalogBO> findByCode(String catalogCode) {
        Optional<ProofCatalogDo> byCode = proofCatalogRepository.findByCode(catalogCode);
        return byCode.map(proofCatalogDo -> BeanCopyUtils.copy(proofCatalogDo, ProofCatalogBO.class));
    }

    @Override
    public Page<ProofCatalogPageBo> queryArtificialRelationCatalog(ProofCatalogArtificialRelationQuery query, Pageable pageable) {
        query.setExistsInvestigation(true);
        query.setExistsLicenseItem(false);
        Page<ProofCatalogArtificialRelationViewDo> all = proofCatalogArtificialRelationViewDoRepository.findAll(query.toSpec(), pageable);
        return BeanCopyUtils.copyPageItemToVo(all, ProofCatalogPageBo.class);
    }

    @Override
    public List<AuditRelationTemplateDto> findCatalogAuditRelationTempByKey(String onlyKey) {
        return BeanCopyUtils.copyList(auditRelationTemplateRepository.findAllByOnlyKey(onlyKey), AuditRelationTemplateDto.class);
    }

    @Override
    public List<ProofCatalogDto> findAllByCatalogNameLike(String proofCatalogName) {
        Specification<ProofCatalogDo> spec = Specifications.<ProofCatalogDo>and()
                .like(StringUtils.isNotBlank(proofCatalogName), "name", proofCatalogName).build();
        List<ProofCatalogDo> all = proofCatalogRepository.findAll(spec);
        return BeanCopyUtils.copyList(all, ProofCatalogDto.class);
    }


    @Override
    public List<CatalogApiBo> getAuditSuccessProofCatalog(CatalogApiQuery query) {
        List<CatalogApiBo> result = Lists.newArrayList();
        ProofListByPageViewQuery proofListByPageViewQuery = new ProofListByPageViewQuery();
        proofListByPageViewQuery.setItemCode(query.getItemCode());
        proofListByPageViewQuery.setMaterialId(query.getMaterialId());
        proofListByPageViewQuery.setProofName(query.getMaterialName());

        List<ProofListByPageViewBo> proofListPageBos = proofListService.queryProofList(proofListByPageViewQuery);
        if (CollectionUtils.isEmpty(proofListPageBos)) {
            return result;
        }

        Set<String> proofCatalogIdSet = proofListPageBos.stream().map(ProofListByPageViewBo::getProofCatalogId).collect(Collectors.toSet());
        List<ProofCatalogDo> catalogList = proofCatalogRepository.findAllById(proofCatalogIdSet);
        Map<String, ProofCatalogDo> idAndCatalogMap = catalogList.stream().filter(Objects::nonNull).collect(Collectors.toMap(BaseDo::getId, (v) -> v,
                (before, after) -> after));
        Map<String, ArrayList<ProofCatalogLicenseRelationDo>> catalogIdAndLicenseListMap = getCatalogLicenseByCatalogIdSet(proofCatalogIdSet);
        Map<String, ArrayList<ProofCatalogClerkCommitmentDo>> catalogIdAndcatalogIdClerkListMap = getCatalogClerkByCatalogIdSet(proofCatalogIdSet);
        Map<String, ArrayList<ProofCatalogDataSharedRelationDo>> catalogIdAndDataSharedListMap = getCatalogDataSharedByCatalogIdSet(proofCatalogIdSet);
        Map<String, ArrayList<ProofCatalogArtificialRelationDo>> catalogIdAndArtificialListMap = getCatalogArtificialMapByCatalogIdSet(proofCatalogIdSet);
        Map<String, ArrayList<ProofCatalogLicenseItemRelationDo>> catalogIdAndLicenseItemListMap = getCatalogLicenseItemMapByCatalogIdSet(proofCatalogIdSet);

        for (ProofListByPageViewBo item : proofListPageBos) {
            CatalogApiBo apiBo = new CatalogApiBo();
            apiBo.setMaterialName(item.getProofName());
            apiBo.setProofClearType(item.getProofClearType());
            apiBo.setReplaceCancelWay(item.getReplaceCancelWay());
            apiBo.setProofCatalogId(item.getProofCatalogId());
            apiBo.setItemCode(item.getItemCode());
            apiBo.setItemName(item.getItemName());
            apiBo.setMaterialId(item.getMaterialId());
            ProofCatalogDo proofCatalogDo = idAndCatalogMap.get(item.getProofCatalogId());
            if (Objects.nonNull(proofCatalogDo)) {
                apiBo.setName(proofCatalogDo.getName());
                apiBo.setCode(proofCatalogDo.getCode());
            }
            ArrayList<ProofCatalogLicenseRelationDo> proofCatalogLicenseRelationDos = catalogIdAndLicenseListMap.get(item.getProofCatalogId());
            List<ProofCatalogLicenseRelationDto> proofCatalogLicenseRelationDtos = BeanCopyUtils.copyList(proofCatalogLicenseRelationDos, ProofCatalogLicenseRelationDto.class);
            apiBo.setCatalogLicense(proofCatalogLicenseRelationDtos);

            ArrayList<ProofCatalogClerkCommitmentDo> proofCatalogClerkCommitmentDos = catalogIdAndcatalogIdClerkListMap.get(item.getProofCatalogId());
            List<ProofCatalogClerkCommitmentDto> catalogClerkCommitmentBoList =
                    BeanCopyUtils.copyList(proofCatalogClerkCommitmentDos, ProofCatalogClerkCommitmentDto.class);
            if (CollectionUtils.isNotEmpty(catalogClerkCommitmentBoList)) {
                catalogClerkCommitmentBoList.forEach(itemBo -> {
                    ProofCatalogClerkCommitmentDo doEntity =
                            proofCatalogClerkCommitmentDos.stream().filter(doItem -> doItem.getId().equals(itemBo.getId())).findFirst().orElse(null);
                    if (Objects.nonNull(doEntity)) {
                        String fileBase64 = Base64EncoderUtils.transformBase64(doEntity.getFileData());
                        itemBo.setFileData(fileBase64);
                    }
                });
            }
            apiBo.setCatalogClerkCommitment(catalogClerkCommitmentBoList);

            ArrayList<ProofCatalogDataSharedRelationDo> proofCatalogDataSharedRelationDos = catalogIdAndDataSharedListMap.get(item.getProofCatalogId());
            List<ProofCatalogDataSharedRelationDto> catalogDataSharedRelationBos =
                    BeanCopyUtils.copyList(proofCatalogDataSharedRelationDos, ProofCatalogDataSharedRelationDto.class);
            apiBo.setDataSharedRelation(catalogDataSharedRelationBos);

            ArrayList<ProofCatalogArtificialRelationDo> catalogArtificialRelationDos = catalogIdAndArtificialListMap.get(item.getProofCatalogId());
            List<ProofCatalogArtificialRelationDto> catalogArtificialRelationBos = BeanCopyUtils.copyList(catalogArtificialRelationDos, ProofCatalogArtificialRelationDto.class);
            if (CollectionUtils.isNotEmpty(catalogArtificialRelationBos)) {
                catalogArtificialRelationBos.stream().filter(artificialRelationDto -> StringUtils.isNotBlank(artificialRelationDto.getAuditRelationTemplateKey())).forEach(artificialRelationDto -> {
                    String tempKey = artificialRelationDto.getAuditRelationTemplateKey();
                    List<AuditRelationTemplateDo> allByOnlyKey = auditRelationTemplateRepository.findAllByOnlyKey(tempKey);
                    artificialRelationDto.setAuditRelationTemplateList(BeanCopyUtils.copyList(allByOnlyKey, AuditRelationTemplateDto.class));
                });
            }
            ArrayList<ProofCatalogLicenseItemRelationDo> proofCatalogLicenseItemRelationDos = catalogIdAndLicenseItemListMap.get(item.getProofCatalogId());
            if (CollectionUtils.isNotEmpty(proofCatalogLicenseItemRelationDos)) {
                List<ProofCatalogLicenseItemRelationDto> licenseItemBoList = BeanCopyUtils.copyList(proofCatalogLicenseItemRelationDos, ProofCatalogLicenseItemRelationDto.class);
                licenseItemBoList.forEach(licenseItem -> {
                    if (StringUtils.isNotBlank(licenseItem.getImplementItemInfoJson())) {
                        licenseItem.setImplementItemInfo(licenseItem.buildByImplItemInfoJson());
                    }
                });
                apiBo.setLicenseItemRelationList(licenseItemBoList);
            }

            apiBo.setArtificialRelationList(catalogArtificialRelationBos);
            result.add(apiBo);
        }
        return result;
    }

    private Map<String, ArrayList<ProofCatalogLicenseRelationDo>> getCatalogLicenseByCatalogIdSet(Set<String> proofCatalogIdSet) {
        List<ProofCatalogLicenseRelationDo> licenseRelationList =
                proofCatalogLicenseRelationRepository.findAllInProofCatalogId(proofCatalogIdSet);
        return licenseRelationList.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(ProofCatalogLicenseRelationDo::getProofCatalogId,
                        Lists::newArrayList,
                        (before, after) -> {
                            before.addAll(after);
                            return before;
                        }));
    }

    private Map<String, ArrayList<ProofCatalogClerkCommitmentDo>> getCatalogClerkByCatalogIdSet(Set<String> proofCatalogIdSet) {
        List<ProofCatalogClerkCommitmentDo> clerkCommitmentList = proofCatalogClerkCommitmentRepository.findAllInProofCatalogId(proofCatalogIdSet);
        return clerkCommitmentList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ProofCatalogClerkCommitmentDo::getProofCatalogId,
                Lists::newArrayList
                , (before, after) -> {
                    before.addAll(after);
                    return before;
                }));
    }

    private Map<String, ArrayList<ProofCatalogDataSharedRelationDo>> getCatalogDataSharedByCatalogIdSet(Set<String> proofCatalogIdSet) {
        List<ProofCatalogDataSharedRelationDo> dataSharedList = proofCatalogDataSharedRelationRepository.findAllInProofCatalogId(proofCatalogIdSet);
        return dataSharedList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ProofCatalogDataSharedRelationDo::getProofCatalogId,
                Lists::newArrayList
                , (before, after) -> {
                    before.addAll(after);
                    return before;
                }));
    }

    private Map<String, ArrayList<ProofCatalogArtificialRelationDo>> getCatalogArtificialMapByCatalogIdSet(Set<String> proofCatalogIdSet) {
        List<ProofCatalogArtificialRelationDo> artificialList =
                proofCatalogArtificialRelationRepository.findAllInProofCatalogId(proofCatalogIdSet);
        return artificialList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ProofCatalogArtificialRelationDo::getProofCatalogId,
                Lists::newArrayList
                , (before, after) -> {
                    before.addAll(after);
                    return before;
                }));
    }

    private Map<String, ArrayList<ProofCatalogLicenseItemRelationDo>> getCatalogLicenseItemMapByCatalogIdSet(Set<String> proofCatalogIdSet) {
        List<ProofCatalogLicenseItemRelationDo> resultList =
                proofCatalogLicenseItemRelationRepository.findAllByProofCatalogIdIn(proofCatalogIdSet);
        return resultList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ProofCatalogLicenseItemRelationDo::getProofCatalogId,
                Lists::newArrayList
                , (before, after) -> {
                    before.addAll(after);
                    return before;
                }));
    }

}
