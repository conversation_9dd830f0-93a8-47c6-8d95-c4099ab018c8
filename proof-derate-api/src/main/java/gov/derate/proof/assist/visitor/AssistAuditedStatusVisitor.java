package gov.derate.proof.assist.visitor;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import gov.derate.proof.account.dto.AccountInfoDto;
import gov.derate.proof.account.service.AccountInfoService;
import gov.derate.proof.assist.bo.AssistInfoItemBo;
import gov.derate.proof.assist.bo.AssistRecordDto;
import gov.derate.proof.assist.dto.AssistAuditDto;
import gov.derate.proof.assist.dto.LicenseTenBaseFieldDto;
import gov.derate.proof.assist.entity.AssistAuditDo;
import gov.derate.proof.assist.entity.AssistRecordDo;
import gov.derate.proof.assist.exception.AssistRecordServiceException;
import gov.derate.proof.assist.repository.AssistAuditRepository;
import gov.derate.proof.assist.repository.AssistRecordRepository;
import gov.derate.proof.assist.resp.AssistResponseCode;
import gov.derate.proof.assist.service.AssistRecordService;
import gov.derate.proof.catalog.dto.AuditRelationTemplateDto;
import gov.derate.proof.common.enums.AssistIdentityType;
import gov.derate.proof.common.enums.AssistResultEnum;
import gov.derate.proof.common.enums.LegalPersonIdentityType;
import gov.derate.proof.common.service.BeanFactoryUtils;
import gov.derate.proof.common.utils.BeanCopyUtils;
import gov.derate.proof.common.visitor.Visitor;
import gov.derate.proof.dictionary.entity.DictionaryTypeItemConstant;
import gov.derate.proof.license.dto.ImplementLicenseItemInfoDto;
import gov.derate.proof.license.dto.OperatorDto;
import gov.derate.proof.license.resp.LicenseItemGetGroupSealResp;
import gov.derate.proof.license.resp.LicenseItemGetGroupSealResponse;
import gov.derate.proof.license.sdk.LicenseProofSdk;
import gov.derate.proof.license.service.LicenseFacadeService;
import gov.derate.proof.license.service.LicenseFacadeStrategyService;
import gov.licc.func.api.amp.service.DictPublicService;
import gov.licc.func.api.auth.dto.OrganizationDto;
import gov.licc.func.api.auth.service.OrganizationPublicService;
import gov.license.common.api.entity.BaseDo;
import gov.license.common.api.utils.Assert;
import gov.license.common.tools.date.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * 协查审核，所有协查审核完成逻辑
 * <p>
 * Company: Zsoft
 * CreateDate:2024/7/30
 *
 * <AUTHOR>
 */
public class AssistAuditedStatusVisitor implements Visitor {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssistAuditedStatusVisitor.class);
    /**
     * 协查数据
     */
    private final AssistRecordDto assistRecord;
    /**
     * 当前审核协查审核对象列表
     */
    private final List<AssistAuditDto> currentLevelAuditList;
    /**
     * 协查审核对象列表
     */
    private final List<AssistAuditDto> allLevelAuditList;
    /**
     * 当前操作用户信息
     */
    private AccountInfoDto currentAuditUserInfo;
    /**
     * 审核员-issue接口，多层审核数据项
     */
    private static final String SHY_ISSUE_DATA_ITEM = "SHY%d-%d";
    /**
     * 审核员部门名称-issue接口，多层审核数据项
     */
    private static final String SHBM_ISSUE_DATA_ITEM = "SHBM%d-%d";
    /**
     * 审核意见-issue接口，多层审核数据项
     */
    private static final String SHYJ_ISSUE_DATA_ITEM = "SHYJ%d-%d";
    /**
     * 审核时间-issue接口，多层审核数据项
     */
    private static final String SHSJ_ISSUE_DATA_ITEM = "SHSJ%d-%d";
    /**
     * 审核结果-issue接口，多层审核数据项
     */
    private static final String SHJG_ISSUE_DATA_ITEM = "SHJG%d-%d";
    /**
     * 审核level
     */
    private Integer level;

    @Override
    public Boolean visitor() {
        LOGGER.debug("running status logic [{}]", getVisitorName());
        Assert.isTrue(AssistResultEnum.WAIT.equals(assistRecord.getAuditResult()), AssistResponseCode.ASSIST_RECORD_FINISH_ERROR);
        AssistRecordRepository assistRecordRepository = BeanFactoryUtils.getBean(AssistRecordRepository.class);
        AssistRecordService bean = BeanFactoryUtils.getBean(AssistRecordService.class);
        Date now = new Date();
        // 保存层级审核对象数据。
        assistRecord.setLastModificationTime(now);
        currentLevelAuditList.stream()
                .filter(AssistAuditDto::getNeedAudit)
                .filter(AssistAuditDto::getAuditRecord)
                .forEach(item -> item.setAuditTime(now));
        saveAssistAudit(now, currentLevelAuditList);
        if (level == 1) {
            // 对第一层的审核对象用户，
            Optional<AssistAuditDto> first = currentLevelAuditList.stream().filter(item -> item.getAuditLevel().equals(1))
                    .filter(AssistAuditDto::getNeedAudit)
                    .filter(AssistAuditDto::getAuditRecord)
                    .findFirst();
            if (first.isPresent()) {
                AssistAuditDto auditDto = first.get();
                AccountInfoService accountInfoService = BeanFactoryUtils.getBean(AccountInfoService.class);
                assistRecord.setToUserName(auditDto.getToUserName());
                Optional<AccountInfoDto> accountInfoByAccountOption = accountInfoService.getAccountInfoByAccountOption(auditDto.getToUserAccount());
                accountInfoByAccountOption.ifPresent(item -> {
                    assistRecord.setToUserContain(item.getMobilePhone());
                    assistRecord.setToUserId(item.getAccountId());
                });
                assistRecord.setAuditUserId(auditDto.getToUserAccount());
                assistRecord.setAuditSuggestion(auditDto.getAuditSuggestion());
            }
        }
        int nextLevel = level + 1;
        Optional<AssistAuditDto> nextLevelOptional = allLevelAuditList.stream().filter(item -> item.getAuditLevel() == nextLevel).findFirst();
        boolean haveNextLevel = nextLevelOptional.isPresent();

        // 判断当前层级是否审核完成
        List<AssistAuditDto> assistAuditDbBySerialNum = bean.getAssistAuditBySerialNum(assistRecord.getAssistSerialNumber());
        List<AssistAuditDto> currentDbLevelAuditList = assistAuditDbBySerialNum.stream().filter(item -> item.getAuditLevel().equals(level)).collect(Collectors.toList());
        long currentLevelNeedAuditCount = currentDbLevelAuditList.stream()
                .filter(AssistAuditDto::getNeedAudit)
                .count();
        long currentLevelAuditCount = currentDbLevelAuditList.stream()
                .filter(AssistAuditDto::getNeedAudit)
                .filter(item -> AssistResultEnum.SUCCESS.equals(item.getAssistAuditResult()) || AssistResultEnum.FAIL.equals(item.getAssistAuditResult())).count();
        boolean isAllCurrentLevelAudit = currentLevelNeedAuditCount == currentLevelAuditCount;

        currentLevelAuditList.stream().filter(item -> Objects.isNull(item.getAuditTime()))
                .filter(AssistAuditDto::getNeedAudit)
                .filter(AssistAuditDto::getAuditRecord)
                .forEach(item -> item.setAuditTime(now));
        if (haveNextLevel && isAllCurrentLevelAudit) {
            // 有下级审核的情况，对勾选的下层需要审核的对象，进行设值保存
            // 当前层级审核完成，改变下一级需要审核的对象更变审核状态
            // 处理下一个层级的状态，未审核状态单更变为待审核
            List<AssistAuditDto> nextLevelDbLevelAuditList = assistAuditDbBySerialNum.stream().filter(item -> item.getAuditLevel().equals(nextLevel)).collect(Collectors.toList());
            Set<String> changeStatusNextAuditLevelIdSet = nextLevelDbLevelAuditList.stream().filter(AssistAuditDto::getNeedAudit).map(BaseDo::getId).collect(Collectors.toSet());
            allLevelAuditList.stream().filter(item -> item.getAuditLevel().equals(nextLevel))
                    .filter(item -> item.getAssistAuditResult() == AssistResultEnum.NOT_WAIT)
                    .filter(item -> changeStatusNextAuditLevelIdSet.contains(item.getId()))
                    .forEach(item -> {
                        item.setAssistAuditResult(AssistResultEnum.WAIT);
                        item.setAuditRecord(true);
                        item.setNeedAudit(true);
                    });
            saveAssistAudit(now, allLevelAuditList);
            List<String> needSendMsgCreditCodeList = allLevelAuditList.stream().filter(item -> item.getAuditLevel().equals(nextLevel)).filter(item -> item.getAssistAuditResult() == AssistResultEnum.WAIT).map(AuditRelationTemplateDto::getAuditOrgCode).distinct().collect(Collectors.toList());
            bean.sendMsgByAssistCodeAndCreditCodeList(assistRecord.getAssistSerialNumber(), needSendMsgCreditCodeList);
        }
        if (!haveNextLevel) {
            // 是，判断是否所有层级审核完成
            long allLevelAuditCount = assistAuditDbBySerialNum.stream()
                    .filter(AssistAuditDto::getNeedAudit)
                    .filter(item -> AssistResultEnum.SUCCESS.equals(item.getAssistAuditResult()) || AssistResultEnum.FAIL.equals(item.getAssistAuditResult()))
                    .count();
            long allLevelNeedCount = assistAuditDbBySerialNum.stream()
                    .filter(AssistAuditDto::getNeedAudit)
                    .count();
            // 所有审核层级所有已经审核完毕
            boolean isAllLevelAudit = allLevelNeedCount == allLevelAuditCount;
            if (isAllCurrentLevelAudit && isAllLevelAudit) {
                // 是，协查单改变状态=已完成，更新状态等操作
                assistRecord.setAuditResult(AssistResultEnum.SUCCESS);
                assistRecord.setAuditTime(now);
                callLicenseProofIssueInterface();
                // 协查回调逻辑，由executor工具提供 【scanAssistRecordCallBackHandle】
            }
        }
        Optional<AssistRecordDo> assistRecordDoOptional = Optional.ofNullable(BeanCopyUtils.copy(assistRecord, AssistRecordDo.class));
        AssistRecordDo assistRecordDo = assistRecordDoOptional.orElseThrow(() -> new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_AUDIT_VALID_ERROR.formatByReplaceFlag("协查单无法找到")));
        assistRecordRepository.save(assistRecordDo);
        saveAssistAudit(now, currentLevelAuditList);
        return true;
    }

    private void saveAssistAudit(Date now, List<AssistAuditDto> currentLevelAuditList) {
        AssistAuditRepository assistAuditRepository = BeanFactoryUtils.getBean(AssistAuditRepository.class);
        Set<String> collect = currentLevelAuditList.stream().map(BaseDo::getId).collect(Collectors.toSet());
        List<AssistAuditDo> curentLevelDbList = assistAuditRepository.findAllById(collect);
        if (CollectionUtils.isNotEmpty(curentLevelDbList)) {
            Map<String, AssistAuditDo> idAndCurrentLevelAuditDtoMap = curentLevelDbList.stream()
                    .collect(Collectors.toMap(BaseDo::getId, v -> v, (b, a) -> a));
            // 被设值为要审核的数据，才进行copy
            List<AssistAuditDo> saveDo = currentLevelAuditList.stream()
                    .filter(AssistAuditDto::getNeedAudit)
                    .filter(AssistAuditDto::getAuditRecord)
                    .map(item -> {
                        AssistAuditDo auditDo = idAndCurrentLevelAuditDtoMap.get(item.getId());
                        BeanCopyUtils.copy(item, auditDo);
                        item.setAuditTime(now);
                        return auditDo;
                    }).collect(Collectors.toList());
            assistAuditRepository.saveAll(saveDo);
        }
    }

    /**
     * 调用电子证明issue接口制证。
     */
    private void callLicenseProofIssueInterface() {
        //1.不为空;2.是开局;3.开具方式不为空;4.系统开具

        switch (assistRecord.getIssueProofLicense()) {
            case NOT_ISSUE:
                break;
            case ISSUE:
                assistRecordSetAuthCode();
                break;
            case NOT_ISSUE_ANY_NO_PASS:
                boolean flag = true;
                for (AssistAuditDto assistAuditDto : allLevelAuditList) {
                    if (assistAuditDto.getAssistAuditResult().equals(AssistResultEnum.FAIL)) {
                        flag = false;
                        break;
                    }
                }
                if (flag) {
                    assistRecordSetAuthCode();
                }
                break;
            case NOT_ISSUE_LAST_NO_PASS:
                break;
            default:
                break;
        }

    }

    /**
     * 协查数据 调用电子证照证明签发证明文件 并设置 电子证明归档authCode
     */
    private void assistRecordSetAuthCode() {
        // 调用接口，获取auth_code,若数据项为空的情况下，跳过制证方式，没有授权authCode
        String authCode = issueLicenseItem();
        if (StringUtils.isNotBlank(authCode)) {
            assistRecord.setLicenseItemAuthCode(authCode);
        }
    }


    /**
     * 调用电子证明，issue接口逻辑
     */
    private String issueLicenseItem() {
        Map<String, String> dataItemMap = getAssistRecordDataItemMapping();
        if (dataItemMap.isEmpty()) {
            return StringUtils.EMPTY;
        }

        // 触发获取证明目录-电子证明的配置实施码的具体配置。包括模板，印章信息等。
        // 调用电子证明issue接口，进行签发电子证明。
        String implementCode = this.assistRecord.getImplementCode();
        Assert.hasText(implementCode, AssistResponseCode.ASSIST_RECORD_AUDIT_ISSUE_VALID_ERROR.formatByReplaceFlag("审核失败，协查单实施码为空"));
        String licenseGroup = getLicenseGroupByImplementCode(implementCode);
        String sealCode = getSealCodeByImplementCode(implementCode, dataItemMap);
        OperatorDto operatorDto = buildCurrentUserOperatorDto();
        // 需要通过实施码具体详情，获取dataItem，找到中文对应的映射关系。传递到dataItemMap
        LicenseProofSdk sdk = BeanFactoryUtils.getBean(LicenseProofSdk.class);
        String bizNum = assistRecord.getAssistSerialNumber();
        return sdk.issueLicense(assistRecord.getItemName(), assistRecord.getItemCode(), licenseGroup, sealCode, bizNum, implementCode, operatorDto, dataItemMap);
    }

    /**
     * 根据当前协查单，获取协查单对应数据项
     *
     * @return 数据项
     */
    private Map<String, String> getAssistRecordDataItemMapping() {
        AssistRecordService bean = BeanFactoryUtils.getBean(AssistRecordService.class);
        LicenseFacadeStrategyService licenseFacadeStrategyService = BeanFactoryUtils.getBean(LicenseFacadeStrategyService.class);
        LicenseFacadeService licenseFacadeService = licenseFacadeStrategyService.settingStrategy(LicenseFacadeStrategyService.LicenseProcessStrategy.LICENSE_PROOF);
        List<AssistInfoItemBo> infoItemBo = bean.getAssistInfoItemByAssistRecordId(assistRecord.getAssistSerialNumber());
        Map<String, String> dataItem = Maps.newHashMap();

        String implementCode = assistRecord.getImplementCode();
        // 证照数据项处理
        Assert.hasText(implementCode, AssistResponseCode.ASSIST_RECORD_AUDIT_DATA_ITEM_VALID_ERROR.formatByReplaceFlag("审核失败，协查单实施码为空"));
        String basicCode = implementCode.substring(0, 9);
        List<ImplementLicenseItemInfoDto> implementLicenseItemInfoDtos = licenseFacadeService.queryImplLicenseItemInfoByBasicCode(basicCode);
        Optional<ImplementLicenseItemInfoDto> licenseItemInfoDto = implementLicenseItemInfoDtos.stream().filter(item -> item.getImplementCode().equals(implementCode))
                .findFirst();
        ImplementLicenseItemInfoDto implementLicenseItemInfoDto = licenseItemInfoDto.orElseThrow(() -> new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_AUDIT_DATA_ITEM_VALID_ERROR.formatByReplaceFlag("审核失败，实施码开通目录数据获取异常")));
        Optional<ImplementLicenseItemInfoDto.LicenseItemDataItemInfoDto> dataItemDtoOptional = implementLicenseItemInfoDto.buildDataItemDto();
        Assert.isTrue(dataItemDtoOptional.isPresent(), AssistResponseCode.ASSIST_RECORD_AUDIT_DATA_ITEM_VALID_ERROR.formatByReplaceFlag("审核失败,构造电子证明-数据项失败"));
        ImplementLicenseItemInfoDto.LicenseItemDataItemInfoDto dataItemDto = dataItemDtoOptional.get();
        OrganizationPublicService organizationPublicService = BeanFactoryUtils.getBean(OrganizationPublicService.class);
        Optional<OrganizationDto> organizationDtoOptional = organizationPublicService.findByCreditCode(this.assistRecord.getFromAssistCreditCode());
        organizationDtoOptional.orElseThrow(() -> new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_AUDIT_DATA_ITEM_VALID_ERROR.formatByReplaceFlag("审核失败，发起人部门在系统不存在")));
        OrganizationDto organizationDto = organizationDtoOptional.get();
        String divisionCode = organizationDto.getDivisionCode();
        String holderIdentityType = "";
        if (Objects.nonNull(assistRecord.getHandleAffairsAssistIdentityType())) {
            holderIdentityType = convertRecordIdTypeToLicenseHolderIdentityType(assistRecord.getHandleAffairsAssistIdentityType());
        }
        if (Objects.nonNull(assistRecord.getLegalPersonIdentityType())) {
            holderIdentityType = convertRecordIdTypeToLicenseHolderIdentityType(assistRecord.getLegalPersonIdentityType());
        }
        // 基础10项数据项处理
        LicenseTenBaseFieldDto licenseTenBaseFieldDto = new LicenseTenBaseFieldDto(implementLicenseItemInfoDto.getName(), UUID.randomUUID().toString(), this.assistRecord.getHandleAffairsName(), holderIdentityType, this.assistRecord.getHandleAffairsIdentityNumber(), this.assistRecord.getFromAssistOrgName(), this.assistRecord.getFromAssistCreditCode(), divisionCode, DateUtil.formatDate(new Date()), null);
        Map<String, String> basicFieldDataItemMap = Maps.newHashMap();
        basicFieldDataItemMap.putAll(licenseTenBaseFieldDto.buildJsonMap());
        Set<String> basicFieldKeySet = basicFieldDataItemMap.keySet();

        // 目录非审核字段配置字段
        Map<String, String> withoutAuditFieldConfigDataItemMap = Maps.newHashMap();
        String auditUserNamePrefixFieldName = "SHY";
        String auditOrgNamePrefixFieldName = "SHBM";
        String auditCommentPrefixFieldName = "SHYJ";
        String auditTimePrefixFieldName = "SHSJ";
        String auditResultPrefixFieldName = "SHJG";
        dataItemDto.getItems().stream()
                .filter(item -> !item.getKey().startsWith(auditUserNamePrefixFieldName))
                .filter(item -> !item.getKey().startsWith(auditOrgNamePrefixFieldName))
                .filter(item -> !item.getKey().startsWith(auditCommentPrefixFieldName))
                .filter(item -> !item.getKey().startsWith(auditResultPrefixFieldName))
                .forEach(item -> withoutAuditFieldConfigDataItemMap.put(item.getKey(), item.getLabel()));

        // 计算实施目录的业务字段key值数量
        Set<String> configDataItemKeySet = withoutAuditFieldConfigDataItemMap.keySet();
        configDataItemKeySet.removeAll(basicFieldKeySet);

        boolean isHaveBizConfigDataItem = configDataItemKeySet.size() > 0;
        if (isHaveBizConfigDataItem) {
            // 有配置业务信息项，
            // 业务数据项处理
            Map<String, String> bizDataItemMap = Maps.newHashMap();
            infoItemBo.forEach(item -> {
                String keyCn = item.getKey();
                Optional<ImplementLicenseItemInfoDto.LicenseItemDataItemDetailDto> dataItemConfigOptional = dataItemDto.getItems().stream().filter(dataDto -> dataDto.getLabel().equals(keyCn)).findFirst();
                dataItemConfigOptional.ifPresent(itemConfig -> bizDataItemMap.putIfAbsent(itemConfig.getKey(), item.getValue()));
            });

            // 计算实际入参的业务字段key值数量
            Set<String> bizDataItemKeySet = bizDataItemMap.keySet();
            bizDataItemKeySet.removeAll(basicFieldKeySet);
            boolean isHaveCallInBizDataItem = bizDataItemKeySet.size() > 0;
            if (isHaveCallInBizDataItem) {
                // 有业务配置，有业务入参，继续组装dataItem数据项。
                dataItem.putAll(bizDataItemMap);
            } else {
                // 不调用issue接口，返回空Map
                return Maps.newHashMap();
            }
        }
        // 无配置业务信息项，调用issue接口
        dataItem.putAll(basicFieldDataItemMap);
        dataItemDto.getItems().stream()
                .filter(item -> item.getKey().startsWith(auditUserNamePrefixFieldName)
                        || item.getKey().startsWith(auditOrgNamePrefixFieldName)
                        || item.getKey().startsWith(auditCommentPrefixFieldName)
                        || item.getKey().startsWith(auditTimePrefixFieldName)
                        || item.getKey().startsWith(auditResultPrefixFieldName))
                .forEach(item -> {
                    dataItem.put(item.getKey(), item.getLabel());
                });
        LOGGER.debug("have data item [{}]", dataItem);
        // 预设置审核部门值
        DictPublicService dictPublicService = BeanFactoryUtils.getBean(DictPublicService.class);
        Optional<String> detailValue = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_PROOF_ISSUE_ASSIST_RESULT_MAPPING.getDictType().name(), DictionaryTypeItemConstant.LICENSE_PROOF_ISSUE_ASSIST_RESULT_MAPPING.name());
        String convertRule = detailValue.orElse("SUCCESS:通过,FAIL:不通过");

        // 多层审核数据项规则处理。
        processDataItemByAuditLevelInfo(dataItem, (dataItemKeySet, item) -> {
                    dataItemKeySet.setValue(item.getToUserName());
                }, (dataItemKeySet, item) -> {
                    dataItemKeySet.setValue(item.getAuditOrgName());
                }, (dataItemKeySet, item) -> {
                    dataItemKeySet.setValue(item.getAuditSuggestion());
                }, (dataItemKeySet, item) -> {
                    dataItemKeySet.setValue(DateUtil.format(item.getAuditTime(), "yyyy-MM-dd"));
                }, (dataItemKeySet, item) -> {
                    if (StringUtils.isNotEmpty(convertRule)) {
                        Optional<String> firstMapping = Arrays.stream(convertRule.split(","))
                                .map(roleStr -> {
                                    ArrayList<String> strings = Lists.newArrayList(roleStr.split(":"));
                                    String auditResultEnumName = strings.get(0);
                                    String auditResultEnumMappingStr = strings.get(1);
                                    if (StringUtils.equals(item.getAssistAuditResult().name(), auditResultEnumName)) {
                                        return auditResultEnumMappingStr;
                                    } else {
                                        return null;
                                    }
                                }).filter(StringUtils::isNotEmpty).findFirst();
                        if (firstMapping.isPresent()) {
                            dataItemKeySet.setValue(firstMapping.get());
                        } else {
                            dataItemKeySet.setValue(item.getAssistAuditResult().getAuditPageDesc());
                        }
                    } else {
                        dataItemKeySet.setValue(item.getAssistAuditResult().getAuditPageDesc());
                    }

                }, true
        );
        LOGGER.debug("processDataItemByAuditLevelInfo  [{}]", dataItem);

        // 处理映射值
        dataItem.entrySet().forEach(mapEntry -> {
            String key = mapEntry.getKey();
            Optional<ImplementLicenseItemInfoDto.LicenseItemDataItemDetailDto> dataItemConfigOptional = dataItemDto.getItems().stream().filter(dataDto -> dataDto.getKey().equals(key)).findFirst();
            dataItemConfigOptional.ifPresent(itemConfig -> {
                String value = getLicenseItemDataItemOptionRuleMapValue(itemConfig.getOptions(), mapEntry.getValue(), itemConfig.getTypeCode());
                mapEntry.setValue(value);
            });
        });

        return dataItem;
    }

    /**
     * 处理数据项的审核层级字段数据
     *
     * @param dataItem 数据项，包含审核字段Key的dataItem
     * @param shyProc  处理审核人的函数,包含匹配到的数据项和对应层级dto数据
     * @param shbmProc 处理审核部门的函数,包含匹配到的数据项和对应层级dto数据
     * @param shyjProc 处理审核意见的函数,包含匹配到的数据项和对应层级dto数据
     * @param shsjProc 处理审核时间的函数,包含匹配到的数据项和对应层级dto数据
     * @param shjgProc 处理审核结果的函数,包含匹配到的数据项和对应层级dto数据
     */
    private void processDataItemByAuditLevelInfo(Map<String, String> dataItem, BiConsumer<Map.Entry<String, String>, AssistAuditDto> shyProc, BiConsumer<Map.Entry<String, String>, AssistAuditDto> shbmProc, BiConsumer<Map.Entry<String, String>, AssistAuditDto> shyjProc, BiConsumer<Map.Entry<String, String>, AssistAuditDto> shsjProc, BiConsumer<Map.Entry<String, String>, AssistAuditDto> shjgProc, boolean removeAuditDataItem) {
        Map<Integer, ArrayList<AssistAuditDto>> auditLevelAndAuditDtoMap = allLevelAuditList.stream()
                .filter(item -> Objects.nonNull(item.getNeedAudit()) && item.getNeedAudit())
                .collect(Collectors.toMap(AuditRelationTemplateDto::getAuditLevel, Lists::newArrayList, (before, after) -> {
                    before.addAll(after);
                    return before;
                }));

        String auditLevelByDataFieldPatten = "%d-%d";
        List<String> existsAuditDataItemKey = Lists.newArrayList();
        for (Map.Entry<Integer, ArrayList<AssistAuditDto>> entry : auditLevelAndAuditDtoMap.entrySet()) {
            entry.getValue().sort(Comparator.comparing(AssistAuditDto::getAssistTime));
            ArrayList<AssistAuditDto> value = entry.getValue();
            int auditIndex = 1;
            for (AssistAuditDto item : value) {
                int finalIndex = auditIndex;
                String auditLevelStr = String.format(auditLevelByDataFieldPatten, item.getAuditLevel(), finalIndex);
                dataItem.entrySet().stream()
                        .filter(dataFieldKeySet -> dataFieldKeySet.getKey().contains(auditLevelStr))
                        .forEach(dataFieldKeySet -> {
                            if (dataFieldKeySet.getKey().contains(String.format(SHY_ISSUE_DATA_ITEM, item.getAuditLevel(), finalIndex))) {
                                shyProc.accept(dataFieldKeySet, item);
                                existsAuditDataItemKey.add(dataFieldKeySet.getKey());
                            }
                            if (dataFieldKeySet.getKey().contains(String.format(SHBM_ISSUE_DATA_ITEM, item.getAuditLevel(), finalIndex))) {
                                shbmProc.accept(dataFieldKeySet, item);
                                existsAuditDataItemKey.add(dataFieldKeySet.getKey());
                            }
                            if (dataFieldKeySet.getKey().contains(String.format(SHYJ_ISSUE_DATA_ITEM, item.getAuditLevel(), finalIndex))) {
                                shyjProc.accept(dataFieldKeySet, item);
                                existsAuditDataItemKey.add(dataFieldKeySet.getKey());
                            }
                            if (dataFieldKeySet.getKey().contains(String.format(SHSJ_ISSUE_DATA_ITEM, item.getAuditLevel(), finalIndex))) {
                                shsjProc.accept(dataFieldKeySet, item);
                                existsAuditDataItemKey.add(dataFieldKeySet.getKey());
                            }
                            if (dataFieldKeySet.getKey().contains(String.format(SHJG_ISSUE_DATA_ITEM, item.getAuditLevel(), finalIndex))) {
                                shjgProc.accept(dataFieldKeySet, item);
                                existsAuditDataItemKey.add(dataFieldKeySet.getKey());
                            }
                        });
                auditIndex++;
            }
        }

        LOGGER.debug("processDataItemByAuditLevelInfo dataItem existsAuditDataItemKey = [{}]", existsAuditDataItemKey);

        if (removeAuditDataItem) {
            // 删除没有审核的多余的证明目录配置项
            List<String> issueDataItemList = Lists.newArrayList();
            dataItem.entrySet().stream()
                    .filter(item -> item.getKey().contains("SHY") || item.getKey().contains("SHBM") || item.getKey().contains("SHYJ") || item.getKey().contains("SHSJ") || item.getKey().contains("SHJG"))
                    .forEach(item -> issueDataItemList.add(item.getKey()));
            LOGGER.debug("processDataItemByAuditLevelInfo dataItem itemIssueDataItem = [{}]", issueDataItemList);

            Collection<String> removeKeys = CollectionUtils.subtract(issueDataItemList, existsAuditDataItemKey);
            LOGGER.debug("processDataItemByAuditLevelInfo dataItem removeKey = [{}]", removeKeys);

            removeKeys.forEach(dataItem::remove);
        }
    }

    /**
     * 电子证明option字段找映射规则值方法
     *
     * @param licenseItemOptionsField 电子证明目录options字段值
     * @param mappingValue            映射值。
     * @param typeCode                电子证明目录type_code字段值,数据项类型
     * @return 规则值，若找不到规则值，返回mappingValue
     */
    private static String getLicenseItemDataItemOptionRuleMapValue(String licenseItemOptionsField, String mappingValue, String typeCode) {
        String finalValue = mappingValue;
        if (StringUtils.isNotBlank(licenseItemOptionsField)) {
            // 解析值。
            String licenseOptionsRuleSplitStr = ",";
            String mappingValueSplitStr = ",";
            List<String> optionRuleList = Lists.newArrayList(licenseItemOptionsField.split(licenseOptionsRuleSplitStr));
            if (CollectionUtils.isNotEmpty(optionRuleList)) {
                if (typeCode.equals("checkbox")) {// 找到映射值，取值； 处理值例子 mappingValue=映射值1,映射值2  licenseItemOptionsField=值1|映射值1,值2|映射值2
                    String joinStr = optionRuleList.stream().map(rule -> Lists.newArrayList(rule.split("\\|")))
                            .filter(CollectionUtils::isNotEmpty)
                            .filter(ruleList -> ruleList.size() == 2)
                            .filter(ruleList -> {
                                // 找映射到的数据
                                String valueTmp = ruleList.get(0);
                                String mappingTmp = ruleList.get(1);
                                List<String> mappingValueSplitList = Lists.newArrayList(mappingValue.split(mappingValueSplitStr));
                                Optional<String> mayExistsMappingOptional = mappingValueSplitList.stream().filter(item -> item.equals(mappingTmp)).findFirst();
                                return mayExistsMappingOptional.isPresent();
                            }).map(ruleList -> {
                                // 取映射后的数据
                                String valueTmp = ruleList.get(0);
                                String mappingTmp = ruleList.get(1);
                                return valueTmp;
                            })
                            .collect(Collectors.joining(","));
                    // 找不到映射值，取value
                    finalValue = StringUtils.isNotBlank(joinStr) ? joinStr : mappingValue;
                } else {// 找到映射值，取值 处理值例子 mappingValue=映射值1  licenseItemOptionsField=值1|映射值1,值2|映射值2
                    Optional<String> ruleValueOptional = optionRuleList.stream().map(rule -> Lists.newArrayList(rule.split("\\|")))
                            .filter(CollectionUtils::isNotEmpty)
                            .filter(ruleList -> ruleList.size() == 2)
                            .filter(ruleList -> {
                                // 找映射到的数据
                                String valueTmp = ruleList.get(0);
                                String mappingTmp = ruleList.get(1);
                                return mappingValue.equals(mappingTmp);
                            }).map(ruleList -> {
                                // 取映射后的数据
                                String valueTmp = ruleList.get(0);
                                String mappingTmp = ruleList.get(1);
                                return valueTmp;
                            })
                            .findFirst();
                    // 找不到映射值，取value
                    finalValue = ruleValueOptional.orElse(mappingValue);
                }

            }
        }
        return finalValue;
    }

    /**
     * 协查单类型，根据规则转换到issue接口身份证值类型
     *
     * @param handleAffairsAssistIdentityType 协查证件类型
     * @return 证照持有人证件类型值。
     */
    private String convertRecordIdTypeToLicenseHolderIdentityType(AssistIdentityType handleAffairsAssistIdentityType) {
        DictPublicService dictPublicService = BeanFactoryUtils.getBean(DictPublicService.class);
        Optional<String> detailValue = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_PROOF_HOLDER_ID_TYPE_CONVERT_RULE.getDictType().name(), DictionaryTypeItemConstant.LICENSE_PROOF_HOLDER_ID_TYPE_CONVERT_RULE.name());
        String convertRule = detailValue.orElseThrow(() -> new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_AUDIT_DATA_ITEM_VALID_ERROR.formatByReplaceFlag("审核失败，规则转换获取失败")));
        String splitStr = ",";
        List<String> convertRuleList = Lists.newArrayList(convertRule.split(splitStr));
        for (String item : convertRuleList) {
            String ruleSplitStr = "\\|";
            String[] split = item.split(ruleSplitStr);
            String assistIdentityTypeName = split[0];
            String licenseHolderType = split[1];
            if (handleAffairsAssistIdentityType.name().equals(assistIdentityTypeName)) {
                return licenseHolderType;
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 协查单类型，根据规则转换到issue接口身份证值类型
     *
     * @param identityType 协查证件类型
     * @return 证照持有人证件类型值。
     */
    private String convertRecordIdTypeToLicenseHolderIdentityType(LegalPersonIdentityType identityType) {
        DictPublicService dictPublicService = BeanFactoryUtils.getBean(DictPublicService.class);
        Optional<String> detailValue = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_PROOF_LEGAL_HOLDER_ID_TYPE_CONVERT_RULE.getDictType().name(), DictionaryTypeItemConstant.LICENSE_PROOF_LEGAL_HOLDER_ID_TYPE_CONVERT_RULE.name());
        String convertRule = detailValue.orElseThrow(() -> new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_AUDIT_DATA_ITEM_VALID_ERROR.formatByReplaceFlag("审核失败，规则转换获取失败")));
        String splitStr = ",";
        List<String> convertRuleList = Lists.newArrayList(convertRule.split(splitStr));
        for (String item : convertRuleList) {
            String ruleSplitStr = "\\|";
            String[] split = item.split(ruleSplitStr);
            String assistIdentityTypeName = split[0];
            String licenseHolderType = split[1];
            if (identityType.name().equals(assistIdentityTypeName)) {
                return licenseHolderType;
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 根据当前账号，构造操作人对象
     *
     * @return 操作人对象
     */
    @NotNull
    private OperatorDto buildCurrentUserOperatorDto() {
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setAccount(currentAuditUserInfo.getAccountName());
        operatorDto.setName(currentAuditUserInfo.getUserName());
        operatorDto.setIdentityNum(currentAuditUserInfo.getIdentityNumber());
        operatorDto.setServiceOrg(currentAuditUserInfo.getOrgName());
        operatorDto.setServiceOrgCode(currentAuditUserInfo.getOrgCode());
        operatorDto.setDivision(currentAuditUserInfo.getDivisionName());
        operatorDto.setDivisionCode(currentAuditUserInfo.getDivisionName());
        return operatorDto;
    }

    /**
     * 获取电子证明-实施目录-印章编码唯一值
     *
     * @param implementCode 实施码
     * @param dataItemMap   发送的数据项参数的Key和value值
     * @return 印章编码唯一值
     */
    private String getSealCodeByImplementCode(String implementCode, Map<String, String> dataItemMap) {
        LicenseProofSdk sdk = BeanFactoryUtils.getBean(LicenseProofSdk.class);
        String basicCode = null;
        String orgCode = null;
        LicenseItemGetGroupSealResponse resp = sdk.getSealInfo(implementCode, basicCode, orgCode);
        Assert.notEmpty(resp.getData(), AssistResponseCode.ASSIST_RECORD_AUDIT_ISSUE_VALID_ERROR.formatByReplaceFlag("审核失败，获取证明归档印章失败"));
        LOGGER.debug("getSealCodeByImplementCode dataItem is [{}]", dataItemMap);
        // 匹配seal_org 组织机构代码与审核对象人员组织机构代码是否一致。
        return resp.getData().stream().flatMap(item -> item.getSealList().stream())
                .filter(item -> CollectionUtils.isNotEmpty(item.getElectronicSeal()))
                .sorted(Comparator.comparing(LicenseItemGetGroupSealResp.SealListResp::getSealPosition))
                .filter(item -> CollectionUtils.isNotEmpty(item.getElectronicSeal()))
                .map(item -> {
                    AtomicReference<String> dataItemSealNum = new AtomicReference<>(null);
                    Set<String> dtoCreditCode = Sets.newConcurrentHashSet();
                    processDataItemByAuditLevelInfo(dataItemMap, (dataItemKeySet, dto) -> {
                                //为空
                            }, (dataItemKeySet, dto) -> {
                                // 只要当前印章位置层的数据
                                String sealNumStr = dataItemKeySet.getKey().substring(dataItemKeySet.getKey().lastIndexOf("-"));
                                if (sealNumStr.contains(item.getSealPosition())) {
                                    // 根据部门的key，获取最后一个数字，对应印章位置和当前印章位置相等，并且取出当前dto的creditCode匹配印章
                                    dataItemSealNum.set(item.getSealPosition());
                                    dtoCreditCode.add(dto.getAuditOrgCode());
                                }
                            }, (dataItemKeySet, dto) -> {
                                //为空
                            }, (dataItemKeySet, dto) -> {
                                //为空
                            }, (dataItemKeySet, dto) -> {
                                //为空
                            }, false
                    );
                    if (StringUtils.isBlank(dataItemSealNum.get()) || CollectionUtils.isEmpty(dtoCreditCode)) {
                        LOGGER.debug("process seal not match  seal index [{}] match sealNum [{}] match creditCode [{}]", item.getSealPosition(), dataItemSealNum.get(), dtoCreditCode);
                        // 匹配不到，返回BLANK
                        return "BLANK";
                    }
                    LOGGER.debug("process seal index [{}] match sealNum [{}] match creditCode [{}]", item.getSealPosition(), dataItemSealNum.get(), dtoCreditCode);

                    Optional<String> optional = item.getElectronicSeal().stream()
                            .filter(seal -> dtoCreditCode.stream().anyMatch(code -> code.contains(seal.getSealOrg())))
                            .map(LicenseItemGetGroupSealResp.SealListResp.ElectronicSealResp::getSealCode)
                            .findFirst();
                    return optional.orElse("BLANK");
                }).collect(Collectors.joining(","));
    }

    /**
     * 获取电子证明-实施目录licenseGroup唯一值
     *
     * @param implementCode 实施码
     * @return licenseGroup唯一值
     */
    private String getLicenseGroupByImplementCode(String implementCode) {
        LicenseProofSdk sdk = BeanFactoryUtils.getBean(LicenseProofSdk.class);
        String basicCode = null;
        String orgCode = null;
        LicenseItemGetGroupSealResponse resp = sdk.getSealInfo(implementCode, basicCode, orgCode);
        Assert.notEmpty(resp.getData(), AssistResponseCode.ASSIST_RECORD_AUDIT_ISSUE_VALID_ERROR.formatByReplaceFlag("审核失败，获取证明归档印章失败"));
        Optional<LicenseItemGetGroupSealResp> first = resp.getData().stream().findFirst();
        Assert.isTrue(first.isPresent(), AssistResponseCode.ASSIST_RECORD_AUDIT_ISSUE_VALID_ERROR.formatByReplaceFlag("审核失败，获取证明归档印章失败"));
        Optional<String> firstLicenseGroup = Optional.ofNullable(first.get().getLicenseGroup());
        return firstLicenseGroup.orElseThrow(() -> new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_AUDIT_ISSUE_VALID_ERROR.formatByReplaceFlag("审核失败，获取证明归档印章组别失败")));
    }

    @Override
    public String getVisitorName() {
        return "AssistAuditedStatusVisitor";
    }

    public AssistAuditedStatusVisitor(AssistRecordDto assistRecord, Integer level, List<AssistAuditDto> allLevelAuditList, AccountInfoDto currentAuditUserInfo) {
        this.assistRecord = assistRecord;
        this.level = level;
        this.currentLevelAuditList = allLevelAuditList.stream().filter(item -> item.getAuditLevel().equals(level)).collect(Collectors.toList());
        this.allLevelAuditList = allLevelAuditList;
        this.currentAuditUserInfo = currentAuditUserInfo;
    }
}
