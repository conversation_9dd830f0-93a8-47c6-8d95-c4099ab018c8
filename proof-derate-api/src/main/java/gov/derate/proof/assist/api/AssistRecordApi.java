package gov.derate.proof.assist.api;

import com.google.common.collect.Lists;
import gov.derate.proof.assist.bo.AssistOrgSelectItemBo;
import gov.derate.proof.assist.bo.AssistRecordDetailPageBo;
import gov.derate.proof.assist.bo.AssistRecordHistoryDto;
import gov.derate.proof.assist.dto.GetAssistLicenseProofRecordDto;
import gov.derate.proof.assist.dto.GetAssistRecordApiDto;
import gov.derate.proof.assist.dto.LicenseDerateAssistSignApiDto;
import gov.derate.proof.assist.entity.AssistRecordDo;
import gov.derate.proof.assist.exception.AssistRecordServiceException;
import gov.derate.proof.assist.query.AssistRecordApiQuery;
import gov.derate.proof.assist.query.AssistUserQuery;
import gov.derate.proof.assist.query.CatalogApiQuery;
import gov.derate.proof.assist.req.AssistRecordSaveApiRequest;
import gov.derate.proof.assist.req.GetLicenseDerateAssistApiRequest;
import gov.derate.proof.assist.req.LicenseDerateAssistSignApiRequest;
import gov.derate.proof.assist.resp.AssistResponseCode;
import gov.derate.proof.assist.resp.LicenseDerateAssistSignApiResponse;
import gov.derate.proof.assist.service.AssistAttachmentService;
import gov.derate.proof.assist.service.AssistRecordService;
import gov.derate.proof.assist.service.AssistUserService;
import gov.derate.proof.catalog.bo.CatalogApiBo;
import gov.derate.proof.catalog.service.ProofCatalogService;
import gov.derate.proof.common.response.ResponseCode;
import gov.derate.proof.common.utils.BeanCopyUtils;
import gov.derate.proof.common.utils.FileUtil;
import gov.license.common.api.annotation.ApiAuth;
import gov.license.common.api.controller.BaseController;
import gov.license.common.api.exception.ApplicationGlobalRuntimeException;
import gov.license.common.api.resp.ResponseHelper;
import gov.license.common.api.resp.ResponseResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;


/**
 * 综受平台对外api接口
 * Company: Zsoft
 * CreateDate:2021-10-21
 *
 * <AUTHOR>
 */
@RestController
public class AssistRecordApi extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssistRecordApi.class);
    /**
     * 协查记录服务
     */
    @Autowired
    private AssistRecordService assistRecordService;
    /**
     * 协查用户服务
     */
    @Autowired
    private AssistUserService assistUserService;
    /**
     * 证明目录服务
     */
    @Autowired
    private ProofCatalogService proofCatalogService;

    @Autowired
    private AssistAttachmentService assistAttachmentService;

    /**
     * 查看协查结果接口
     *
     * @param assistSerialNumber 协查流水号  如有多个，用|号分割
     * @return 返回查询数据
     */
    @GetMapping("/api/v1/assist")
    @ApiAuth
    public ResponseResult<List<GetAssistRecordApiDto>> queryListApi(@RequestParam("assist_serial_number") String assistSerialNumber) throws IllegalAccessException, InstantiationException {
        LOGGER.info("AssistRecordApiController.queryListApi request is [{}]", assistSerialNumber);
        AssistRecordApiQuery query = new AssistRecordApiQuery();
        query.setAssistSerialNumberSet(Arrays.asList(assistSerialNumber.split("\\|")));
        List<GetAssistRecordApiDto> resultList = assistRecordService.queryListApi(query);
        return ResponseHelper.success(resultList);
    }

    /**
     * 发起协查申请
     *
     * @param createVo 创建对象
     * @return 返回成功，错误返回错误编码
     */
    @PostMapping(value = "/api/v1/assist", headers = "Content-Type=application/json")
    @ApiAuth
    public ResponseResult<String> createAssistByApi(@Validated @RequestBody AssistRecordSaveApiRequest createVo) throws Exception {
        LOGGER.info("AssistRecordApiController.createApi request is [{}]", createVo);
        try {
            AssistRecordDo recordData = assistRecordService.createAssistByApi(createVo);
            return ResponseHelper.success(recordData.getAssistSerialNumber());
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("createApi error", e);
            throw new AssistRecordServiceException(e.getResponseCode());
        }
    }

    /**
     * 校验第三方接入系统跳转到协查工具，是否通过校验。
     * <p>
     * 该接口由前端进行调用，通过后再进入对应的逻辑。
     * 校验逻辑在ProofApiAuthHandler与apiAuth已经处理，到controller后已经校验通过。
     * appKey作为预留后面可能有另类的校验，进行处理，进行预留
     *
     * @param appKey 接入系统key，预留参数
     * @return 校验成功返回SUCCESS，失败，返回FAILURE
     */
    @GetMapping("/api/v1/auth_third_party")
    @ApiAuth
    public ResponseResult<String> authGovEasyUser(@RequestParam(value = "x-license-appkey", required = false) String appKey) {
        try {
            return ResponseHelper.success("SUCCESS");
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("authGovEasyUser error", e);
            return ResponseHelper.failure(e.getResponseCode());
        }
    }

    /**
     * 查看协查部门列表接口
     * 调用方：综受系统
     *
     * @param creditCode     协查部门统一信用代码
     * @param orgName        协查部门名称
     * @param itemCode       事项编码
     * @param materialName   材料名称[对应无证明城市系统，审核通过的清单的材料名称]
     * @param materialId     材料ID[对应无证明城市系统，审核通过的清单的材料id]
     * @param proofCatalogId 证明目录ID
     * @return 协查部门数据
     */
    @GetMapping("/api/v1/assist_orglist")
    @ApiAuth
    public ResponseResult<List<AssistOrgSelectItemBo>> getAssistOrgList(
            @RequestParam(value = "credit_code", required = false) String creditCode,
            @RequestParam(value = "org_name", required = false) String orgName,
            @RequestParam(value = "item_code", required = false) String itemCode,
            @RequestParam(value = "material_name", required = false) String materialName,
            @RequestParam(value = "material_id", required = false) String materialId,
            @RequestParam(value = "proof_catalog_id", required = false) String proofCatalogId)
            throws IllegalAccessException, InstantiationException {
        if (StringUtils.isEmpty(creditCode)
                && StringUtils.isEmpty(orgName)
                && StringUtils.isEmpty(itemCode)
                && StringUtils.isEmpty(materialName)
                && StringUtils.isEmpty(materialId)
                && StringUtils.isEmpty(proofCatalogId)) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_SERVICE_ORG_LIST_EMPTY_ERROR);
        }
        AssistUserQuery query = new AssistUserQuery();
        query.setOrgCreditCode(creditCode);
        query.setOrgName(orgName);
        query.setItemCode(itemCode);
        query.setMaterialId(materialId);
        query.setMaterialName(materialName);
        query.setProofCatalogIds(new HashSet<>(Collections.singletonList(proofCatalogId)));
        List<AssistOrgSelectItemBo> resList = assistUserService.queryListGroupByOrgCode(query);
        return ResponseHelper.success(resList);
    }


    /**
     * 证明材料目录信息接口
     * 调用方：综受系统
     *
     * @param itemCode     事项编码
     * @param materialName 材料名称[对应无证明城市系统，审核通过的清单的材料名称]
     * @param materialId   材料ID[对应无证明城市系统，审核通过的清单的材料id]
     * @return 查询证明目录与清单的内容
     */
    @GetMapping("/api/v1/catalog")
    @ApiAuth
    public ResponseResult<List<CatalogApiBo>> getAuditSuccessProofCatalog(
            @RequestParam(value = "item_code", required = false) String itemCode,
            @RequestParam(value = "material_name", required = false) String materialName,
            @RequestParam(value = "material_id", required = false) String materialId) {
        if (StringUtils.isEmpty(itemCode)
                && StringUtils.isEmpty(materialName)
                && StringUtils.isEmpty(materialId)) {
            return ResponseHelper.success();
        }
        CatalogApiQuery query = new CatalogApiQuery();
        query.setItemCode(itemCode);
        query.setMaterialId(materialId);
        query.setMaterialName(materialName);
        List<CatalogApiBo> resList = proofCatalogService.getAuditSuccessProofCatalog(query);
        return ResponseHelper.success(resList);
    }


    /**
     * 查看协查详情接口-页面调用
     *
     * @param assistSerialNumber 协查流水号
     * @return 查询协查接口数据
     */
    @ApiAuth
    @GetMapping("/api/v1/assist/detail")
    public ResponseResult<AssistRecordDetailPageBo> assistDetailApi(@RequestParam("assist_serial_number") String assistSerialNumber) throws InstantiationException, IllegalAccessException {
        ResponseResult<AssistRecordDetailPageBo> responseResult;
        AssistRecordApiQuery query = new AssistRecordApiQuery();
        query.setAssistSerialNumberSet(Collections.singletonList(assistSerialNumber));
        AssistRecordDetailPageBo result = assistRecordService.assistDetailApi(query);
        if (result != null) {
            return ResponseHelper.success(result);
        } else {
            return ResponseHelper.success();
        }
    }

    /**
     * 查看协查历史接口-页面调用
     *
     * @param assistSerialNumber 协查流水号
     * @return 查询协查接口数据
     */
    @GetMapping("/api/v1/assist/history")
    @ApiAuth
    public ResponseResult<List<AssistRecordHistoryDto>> assistHistoryApi(@RequestParam("assist_serial_number") String assistSerialNumber) throws IllegalAccessException, InstantiationException {
        List<AssistRecordHistoryDto> resultList = assistRecordService.assistHistoryApi(assistSerialNumber);
        if (CollectionUtils.isEmpty(resultList)) {
            return ResponseHelper.success(Lists.newArrayList());
        }
        return ResponseHelper.success(resultList);
    }

    /**
     * 发起证明目录开具申请
     *
     * @param createVo 创建对象
     * @return 返回成功，错误返回错误编码
     */
    @PostMapping(value = "/license_derate/api/common/v1/assist_sign", headers = "Content-Type=application/json")
    @ApiAuth
    public ResponseResult<LicenseDerateAssistSignApiResponse> createLicenseDerateAssistSignApi(@Validated @RequestBody LicenseDerateAssistSignApiRequest createVo) {
        LOGGER.info("AssistRecordApiController.createApi request is [{}]", createVo);
        try {
            String serialNumber = assistRecordService.createLicenseDerateAssistSign(BeanCopyUtils.copy(createVo, LicenseDerateAssistSignApiDto.class));
            LicenseDerateAssistSignApiResponse data = new LicenseDerateAssistSignApiResponse();
            data.setSerialNumber(serialNumber);
            return ResponseHelper.success(data);
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("createLicenseDerateAssistSignApi error", e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("createLicenseDerateAssistSignApi error", e);
            return ResponseHelper.failure(e);
        }
    }

    /**
     * 获取电子证明开具接口
     *
     * @param request 请求对象
     * @return 返回成功，错误返回错误编码
     */
    @PostMapping(value = "/license_derate/api/common/v1/get_assist_sign", headers = "Content-Type=application/json")
    @ApiAuth
    public ResponseResult<GetAssistLicenseProofRecordDto> getLicenseDerateAssistApi(@Validated @RequestBody GetLicenseDerateAssistApiRequest request) {
        LOGGER.info("AssistRecordApiController.getLicenseDerateAssistApi request is [{}]", request);
        try {
            Optional<GetAssistLicenseProofRecordDto> result = assistRecordService.getLicenseDerateAssistBySerialNumber(request.getSerialNumber());
            GetAssistLicenseProofRecordDto recordDto = result.orElseThrow(() -> new AssistRecordServiceException(AssistResponseCode.ASSIST_LICENSE_DERATE_RECORD_NOT_EXISTS_ERROR));
            return ResponseHelper.success(recordDto);
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("getLicenseDerateAssistApi error", e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("getLicenseDerateAssistApi error", e);
            return ResponseHelper.failure(e);
        }
    }

    /**
     * 发起证明目录开具申请
     *
     * @param file     文件对象
     * @param relation 关联字段 = 协查单号
     * @return 返回成功，错误返回错误编码
     */
    @PostMapping(value = "/assist/api/common/v1/upload_file")
    @ApiAuth
    public ResponseResult<String> uploadAssistFileApi(@RequestParam(value = "file", required = false) MultipartFile file,
                                                      @RequestParam(value = "relation", required = false) String relation) {
        try (InputStream inputStream = file.getInputStream()) {
            byte[] bytes = IOUtils.toByteArray(inputStream);
//            FileUtil.checkOriginalFileFormatSize(file.getOriginalFilename(), file.getSize(), bytes, "", uploadFileAttachmentService.getLimitMaxFileSizeConfig());
            FileUtil.checkOriginalFileNameSuffix(file.getOriginalFilename(), "");
            FileUtil.checkOriginalFileData(bytes, file.getOriginalFilename());
            String fileId = assistAttachmentService.uploadFile(relation, file.getBytes(), file.getOriginalFilename());
            return ResponseHelper.success(fileId);
        } catch (IOException e) {
            LOGGER.error("uploadAssistFileApi error", e);
            return ResponseHelper.failure(ResponseCode.BIZ_ERROR);
        } catch (IllegalArgumentException e) {
            LOGGER.error("{}.uploadAssistFileApi IllegalArgumentException", this.getClass().getSimpleName(), e);
            return ResponseHelper.failure(ResponseCode.UPLOAD_FILE_ERROR, null, true);
        } catch (Exception e) {
            LOGGER.error("{}.uploadAssistFileApi exception", this.getClass().getSimpleName(), e);
            return ResponseHelper.failure(ResponseCode.UPLOAD_FILE_ERROR, null, true);
        }
    }


}

