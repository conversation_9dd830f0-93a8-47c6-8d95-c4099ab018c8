package gov.derate.proof.assist.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import gov.derate.proof.account.dto.AccountInfoDto;
import gov.derate.proof.account.query.AccountInfoPageQuery;
import gov.derate.proof.account.service.AccountInfoService;
import gov.derate.proof.assist.bo.*;
import gov.derate.proof.assist.dto.*;
import gov.derate.proof.assist.entity.*;
import gov.derate.proof.assist.enums.IssueProofLicenseTypeEnum;
import gov.derate.proof.assist.exception.AssistRecordServiceException;
import gov.derate.proof.assist.exception.GovEasyApiPermissionException;
import gov.derate.proof.assist.manager.GovEasyManager;
import gov.derate.proof.assist.query.*;
import gov.derate.proof.assist.repository.*;
import gov.derate.proof.assist.req.*;
import gov.derate.proof.assist.resp.AssistRecordListApiResponse;
import gov.derate.proof.assist.resp.AssistRecordViewDetailApiDto;
import gov.derate.proof.assist.resp.AssistResponseCode;
import gov.derate.proof.assist.resp.GovEasyManagerResponseCode;
import gov.derate.proof.assist.service.AssistRecordService;
import gov.derate.proof.assist.utils.GovEasyJwtUtil;
import gov.derate.proof.assist.visitor.AssistApiEqToUserIdAndJwtTokenUserIdVisitor;
import gov.derate.proof.catalog.bo.*;
import gov.derate.proof.catalog.dto.AuditRelationStatusDto;
import gov.derate.proof.catalog.dto.AuditRelationTemplateDto;
import gov.derate.proof.catalog.dto.ProofCatalogLicenseItemImplItemInfoDto;
import gov.derate.proof.catalog.entity.ProofCatalogDo;
import gov.derate.proof.catalog.enums.AuditRelationStatusEnums;
import gov.derate.proof.catalog.resp.CatalogResponseCode;
import gov.derate.proof.catalog.service.ProofCatalogService;
import gov.derate.proof.common.bo.UploadFileAttachmentBo;
import gov.derate.proof.common.constant.CacheKeyBuilder;
import gov.derate.proof.common.enums.*;
import gov.derate.proof.common.exception.DictionaryConfigException;
import gov.derate.proof.common.response.DictionaryResponseCode;
import gov.derate.proof.common.response.ResponseCode;
import gov.derate.proof.common.service.BeanFactoryUtils;
import gov.derate.proof.common.service.NoticeService;
import gov.derate.proof.common.utils.BeanCopyUtils;
import gov.derate.proof.dictionary.entity.DictionaryDicTypeConstant;
import gov.derate.proof.dictionary.entity.DictionaryTypeItemConstant;
import gov.derate.proof.item.dto.ItemDto;
import gov.derate.proof.item.dto.ItemMaterialDto;
import gov.derate.proof.item.entity.ItemMaterialDo;
import gov.derate.proof.item.repository.ItemMaterialRepository;
import gov.derate.proof.item.resp.ItemResponseCode;
import gov.derate.proof.item.service.ItemMaterialService;
import gov.derate.proof.item.service.ItemService;
import gov.derate.proof.license.resp.LicenseAttachmentArchivingDataResponse;
import gov.derate.proof.license.resp.LicenseAttachmentArchivingResponse;
import gov.derate.proof.license.sdk.LicenseProofSdk;
import gov.derate.proof.list.bo.ProofListByPageViewBo;
import gov.derate.proof.list.query.ProofListByPageViewQuery;
import gov.derate.proof.list.service.ProofListService;
import gov.licc.func.api.amp.dto.DictDetailDto;
import gov.licc.func.api.amp.dto.DictDto;
import gov.licc.func.api.amp.service.DictPublicService;
import gov.licc.func.api.auth.AccountHelper;
import gov.licc.func.api.auth.dto.CurrentAccountDto;
import gov.licc.func.api.auth.dto.OrganizationDto;
import gov.licc.func.api.auth.service.OrganizationPublicService;
import gov.license.cache.service.CacheService;
import gov.license.common.api.entity.BaseDo;
import gov.license.common.api.exception.ApplicationGlobalRuntimeException;
import gov.license.common.api.resp.BaseResponseCode;
import gov.license.common.api.resp.data.BasePageRespData;
import gov.license.common.api.utils.Assert;
import gov.license.common.tools.bean.BeanUtil;
import gov.license.common.tools.date.DateUtil;
import gov.license.common.tools.jackson.JacksonUtil;
import gov.license.jpa.PredicateBuilder;
import gov.license.jpa.Specifications;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据字段管理service 实现类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-10-21
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:yxh;Date:2021-10-21;
 */
@Service
public class AssistRecordServiceImpl implements AssistRecordService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AssistRecordServiceImpl.class);
    public static final String ASSIST_AUDIT_PLATFORM_PC_FLAG = "pc";
    public static final String ASSIST_AUDIT_PLATFORM_MOBILE_FLAG = "mobile";

    @Autowired
    private AssistRecordRepository assistRecordRepository;

    @Autowired
    private AssistInfoRepository assistInfoRepository;

    @Autowired
    private ItemMaterialRepository itemMaterialRepository;

    @Autowired
    private AssistInfoItemRepository assistInfoItemRepository;
    @Autowired
    private GovEasyManager govEasyManager;

    /**
     * 协查用户配置。
     */
    @Autowired
    private AssistUserRepository assistUserRepository;
    @Autowired
    private ProofListService proofListService;

    @Autowired
    private AssistUserProofRelationViewDoRepository assistUserProofRelationViewDoRepository;

    @Autowired
    private GovEasyJwtUtil govEasyJwtUtil;
    /**
     * 协查人用户仓库组件
     */
    @Autowired
    private AssistUserDoRepository assistUserDoRepository;
    @Autowired
    private AssistRecordProofRelationViewDoRepository assistRecordProofRelationViewDoRepository;
    /**
     * 通知服务
     */
    @Autowired
    private NoticeService noticeService;
    /**
     * 证明目录服务
     */
    @Autowired
    private ProofCatalogService proofCatalogService;

    @Autowired
    private DictPublicService dictPublicService;
    /**
     * 材料服务
     */
    @Autowired
    private ItemMaterialService itemMaterialService;
    /**
     * 事项服务
     */
    @Autowired
    private ItemService itemService;

    /**
     * 电子证明协查记录表 Repository
     */
    @Autowired
    private AssistLicenseProofRecordRepository assistLicenseProofRecordRepository;
    /**
     * 电子证明sdk服务
     */
    @Autowired
    private LicenseProofSdk licenseProofSdk;
    /**
     * 协查审核仓库
     */
    @Autowired
    private AssistAuditRepository assistAuditRepository;
    /**
     * 账号服务
     */
    @Autowired
    private AccountHelper accountHelper;
    /**
     * 缓存服务
     */
    @Autowired
    private CacheService cacheService;
    /**
     * 协查与多层审核视图仓库
     */
    @Autowired
    private AssistRecordAuditRelationViewDoRepository assistRecordAuditRelationViewDoRepository;
    /**
     * 账号信息服务
     */
    @Autowired
    private AccountInfoService accountInfoService;
    /**
     * 部门服务
     */
    @Autowired
    private OrganizationPublicService organizationPublicService;

    @Override
    public List<GetAssistRecordApiDto> queryListApi(AssistRecordApiQuery query) {
        //协查记录
        List<AssistAuditDo> allByAssistCode = assistAuditRepository.findAllByAssistCodeIn(query.getAssistSerialNumberSet());
        if (CollectionUtils.isNotEmpty(allByAssistCode)) {
            Set<String> serialNumberSet = allByAssistCode.stream().map(AssistAuditDo::getAssistCode).collect(Collectors.toSet());
            List<AssistRecordDo> allInAssistSerialNumber = assistRecordRepository.findAllInAssistSerialNumber(serialNumberSet);
            Map<String, AssistRecordDo> serialNumberAndRecDoMap = allInAssistSerialNumber.stream().collect(Collectors.toMap(AssistRecordDo::getAssistSerialNumber, v -> v, (before, after) -> after));
            Set<String> materialIdSet = allByAssistCode.stream().map(AssistAuditDo::getItemMaterialId).collect(Collectors.toSet());
            List<ItemMaterialDto> materialByMaterialIdList = itemService.findMaterialByMaterialIdList(materialIdSet);
            Map<String, ItemMaterialDto> materialIdAndMaterialDtoMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(materialByMaterialIdList)) {
                materialIdAndMaterialDtoMap = materialByMaterialIdList.stream().collect(Collectors.toMap(ItemMaterialDto::getId, v -> v, (before, after) -> after));
            }

            Set<String> proofCatalogCodeSet = allByAssistCode.stream().map(AssistAuditDo::getProofCatalogCode).collect(Collectors.toSet());
            List<ProofCatalogBO> allByCatalogCode = proofCatalogService.findAllByCatalogCode(proofCatalogCodeSet);
            Map<String, String> catalogCodeAndNameMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(allByCatalogCode)) {
                catalogCodeAndNameMap = allByCatalogCode.stream().collect(Collectors.toMap(ProofCatalogDo::getCode, ProofCatalogDo::getName, (before, after) -> after));
            }

            Set<String> toUserAccountSet = allByAssistCode.stream().map(AssistAuditDo::getToUserAccount).collect(Collectors.toSet());
            List<AccountInfoDto> accountInfoByAccount = accountInfoService.getAccountInfoByAccount(toUserAccountSet);
            Map<String, String> accountAndPhoneMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(accountInfoByAccount)) {
                accountAndPhoneMap = accountInfoByAccount.stream().collect(Collectors.toMap(AccountInfoDto::getAccountName, AccountInfoDto::getMobilePhone, (before, after) -> after));
            }

            Map<String, String> finalCatalogCodeAndNameMap = catalogCodeAndNameMap;
            Map<String, ItemMaterialDto> finalMaterialIdAndMaterialDtoMap = materialIdAndMaterialDtoMap;
            Map<String, String> finalAccountAndPhoneMap = accountAndPhoneMap;
            return allByAssistCode.stream().map(item -> {
                GetAssistRecordApiDto dto = new GetAssistRecordApiDto();
                dto.setToUserName(item.getToUserName());
                dto.setToAssistOrgName(item.getAuditOrgName());
                dto.setToAssistCreditCode(item.getAuditOrgCode());
                dto.setAuditResult(item.getAssistAuditResult());
                dto.setAuditTime(item.getAuditTime());
                dto.setAuditSuggestion(item.getAuditSuggestion());
                dto.setAssistSerialNumber(item.getAssistCode());
                Optional<AssistRecordDo> assistRecordDo = Optional.ofNullable(serialNumberAndRecDoMap.get(item.getAssistCode()));
                assistRecordDo.ifPresent(recordDo -> {
                    dto.setBusinessSerialNumber(recordDo.getBusinessSerialNumber());
                });
                Optional<String> phoneOptional = Optional.ofNullable(finalAccountAndPhoneMap.get(item.getToUserAccount()));
                phoneOptional.ifPresent(dto::setToAssistContent);
                Optional<ItemMaterialDto> materialDtoOptional = Optional.ofNullable(finalMaterialIdAndMaterialDtoMap.get(item.getItemMaterialId()));
                materialDtoOptional.ifPresent(materialDto -> {
                    dto.setMaterialName(materialDto.getMaterialName());
                });
                Optional<String> catalogNameOptional = Optional.ofNullable(finalCatalogCodeAndNameMap.get(item.getProofCatalogCode()));
                catalogNameOptional.ifPresent(dto::setProofCatalogName);
                return dto;
            }).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public Page<AssistRecordApiDto> waitAssistPage(WaitAssistRecordApiQuery query, Pageable pageable) {
        // 校验 协查处理页面，如果当前处理人的姓名、证件类型、证件号码与协查人员姓名、证件类型、证件号码不一致，则页面显示“无权限处理该协查”
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null != attrs ? attrs.getRequest() : null;
        String token = null;
        if (request != null) {
            token = request.getHeader(GovEasyJwtUtil.HEADER_AUTHORIZATION);
        }
        if (StringUtils.isEmpty(token)) {
            throw new RuntimeException("header is empty");
        }
//        Boolean isEqGovUser = query.visitor(new AssistApiEqToUserIdAndJwtTokenUserIdVisitor(token, govEasyJwtUtil, query.getToUserId()));
//        if (!isEqGovUser) {
//            throw new GovEasyApiPermissionException(GovEasyManagerResponseCode.ASSIST_RECORD_GOV_EASY_NO_PERMISSION);
//        }

        Specification<AssistRecordDo> spec = query.toSpec();
        Page<AssistRecordDo> resultDo = assistRecordRepository.findAll(spec, pageable);

        Page<AssistRecordApiDto> pageDto = BeanCopyUtils.copyPageItemToVo(resultDo, AssistRecordApiDto.class);
        //下面一串带为了给vo加上材料的名称
        if (Objects.nonNull(pageDto) && CollectionUtils.isNotEmpty(pageDto.getContent())) {
            Set<String> materialIds = resultDo.getContent().stream().map(AssistRecordDo::getMaterialId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            List<ItemMaterialDo> itemMaterialList = itemMaterialRepository.findAllById(materialIds);
            if (CollectionUtils.isNotEmpty(itemMaterialList)) {
                Map<String, String> idAndNameFromMaterial = itemMaterialList.stream().collect(Collectors.toMap(ItemMaterialDo::getId, ItemMaterialDo::getMaterialName, (before, after) -> after));
                pageDto.forEach(vo -> vo.setMaterialName(idAndNameFromMaterial.get(vo.getMaterialId())));
            }
        }

        return pageDto;
    }

    @Override
    public AssistRecordApiBo findByAssistSerialNumber(String assistSerialNumber) {
        AssistRecordApiQuery query = new AssistRecordApiQuery();
        query.setAssistSerialNumberSet(Collections.singletonList(assistSerialNumber));
        List<AssistRecordDo> resDataList = assistRecordRepository.findAll(query.toSpec());
        return CollectionUtils.isNotEmpty(resDataList) ? BeanCopyUtils.copy(resDataList.get(0), AssistRecordApiBo.class) : null;
    }

    @Override
    public void remove(String id) {
        assistRecordRepository.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AssistRecordDo createAssistByApi(AssistRecordSaveApiRequest createVo) {
        if (Objects.isNull(createVo.getHandleAffairsType())) {
            createVo.setHandleAffairsType(AssistHandleAffairsTypeEnum.NATURAL_PERSON);
        }
        validateToAssist(createVo);
        AssistUserProofRelationViewDo govEasyConfigUser = getAssistUserByOrgCreditCodeAndItemCodeAndItemNameAndMaterialId(createVo.getToAssistCreditCode(), createVo.getItemCode(), createVo.getItemName(), createVo.getMaterialId());
        // 加入法人或者自然人，对 setHandleAffairsName  setHandleAffairsIdentityNumber 的设值和判断校验
        validateHandleAffairsTypeData(createVo);
        String assistSerialNumber = generatorAssistSerialNumber();
        AssistRecordSaveRequest createAssistReq = BeanCopyUtils.copy(createVo, AssistRecordSaveRequest.class);
        createAssistReq.setHandleAffairsAssistIdentityType(createVo.getHandleAffairsAssistIdentityType());
        createAssistReq.setToAssistOrgName(createVo.getToAssistOrgName());
        createAssistReq.setToAssistCreditCode(createVo.getToAssistCreditCode());
        createAssistReq.setFromAssistOrgName(createVo.getFromAssistOrgName());
        createAssistReq.setFromAssistCreditCode(createVo.getFromAssistCreditCode());
        createAssistReq.setBusinessSerialNumber(createVo.getBusinessSerialNumber());
        createAssistReq.setFromAssistUserName(createVo.getFromAssistUserName());
        createAssistReq.setFromAssistContain(createVo.getFromAssistContain());
        createAssistReq.setItemCode(createVo.getItemCode());
        createAssistReq.setItemName(createVo.getItemName());
        createAssistReq.setMaterialId(createVo.getMaterialId());
        createAssistReq.setAssistTime(createVo.getAssistTime());
        createAssistReq.setAssistSerialNumber(assistSerialNumber);
        createAssistReq.setHandleAffairsType(createVo.getHandleAffairsType());
        createAssistReq.setFromDemand(createVo.getFromDemand());
        createAssistReq.setAssistTime(new Date());
        switch (createVo.getHandleAffairsType()) {
            case LEGAL_PERSON:
                createAssistReq.setHandleAffairsName(createVo.getAssistHandleOrgName());
                createAssistReq.setHandleAffairsIdentityNumber(createVo.getAssistHandleCreditCode());
                // createAssistReq.setLegalPersonIdentityType(); 目前没有途径可以设值
                createAssistReq.setLegalPersonIdentityType(LegalPersonIdentityType.CREDIT_CODE);
                break;
            case NATURAL_PERSON:
                createAssistReq.setHandleAffairsName(createVo.getHandleAffairsName());
                createAssistReq.setHandleAffairsIdentityNumber(createVo.getHandleAffairsIdentityNumber());
                break;
            default:
                break;
        }
        String operator = "zongShou";
        String creatorId = StringUtils.isBlank(govEasyConfigUser.getUserId()) ? operator : govEasyConfigUser.getUserId();
        createAssistReq.setCreatorId(creatorId);
        createAssistReq.setLastModificatorId(creatorId);
        createAssistReq.setHistoryAssistSerialNumber(createVo.getHistoryAssistSerialNumber());
        createAssistReq.setProofCatalogId(createVo.getProofCatalogId());
        ProofCatalogDetailDto proofCatalog = proofCatalogService.findDetailById(createVo.getProofCatalogId());
        createAssistReq.setProofCatalogName(Objects.nonNull(proofCatalog) ? proofCatalog.getName() : null);
        AssistRecordDto recordData = createAssist(createAssistReq);
        String recordDataId = recordData.getId();
        //遍历处理协查内容
        if (CollectionUtils.isNotEmpty(createVo.getAssistDataList())) {
            //协查内容
            List<AssistInfoDo> infoDataList = new ArrayList<>(createVo.getAssistDataList().size());
            //协查内容项
            List<AssistInfoItemDo> infoItemList = new ArrayList<>();
            createVo.getAssistDataList().forEach(infoVo -> {
                AssistInfoDo infoData = new AssistInfoDo();
                String infoId = UUID.randomUUID().toString();
                infoData.setId(infoId);
                BeanCopyUtils.copy(infoVo, infoData);
                infoData.setAssistRecordId(recordDataId);
                infoData.setCreatorId(creatorId);
                infoData.setLastModificatorId(creatorId);
                //遍历处理协查内容项
                if (CollectionUtils.isNotEmpty(infoVo.getItemList())) {
                    List<AssistInfoItemSaveApiRequest> infoVoItemList = infoVo.getItemList();
                    for (int i = 0; i < infoVoItemList.size(); i++) {
                        AssistInfoItemSaveApiRequest infoItemVo = infoVoItemList.get(i);
                        AssistInfoItemDo infoItemData = new AssistInfoItemDo();
                        BeanCopyUtils.copy(infoItemVo, infoItemData);
                        infoItemData.setAssistInfoId(infoId);
                        infoItemData.setAssistRecordId(recordDataId);
                        infoItemData.setCreatorId(creatorId);
                        infoItemData.setLastModificatorId(creatorId);
                        infoItemData.setSortNum(i);
                        infoItemList.add(infoItemData);
                    }
                }
                infoDataList.add(infoData);
            });
            //批量保存
            if (CollectionUtils.isNotEmpty(infoDataList)) {
                assistInfoRepository.saveAll(infoDataList);
            }
            //批量保存
            if (CollectionUtils.isNotEmpty(infoItemList)) {
                assistInfoItemRepository.saveAll(infoItemList);
            }
        }
        return BeanCopyUtils.copy(recordData, AssistRecordDo.class);
    }

    /**
     * 发送协查单，通知逻辑
     *
     * @param recordData 协查单对象
     */
    @Override
    public void sendAssistNeedAuditMsg(AssistRecordDto recordData) {
        try {
            List<AssistAuditDo> allByAssistCode = assistAuditRepository.findAllByAssistCode(recordData.getAssistSerialNumber());
            List<String> needAuditOrgCodeList = allByAssistCode.stream().filter(AssistAuditDo::getNeedAudit).filter(item -> item.getAssistAuditResult() == AssistResultEnum.WAIT).map(AssistAuditDo::getAuditOrgCode).distinct().collect(Collectors.toList());
            sendMsgByAssistCodeAndCreditCodeList(recordData.getAssistSerialNumber(), needAuditOrgCodeList);
        } catch (Exception e) {
            LOGGER.warn("noticeService.sendNoticeMsg error skip notice", e);
        }
    }

    @Override
    public void sendMsgByAssistCodeAndCreditCodeList(String serialNumber, Collection<String> needAuditOrgCodeList) {
        if (CollectionUtils.isEmpty(needAuditOrgCodeList)) {
            return;
        }
        try {
            AccountInfoPageQuery request = new AccountInfoPageQuery();
            request.setPageNum("0");
            request.setPageSize("10");
            request.setSort(Sort.by("id"));
            request.setCreditCodeSearchList(needAuditOrgCodeList);
            BasePageRespData<List<AccountInfoDto>> respPage = accountInfoService.queryNotFilterPermissionRangePage(request);
            LOGGER.debug("sendAssistNeedAuditMsg accountInfoService.queryPage [{}]", respPage);
            while (Objects.nonNull(respPage) && CollectionUtils.isNotEmpty(respPage.getContent())) {
                respPage.getContent().stream().map(AccountInfoDto::getGovEasyId).filter(StringUtils::isNotBlank).distinct().forEach(govEasyId -> {
                    try {
                        LOGGER.debug("noticeService.sendNoticeMsg govEasyId [{}]", govEasyId);
                        noticeService.sendNoticeMsg(serialNumber, govEasyId);
                    } catch (Exception e) {
                        LOGGER.warn("noticeService.sendNoticeMsg error skip notice ,error govEasyUserId [{}]", govEasyId, e);
                    }
                });
                request.setPageNum(String.valueOf(Integer.parseInt(respPage.getPageNum()) + 1));
                respPage = accountInfoService.queryPage(request);

            }
        } catch (Exception e) {
            LOGGER.warn("noticeService.sendNoticeMsg error skip notice", e);
        }
    }

    /**
     * 校验办事人类型相关
     *
     * @param createVo request对象
     */
    private void validateHandleAffairsTypeData(AssistRecordSaveApiRequest createVo) {
        if (createVo.getHandleAffairsType() == AssistHandleAffairsTypeEnum.NATURAL_PERSON) {
            if (StringUtils.isBlank(createVo.getHandleAffairsName()) || Objects.isNull(createVo.getHandleAffairsAssistIdentityType()) || StringUtils.isBlank(createVo.getHandleAffairsIdentityNumber())) {
                throw new AssistRecordServiceException(ResponseCode.PARAMETER_VALIDATE_ERROR);
            }
        }
        if (createVo.getHandleAffairsType() == AssistHandleAffairsTypeEnum.LEGAL_PERSON) {
            if (StringUtils.isBlank(createVo.getAssistHandleCreditCode()) || StringUtils.isBlank(createVo.getAssistHandleOrgName())) {
                throw new AssistRecordServiceException(ResponseCode.PARAMETER_VALIDATE_ERROR);
            }
        }
    }

    /**
     * 根据参数，获取唯一的协查用户
     *
     * @param creditCode creditCode
     * @param itemCode   itemCode
     * @param itemName   itemName
     * @param materialId materialId
     * @return 协查用户
     */
    public AssistUserProofRelationViewDo getAssistUserByOrgCreditCodeAndItemCodeAndItemNameAndMaterialId(String creditCode, String itemCode, String itemName, String materialId) {
        ProofListByPageViewQuery proofListByPageViewQuery = new ProofListByPageViewQuery();
        proofListByPageViewQuery.setItemCode(itemCode);
        proofListByPageViewQuery.setMaterialId(materialId);
        proofListByPageViewQuery.setItemName(itemName);
        proofListByPageViewQuery.setItemProofStatus(ItemProofStatusEnum.APPROVED);
        List<ProofListByPageViewBo> proofListByPageViewBos = proofListService.queryProofList(proofListByPageViewQuery);
        // 校验协查的材料是否存在。
        if (CollectionUtils.isEmpty(proofListByPageViewBos)) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_SERVICE_CAN_NOT_FIND_PROOF_LIST_ERROR);
        }
        ProofListByPageViewBo proofListByPageViewBo = proofListByPageViewBos.get(0);
        String catalogCode = proofListByPageViewBo.getProofCatalogCode();
        List<AssistUserProofRelationViewDo> assistUserViewList = assistUserProofRelationViewDoRepository.findAllByOrgCreditCodeAndProofCodeLike(creditCode, "%" + catalogCode + "%");
        if (CollectionUtils.isEmpty(assistUserViewList)) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_SERVICE_CAN_NOT_FIND_ASSIST_ERROR);
        }
        return assistUserViewList.get(0);
    }


    /**
     * 校验协查逻辑
     * 校验事项和材料id，是否审核通过，并且是否有绑定当前配置协查用户的证明目录。
     * 校验协查人员，是否在配置文件中。
     *
     * @param createVo 请求对象
     */
    private void validateToAssist(AssistRecordSaveApiRequest createVo) {
        if (Objects.nonNull(createVo.getHandleAffairsType()) && AssistHandleAffairsTypeEnum.NATURAL_PERSON.equals(createVo.getHandleAffairsType()) && StringUtils.isBlank(createVo.getHandleAffairsName())) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_API_PARAM_VALID_ERROR.format("办事人姓名必填"));
        }
        if (Objects.nonNull(createVo.getHandleAffairsType()) && AssistHandleAffairsTypeEnum.NATURAL_PERSON.equals(createVo.getHandleAffairsType()) && Objects.isNull(createVo.getHandleAffairsAssistIdentityType())) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_API_PARAM_VALID_ERROR.format("办事人证件类型必填"));
        }
        if (Objects.nonNull(createVo.getHandleAffairsType()) && AssistHandleAffairsTypeEnum.NATURAL_PERSON.equals(createVo.getHandleAffairsType()) && StringUtils.isBlank(createVo.getHandleAffairsIdentityNumber())) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_API_PARAM_VALID_ERROR.format("办事人证件号码必填"));
        }
        if (Objects.nonNull(createVo.getHandleAffairsType()) && AssistHandleAffairsTypeEnum.LEGAL_PERSON.equals(createVo.getHandleAffairsType()) && StringUtils.isBlank(createVo.getAssistHandleOrgName())) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_API_PARAM_VALID_ERROR.format("办事单位名称必填"));
        }
        if (Objects.nonNull(createVo.getHandleAffairsType()) && AssistHandleAffairsTypeEnum.LEGAL_PERSON.equals(createVo.getHandleAffairsType()) && StringUtils.isBlank(createVo.getAssistHandleCreditCode())) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_API_PARAM_VALID_ERROR.format("办事单位统一社会信用代码必填"));
        }
        // 根据协查部门与材料，查询id，事项编码，事项名称，定位是否有配置人员和配置部门。
        ProofListByPageViewQuery proofListByPageViewQuery = new ProofListByPageViewQuery();
        proofListByPageViewQuery.setItemCode(createVo.getItemCode());
        proofListByPageViewQuery.setMaterialId(createVo.getMaterialId());
        proofListByPageViewQuery.setItemName(createVo.getItemName());
        proofListByPageViewQuery.setItemProofStatus(ItemProofStatusEnum.APPROVED);
        List<ProofListByPageViewBo> proofListByPageViewBos = proofListService.queryProofList(proofListByPageViewQuery);
        ProofListByPageViewBo proofListByPageViewBo = null;
        // 校验协查的材料是否存在。
        if (CollectionUtils.isEmpty(proofListByPageViewBos)) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_SERVICE_CAN_NOT_FIND_PROOF_LIST_ERROR);
        } else {
            proofListByPageViewBo = proofListByPageViewBos.get(0);
        }
        String catalogCode = proofListByPageViewBo.getProofCatalogCode();
        List<AssistUserProofRelationViewDo> assistUserViewList = assistUserProofRelationViewDoRepository.findAllByOrgCreditCodeAndProofCodeLike(createVo.getToAssistCreditCode(), "%" + catalogCode + "%");

        // 校验配置协查部门人员是否存在关联关系
        Assert.isTrue(CollectionUtils.isNotEmpty(assistUserViewList), new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_SERVICE_CAN_NOT_FIND_ASSIST_ERROR));

        AssistUserProofRelationViewDo assistUserDo = assistUserViewList.get(0);
        Assert.isTrue(StringUtils.equals(assistUserDo.getOrgCreditCode(), createVo.getToAssistCreditCode()), new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_SERVICE_ASSIST_USER_NOT_EQ_CREDIT_CODE_ERROR));

        // 发送短信方式，默认为空，不发消息
        Optional<String> sendNoticeWayConfigOptional = dictPublicService.getDetailValue(DictionaryDicTypeConstant.FUNC_CONFIG.name(), DictionaryTypeItemConstant.ASSIST_NOTICE_WAY.name());
        String sendNoticeWay = sendNoticeWayConfigOptional.orElse("");

        if (StringUtils.isNotBlank(assistUserDo.getUserId()) && StringUtils.isNotBlank(sendNoticeWay) && StringUtils.equals(sendNoticeWay, NoticeService.SEND_GOV_EASY_WAY)) {
            // 没有配置粤证易id，则不校验粤证易id
            GovEasyUserBo userBo = govEasyManager.getUserInfoByUserId(assistUserDo.getUserId());
            Assert.notNull(userBo, new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_SERVICE_GOV_EASY_FIND_USER_ID_ERROR));

            if (!StringUtils.equals(userBo.getDisplayName(), assistUserDo.getAssistorName()) && !StringUtils.equals(userBo.getCertificateNumber(), assistUserDo.getAssistorIdentityNumber())) {
                // 粤证易审核用户名称!=配置协查人名称,并且身份证!=配置的身份证号码，抛出错误
                LOGGER.error("gov easy user info config error, govUserInfo [{}], configUserInfo [{}]", userBo, assistUserDo);
                throw new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_SERVICE_CAN_NOT_FIND_GOV_EASY_ERROR);
            }
        }
        //协查记录信息检查
        if (CollectionUtils.isNotEmpty(createVo.getAssistDataList())) {
            createVo.getAssistDataList().forEach(assistData -> {
                //遍历协查项内容
                if (CollectionUtils.isNotEmpty(assistData.getItemList())) {
                    //检查项KEY、COLS内容
                    assistData.getItemList().forEach(item -> {
                        if (StringUtils.isEmpty(item.getKey())) {
                            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_SERVICE_ITEM_DATA_KEY_EMPTY_ERROR);
                        } else if (Objects.isNull(item.getCols())) {
                            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_SERVICE_ITEM_DATA_COLS_EMPTY_ERROR);
                        }
                    });
                }
            });
        }
    }

    /**
     * 生成时间戳+随机号生成
     *
     * @return 号码
     */
    @Override
    public String generatorAssistSerialNumber() {
        long timestrap = System.currentTimeMillis();
        String substring = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 5);
        return timestrap + substring;
    }

    @Override
    public AssistRecordDto viewBySerialNumber(String serialNumber) {
        Specification<AssistRecordProofRelationViewDo> spec = Specifications.<AssistRecordProofRelationViewDo>and().eq("assistSerialNumber", serialNumber).build();
        List<AssistRecordProofRelationViewDo> all = assistRecordProofRelationViewDoRepository.findAll(spec);
        if (CollectionUtils.isEmpty(all)) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_QUERY_NULL_ERROR);
        }
        AssistRecordProofRelationViewDo assistRecordDo = all.get(0);
        if (Objects.isNull(assistRecordDo)) {
            return null;
        }
        AssistRecordDto responseBo = BeanCopyUtils.copy(assistRecordDo, AssistRecordDto.class);
        if (Objects.nonNull(responseBo)) {
            ArrayList<String> recordIds = Lists.newArrayList(responseBo.getId());
            List<AssistInfoDo> infoList = getAssistInfoDos(recordIds);
            List<AssistInfoItemDo> infoItemList = getAssistInfoItemDos(recordIds);
            List<AssistInfoBo> assistInfoBos = BeanCopyUtils.copyList(infoList, AssistInfoBo.class);
            if (CollectionUtils.isNotEmpty(assistInfoBos) && CollectionUtils.isNotEmpty(infoItemList)) {
                Map<String, ArrayList<AssistInfoItemDo>> infoIdAndInfoItemListMap = infoItemList.stream().collect(Collectors.toMap(AssistInfoItemDo::getAssistInfoId, Lists::newArrayList, (before, after) -> {
                    before.addAll(after);
                    return before;
                }));
                for (AssistInfoBo item : assistInfoBos) {
                    ArrayList<AssistInfoItemDo> assistInfoItemDos = infoIdAndInfoItemListMap.get(item.getId());
                    List<AssistInfoItemBo> assistInfoItemBos = BeanCopyUtils.copyList(assistInfoItemDos, AssistInfoItemBo.class);
                    item.setItemList(assistInfoItemBos);
                }
            }
            responseBo.setAssistDataList(assistInfoBos);
        }
        return responseBo;
    }

    @Override
    public Long statExemptInitiate() {
        return assistRecordProofRelationViewDoRepository.countRecord();
    }

    @Override
    public Long statExemptFinishCount() {
        return assistRecordProofRelationViewDoRepository.countRecord(Lists.newArrayList(AssistResultEnum.FAIL, AssistResultEnum.SUCCESS));
    }

    @Override
    public String createLicenseDerateAssistSign(LicenseDerateAssistSignApiDto createDto) {
        createDto.valid();
        String serialNumber = generatorAssistSerialNumber();
        ItemDto byItemCode = itemService.findByItemCode(createDto.getItemCode());
        Assert.notNull(byItemCode, ItemResponseCode.ITEM_QUERY_NOT_EXISTS_ERROR);
        Assert.isTrue(byItemCode.getItemName().equals(createDto.getItemName()), ItemResponseCode.ITEM_QUERY_NOT_EXISTS_ERROR);
        ItemMaterialDo materialDo = itemMaterialService.findById(createDto.getMaterialId());
        Assert.notNull(materialDo, ItemResponseCode.ITEM_MATERIAL_NOT_EXISTS_ERROR);
        Assert.isTrue(materialDo.getMaterialName().equals(createDto.getMaterialName()), ItemResponseCode.ITEM_MATERIAL_NOT_EXISTS_ERROR);
        ProofCatalogDetailDto detailById = proofCatalogService.findDetailById(createDto.getProofCatalogId());
        Assert.notNull(detailById, CatalogResponseCode.CATALOG_NOT_EXISTS_ERROR);
        List<ProofCatalogLicenseItemRelationDto> licenseItemRelationBo = detailById.getProofCatalogLicenseItemRelation();
        Assert.notNull(licenseItemRelationBo, CatalogResponseCode.CATALOG_LICENSE_ITEM_NOT_EXISTS_ERROR);
        Assert.notEmpty(licenseItemRelationBo, CatalogResponseCode.CATALOG_LICENSE_ITEM_NOT_EXISTS_ERROR);
        // 校验实施码配置，是否在里面
        Optional<ProofCatalogLicenseItemImplItemInfoDto> itemInfoDtoOptional = licenseItemRelationBo.stream().filter(Objects::nonNull).filter(item -> StringUtils.isNotBlank(item.getImplementItemInfoJson())).map(ProofCatalogLicenseItemRelationDto::buildByImplItemInfoJson).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).filter(item -> item.getImplementCode().equals(createDto.getImplementCode())).findFirst();
        Assert.isTrue(itemInfoDtoOptional.isPresent(), CatalogResponseCode.CATALOG_LICENSE_ITEM_IMPL_CODE_NOT_EXISTS_ERROR);
        ProofCatalogLicenseItemImplItemInfoDto proofCatalogLicenseItemImplItemInfoDto = itemInfoDtoOptional.get();
        Optional<ProofCatalogLicenseItemRelationDto> licenseItemDtoOptional = licenseItemRelationBo.stream().filter(item -> item.getLicenseCode().equals(proofCatalogLicenseItemImplItemInfoDto.getBasicCode())).findFirst();
        Assert.isTrue(licenseItemDtoOptional.isPresent(), CatalogResponseCode.CATALOG_LICENSE_ITEM_IMPL_CODE_NOT_EXISTS_ERROR);
        Date now = new Date();
        AssistLicenseProofRecordDo assistLicenseProofRecordDo = new AssistLicenseProofRecordDo();
        assistLicenseProofRecordDo.setHandleAffairsName(createDto.getHandleAffairsName());
        assistLicenseProofRecordDo.setHandleAffairsAssistIdentityType(createDto.getHandleAffairsAssistIdentityType());
        assistLicenseProofRecordDo.setHandleAffairsIdentityNumber(createDto.getHandleAffairsIdentityNumber());
        assistLicenseProofRecordDo.setToAssistOrgName(createDto.getToAssistOrgName());
        assistLicenseProofRecordDo.setToAssistCreditCode(createDto.getToAssistCreditCode());
        assistLicenseProofRecordDo.setFromAssistOrgName(createDto.getFromAssistOrgName());
        assistLicenseProofRecordDo.setFromAssistCreditCode(createDto.getFromAssistCreditCode());
        assistLicenseProofRecordDo.setFromAssistUserName(createDto.getFromAssistUserName());
        assistLicenseProofRecordDo.setAssistTime(now);
        assistLicenseProofRecordDo.setCallBack(false);
        assistLicenseProofRecordDo.setItemCode(createDto.getItemCode());
        assistLicenseProofRecordDo.setItemName(createDto.getItemName());
        assistLicenseProofRecordDo.setMaterialId(createDto.getMaterialId());
        assistLicenseProofRecordDo.setHandleAffairsType(createDto.getHandleAffairsType());
        assistLicenseProofRecordDo.setFromDemand(createDto.getFromDemand());
        assistLicenseProofRecordDo.setLegalPersonIdentityType(createDto.getLegalPersonIdentityType());
        assistLicenseProofRecordDo.setProofCatalogId(createDto.getProofCatalogId());
        assistLicenseProofRecordDo.setProofCatalogName(detailById.getName());
        assistLicenseProofRecordDo.setSerialNumber(serialNumber);
        assistLicenseProofRecordDo.setProofResult(AssistLicenseProofResultEnum.not_sign);
        assistLicenseProofRecordDo.setMaterialName(createDto.getMaterialName());
        assistLicenseProofRecordDo.setAuditResult(AssistResultEnum.WAIT);
        assistLicenseProofRecordDo.setImplementCode(createDto.getImplementCode());
        assistLicenseProofRecordDo.setFromAssistContain(createDto.getFromAssistContain());
        assistLicenseProofRecordDo.setBizOrgName(createDto.getBizOrgName());
        assistLicenseProofRecordDo.setBizOrgCreditCode(createDto.getBizOrgCreditCode());
        licenseItemDtoOptional.ifPresent(item -> assistLicenseProofRecordDo.setLicenseName(item.getLicenseName()));
        assistLicenseProofRecordDo.setBusinessSerialNumber(createDto.getBusinessSerialNumber());
        assistLicenseProofRecordRepository.save(assistLicenseProofRecordDo);
        return serialNumber;
    }

    @Override
    public Optional<GetAssistLicenseProofRecordDto> getLicenseDerateAssistBySerialNumber(String serialNumber) {
        Optional<AssistLicenseProofRecordDo> bySerialNumber = assistLicenseProofRecordRepository.findBySerialNumber(serialNumber);
        AssistLicenseProofRecordDo item = bySerialNumber.orElseThrow(() -> new AssistRecordServiceException(AssistResponseCode.ASSIST_LICENSE_DERATE_RECORD_NOT_EXISTS_ERROR));
        GetAssistLicenseProofRecordDto result = new GetAssistLicenseProofRecordDto();
        result.setSerialNumber(item.getSerialNumber());
        result.setProofResult(item.getProofResult());
        if (AssistLicenseProofResultEnum.signed.equals(item.getProofResult())) {
            result.setToUserName(item.getToUserName());
            result.setToAssistContain(item.getToAssistContain());
            result.setToAssistCreditCode(item.getToAssistCreditCode());
            result.setToAssistOrgName(item.getToAssistOrgName());
            try {
                LicenseAttachmentArchivingResponse licenseAttachmentArchivingResponse = licenseProofSdk.licenseAttachmentArchiving(item.getCreateAuthCode());
                if (licenseAttachmentArchivingResponse.getIsSuccessStatus()) {
                    result.setLicenseItemPdf(licenseAttachmentArchivingResponse.getData().getFileData());
                }
            } catch (Exception e) {
                LOGGER.warn("getLicenseDerateAssistBySerialNumber licenseProofSdk.licenseAttachmentArchiving  [{}] 记录异常，获取归档文件异常", item, e);
                throw new AssistRecordServiceException(AssistResponseCode.ASSIST_LICENSE_DERATE_RECORD_ATTACHMENT_ERROR);
            }
            result.setAuditTime(item.getAuditTime());
        }
        result.setFromDemand(item.getFromDemand());
        result.setFromAssistCreditCode(item.getFromAssistCreditCode());
        result.setFromAssistOrgName(item.getFromAssistOrgName());
        result.setFromAssistUserName(item.getFromAssistUserName());
        result.setItemCode(item.getItemCode());
        result.setItemName(item.getItemName());
        result.setMaterialName(item.getMaterialName());
        result.setMaterialId(item.getMaterialId());
        return Optional.of(result);
    }

    @Override
    public Page<AssistLicenseProofRecordDto> queryLicenseProofArchivesPage(AssistLicenseProofRecordPageQuery query) {
        PredicateBuilder<AssistLicenseProofRecordDo> and = Specifications.<AssistLicenseProofRecordDo>and().eq(StringUtils.isNotBlank(query.getSerialNumber()), "serialNumber", query.getSerialNumber()).eq(StringUtils.isNotBlank(query.getBusinessSerialNumber()), "businessSerialNumber", query.getBusinessSerialNumber()).like(StringUtils.isNotBlank(query.getLicenseName()), "licenseName", String.format("%s%s%s", "%", query.getLicenseName(), "%")).in(CollectionUtils.isNotEmpty(query.getProofResultList()), "proofResult", query.getProofResultList());
        if (CollectionUtils.isNotEmpty(query.getOrgToAssistCreditCode())) {
            PredicateBuilder<AssistLicenseProofRecordDo> or = Specifications.or();
            List<List<String>> partition = Lists.partition(query.getOrgToAssistCreditCode(), 1000);
            partition.forEach(item -> or.in("toAssistCreditCode", item));
            and.predicate(or.build());
        }
        Page<AssistLicenseProofRecordDo> all = assistLicenseProofRecordRepository.findAll(and.build(), query.buildPageable());
        return BeanCopyUtils.copyPageItemToVo(all, AssistLicenseProofRecordDto.class);
    }

    @Override
    public Optional<AssistLicenseProofRecordDto> queryLicenseProofArchivesView(String serialNumber) {
        Optional<AssistLicenseProofRecordDo> bySerialNumber = assistLicenseProofRecordRepository.findBySerialNumber(serialNumber);
        return bySerialNumber.map(assistLicenseProofRecordDo -> BeanCopyUtils.copy(assistLicenseProofRecordDo, AssistLicenseProofRecordDto.class));
    }

    @Override
    public UploadFileAttachmentBo downloadLicenseProofArchives(String serialNumber) throws Exception {
        Optional<AssistLicenseProofRecordDo> bySerialNumber = assistLicenseProofRecordRepository.findBySerialNumber(serialNumber);
        AssistLicenseProofRecordDo assistLicenseProofRecordDo = bySerialNumber.orElseThrow(() -> new AssistRecordServiceException(ResponseCode.BIZ_ERROR));
        // 1. 获取是否有auth_code
        Assert.isTrue(AssistLicenseProofResultEnum.signed.equals(assistLicenseProofRecordDo.getProofResult()), ResponseCode.SERVICE_BIZ_REQUEST_ERROR.format("未开具"));
        String createAuthCode = assistLicenseProofRecordDo.getCreateAuthCode();
        Assert.hasText(createAuthCode, ResponseCode.SERVICE_BIZ_REQUEST_ERROR.format("未开具，未有authCode"));
        // 2. 有auth_code 调用电子证明接口，获取签名附件
        LicenseAttachmentArchivingResponse licenseAttachmentArchivingResponse = licenseProofSdk.licenseAttachmentArchiving(createAuthCode);
        Assert.notNull(licenseAttachmentArchivingResponse, BaseResponseCode.SERVICE_ERROR.format("归档文件为空"));
        Assert.isTrue(licenseAttachmentArchivingResponse.getIsSuccessStatus(), BaseResponseCode.SERVICE_ERROR.format("归档文件失败"));
        // 3. 转换文件对象，返回
        UploadFileAttachmentBo uploadFileAttachmentBo = new UploadFileAttachmentBo();
        uploadFileAttachmentBo.setFileDataBase64(licenseAttachmentArchivingResponse.getData().getFileData());
        uploadFileAttachmentBo.setFileName(licenseAttachmentArchivingResponse.getData().getFileName());
        return uploadFileAttachmentBo;
    }

    @Override
    public List<AssistInfoItemBo> getAssistInfoItemByAssistRecordId(String serialNumber) {
        AssistRecordDo byAssistSerialNumber = assistRecordRepository.findByAssistSerialNumber(serialNumber);
        Assert.notNull(byAssistSerialNumber, AssistResponseCode.ASSIST_RECORD_NULL_ERROR);
        String id = byAssistSerialNumber.getId();
        return BeanCopyUtils.copyList(assistInfoItemRepository.findAllByAssistRecordId(id), AssistInfoItemBo.class);
    }

    @Override
    public UploadFileAttachmentBo downloadLicenseItemAttachment(String assistSerialNumber) {
        Optional<AssistRecordDo> byId = Optional.ofNullable(assistRecordRepository.findByAssistSerialNumber(assistSerialNumber));
        Assert.isTrue(byId.isPresent(), AssistResponseCode.ASSIST_RECORD_CREATE_BIZ_ERROR.format("协查单不存在"));
        AssistRecordDo assistRecordDo = byId.get();
        String authCode = assistRecordDo.getLicenseItemAuthCode();
        Assert.hasText(authCode, AssistResponseCode.ASSIST_RECORD_CREATE_BIZ_ERROR.format("电子证明归档authCode不存在，无法下载归档"));
        LicenseAttachmentArchivingResponse licenseGetLicenseResponse = null;
        try {
            licenseGetLicenseResponse = licenseProofSdk.licenseAttachmentArchiving(authCode);
        } catch (Exception e) {
            LOGGER.error("downloadLicenseItemAttachment error", e);
            throw new AssistRecordServiceException(AssistResponseCode.SERVICE_ASSIST_ERROR.format("电子证明归档失败"));
        }
        if (licenseGetLicenseResponse.getIsSuccessStatus()) {
            LicenseAttachmentArchivingDataResponse data = licenseGetLicenseResponse.getData();
            UploadFileAttachmentBo fileAttachmentBo = new UploadFileAttachmentBo();
            fileAttachmentBo.setFileName(data.getFileName());
            fileAttachmentBo.setFileDataBase64(data.getFileData());
            return fileAttachmentBo;
        }
        throw new AssistRecordServiceException(AssistResponseCode.SERVICE_ASSIST_ERROR.format("电子证明归档失败"));
    }


    @Override
    public AssistRecordDetailPageBo assistDetailGovEasyApi(AssistRecordApiQuery query) {
        return this.assistDetailApi(query);
    }


    /**
     * 构造协查记录Detail的处理
     *
     * @param apiBoList apiBoList
     */
    private void setDetailItemPageBoApi(List<AssistRecordDetailPageBo> apiBoList) {
        List<String> recordIds = apiBoList.stream().map(AssistRecordDetailPageBo::getId).collect(Collectors.toList());
        List<AssistInfoDo> infoList = getAssistInfoDos(recordIds);
        List<AssistInfoItemDo> infoItemList = getAssistInfoItemDos(recordIds);
        List<String> serialNumberList = apiBoList.stream().filter(Objects::nonNull).map(AssistRecordDetailPageBo::getAssistSerialNumber).distinct().collect(Collectors.toList());
        List<AssistAuditDo> allByAssistCodeIn = assistAuditRepository.findAllByAssistCodeIn(serialNumberList);
        //遍历设置协查记录
        for (AssistRecordDetailPageBo record : apiBoList) {
            if (StringUtils.isNoneBlank(record.getToUserId())) {
                AssistUserDo assistUserDo = assistUserRepository.findById(record.getToUserId()).orElse(null);
                if (Objects.nonNull(assistUserDo)) {
                    record.setToUserName(assistUserDo.getAssistorName());
                }
            }
            //根据协查记录过滤协查信息
            List<AssistInfoDo> recordInfoList = infoList.stream().filter(f -> f.getAssistRecordId().equals(record.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(recordInfoList)) {
                //协查信息BO
                List<AssistInfoBo> infoBoList = BeanCopyUtils.copyList(recordInfoList, AssistInfoBo.class);
                if (CollectionUtils.isEmpty(infoBoList)) {
                    continue;
                }
                //遍历设置协查信息
                for (AssistInfoBo info : infoBoList) {
                    //根据协查信息过滤协查项
                    List<AssistInfoItemDo> recordInfoItemList = infoItemList.stream().filter(f -> f.getAssistInfoId().equals(info.getId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(recordInfoItemList)) {
                        //协查信息BO
                        List<AssistInfoItemBo> infoItemBoList = BeanCopyUtils.copyList(recordInfoItemList, AssistInfoItemBo.class);
                        if (CollectionUtils.isNotEmpty(infoItemBoList)) {
                            Map<String, AssistInfoItemDo> doMap = recordInfoItemList.stream().collect(Collectors.toMap(BaseDo::getId, v -> v, (before, after) -> after));
                            infoItemBoList.forEach(item -> {
                                Optional<AssistInfoItemDo> assistInfoItemDo = Optional.ofNullable(doMap.get(item.getId()));
                                assistInfoItemDo.ifPresent(doItem -> item.setCols(String.valueOf(doItem.getCols())));
                            });
                        }
                        info.setItemList(infoItemBoList);
                    }

                }
                record.setAssistDataList(infoBoList);
            }
            if (CollectionUtils.isNotEmpty(allByAssistCodeIn)) {
                List<AssistAuditDo> assistAuditDoList = allByAssistCodeIn.stream().filter(item -> item.getAssistCode().equals(record.getAssistSerialNumber())).collect(Collectors.toList());
                AssistAuditContextBo assistAuditContextBo = AssistAuditContextBo.buildContext(BeanCopyUtils.copy(record, AssistRecordDto.class), BeanCopyUtils.copyList(assistAuditDoList, AssistAuditDto.class), ASSIST_AUDIT_PLATFORM_PC_FLAG, null, false);
                record.setAuditRelationList(assistAuditContextBo.getAuditStatusList());
            }
        }

    }

    /**
     * 根据id，协查记录，查询info
     *
     * @param recordIds 协查id
     * @return info
     */
    private List<AssistInfoItemDo> getAssistInfoItemDos(List<String> recordIds) {
        //协查信息项
        AssistInfoItemQuery infoItemQuery = new AssistInfoItemQuery();
        infoItemQuery.setAssistRecordList(recordIds);
        //协查信息项列表
        List<AssistInfoItemDo> assistInfoItemDos = assistInfoItemRepository.findAll(infoItemQuery.toSpec());
        return assistInfoItemDos.stream().sorted(Comparator.comparing(AssistInfoItemDo::getSortNum, Comparator.naturalOrder())).collect(Collectors.toList());
    }

    /**
     * 根据id，协查记录，查询infoItem
     *
     * @param recordIds 协查id
     * @return infoItem
     */
    private List<AssistInfoDo> getAssistInfoDos(List<String> recordIds) {
        //协查信息
        AssistInfoQuery infoQuery = new AssistInfoQuery();
        infoQuery.setAssistRecordList(recordIds);
        //协查信息列表
        return assistInfoRepository.findAll(infoQuery.toSpec());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String assistAuditByGovEasyApi(AssistRecordHandleGovEasyRequest param) {
        // 校验 协查处理页面，如果当前处理人的姓名、证件类型、证件号码与协查人员姓名、证件类型、证件号码不一致，则页面显示“无权限处理该协查”
        // 根据流水号，查询对象
        AssistRecordDo entity = assistRecordRepository.findByAssistSerialNumber(param.getAssistSerialNumber());
        if (Objects.isNull(entity)) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_AUDIT_VALID_ERROR.format("查询协查流水号为空"));
        }
        if (entity.getAuditResult() != AssistResultEnum.WAIT) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_AUDIT_VALID_ERROR.format("该协查，已经审核过，不能再次审核"));
        }
//        AccountInfoDto accountInfoDto = getGovEasyUserCreditCodeByGovEasyUserId(param.getUserId());
        String accountInfoDtoJson = cacheService.get(CacheKeyBuilder.GOV_EASY_USER_LOGIN_RELATION.getCacheKey(param.getUserId()));
        if (StringUtils.isBlank(accountInfoDtoJson)) {
            // 登陆过时，报错，进行重新登录
            LOGGER.warn("gov easy user can't find getGovEasyUserCreditCodeByGovEasyUserId ,request userId is [{}]", param.getUserId());
            throw new GovEasyApiPermissionException(GovEasyManagerResponseCode.ASSIST_RECORD_GOV_EASY_NO_PERMISSION);
        }
        AccountInfoDto accountInfoDto = JacksonUtil.toBean(accountInfoDtoJson, AccountInfoDto.class);
        auditAssistRecordByMutileAuditTemp(param.getAssistSerialNumber(), serialNumber -> {
            AssistAuditContextBo assistAuditContextBo = AssistAuditContextBo.buildContext(BeanCopyUtils.copy(entity, AssistRecordDto.class), param.getAuditRelationList(), ASSIST_AUDIT_PLATFORM_MOBILE_FLAG, accountInfoDto, false);
            assistAuditContextBo.executeAllLevelStatusProcess();
        });
        return param.getAssistSerialNumber();
    }

    /**
     * 根据粤证易用户id，获取系统绑定的用户所属部门统一社会信用代码
     *
     * @param govUserId 粤证易用户id
     * @return 统一社会信用代码
     */
    @Deprecated
    @Override
    public AccountInfoDto getGovEasyUserCreditCodeByGovEasyUserId(String govUserId) {
        AccountInfoService accountInfoService = BeanFactoryUtils.getBean(AccountInfoService.class);
        GovEasyUserBo userInfoByUserId = govEasyManager.getUserInfoByUserId(govUserId);
        String mobileNumber = userInfoByUserId.getMobileNumber();
        List<AccountInfoDto> accountInfoDtos = accountInfoService.queryAccountInfoByMobilePhone(mobileNumber);
        if (CollectionUtils.isNotEmpty(accountInfoDtos)) {
            Optional<AccountInfoDto> accountInfo = accountInfoDtos.stream().filter(Objects::nonNull).findFirst();
            return accountInfo.orElseThrow(() -> new AssistRecordServiceException(AssistResponseCode.SERVICE_ASSIST_ERROR.format("当前粤证易用户，手机号不存在系统。需要检查配置")));
        } else {
            throw new AssistRecordServiceException(AssistResponseCode.SERVICE_ASSIST_ERROR.format("当前粤证易用户，手机号不存在系统。需要检查配置"));
        }
    }

    @Override
    public List<AssistRecordHistoryDto> assistHistory(String assistSerialNumber) {
        AssistRecordDo entity = this.assistRecordRepository.findByAssistSerialNumber(assistSerialNumber);
        if (Objects.isNull(entity)) {
            return null;
        }
        if (StringUtils.isBlank(entity.getHistoryAssistSerialNumber())) {
            // 没有历史协查号，返回空
            return null;
        }
        List<String> serialNumber = Lists.newArrayList(entity.getHistoryAssistSerialNumber().split(","));
        if (CollectionUtils.isNotEmpty(serialNumber)) {
            List<AssistRecordDo> result = assistRecordRepository.findAllInAssistSerialNumber(serialNumber);
            List<AssistRecordHistoryDto> boList = BeanCopyUtils.copyList(result, AssistRecordHistoryDto.class);
            if (CollectionUtils.isNotEmpty(boList)) {
                // 审核时间倒叙排序，null值放最后
                boList.sort(Comparator.comparing(AssistRecordHistoryDto::getAuditTime, Comparator.nullsFirst(Date::compareTo)).reversed());
            }
            return boList;
        }
        // 查询不到数据，返回空列表。
        return null;
    }

    @Override
    public Page<AssistRecordHistoryPageBo> assistHistoryPage(AssistRecordHistoryPageQuery query, Pageable pageable) {
        // 校验 协查处理页面，如果当前处理人的姓名、证件类型、证件号码与协查人员姓名、证件类型、证件号码不一致，则页面显示“无权限处理该协查”
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null != attrs ? attrs.getRequest() : null;
        String token = null;
        if (request != null) {
            token = request.getHeader(GovEasyJwtUtil.HEADER_AUTHORIZATION);
        }
        if (StringUtils.isEmpty(token)) {
            throw new RuntimeException("header is empty");
        }
        Boolean isEqGovUser = query.visitor(new AssistApiEqToUserIdAndJwtTokenUserIdVisitor(token, govEasyJwtUtil, query.getToUserId()));
        if (!isEqGovUser) {
            throw new GovEasyApiPermissionException(GovEasyManagerResponseCode.ASSIST_RECORD_GOV_EASY_NO_PERMISSION);
        }

        Specification<AssistRecordDo> spec = query.toSpec();
        Page<AssistRecordDo> result = assistRecordRepository.findAll(spec, pageable);
        return BeanCopyUtils.copyPageItemToVo(result, AssistRecordHistoryPageBo.class);
    }

    @Override
    public AssistRecordDetailPageBo assistDetailApi(AssistRecordApiQuery query) {
        //协查记录
        List<AssistRecordDo> recordList = assistRecordRepository.findAll(query.toSpec());
        if (CollectionUtils.isEmpty(recordList)) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_DATA_NULL_ERROR);
        }
        if (recordList.size() > 1) {
            LOGGER.warn("assistDetailApi query too much data,query is [{}],result is [{}]", query, recordList);
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_DATA_TOO_MUCH_ERROR);
        }

        AssistRecordProofRelationViewDo assistRecordDo = assistRecordProofRelationViewDoRepository.findById(recordList.get(0).getId())
                .orElseThrow(() -> new AssistRecordServiceException(AssistResponseCode.ASSIST_DATA_NULL_ERROR));

        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null != attrs ? attrs.getRequest() : null;
        String token = request.getHeader(GovEasyJwtUtil.HEADER_AUTHORIZATION);
        String govUserId = GovEasyJwtUtil.getGovEasyUserId(token);
        String accountInfoDtoJson = cacheService.get(CacheKeyBuilder.GOV_EASY_USER_LOGIN_RELATION.getCacheKey(govUserId));
        AccountInfoDto accountInfoDto = JacksonUtil.toBean(accountInfoDtoJson, AccountInfoDto.class);
        AssistRecordAuditDto assistRecordAuditDto = assistRecordProofRelationViewConvertAssRecordAudit(assistRecordDo, accountInfoDto, false);
        AssistRecordDetailPageBo result = BeanUtil.copy(assistRecordAuditDto, AssistRecordDetailPageBo.class);
        result.setHandleAffairsAssistIdentityTypeName(result.getHandleAffairsAssistIdentityType().getName());
        return result;
    }

    /**
     * 获取协查任务 协查服务与材料视图 转 协查服务页面返回dto 逻辑处理
     *
     * @param assistRecordProofRelationViewDo 协查服务与材料视图
     * @return 协查服务页面返回dto
     */
    private AssistRecordAuditDto assistRecordProofRelationViewConvertAssRecordAudit(AssistRecordProofRelationViewDo assistRecordProofRelationViewDo, AccountInfoDto accountInfoDto, Boolean isUseCurrentLoginUser) {
        AssistRecordAuditDto assistRecordAuditDto = BeanCopyUtils.copy(assistRecordProofRelationViewDo, AssistRecordAuditDto.class);

        ArrayList<String> recordIds = Lists.newArrayList(assistRecordAuditDto.getId());
        List<AssistInfoDo> infoList = getAssistInfoDos(recordIds);
        List<AssistInfoItemDo> infoItemList = getAssistInfoItemDos(recordIds);
        List<AssistInfoBo> assistInfoBos = BeanCopyUtils.copyList(infoList, AssistInfoBo.class);

        if (CollectionUtils.isNotEmpty(assistInfoBos) && CollectionUtils.isNotEmpty(infoItemList)) {
            Map<String, ArrayList<AssistInfoItemDo>> infoIdAndInfoItemListMap = infoItemList.stream().collect(Collectors.toMap(AssistInfoItemDo::getAssistInfoId, Lists::newArrayList, (before, after) -> {
                before.addAll(after);
                return before;
            }));
            for (AssistInfoBo item : assistInfoBos) {
                ArrayList<AssistInfoItemDo> assistInfoItemDos = infoIdAndInfoItemListMap.get(item.getId());
                List<AssistInfoItemBo> assistInfoItemBos = BeanCopyUtils.copyList(assistInfoItemDos, AssistInfoItemBo.class, (o, aClass, o1) -> {
                    if (o instanceof Integer) {
                        return o.toString();
                    } else {
                        return o;
                    }
                });
                item.setItemList(assistInfoItemBos);
            }
        }

        assistRecordAuditDto.setAssistDataList(assistInfoBos);

        //协查部门 的所属行政区划号码,不足9位按9位考虑
        OrganizationDto toAssistOrgName = organizationPublicService.findByCreditCode(assistRecordAuditDto.getToAssistCreditCode())
                .orElseThrow(() -> new AssistRecordServiceException(AssistResponseCode.PARAM_ASSIST_SERVICE_QUERY_ERROR));
        String toAssistOrgNameDivisionCode = toAssistOrgName.getDivisionCode();
        if (toAssistOrgName.getDivisionCode().length() < 9) {
            toAssistOrgNameDivisionCode = StringUtils.rightPad(toAssistOrgNameDivisionCode, 9, "0");
        }
        toAssistOrgNameDivisionCode = toAssistOrgNameDivisionCode.substring(0, 9);
        final String targetDivisionCode = toAssistOrgNameDivisionCode;

        List<AssistAuditDo> allByAssistCode = assistAuditRepository.findAllByAssistCode(assistRecordAuditDto.getAssistSerialNumber());
        List<AssistAuditDto> assistAuditDtos = BeanCopyUtils.copyList(allByAssistCode, AssistAuditDto.class);
        List<AuditRelationStatusDto<List<AssistAuditDto>>> auditStatusList = AssistAuditContextBo
                .buildContext(assistRecordAuditDto, assistAuditDtos, ASSIST_AUDIT_PLATFORM_PC_FLAG, accountInfoDto, isUseCurrentLoginUser)
                .getAuditStatusList();

        //下级审批部门只显示协查部门所属行政区划下的部门;
        List<AuditRelationStatusDto<List<AssistAuditDto>>> resultData = auditStatusList.stream()
                .peek(listAuditRelationStatusDto -> {
                    List<AssistAuditDto> assistAuditDtoList = listAuditRelationStatusDto.getSubAuditObj().stream()
                            .filter(assistAuditDto -> {
                                String auditDivisionCode = assistAuditDto.getAuditDivisionCode();
                                if (auditDivisionCode.length() < 9) {
                                    auditDivisionCode = StringUtils.rightPad(auditDivisionCode, 9, "0");
                                }
                                auditDivisionCode = auditDivisionCode.substring(0, 9);
                                return targetDivisionCode.equals(auditDivisionCode);
                            }).collect(Collectors.toList());
                    listAuditRelationStatusDto.setSubAuditObj(assistAuditDtoList);
                }).collect(Collectors.toList());

        assistRecordAuditDto.setAuditRelationList(resultData);

        return assistRecordAuditDto;
    }

    @Override
    public List<AssistRecordHistoryDto> assistHistoryApi(String assistSerialNumber) {
        AssistRecordDo entity = this.assistRecordRepository.findByAssistSerialNumber(assistSerialNumber);
        if (Objects.isNull(entity)) {
            return null;
        }
        if (StringUtils.isBlank(entity.getHistoryAssistSerialNumber())) {
            // 没有历史协查号，返回空
            return null;
        }
        List<String> serialNumber = Lists.newArrayList(entity.getHistoryAssistSerialNumber().split(","));
        if (CollectionUtils.isNotEmpty(serialNumber)) {
            List<AssistRecordDo> result = assistRecordRepository.findAllInAssistSerialNumber(serialNumber);
            List<AssistRecordHistoryDto> boList = BeanCopyUtils.copyList(result, AssistRecordHistoryDto.class);
            if (CollectionUtils.isNotEmpty(boList)) {
                // 审核时间倒叙排序，null值放最后
                boList.sort(Comparator.comparing(AssistRecordHistoryDto::getAuditTime, Comparator.nullsFirst(Date::compareTo)).reversed());
            }
            return boList;
        }
        // 查询不到数据，返回空列表。
        return null;
    }


    /**
     * 查询协查服务
     *
     * @param query    查询对象
     * @param pageable 分页对象
     * @return 协查服务分页数据
     */
    @Override
    public Page<AssistRecordAuditRelationViewDto> queryAssistRecordProofRelationViewPage(AssistRecordPageQuery query, Pageable pageable) {
        //分页对象
        //构造查询参数
        Page<AssistRecordAuditRelationViewDo> resultDo = assistRecordAuditRelationViewDoRepository.findAll(query.buildSpecByAssistRecordAuditRelationView(), pageable);
        Page<AssistRecordAuditRelationViewDto> assistInvestigateRecordBaseBos = BeanCopyUtils.copyPageItemToVo(resultDo, AssistRecordAuditRelationViewDto.class);
        if (Objects.nonNull(assistInvestigateRecordBaseBos) && CollectionUtils.isNotEmpty(assistInvestigateRecordBaseBos.getContent()) && Objects.nonNull(query.getCalInvalidTime()) && query.getCalInvalidTime()) {
            Date currentDate = new Date();
            // 处理该分页数据中，剩余处理时限
            List<List<LocalDate>> holidayRangeList = getHolidayRangeList();
            List<List<LocalDate>> holidayWorkRangeList = getHolidayWorkRangeList();
            List<List<LocalTime>> workHourRangeTimeList = getNotWorkHourRangeList();
            List<AssistRecordAuditRelationViewDto> dataResult = assistInvestigateRecordBaseBos.getContent().stream().peek(item -> {
                        if (Objects.nonNull(item.getInvalidDate()) && Objects.nonNull(item.getAssistTime())) {
                            Integer currentDateWorkDateMinus = incrWorkDateMinusByAssistDate2CurrentDate(workHourRangeTimeList, currentDate, item.getAssistTime(), holidayRangeList, holidayWorkRangeList);
                            Integer invalidDateWorkDateMinus = incrWorkDateMinusByAssistDate2CurrentDate(workHourRangeTimeList, item.getInvalidDate(), item.getAssistTime(), holidayRangeList, holidayWorkRangeList);
                            // 剩余分钟数 = 范围总分钟时间-非工作总分钟时间
                            long restWorkMinus = invalidDateWorkDateMinus - currentDateWorkDateMinus;
                            // 页面显示小时数 =
                            long restHour = restWorkMinus / 60;
                            // 页面显示分钟数 =
                            long restMinus = restWorkMinus - (restHour * 60);
                            item.setResidualTreatmentHourLimit((int) restHour);
                            item.setResidualTreatmentLimit((int) restMinus);
                        } else {
                            item.setResidualTreatmentHourLimit(0);
                            item.setResidualTreatmentLimit(0);
                        }
                    })
//                    .sorted(Comparator.comparing(AssistInvestigateRecordBaseBo::getResidualTreatmentLimit, Comparator.nullsLast(Integer::compareTo)))
                    .collect(Collectors.toList());
            return new PageImpl<>(dataResult, assistInvestigateRecordBaseBos.getPageable(), assistInvestigateRecordBaseBos.getTotalElements());
        }

        return assistInvestigateRecordBaseBos;
    }

    /**
     * 获取补班日期
     *
     * @return 补班对象List
     */
    private List<List<LocalDate>> getHolidayWorkRangeList() {
        String splitStr = ",";
        Optional<DictDto> optionalDict = dictPublicService.findDictAndDetailByName(DictionaryDicTypeConstant.ASSIST_HOLIDAY_WORK_RANGE.name());
        DictDto dictDto = optionalDict.orElseThrow(() -> new DictionaryConfigException(DictionaryResponseCode.ASSIST_HOLIDAY_WORK_RANGE_NOT_NULL));
        List<DictDetailDto> dictDetailList = dictDto.getDictDetailList();

        List<List<LocalDate>> result = Lists.newArrayList();
        dictDetailList.forEach(item -> {
            String typeCode = item.getValue();
            List<String> timeRangeStrList = Lists.newArrayList(typeCode.split(splitStr));
            List<LocalDate> resultList = timeRangeStrList.stream().map(timeRangeStr -> {
                try {
                    return LocalDate.parse(timeRangeStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
                } catch (Exception e) {
                    LOGGER.warn("getHolidayRangeList parse date Error", e);
                }
                return null;
            }).filter(Objects::nonNull).sorted(LocalDate::compareTo).collect(Collectors.toList());
            result.add(resultList);
        });
        return result;
    }

    /**
     * 从数据字典生成节假日对象List
     *
     * @return 节假日对象List
     */
    private List<List<LocalDate>> getHolidayRangeList() {
        String splitStr = ",";
        Optional<DictDto> optionalDict = dictPublicService.findDictAndDetailByName(DictionaryDicTypeConstant.ASSIST_HOLIDAY_RANGE.name());
        DictDto dictDto = optionalDict.orElseThrow(() -> new DictionaryConfigException(DictionaryResponseCode.ASSIST_HOLIDAY_RANGE_NOT_NULL));
        List<DictDetailDto> dictDetailList = dictDto.getDictDetailList();
        List<List<LocalDate>> result = Lists.newArrayList();
        dictDetailList.forEach(item -> {
            String typeCode = item.getValue();
            List<String> timeRangeStrList = Lists.newArrayList(typeCode.split(splitStr));
            List<LocalDate> resultList = timeRangeStrList.stream().map(timeRangeStr -> {
                try {
                    return LocalDate.parse(timeRangeStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
                } catch (Exception e) {
                    LOGGER.warn("getHolidayRangeList parse date Error", e);
                }
                return null;
            }).filter(Objects::nonNull).sorted(LocalDate::compareTo).collect(Collectors.toList());
            result.add(resultList);
        });
        return result;
    }

    /**
     * 获取非工作时间的时分秒List
     *
     * @return 工作时间的时分秒List
     */
    private List<List<LocalTime>> getNotWorkHourRangeList() {
        String splitStr = ",";

        Optional<DictDto> optionalDict = dictPublicService.findDictAndDetailByName(DictionaryDicTypeConstant.ASSIST_WORK_HOUR_RANGE.name());
        DictDto dictDto = optionalDict.orElseThrow(() -> new DictionaryConfigException(DictionaryResponseCode.ASSIST_WORK_HOUR_RANGE_NOT_NULL));
        List<DictDetailDto> dictDetailList = dictDto.getDictDetailList();


        List<List<LocalTime>> resultList = Lists.newArrayList();
        dictDetailList.forEach(item -> {
            String typeCode = item.getValue();
            List<String> timeRangeStrList = Lists.newArrayList(typeCode.split(splitStr));

            List<LocalTime> result = timeRangeStrList.stream().map(timeRangeStr -> {
                try {
                    return LocalTime.parse(timeRangeStr, DateTimeFormatter.ofPattern("HHmm"));
                } catch (Exception e) {
                    LOGGER.warn("getWorkHourRangeList parse date error", e);
                }
                return null;
            }).filter(Objects::nonNull).sorted(LocalTime::compareTo).collect(Collectors.toList());
            resultList.add(result);
        });
        return resultList;
    }

    /**
     * 协查申请
     *
     * @param request 申请请求对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssistRecordDto createAssist(AssistRecordSaveRequest request) {
        // 检查协查材料，是否属于已取消状态
        ItemDto byItemCode = itemService.findByItemCode(request.getItemCode());
        Assert.notNull(byItemCode, AssistResponseCode.ASSIST_RECORD_CREATE_BIZ_ERROR.format("事项不存在"));

        ItemMaterialDo byId = itemMaterialService.findById(request.getMaterialId());
        Assert.notNull(byId, AssistResponseCode.ASSIST_RECORD_CREATE_BIZ_ERROR.format("材料不存在"));
        Assert.isTrue(!ProofClearTypeEnum.DIRECTLY_CANCEL.equals(byId.getProofClearType()) || !byId.getLogicCancel(), AssistResponseCode.ASSIST_RECORD_CREATE_BIZ_ERROR.format("材料已经删除或直接取消，无法协查"));
        AssistRecordDo assistRecordDo = new AssistRecordDo();
        BeanCopyUtils.copy(request, assistRecordDo);
        Optional<CurrentAccountDto> current = accountHelper.getCurrent();
        if (StringUtils.isNotBlank(request.getAssistSerialNumber())) {
            AssistRecordDo byAssistSerialNumber = assistRecordRepository.findByAssistSerialNumber(request.getAssistSerialNumber());
            Assert.isNull(byAssistSerialNumber, AssistResponseCode.ASSIST_RECORD_CREATE_BIZ_ERROR.format("协查单已经存在"));
        }
        // 协查发起人信息设值
        if (!current.isPresent()) {
            assistRecordDo.setFromAssistCreditCode(request.getFromAssistCreditCode());
            assistRecordDo.setFromAssistOrgName(request.getFromAssistOrgName());
            assistRecordDo.setFromAssistUserName(request.getFromAssistUserName());
            assistRecordDo.setFromAssistContain(request.getFromAssistContain());
        } else {
            CurrentAccountDto currentAccountDto = current.orElseThrow(() -> new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_CREATE_BIZ_ERROR.format("当前用户不存在，无法协查")));
            Optional<AccountInfoDto> accountInfoByAccountOption = accountInfoService.getAccountInfoByAccountOption(currentAccountDto.getAccount().getAccount());
            assistRecordDo.setFromAssistCreditCode(currentAccountDto.getOrganization().getCreditCode());
            assistRecordDo.setFromAssistOrgName(currentAccountDto.getOrganization().getName());
            assistRecordDo.setFromAssistUserName(currentAccountDto.getAccount().getUserName());
            accountInfoByAccountOption.ifPresent(item -> assistRecordDo.setFromAssistContain(item.getMobilePhone()));
        }

        assistRecordDo.setAssistTime(new Date());
        assistRecordDo.setAssistTime(request.getAssistTime());
        assistRecordDo.setAuditResult(AssistResultEnum.WAIT);
        assistRecordDo.setBusinessSerialNumber(request.getBusinessSerialNumber());
        assistRecordDo.setLegalPersonIdentityType(request.getLegalPersonIdentityType());
        assistRecordDo.setInvalidDate(getAssistRecordInvalidDate(assistRecordDo.getProofCatalogId(), request.getAssistTime()).orElse(null));
        assistRecordDo.setCallBack(false);
        assistRecordDo.setItemCode(byItemCode.getItemCode());
        assistRecordDo.setItemName(byItemCode.getItemName());
        assistRecordDo.setMaterialId(byId.getId());
        assistRecordDo.setProofCatalogId(request.getProofCatalogId());
        assistRecordDo.setProofCatalogName(request.getProofCatalogName());
        // 协查处理人信息设值
        assistRecordDo.setToAssistCreditCode(request.getToAssistCreditCode());
        Optional<OrganizationDto> byCreditCode = organizationPublicService.findByCreditCode(request.getToAssistCreditCode());
        byCreditCode.ifPresent(item -> assistRecordDo.setToAssistOrgName(item.getName()));
        AssistRecordDo save = assistRecordRepository.save(assistRecordDo);
        // 生成，并且保存多层协查处理。
        // 需要查询，协查数据还是电子证明的数据进行生成审核数据。
        List<AssistAuditDo> entities = buildAssistAuditSaveDo(assistRecordDo, request.getToAssistCreditCode());
        Assert.notEmpty(entities, AssistResponseCode.ASSIST_RECORD_CREATE_BIZ_ERROR.formatByReplaceFlag("证明目录审核配置异常"));
        assistAuditRepository.saveAll(entities);
        sendAssistNeedAuditMsg(BeanUtil.copy(assistRecordDo, AssistRecordDto.class));
        return BeanCopyUtils.copy(save, AssistRecordDto.class);
    }

    /**
     * 构造协查对象
     *
     * @param assistRecordDo       协查记录
     * @param firstAuditCreditCode 申请协查人统一社会信用代码
     * @return 多层协查保存对象
     */
    @Nullable
    private List<AssistAuditDo> buildAssistAuditSaveDo(AssistRecordDo assistRecordDo, String firstAuditCreditCode) {
        ProofCatalogDetailDto detailById = proofCatalogService.findDetailById(assistRecordDo.getProofCatalogId());
        List<AssistAuditDto> saveAssistAuditDto = null;
        List<ProofCatalogArtificialRelationDto> proofCatalogArtificialList = detailById.getProofCatalogArtificialList();
        Assert.notEmpty(proofCatalogArtificialList, AssistResponseCode.ASSIST_RECORD_CREATE_NOT_EXISTS_REPLACE_ERROR.format("协查失败，找不到证明目录-人工协查数据"));
        ProofCatalogArtificialRelationDto proofCatalogArtificialRelationDto = proofCatalogArtificialList.get(0);
        IssueProofLicenseTypeEnum issueProofLicense = Objects.nonNull(proofCatalogArtificialRelationDto.getIssueProofLicense()) ? proofCatalogArtificialRelationDto.getIssueProofLicense() : IssueProofLicenseTypeEnum.NOT_ISSUE;
        assistRecordDo.setIssueProofLicense(issueProofLicense);
        assistRecordDo.setIssueProofLicenseWay(proofCatalogArtificialRelationDto.getIssueProofLicenseWay());
        if (IssueProofLicenseTypeEnum.isIssue(issueProofLicense)) {
            List<ProofCatalogLicenseItemImplItemInfoDto> implItemInfoDtos = proofCatalogArtificialRelationDto.buildByImplItemInfoJson();
            Assert.notEmpty(implItemInfoDtos, AssistResponseCode.ASSIST_RECORD_CREATE_NOT_EXISTS_REPLACE_ERROR.format("协查失败，证明目录，电子证明实施码配置问题。。检查配置"));
            ProofCatalogLicenseItemImplItemInfoDto infoDto = implItemInfoDtos.get(0);
            Assert.notNull(infoDto, AssistResponseCode.ASSIST_RECORD_CREATE_NOT_EXISTS_REPLACE_ERROR.format("协查失败，证明目录，电子证明实施码配置问题。。检查配置"));
            assistRecordDo.setImplementCode(infoDto.getImplementCode());
            assistRecordDo.setImplementItemInfoJson(proofCatalogArtificialRelationDto.getImplementItemInfoJson());
        }
        saveAssistAuditDto = buildSaveAssistAuditDto(assistRecordDo, proofCatalogArtificialRelationDto, firstAuditCreditCode);
        return BeanCopyUtils.copyList(saveAssistAuditDto, AssistAuditDo.class);
    }

    /**
     * 构造基于证明目录数据，协查数据，新建的多层协查dto
     *
     * @param assistRecordDo        协查do
     * @param artificialRelationDto 证明目录，人工协查dto
     * @param firstAuditCreditCode  申请审核统一社会信用代码
     * @return 多层协查新建对象
     */
    private List<AssistAuditDto> buildSaveAssistAuditDto(AssistRecordDo assistRecordDo, ProofCatalogArtificialRelationDto artificialRelationDto, String firstAuditCreditCode) {
        String catalogCode = artificialRelationDto.getProofCatalogCode();
        String catalogId = artificialRelationDto.getProofCatalogId();
        String auditTempKey = artificialRelationDto.getAuditRelationTemplateKey();
        List<AuditRelationTemplateDto> auditRelationTemplateList = proofCatalogService.findCatalogAuditRelationTempByKey(auditTempKey);
        List<AuditRelationStatusDto<List<AuditRelationTemplateDto>>> auditRelationStatusDtos = AuditRelationTemplateBo.buildAuditStatusList(auditRelationTemplateList, AuditRelationStatusEnums.NOT_AUDIT);
        AtomicBoolean isEqFirstAuditCreditCodeWait = new AtomicBoolean(false);
        List<AssistAuditDto> collect = auditRelationStatusDtos.stream().flatMap(item -> {
                    List<AssistAuditDto> assistAuditDtos = BeanCopyUtils.copyList(item.getSubAuditObj(), AssistAuditDto.class);
                    assistAuditDtos.forEach(auditDto -> {
                        auditDto.setId(null);
                        auditDto.setAssistFromUser(assistRecordDo.getFromAssistUserName());
                        List<AccountInfoDto> accountInfoByAccountName = accountInfoService.getAccountInfoByUserName(assistRecordDo.getFromAssistUserName());
                        if (CollectionUtils.isNotEmpty(accountInfoByAccountName)) {
                            AccountInfoDto accountInfoDto = accountInfoByAccountName.get(0);
                            auditDto.setAssistFromUserAccount(accountInfoDto.getAccountName());
                        }
                        auditDto.setAssistTime(assistRecordDo.getAssistTime());
                        auditDto.setAssistAuditResult(AssistResultEnum.NOT_WAIT);
                        auditDto.setAssistCode(assistRecordDo.getAssistSerialNumber());
                        auditDto.setItemCode(assistRecordDo.getItemCode());
                        auditDto.setItemMaterialId(assistRecordDo.getMaterialId());
                        auditDto.setProofCatalogCode(catalogCode);
                        auditDto.setCatalogReplaceWayId(catalogId);
                        auditDto.setNeedAudit(false);
                    });
                    return assistAuditDtos.stream();
                }).peek(auditDto -> {
                    if (auditDto.getAuditLevel() == 1 && auditDto.getAuditOrgCode().equals(firstAuditCreditCode)) {
                        // 设置一级审核数据为待审核&&需要审核标识。
                        auditDto.setAssistAuditResult(AssistResultEnum.WAIT);
                        auditDto.setNeedAudit(true);
                        isEqFirstAuditCreditCodeWait.set(true);
                    }
                })
                // 只保存一级要审核的数据和非1级的多层审核数据
                .filter(auditDto -> (auditDto.getAuditLevel() == 1 && auditDto.getNeedAudit()) || auditDto.getAuditLevel() != 1).collect(Collectors.toList());
        Assert.isTrue(isEqFirstAuditCreditCodeWait.get(), AssistResponseCode.ASSIST_RECORD_CREATE_BIZ_ERROR.format("协查创建失败，第一层审核部门统一社会信用代码不匹配"));
        return collect;
    }

    /**
     * 根据证明目录与协查时间，获取协查到期时间
     *
     * @param catalogId  证明目录id
     * @param assistTime 协查时间
     * @return 根据证明目录，人工协查时间推算出的到期时间
     */
    public Optional<Date> getAssistRecordInvalidDate(String catalogId, Date assistTime) {
        try {
            List<List<LocalDate>> holidayRangeList = getHolidayRangeList();
            List<List<LocalDate>> holidayWorkRangeList = getHolidayWorkRangeList();
            List<List<LocalTime>> workHourRangeTimeList = getNotWorkHourRangeList();
            Optional<List<ProofCatalogArtificialRelationDto>> proofCatalogArtificialRelationBoList = proofCatalogService.findArtificialByCatalogIdIn(Lists.newArrayList(catalogId));
            String minusPattern = "yyyy-MM-dd HH:mm";
            Date invalidDate = DateUtil.parse(DateUtil.format(assistTime, minusPattern), minusPattern);
            int assistTimeHour = 0;
            int assistTimeMinute = 0;
            if (proofCatalogArtificialRelationBoList.isPresent() && CollectionUtils.isNotEmpty(proofCatalogArtificialRelationBoList.get())) {
                Optional<ProofCatalogArtificialRelationDto> first = proofCatalogArtificialRelationBoList.get().stream().filter(item -> catalogId.equals(item.getProofCatalogId())).findFirst();
                if (first.isPresent()) {
                    ProofCatalogArtificialRelationDto dto = first.get();
                    assistTimeHour = Objects.isNull(dto.getAssistTimeHour()) ? 0 : dto.getAssistTimeHour();
                    assistTimeMinute = Objects.isNull(dto.getAssistTimeMinute()) ? 0 : dto.getAssistTimeMinute();
                }
            }
            // 从协查时间，根据配置超时时间，往后推到期时间。
            // 到期时间 = 协查时间+配置超时的小时+分钟数
            int index = assistTimeHour * 60 + assistTimeMinute;
            invalidDate = incrHolidayRangeAndHolidayWorkRange(holidayRangeList, holidayWorkRangeList, invalidDate);
            invalidDate = incrWorkDate(workHourRangeTimeList, invalidDate, index, holidayRangeList, holidayWorkRangeList);
            return Optional.of(invalidDate);
        } catch (Exception e) {
            LOGGER.warn("getAssistRecordInvalidDate error,return Optional", e);
        }
        // 返回null
        return Optional.empty();
    }

    /**
     * 根据证明目录与协查时间，获取协查到期时间
     *
     * @param catalogId   证明目录id
     * @param currentDate 当前时间
     * @param invalidDate 协查到期时间
     * @return 根据证明目录，人工协查时间推算出的到期时间
     */
    public Optional<Date> getAssistRecordInvalidDate2ToDayMinus(String catalogId, Date currentDate, Date invalidDate) {
        try {
            String minusPattern = "yyyy-MM-dd HH:mm";
            Date currentDateMinus = DateUtil.parse(DateUtil.format(currentDate, minusPattern), minusPattern);
            Optional<List<ProofCatalogArtificialRelationDto>> proofCatalogArtificialRelationBoList = proofCatalogService.findArtificialByCatalogIdIn(Lists.newArrayList(catalogId));
            if (proofCatalogArtificialRelationBoList.isPresent() && CollectionUtils.isNotEmpty(proofCatalogArtificialRelationBoList.get())) {
                Optional<ProofCatalogArtificialRelationDto> first = proofCatalogArtificialRelationBoList.get().stream().filter(item -> catalogId.equals(item.getProofCatalogId())).findFirst();
                if (first.isPresent()) {
                    List<List<LocalDate>> holidayRangeList = getHolidayRangeList();
                    List<List<LocalDate>> holidayWorkRangeList = getHolidayWorkRangeList();
                    List<List<LocalTime>> workHourRangeTimeList = getNotWorkHourRangeList();
                    // 从协查时间，根据配置超时时间，往后推到期时间。
                    ProofCatalogArtificialRelationDto proofCatalogArtificialRelationDto = first.get();
                    // 到期时间 = 协查时间+配置超时的小时+分钟数
                    int assistTimeHour = Objects.isNull(proofCatalogArtificialRelationDto.getAssistTimeHour()) ? 0 : proofCatalogArtificialRelationDto.getAssistTimeHour();
                    int assistTimeMinute = Objects.isNull(proofCatalogArtificialRelationDto.getAssistTimeMinute()) ? 0 : proofCatalogArtificialRelationDto.getAssistTimeMinute();
                    int index = assistTimeHour * 60 + assistTimeMinute;
                    currentDateMinus = incrHolidayRangeAndHolidayWorkRange(holidayRangeList, holidayWorkRangeList, currentDateMinus);
                    currentDateMinus = incrWorkDate(workHourRangeTimeList, currentDateMinus, index, holidayRangeList, holidayWorkRangeList);
                    // 得出当前时间到到期时间的工作分钟数
                    return Optional.of(currentDateMinus);
                }
            }
        } catch (Exception e) {
            LOGGER.warn("getAssistRecordInvalidDate2ToDayMinus error,return Optional", e);
        }
        // 返回null
        return Optional.empty();
    }

    /**
     * 计算到协查时间，假期与补班的时间。
     *
     * @param holidayRangeList     假期时间列表
     * @param holidayWorkRangeList 补班时间列表
     * @param assistTime           协查时间
     * @return 处理后的协查史昂
     */
    private static Date incrHolidayRangeAndHolidayWorkRange(List<List<LocalDate>> holidayRangeList, List<List<LocalDate>> holidayWorkRangeList, Date assistTime) {
        LocalDateTime currentLocalDateTime = LocalDateTime.ofInstant(assistTime.toInstant(), ZoneId.systemDefault());
        LocalDate currentLocalDate = currentLocalDateTime.toLocalDate();

        // 判断是否属于补班日期，直接返回
        for (List<LocalDate> holidayWorkRange : holidayWorkRangeList) {
            LocalDate startDate = holidayWorkRange.get(0);
            LocalDate endDate = holidayWorkRange.get(1);
            boolean isWithinDateRange = currentLocalDate.isAfter(startDate) && currentLocalDate.isBefore(endDate) || (currentLocalDate.equals(startDate) || currentLocalDate.equals(endDate));
            if (isWithinDateRange) {
                // 属于补班日期，直接返回协查时间不处理。
                return assistTime;
            }
        }
        // 判断周六日
        if (DayOfWeek.SATURDAY.equals(currentLocalDate.getDayOfWeek()) || DayOfWeek.SUNDAY.equals(currentLocalDate.getDayOfWeek())) {
            // 是周六日，增加一天。
            assistTime = incrHolidayRangeAndHolidayWorkRange(holidayRangeList, holidayWorkRangeList, DateUtil.offsetDay(assistTime, 1));
        }
        // 判断是否属于节假日
        for (List<LocalDate> holidayRange : holidayRangeList) {
            LocalDate startDate = holidayRange.get(0);
            LocalDate endDate = holidayRange.get(1);
            boolean isWithinDateRange = currentLocalDate.isAfter(startDate) && currentLocalDate.isBefore(endDate) || (currentLocalDate.equals(startDate) || currentLocalDate.equals(endDate));
            if (isWithinDateRange) {
                // 在假期日期内。当前时间+1天计算
                assistTime = incrHolidayRangeAndHolidayWorkRange(holidayRangeList, holidayWorkRangeList, DateUtil.offsetDay(assistTime, 1));
            }
        }
        return assistTime;
    }

    /**
     * 增加协查时间到工作时间范围内
     *
     * @param workHourRangeTimeList 工作时间范围配置
     * @param assistTime            到期时间
     * @param currentDate           当前时间
     * @return 处理后得协查时间。
     */
    private static Integer incrWorkDateMinusByAssistDate2CurrentDate(List<List<LocalTime>> workHourRangeTimeList, Date currentDate, Date assistTime, List<List<LocalDate>> holidayRangeList, List<List<LocalDate>> holidayWorkRangeList) {
        Set<LocalTime> workTimeRange = getWorkTimeLocalTimeSetByMinus(workHourRangeTimeList);
        int count = 0;
        if (Objects.isNull(currentDate) || Objects.isNull(assistTime)) {
            return count;
        }
        while (assistTime.before(currentDate)) {
            //判断是否工作时间范围
            assistTime = incrHolidayRangeAndHolidayWorkRange(holidayRangeList, holidayWorkRangeList, assistTime);
            LocalTime currentRangeTime = LocalDateTime.ofInstant(assistTime.toInstant(), ZoneId.systemDefault()).toLocalTime().withNano(0).withSecond(0);
            if (workTimeRange.contains(currentRangeTime)) {
                count++;
            }
            assistTime = DateUtil.offsetMinute(assistTime, 1);
        }
        return count;
    }

    private static Set<LocalTime> getWorkTimeLocalTimeSetByMinus(List<List<LocalTime>> workHourRangeTimeList) {
        Set<LocalTime> workTimeRange = Sets.newTreeSet();
        // 生成工作时间范围，多个范围内的分钟对象。
        for (List<LocalTime> workHourRangeTime : workHourRangeTimeList) {
            LocalTime startTime = workHourRangeTime.get(0);
            LocalTime endTime = workHourRangeTime.get(1);
            while (startTime.toSecondOfDay() < endTime.toSecondOfDay()) {
                startTime = startTime.plusMinutes(1);
                workTimeRange.add(startTime);
            }
        }
        return workTimeRange;
    }

    /**
     * 增加协查时间到工作时间范围内
     *
     * @param workHourRangeTimeList 工作时间范围配置
     * @param assistTime            协查时间
     * @return 处理后得协查时间。
     */
    private static Date incrWorkDate(List<List<LocalTime>> workHourRangeTimeList, Date assistTime, int minus, List<List<LocalDate>> holidayRangeList, List<List<LocalDate>> holidayWorkRangeList) {
        Set<LocalTime> workTimeRange = getWorkTimeLocalTimeSetByMinus(workHourRangeTimeList);
        while (minus > 1) {
            //判断是否工作时间范围
            assistTime = incrHolidayRangeAndHolidayWorkRange(holidayRangeList, holidayWorkRangeList, assistTime);
            LocalTime currentRangeTime = LocalDateTime.ofInstant(assistTime.toInstant(), ZoneId.systemDefault()).toLocalTime().withNano(0).withSecond(0);
            if (workTimeRange.contains(currentRangeTime)) {
                // 在，则先-1计数，再+一分钟，再重复判断。直到-完计数。
                assistTime = DateUtil.offsetMinute(assistTime, 1);
                minus--;
            } else {
                // 判断assistTime是否在范围内，不在则加1分钟，再重复判断。
                assistTime = DateUtil.offsetMinute(assistTime, 1);
            }
        }
        return assistTime;
    }

    /**
     * 页面触发协查处理
     *
     * @param request 审核请求对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditByWebapi(AssistRecordHandleRequest request) {
        Optional<AssistRecordDo> assistRecordDoOptional = assistRecordRepository.findById(request.getAuditId());
        if (!assistRecordDoOptional.isPresent() || assistRecordDoOptional.get().getAuditResult() != AssistResultEnum.WAIT) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_NULL_ERROR);
        }
        AssistRecordDo assistRecordDo = assistRecordDoOptional.get();
        auditAssistRecordByMutileAuditTemp(assistRecordDo.getAssistSerialNumber(), (serialNumber) -> {
            AssistAuditContextBo assistAuditContextBo = AssistAuditContextBo.buildContext(BeanCopyUtils.copy(assistRecordDo, AssistRecordDto.class), request.getAuditRelationList(), ASSIST_AUDIT_PLATFORM_PC_FLAG, null, true);
            assistAuditContextBo.executeAllLevelStatusProcess();
        });
    }

    /**
     * 协查单多层审核模板
     * 包好校验是否有锁，上锁，解锁。防止多用例，多线程并发操作，同单上锁
     *
     * @param serialNumber 协查单号
     * @param consumer     协查单号具体工作
     */
    @Transactional(rollbackFor = Exception.class)
    public synchronized void auditAssistRecordByMutileAuditTemp(String serialNumber, Consumer<String> consumer) {
        String s = cacheService.get(CacheKeyBuilder.ASSIST_RECORD_AUDIT.getCacheKey(serialNumber));
        Assert.isTrue(StringUtils.isBlank(s), AssistResponseCode.ASSIST_RECORD_AUDIT_LOCK_ERROR.format("该单存在同时审核，稍后再试"));
        try {
            // 上锁
            cacheService.set(CacheKeyBuilder.ASSIST_RECORD_AUDIT.getCacheKey(serialNumber), "true");
            consumer.accept(serialNumber);
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("auditAssistRecordByMutileAuditTemp error", e);
            throw new ApplicationGlobalRuntimeException(new BaseResponseCode(e.getResponseCode().getCode(), e.getResponseCode().getMessage(), e.getResponseCode().isReplaceFlag()));
        } finally {
            // 解锁
            cacheService.del(CacheKeyBuilder.ASSIST_RECORD_AUDIT.getCacheKey(serialNumber));
        }
    }

    /**
     * 获取协查任务
     *
     * @param id 协查服务id
     * @return 协查任务
     */
    @Override
    public AssistRecordAuditDto getAssistRecord(String id) {
        Optional<AssistRecordProofRelationViewDo> assistRecordDo = assistRecordProofRelationViewDoRepository.findById(id);
        if (!assistRecordDo.isPresent()) {
            return null;
        }

        return assistRecordProofRelationViewConvertAssRecordAudit(assistRecordDo.get(), null, true);
    }

    @Override
    public List<AssistAuditDto> getAssistAuditBySerialNum(String serialNumber) {
        List<AssistAuditDo> allByAssistCode = assistAuditRepository.findAllByAssistCode(serialNumber);
        return BeanUtil.copyList(allByAssistCode, AssistAuditDto.class);
    }

    /**
     * 查询协查任务
     *
     * @param assistCreditCode 协查部门代码
     * @param toUserName       协查处理人
     * @param assistStatus     协查状态
     * @return 协查任务集合
     */
    @Override
    public List<AssistRecordListApiResponse> queryListApi(String assistCreditCode, String toUserName, String assistStatus) {
        if (StringUtils.isBlank(assistCreditCode)) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_CREDIT_CODE_NULL_ERROR);
        }
        List<AssistResultEnum> assistResultEnums = Lists.newArrayList();
        if (StringUtils.isNotBlank(assistStatus)) {
            switch (assistStatus) {
                case "待协查":
                    assistResultEnums.add(AssistResultEnum.WAIT);
                    break;
                case "已协查":
                    assistResultEnums.add(AssistResultEnum.SUCCESS);
                    assistResultEnums.add(AssistResultEnum.FAIL);
                    break;
                default:
                    throw new AssistRecordServiceException(AssistResponseCode.ASSIST_QUERY_STATUS_ERROR);
            }
        }
        Specification<AssistRecordProofRelationViewDo> specifications = Specifications.<AssistRecordProofRelationViewDo>and().eq("toAssistCreditCode", assistCreditCode).like(StringUtils.isNotBlank(toUserName), "toUserName", "%" + toUserName + "%").in(CollectionUtils.isNotEmpty(assistResultEnums), "auditResult", assistResultEnums).build();
        List<AssistRecordProofRelationViewDo> viewDos = assistRecordProofRelationViewDoRepository.findAll(specifications);
        if (CollectionUtils.isEmpty(viewDos)) {
            return Collections.emptyList();
        }
        List<AssistRecordListApiResponse> assistRecordListApiResponses = Lists.newArrayList();
        for (AssistRecordProofRelationViewDo relationViewDo : viewDos) {
            AssistRecordListApiResponse assistRecordListApiResponse = new AssistRecordListApiResponse();
            BeanCopyUtils.copy(relationViewDo, assistRecordListApiResponse);
            assistRecordListApiResponse.setAssistStatus(relationViewDo.getAuditResult() == AssistResultEnum.WAIT ? "待协查" : "已协查");
            assistRecordListApiResponses.add(assistRecordListApiResponse);
        }
        return assistRecordListApiResponses;
    }

    /**
     * 查看协查任务详情 api
     *
     * @param assistSerialNumber 协查流水号
     * @return 协查任务
     * @ 异常
     */
    @Override
    public AssistRecordViewDetailApiDto viewDetailApi(String assistSerialNumber) {
        if (StringUtils.isBlank(assistSerialNumber)) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_SERIAL_NUMBER_NULL_ERROR);
        }
        Specification<AssistRecordProofRelationViewDo> specifications = Specifications.<AssistRecordProofRelationViewDo>and().eq("assistSerialNumber", assistSerialNumber).build();
        Optional<AssistRecordProofRelationViewDo> viewDo = assistRecordProofRelationViewDoRepository.findOne(specifications);
        if (!viewDo.isPresent()) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_DATA_NULL_ERROR);
        }
        AssistRecordProofRelationViewDo relationViewDo = viewDo.get();
        AssistRecordViewDetailApiDto assistRecordViewApiDto = new AssistRecordViewDetailApiDto();
        BeanCopyUtils.copy(relationViewDo, assistRecordViewApiDto);
        //协查内容
        assistRecordViewApiDto.setAssistDataList(getAssistInfoBosByAssistRecordId(relationViewDo.getId()));
        if (!AssistResultEnum.WAIT.equals(assistRecordViewApiDto.getAuditResult()) && !AssistResultEnum.NOT_WAIT.equals(assistRecordViewApiDto.getAuditResult())) {
            List<AssistAuditDo> assistAuditDo = assistAuditRepository.findAllByAssistCode(relationViewDo.getAssistSerialNumber());
            List<AssistAuditDto> assistAuditDtos = BeanCopyUtils.copyList(assistAuditDo, AssistAuditDto.class);
            if (CollectionUtils.isNotEmpty(assistAuditDtos)) {
                assistAuditDtos = assistAuditDtos.stream().filter(AssistAuditDto::getNeedAudit).sorted(Comparator.comparing(AssistAuditDto::getAuditLevel)).collect(Collectors.toList());
            }
            assistRecordViewApiDto.setAuditRelationList(assistAuditDtos);
        }
        return assistRecordViewApiDto;
    }

    /**
     * 根据协查记录获取协查内容
     *
     * @param assistRecordId 协查记录id
     * @return 协查内容集合
     */
    private List<AssistInfoBo> getAssistInfoBosByAssistRecordId(String assistRecordId) {
        if (StringUtils.isBlank(assistRecordId)) {
            return Collections.emptyList();
        }
        List<AssistInfoDo> infoList = getAssistInfoDos(Lists.newArrayList(assistRecordId));
        if (CollectionUtils.isEmpty(infoList)) {
            return Collections.emptyList();
        }
        List<AssistInfoBo> infoBoList = BeanCopyUtils.copyList(infoList, AssistInfoBo.class);
        if (CollectionUtils.isEmpty(infoBoList)) {
            return Collections.emptyList();
        }
        List<AssistInfoItemDo> infoItemList = getAssistInfoItemDos(Lists.newArrayList(assistRecordId));
        if (CollectionUtils.isEmpty(infoItemList)) {
            return infoBoList;
        }
        for (AssistInfoBo info : infoBoList) {
            List<AssistInfoItemDo> recordInfoItemList = infoItemList.stream().filter(f -> f.getAssistInfoId().equals(info.getId())).collect(Collectors.toList());
            info.setItemList(BeanCopyUtils.copyList(recordInfoItemList, AssistInfoItemBo.class));
        }
        return infoBoList;
    }

    /**
     * 协查处理 api
     *
     * @param request 协查处理请求对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleApi(AssistRecordHandleApiRequest request) {
        AssistRecordDo assistRecordDo = assistRecordRepository.findByAssistSerialNumber(request.getAssistSerialNumber());
        if (Objects.isNull(assistRecordDo) || assistRecordDo.getAuditResult() != AssistResultEnum.WAIT) {
            throw new AssistRecordServiceException(AssistResponseCode.ASSIST_RECORD_NULL_ERROR);
        }
        List<AssistAuditDto> collect = request.getAuditRelationList().stream().flatMap(item -> item.getSubAuditObj().stream()).collect(Collectors.toList());
        auditAssistRecordByMutileAuditTemp(request.getAssistSerialNumber(), serialNumber -> {
            AssistAuditContextBo assistAuditContextBo = AssistAuditContextBo.buildContext(BeanCopyUtils.copy(assistRecordDo, AssistRecordDto.class), collect, ASSIST_AUDIT_PLATFORM_MOBILE_FLAG, null, false);
            assistAuditContextBo.executeAllLevelStatusProcess();
        });
    }

    /**
     * 统计首页协查任务，待协查的事项数
     *
     * @param orgList orgList
     * @return 待协查的事项数
     */
    @Override
    public Long countItemAndAuditTimeIsNullByIndex(Collection<String> orgList) {
        long count = 0;
        try {
            List<Map<String, Object>> countList;
            if (CollectionUtils.isEmpty(orgList)) {
                countList = assistRecordRepository.countItemAndAuditTimeIsNullByIndex();
            } else {
                countList = assistRecordRepository.countItemAndAuditTimeIsNullByIndex(orgList);
            }
            if (CollectionUtils.isEmpty(countList)) {
                return count;
            }
            count = countList.stream().mapToLong(item -> (long) item.get("counts")).sum();
        } catch (Exception e) {
            LOGGER.error("countItemAndAuditTimeIsNullByIndex error");
        }
        return count;
    }
}
