package gov.derate.proof.common.utils;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UrlPathHelper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * <p>
 * 过滤器
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2020-08-10
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2020-08-10;
 */
public class WebCharFilterUtils {

    private final static Logger LOG = LoggerFactory.getLogger(WebCharFilterUtils.class);
    private String encoding;
    private String defaultFilter = "|,$,@,',\",\\\",(,),+,http,%,&lt;,&gt;,;,&amp;";
    private List<String> legalNameList;
    private Map<String, String> sysIllegalCharsMap = null;
    private AntPathMatcher antPathm = null;
    private UrlPathHelper urlhelper = null;

    public WebCharFilterUtils() throws ServletException {
        init();
    }

    public static byte[] checkFile(MultipartFile file, String suffixFormat, int fileSize) throws Exception {


        if (file == null || file.getInputStream() == null) {
            throw new IllegalArgumentException("未选择上传文件！");
        }
        int fileSizeM = fileSize * 1024 * 1024;
        String originalFilename = file.getOriginalFilename();
        FileUtil.checkOriginalFileNameSuffix(originalFilename);
        FileUtil.checkSuffixFormat(suffixFormat, originalFilename);
        byte[] bytes = FileUtil.checkImageIoRewriteImage(originalFilename, file.getBytes());
        int length = bytes.length;
        if (length == 0) {
            throw new IllegalArgumentException("不能上传空文件！");
        } else if (length > fileSizeM) {
            throw new IllegalArgumentException("文件大小不能超过" + fileSize + "M！");
        }
        return bytes;
    }

    public void init() throws ServletException {

        antPathm = new AntPathMatcher();
        urlhelper = new UrlPathHelper();
        WebCharFilterItem webCharFilterItem = getWebCharFilterItemByXml();

        legalNameList = webCharFilterItem.getLegalNameList();
        encoding = webCharFilterItem.getEncoding();
        defaultFilter = webCharFilterItem.getDefaultFilter();
        sysIllegalCharsMap = webCharFilterItem.getSysIllegalCharsMap();
    }

    /***
     * 根据配置文件获取配置文件初始化后配置实体类
     *
     * <AUTHOR>
     * @date 2017年10月31日 下午5:33:22
     * @return 网页拦截的数据对象
     * @throws ServletException
     */
    private WebCharFilterItem getWebCharFilterItemByXml() throws ServletException {
        try {
            return new WebCharFilterItem();
        } catch (Exception e) {
            LOG.error("Exception :" + e);
            throw new ServletException("初始化SysCharFilterVo参数配置解析sysCharFilter.xml 错误:" + e.getMessage());
        }
    }

    public void illegalCharsDoFilter(HttpServletRequest req, HttpServletResponse response, FilterChain filterChain)
            throws IOException, ServletException {

        // 获取当前请求连接排除项目名
        String lookupPath = urlhelper.getLookupPathForRequest(req);

        // 匹配请求连接是否配置有过滤规则如为空则默认为无过滤规则直接跳过
        String illegalCharsStr = getIllegalCharsByReqUrl(lookupPath);
        // 为空或者无需过滤关键字
        String noNeedFilter = "no_need_to_filter";
        if (StringUtils.isEmpty(illegalCharsStr) || noNeedFilter.equalsIgnoreCase(illegalCharsStr)) {
            filterChain.doFilter(req, response);
            return;
        }

        String[] illegalChars = illegalCharsStr.split(",");

        // 必须手动指定编码格式
        req.setCharacterEncoding(encoding);
        RequestIllegalCharsVO requestIllegalChars = new RequestIllegalCharsVO();
        requestIllegalChars.setRequestUrl(lookupPath);
        // 请求url包含非法字符
        if (urlIllegalRequset(lookupPath, illegalChars, requestIllegalChars).isIllegal()
                // 请求form表达中字段是否包含非法字符
                || formIllegalRequset(req, illegalChars, requestIllegalChars).isIllegal()
                // 请求url后面连接参数中字段是否包含非法字符
                || reqQueryStrIllegalRequset(req, illegalChars, requestIllegalChars).isIllegal()) {
            String logIllegalChars = requestIllegalChars.getIllegalChars();
            if (StringUtils.isBlank(logIllegalChars)) {
                logIllegalChars = "空格";
            }
            LOG.error("请求链接中存在非法字符： " + lookupPath + " 存在非法字符：" + logIllegalChars);
            inllegalResponseMsg(response, requestIllegalChars);
            return;
        }

        filterChain.doFilter(req, response);

    }

    public static boolean illegalCharsByStr(String... illegalCharsStrs) throws IOException, ServletException {
        return new WebCharFilterUtils().illegalCharsUplodFrom(illegalCharsStrs);
    }

    /***
     * 判断传入参数是否存在非法字符
     * <AUTHOR>
     *
     * 2018年11月6日
     */
    public boolean illegalCharsUplodFrom(String... illegalCharsStrs)
            throws IOException, ServletException {

        RequestIllegalCharsVO requestIllegalChars = new RequestIllegalCharsVO();
        List<String> illegalList = Lists.newArrayList(illegalCharsStrs);

        if (illegalCharsUplodFrom(illegalList, defaultFilter, requestIllegalChars).isIllegal()) {
            String logIllegalChars = requestIllegalChars.getIllegalChars();
            if (StringUtils.isBlank(logIllegalChars)) {
                logIllegalChars = "空格";
            }
            LOG.error("请求方法中存在非法字符： 存在非法字符：" + logIllegalChars);
            return true;
        }
        return false;
    }


    private RequestIllegalCharsVO illegalCharsUplodFrom(List<String> illegalList, String illegalCharsStr,
                                                        RequestIllegalCharsVO requestIllegalChars) {

        if (!illegalList.isEmpty()
                && StringUtils.isNotBlank(illegalCharsStr)) {
            for (int i = 0; i < illegalList.size(); i++) {
                String illegalStr = illegalList.get(i);
                String[] illegalChars = illegalCharsStr.split(",");
                String illegalChar = "";
                for (int j = 0; j < illegalChars.length; j++) {
                    illegalChar = illegalChars[j];
                    if (" ".equals(illegalChar)) {
                        illegalStr = illegalStr.replaceAll("\t", " ");
                    }
                    // 参数包含过滤规则内的非法字符
                    if (illegalStr.indexOf(illegalChar) != -1) {

                        requestIllegalChars.setIllegalChars(illegalChar);
                        requestIllegalChars.setIllegal(true);
                        return requestIllegalChars;
                    }
                }
            }
        }
        return requestIllegalChars;
    }

    /***
     * 对请求URL是否包含过滤规则中的非法字符 <br>
     * 存在非法字符则返回true 不存在则false;
     * <AUTHOR>
     * @date 2017年10月27日 上午11:16:05
     * @param lookupPath
     * @param illegalChars
     * @param requestIllegalChars
     * @return
     */
    private RequestIllegalCharsVO urlIllegalRequset(String lookupPath
            , String[] illegalChars, RequestIllegalCharsVO requestIllegalChars) {
        String illegalChar = "";
        for (int j = 0; j < illegalChars.length; j++) {
            illegalChar = illegalChars[j];
            if (StringUtils.isNoneBlank(illegalChar) && lookupPath.indexOf(illegalChar) != -1) {
                requestIllegalChars.setIllegalChars(illegalChar);
                requestIllegalChars.setIllegal(true);
                return requestIllegalChars;
            }
        }
        return requestIllegalChars;
    }

    /***
     * 请求url连接后面参数值是否包含过滤规则中非法字段 <br>
     * 存在非法字符则返回true 不存在则false;
     *
     * <AUTHOR>
     * @date 2017年10月27日 上午11:09:15
     * @param req
     * @param illegalChars
     * @return
     * @throws UnsupportedEncodingException
     */
    @SuppressWarnings("static-access")
    private RequestIllegalCharsVO reqQueryStrIllegalRequset(HttpServletRequest req,
                                                            String[] illegalChars,
                                                            RequestIllegalCharsVO requestIllegalChars)
            throws UnsupportedEncodingException {
        // 对请求参数进行判断
//        String param = req.getQueryString();
//
//        URLDecoder decoder = new URLDecoder();
//        String illegalChar = "";
//        if (StringUtils.isNotBlank(param)) {
//            // 请求参数值URL编码转汉字，过滤特殊字符百分号。
//            param = decoder.decode(param, "UTF-8");
//            for (int j = 0; j < illegalChars.length; j++) {
//                illegalChar = illegalChars[j];
//                if (!"&".equals(illegalChar)) {
//                    if (StringUtils.isNoneBlank(illegalChar) && param.contains(illegalChar)) {
//                        requestIllegalChars.setIllegalChars(illegalChar);
//                        requestIllegalChars.setIllegal(true);
//                        // 非法状态
//                        return requestIllegalChars;
//                    }
//                }
//            }
//        }

        return requestIllegalChars;
    }

    /***
     * 请求form表达中字段是否包含过滤规则中的非法字符 <br>
     * 存在非法字符则返回true 不存在则false;
     *
     * <AUTHOR>
     * @date 2017年10月27日 上午10:51:59
     * @param req
     * @param illegalChars
     * @return
     */
    @SuppressWarnings("rawtypes")
    private RequestIllegalCharsVO formIllegalRequset(HttpServletRequest req,
                                                     String[] illegalChars,
                                                     RequestIllegalCharsVO requestIllegalChars) {

        Enumeration params = req.getParameterNames();

        String illegalChar = "";
        String paramName = "";
        String paramValue = "";
        String[] paramValues = null;
        // 对参数名与参数进行判断
        while (params.hasMoreElements()) {

            paramName = (String) params.nextElement();
            // 密码字段名或表单免校验集合中字段名不做非法校验跳过循环
            if (paramName.toLowerCase().contains("password") || legalNameList.contains(paramName)) {
                continue;
            } else {
                paramValues = req.getParameterValues(paramName);
                for (int i = 0; i < paramValues.length; i++) {

                    paramValue = paramValues[i];
                    for (int j = 0; j < illegalChars.length; j++) {
                        illegalChar = illegalChars[j];
                        if (" ".equals(illegalChar)) {
                            paramValue = paramValue.replaceAll("\t", " ");
                        }
                        // 参数包含过滤规则内的非法字符
                        if (paramValue.indexOf(illegalChar) != -1) {

                            requestIllegalChars.setIllegalChars(illegalChar);
                            requestIllegalChars.setIllegal(true);
                            return requestIllegalChars;
                        }
                    }
                }
            }
        }
        return requestIllegalChars;
    }

    /***
     * 非法请求提示响应
     *
     * <AUTHOR>
     * @param res
     * @throws IOException
     * @date 2017年10月27日 上午10:35:20
     */
    private void inllegalResponseMsg(HttpServletResponse res, RequestIllegalCharsVO requestIllegalChars) throws IOException {
        // 必须手动指定编码格式
        res.setContentType("text/html;charset=" + encoding);
        res.setCharacterEncoding(encoding);
        res.setStatus(403);
        String illegalChars = requestIllegalChars.getIllegalChars();
        if (StringUtils.isBlank(illegalChars)) {
            illegalChars = "空格";
        }
        if (StringUtils.isNotBlank(illegalChars)) {

            String stash = "\\";
            String charStr = "'";
            if (charStr.equals(illegalChars)
                    || stash.equals(illegalChars)) {
                illegalChars = stash + illegalChars;
            }
        }
        String scriptJsAlertStr = "<script>window.alert('当前链接中存在非法字符: " + illegalChars + "');window.history.go(-1);</script>";
        res.getWriter().print(scriptJsAlertStr);
    }

    /***
     * 根据请求url获取url配置对应过滤规则数据
     *
     * <AUTHOR>
     * @date 2017年10月26日 下午5:33:37
     * @param tempUrl
     * @return
     */
    private String getIllegalCharsByReqUrl(String tempUrl) {

        List<String> matchList = new ArrayList<String>();
        String pStr = "";
        if (!CollectionUtils.sizeIsEmpty(sysIllegalCharsMap)) {
            for (Map.Entry<String, String> entry : sysIllegalCharsMap.entrySet()) {
                String name = entry.getKey();
                if (antPathm.match(name, tempUrl)) {
                    matchList.add(name);
                }
            }

            Collections.sort(matchList, new ReqMethodComparator(this.antPathm.getPatternComparator(tempUrl)));

            for (int i = 0; i < matchList.size(); i++) {
                System.out.println("排序后匹配出对应链接：" + matchList.get(i));
            }
            if (!matchList.isEmpty()) {
                pStr = matchList.get(0);
                System.out.println("最终获取：" + pStr);
                System.out.println("最终获取参数配置：" + sysIllegalCharsMap.get(pStr));
                return sysIllegalCharsMap.get(pStr);
            } else {
                pStr = defaultFilter;
            }
        }

        return pStr;
    }

    public class ReqMethodComparator implements Comparator<String> {

        private final Comparator<String> comparator;

        public ReqMethodComparator(Comparator<String> comparator) {
            this.comparator = comparator;
        }

        @Override
        public int compare(String compareStr1, String compareStr2) {
            return comparator.compare(compareStr1, compareStr2);
        }
    }

    /***
     * 系统自定义过滤配置实体类
     *
     * <AUTHOR>
     * @date 2017年10月31日 下午2:14:42
     */
    static class WebCharFilterItem implements Serializable {

        private Logger log = LoggerFactory.getLogger(WebCharFilterItem.class);
        private static final long serialVersionUID = -4374715078864258893L;
        private static final String PARSE_XML_FILE = "/webCharFilter.xml";
        private static final String REQ_URL_SPLIT = "\n|\r\n|\r";

        private String encoding;
        private List<String> legalNameList;
        private String defaultFilter;
        private String sqlFilter;
        private Map<String, String> sysIllegalCharsMap = new TreeMap<>();

        @SuppressWarnings("unchecked")
        public WebCharFilterItem() throws Exception {

            Document document = getParseXmlDocument(PARSE_XML_FILE);
            Element root = document.getRootElement();
            Element sysCharFilterNode = null;
            String filterNodeName = "";
            String legalNameStr = "";
            String errorStr = "系统自定义过滤配置：webCharFilter.xml文件中";
            for (Iterator<?> it = root.elementIterator(); it.hasNext(); ) {
                sysCharFilterNode = (Element) it.next();
                filterNodeName = sysCharFilterNode.getName();
                if ("encoding".equals(filterNodeName)) {

                    encoding = sysCharFilterNode.getTextTrim();
                    Assert.hasLength(encoding, errorStr + " encoding节点值为空!");
                }
                if ("legalNames".equals(filterNodeName)) {

                    legalNameStr = sysCharFilterNode.getTextTrim();
                    Assert.hasLength(legalNameStr, errorStr + " legalNames节点中值为空!");
                    legalNameList = Arrays.asList(legalNameStr.split(","));
                }
                if ("defaultFilterValue".equals(filterNodeName)) {
                    defaultFilter = sysCharFilterNode.getTextTrim();
                }
                if ("urlFilerList".equals(filterNodeName)) {
                    List<Element> xmlNodeList = sysCharFilterNode.elements();
                    Element urlFilerElem = null;
                    Element urlFilerNameElem = null;
                    Element urlFilerValueElem = null;
                    String nameNode = "";
                    String valueNode = "";
                    for (int i = 0; i < xmlNodeList.size(); i++) {

                        urlFilerElem = xmlNodeList.get(i);

                        urlFilerNameElem = (Element) urlFilerElem.selectSingleNode("requestUrl");
                        urlFilerValueElem = (Element) urlFilerElem.selectSingleNode("filerValue");


                        Assert.notNull(urlFilerNameElem, errorStr + " 过滤链接requestFiler 节点中缺少requestUrl节点数据!");
                        Assert.notNull(urlFilerValueElem, errorStr + " 过滤链接requestFiler 节点中缺少filerValue节点数据!");

                        nameNode = urlFilerNameElem.getText();
                        valueNode = urlFilerValueElem.getTextTrim();
                        Assert.hasLength(nameNode, errorStr + " 过滤链接requestFiler 节点requestUrl值为空!!");
                        Assert.hasLength(valueNode, errorStr + " 过滤链接requestFiler 节点filerValue值为空!!");
                        //存在多个相同过滤规则链接
                        String[] reqUrls = nameNode.split(REQ_URL_SPLIT);
                        for (int j = 0; j < reqUrls.length; j++) {
                            String reqUrl = reqUrls[j];
                            if (StringUtils.isNotBlank(reqUrl)) {
                                sysIllegalCharsMap.put(reqUrl.trim(), valueNode);
                            }
                        }
                    }
                }
            }

            Assert.notNull(encoding, errorStr + " encoding节点中缺少数据!");
            Assert.notNull(legalNameStr, errorStr + " legalNames节点中缺少数据!");
        }

        /**
         * @param parseXmlPath 待解析的xml文件路径,该路径必须要在classpath中能找到
         * @throws Exception
         * @method getParseXmlDocument
         * @方法描述：获取指定xml文档的对象document
         */
        private static Document getParseXmlDocument(String parseXmlPath) throws Exception {

            SAXReader reader = new SAXReader();
            Document document = null;
            InputStream inputStream = WebCharFilterItem.class.getResourceAsStream(parseXmlPath);

            Assert.notNull(inputStream, "无法读取系统自定义过滤规则xml文件,xml路径为：" + PARSE_XML_FILE);

            document = reader.read(inputStream);
            return document;
        }

        public String getEncoding() {
            return encoding;
        }


        public List<String> getLegalNameList() {
            return legalNameList;
        }

        public String getDefaultFilter() {
            return defaultFilter;
        }

        public Map<String, String> getSysIllegalCharsMap() {
            return sysIllegalCharsMap;
        }

        public String getSqlFilter() {
            return sqlFilter;
        }

        public void setSqlFilter(String sqlFilter) {
            this.sqlFilter = sqlFilter;
        }

    }

    /***
     * 非法字符相关实体
     * <AUTHOR>
     * @date 2018年1月11日 上午9:39:02
     */
    static class RequestIllegalCharsVO {
        /**
         * 请求url
         **/
        private String requestUrl;
        /**
         * 需过滤的字符串
         **/
        private String illegalCharsStr;
        /**
         * 非法字符
         **/
        private String illegalChars;
        /**
         * 是否非法 true 为非法 false为合法
         * <br>默认false
         */
        private Boolean illegal;


        public Boolean isIllegal() {
            return illegal;
        }

        public void setIllegal(Boolean illegal) {
            this.illegal = illegal;
        }

        public String getRequestUrl() {
            return requestUrl;
        }

        public void setRequestUrl(String requestUrl) {
            this.requestUrl = requestUrl;
        }

        public String getIllegalCharsStr() {
            return illegalCharsStr;
        }

        public void setIllegalCharsStr(String illegalCharsStr) {
            this.illegalCharsStr = illegalCharsStr;
        }

        public String getIllegalChars() {
            return illegalChars;
        }

        public void setIllegalChars(String illegalChars) {
            this.illegalChars = illegalChars;
        }

        @Override
        public String toString() {
            return "RequestIllegalCharsVO{" +
                    "requestUrl='" + requestUrl + '\'' +
                    ", illegalCharsStr='" + illegalCharsStr + '\'' +
                    ", illegalChars='" + illegalChars + '\'' +
                    ", illegal=" + illegal +
                    '}';
        }
    }
}
