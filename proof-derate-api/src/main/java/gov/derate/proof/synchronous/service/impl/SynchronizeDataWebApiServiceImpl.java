package gov.derate.proof.synchronous.service.impl;

import com.google.common.collect.Lists;
import gov.derate.proof.common.enums.*;
import gov.derate.proof.common.query.ListCondition;
import gov.derate.proof.common.repository.SimpleJpaNoCountRepository;
import gov.derate.proof.common.utils.BeanCopyUtils;
import gov.derate.proof.common.utils.UserUtils;
import gov.derate.proof.item.entity.ItemChangeLogDo;
import gov.derate.proof.item.entity.ItemDo;
import gov.derate.proof.item.entity.ItemMaterialDo;
import gov.derate.proof.item.entity.ItemProofRelationDo;
import gov.derate.proof.item.repository.ItemMaterialRepository;
import gov.derate.proof.list.entity.ProofListDo;
import gov.derate.proof.list.entity.ProofListProcedureDo;
import gov.derate.proof.list.entity.ReplaceLicenseDo;
import gov.derate.proof.list.repository.ProofListProcedureRepository;
import gov.derate.proof.list.repository.ProofListRepository;
import gov.derate.proof.list.repository.ReplaceLicenseRepository;
import gov.derate.proof.synchronous.entity.MaterialAttachmentDo;
import gov.derate.proof.synchronous.entity.MiddleItemDo;
import gov.derate.proof.synchronous.entity.MiddleItemMaterialDo;
import gov.derate.proof.synchronous.entity.SynchronizeLogDo;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;
import gov.derate.proof.synchronous.entity.vo.MaterialAttachmentVo;
import gov.derate.proof.synchronous.entity.vo.MiddleItemVo;
import gov.derate.proof.synchronous.entity.vo.MiddleMaterialVo;
import gov.derate.proof.synchronous.entity.vo.ProofListProcedureVo;
import gov.derate.proof.synchronous.query.MiddleItemListQuery;
import gov.derate.proof.synchronous.repository.MaterialAttachmentRepository;
import gov.derate.proof.synchronous.repository.MiddleItemMaterialRepository;
import gov.derate.proof.synchronous.repository.MiddleItemRepository;
import gov.derate.proof.synchronous.repository.SynchronizeLogRepository;
import gov.derate.proof.synchronous.req.MiddleItemListRequest;
import gov.derate.proof.synchronous.req.SynchronizeDetailsRequest;
import gov.derate.proof.synchronous.resp.SynchronizeDetailsResponse;
import gov.derate.proof.synchronous.resp.SynchronizeListResponse;
import gov.derate.proof.synchronous.service.SynchronizeDataWebApiService;
import gov.license.common.api.resp.data.BasePageRespData;
import gov.license.jpa.PredicateBuilder;
import gov.license.jpa.Specifications;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 同步数据服务
 *
 * <AUTHOR>
 * @date 2022/4/24.
 */
@Service
public class SynchronizeDataWebApiServiceImpl implements SynchronizeDataWebApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SynchronizeDataWebApiServiceImpl.class);

    @Autowired
    private SynchronizeLogRepository synchronizeLogRepository;
    @Autowired
    private MiddleItemRepository middleItemRepository;
    @Autowired
    private MiddleItemMaterialRepository middleItemMaterialRepository;
    @Autowired
    private ItemMaterialRepository itemMaterialRepository;
    @Autowired
    private MaterialAttachmentRepository materialAttachmentRepository;
    @Autowired
    private ProofListRepository proofListRepository;
    @Autowired
    private ReplaceLicenseRepository replaceLicenseRepository;
    @Autowired
    private ProofListProcedureRepository proofListProcedureRepository;
    @PersistenceContext
    private EntityManager em;
    /**
     * 编辑事项名称
     */
    public static final String EDIT_OPERATION_STR = "编辑事项";

    @Override
    public BasePageRespData<List<SynchronizeListResponse>> querySynchronizeList(MiddleItemListRequest listVo, Pageable page) {
        MiddleItemListQuery query = new MiddleItemListQuery(listVo);
        LOGGER.debug("querySynchronizeList queryPage sqlParamObj is [{}]", query);
        Page<MiddleItemDo> viewPageData = middleItemRepository.querySynchronizeList(query, page);
        if (Objects.nonNull(viewPageData) && !viewPageData.isEmpty()) {
            List<SynchronizeListResponse> content = viewPageData.getContent().stream()
                    .map(item -> {
                        SynchronizeListResponse response = new SynchronizeListResponse();
                        response.setMiddleItemId(item.getId());
                        response.setItemCode(item.getTaskCode());
                        response.setItemName(item.getTaskName());
                        response.setDeptName(item.getDeptName());
                        response.setItemType(item.getTaskType());
                        response.setItemStatus(item.getTaskState());
                        response.setFailedResult(item.getFailedResult());
                        response.setSynchronizeStatus(item.getSynchronizeStatus());
                        response.setLastModificationTime(item.getLastModificationTime());
                        response.setSituationName(item.getSituationName());
                        return response;
                    }).collect(Collectors.toList());
            return new BasePageRespData<>(viewPageData.getNumber(), viewPageData.getSize(),
                    viewPageData.getTotalElements(), viewPageData.getTotalPages(), content);
        } else {
            return new BasePageRespData<>(viewPageData.getNumber(), viewPageData.getSize(),
                    viewPageData.getTotalElements(), viewPageData.getTotalPages(), Lists.newArrayList());
        }
    }

    @Override
    public BasePageRespData<Long> querySynchronizeListCount(MiddleItemListRequest listVo, Pageable pageable) {
        Long totalCount = 0L;
        MiddleItemListQuery queryObj = new MiddleItemListQuery(listVo);
        totalCount = middleItemRepository.countSynchronizeListView(queryObj);
        PageImpl<Object> page = new PageImpl<>(Lists.newArrayList(), pageable, totalCount);
        return new BasePageRespData<>(page.getNumber(), page.getSize(), page.getTotalElements(), page.getTotalPages(), totalCount);
    }

    /**
     * 转换对象
     *
     * @param item     中间表事项DO
     * @param response 响应response对象
     */
    private void convertByDo(@NotNull MiddleItemDo item, @NotNull SynchronizeListResponse response) {
        response.setMiddleItemId(item.getId());
        response.setItemCode(item.getTaskCode());
        response.setItemName(item.getTaskName());
        response.setDeptName(item.getDeptName());
        response.setItemType(item.getTaskType());
        response.setItemStatus(item.getTaskState());
        response.setAcceptCondition(item.getAcceptCondition());
        response.setFailedResult(item.getFailedResult());
        response.setSynchronizeStatus(item.getSynchronizeStatus());
        response.setLastModificationTime(item.getLastModificationTime());
        response.setSituationName(item.getSituationName());
    }

    @Override
    public SynchronizeDetailsResponse querySynchronizeDetails(SynchronizeDetailsRequest listVo) {
        SynchronizeDetailsResponse synchronizeDetailsResponse = new SynchronizeDetailsResponse();
        //获取事项信息
        Optional<MiddleItemDo> middleItemDoOptional = middleItemRepository.findById(listVo.getMiddleItemId());
        MiddleItemDo middleItemDo = middleItemDoOptional.orElseThrow(() -> new RuntimeException("事项中间表查询为空"));

        //获取材料信息
        List<MiddleItemMaterialDo> middleItemMaterialDoList = middleItemMaterialRepository.findAllByItemGuid(middleItemDo.getRowGuid());

        //获取材料附件
        List<String> materialIdList = middleItemMaterialDoList.stream().map(MiddleItemMaterialDo::getMaterialId).collect(Collectors.toList());
        List<MaterialAttachmentDo> materialAttachmentDoList = materialAttachmentRepository.findAllByMaterialIdIn(materialIdList);

        buildBaseSynchronizeDetails(middleItemDo, middleItemMaterialDoList, materialAttachmentDoList, synchronizeDetailsResponse);
        synchronizeDetailsResponse.getMiddleItemVo().setSynchronizeStatus(listVo.getSynchronizeStatus());
        if (listVo.getSynchronizeStatus().equals(SynchronizeStatusEnum.SYNCHRONIZE_FAILED)) {
            //根据事项编码查询无证明城市中的材料
            List<ItemMaterialDo> itemMaterialDoList = itemMaterialRepository.findAllByItemCode(listVo.getItemCode());
            //材料进行比对
            StringBuilder changeDetails = new StringBuilder();
            for (ItemMaterialDo itemMaterialDo : itemMaterialDoList) {
                boolean flag = true;
                for (MiddleItemMaterialDo middleItemMaterialDo : middleItemMaterialDoList) {
                    if (itemMaterialDo.getMaterialBizId().equals(middleItemMaterialDo.getMaterialId())) {
                        flag = false;
                        break;
                    }
                }
                //保存删除材料的信息
                if (flag) {
                    if (StringUtils.isBlank(changeDetails)) {
                        changeDetails.append("《").append(itemMaterialDo.getMaterialName()).append("》");
                        continue;
                    }
                    changeDetails.append(",").append("《").append(itemMaterialDo.getMaterialName()).append("》");
                }
            }
            //找出新增的材料
            List<MiddleMaterialVo> middleMaterialVoList = synchronizeDetailsResponse.getMiddleMaterialVoList();
            List<ProofListDo> proofListDoList = proofListRepository.findAllByItemCode(middleItemDo.getTaskCode());
            List<String> proofListIds = proofListDoList.stream().map(ProofListDo::getId).collect(Collectors.toList());
            List<ReplaceLicenseDo> replaceLicenseDoList = replaceLicenseRepository.findByProofListIdIn(proofListIds);
            for (MiddleMaterialVo middleMaterialVo : middleMaterialVoList) {
                boolean flag = true;
                for (ItemMaterialDo itemMaterialDo : itemMaterialDoList) {
                    if (middleMaterialVo.getMaterialBizId().equals(itemMaterialDo.getMaterialBizId())) {
                        middleMaterialVo.setMaterialId(itemMaterialDo.getId());
                        flag = false;
                        break;
                    }
                }
                if (flag) {
                    middleMaterialVo.setChangeDetails("新增材料");
                }
                //根据材料查询证明清单
                for (ProofListDo proofListDo : proofListDoList) {
                    if (verifyString(middleMaterialVo.getMaterialId()).equals(verifyString(proofListDo.getMaterialId()))) {
                        if (!verifyString(middleMaterialVo.getSubmissionMeasure()).contains("3") && verifyString(proofListDo.getReplaceCancelWay()).contains("0")) {
                            middleMaterialVo.setChangeDetails("免证替代的电子证照方式有变更");
                        }
                        if (verifyString(middleMaterialVo.getSubmissionMeasure()).contains("3") && !verifyString(proofListDo.getReplaceCancelWay()).contains("0")) {
                            middleMaterialVo.setChangeDetails("免证替代的电子证照方式有变更");
                        }
                        //代替取消方式都是电子证照，判断证照编码是否相同
                        if (verifyString(middleMaterialVo.getSubmissionMeasure()).contains("3") && verifyString(proofListDo.getReplaceCancelWay()).contains("0")) {
                            for (ReplaceLicenseDo replaceLicenseDo : replaceLicenseDoList) {
                                if (proofListDo.getId().equals(replaceLicenseDo.getProofListId())) {
                                    if (!replaceLicenseDo.getLicenseCode().equals(middleMaterialVo.getLicenseCode())) {
                                        middleMaterialVo.setChangeDetails("关联的电子证照有变更");
                                    }
                                }

                            }
                        }
                    }
                }
            }
            synchronizeDetailsResponse.setFailResult(middleItemDo.getFailedResult());
            synchronizeDetailsResponse.setChangeDetails("");
            if (StringUtils.isNoneBlank(changeDetails)) {
                synchronizeDetailsResponse.setChangeDetails(" 删除的证明材料:" + changeDetails);
            }
        }
        return synchronizeDetailsResponse;
    }

    @Override
    public void ignoreSynchronize(String middleItemId) {
        Optional<MiddleItemDo> middleItemDoOptional = middleItemRepository.findById(middleItemId);
        //获取中间表需要忽略数据
        MiddleItemDo middleItemDo = middleItemDoOptional.orElse(new MiddleItemDo());
        SynchronizeLogDo synchronizeLogDo = new SynchronizeLogDo();
        synchronizeLogDo.setItemGuid(middleItemDo.getRowGuid());
        synchronizeLogDo.setItemCode(middleItemDo.getTaskCode());
        synchronizeLogDo.setItemName(middleItemDo.getTaskName());
        synchronizeLogDo.setImplOrgName(middleItemDo.getDeptName());
        synchronizeLogDo.setItemType(middleItemDo.getTaskType());
        synchronizeLogDo.setItemStatus(middleItemDo.getTaskState());
        synchronizeLogDo.setSynchronizeStatus(SynchronizeStatusEnum.IGNORE_SYNCHRONIZE);
        synchronizeLogDo.setMiddleItemTime(middleItemDo.getCreationTime());
        synchronizeLogDo.setLogSource(true);
        SynchronizeLogDo existSynchronizeLogDo = synchronizeLogRepository.findAllByItemGuid(middleItemDo.getRowGuid());
        if (Objects.nonNull(existSynchronizeLogDo)) {
            if (existSynchronizeLogDo.getSynchronizeStatus().equals(SynchronizeStatusEnum.SYNCHRONIZE_SUCCESS)) {
                throw new RuntimeException("该事项已同步成功，无法忽略");
            }
            synchronizeLogDo.setId(existSynchronizeLogDo.getId());
        }
        //操作日志
        ProofListProcedureDo proofListProcedureDo = new ProofListProcedureDo();
        proofListProcedureDo.setAccount(UserUtils.getUserName());
        proofListProcedureDo.setAccountName(UserUtils.getUserName());
        proofListProcedureDo.setProcedureDate(new Date());
        proofListProcedureDo.setItemCode(middleItemDo.getTaskCode());
        proofListProcedureDo.setItemProofStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);
        proofListProcedureDo.setProcedureMessage("忽略");
        proofListProcedureDo.setOperatorLog(OperatorLogEnum.SYNCHRONIZE_IGNORE);
        proofListProcedureRepository.save(proofListProcedureDo);
        synchronizeLogRepository.save(synchronizeLogDo);

    }

    @Override
    public ProofListProcedureVo getSynchronizeOperatorLog(String itemCode, OperatorLogEnum operatorLogEnum) {
        ProofListProcedureVo proofListProcedureVo = new ProofListProcedureVo();
        List<ProofListProcedureDo> allByItemCodeAndOperatorLog = proofListProcedureRepository.findAllByItemCodeAndOperatorLog(itemCode, operatorLogEnum);
        if (CollectionUtils.isEmpty(allByItemCodeAndOperatorLog)) {
            return null;
        }
        ProofListProcedureDo proofListProcedureDo = allByItemCodeAndOperatorLog.get(0);
        BeanCopyUtils.copy(proofListProcedureDo, proofListProcedureVo);
        return proofListProcedureVo;
    }

    /**
     * 构建基本的同步数据详情
     *
     * @param middleItemDo               事项
     * @param middleItemMaterialDoList   材料列表
     * @param materialAttachmentDoList   材料附件列表
     * @param synchronizeDetailsResponse 同步数据详情响应体
     */
    private void buildBaseSynchronizeDetails(MiddleItemDo middleItemDo,
                                             List<MiddleItemMaterialDo> middleItemMaterialDoList,
                                             List<MaterialAttachmentDo> materialAttachmentDoList,
                                             SynchronizeDetailsResponse synchronizeDetailsResponse) {
        MiddleItemVo middleItemVo = new MiddleItemVo();
        middleItemVo.setItemCode(middleItemDo.getTaskCode());
        middleItemVo.setItemName(middleItemDo.getTaskName());
        middleItemVo.setImplOrgName(middleItemDo.getDeptName());
        middleItemVo.setDivisionCode(middleItemDo.getAreaCode());
        middleItemVo.setTaskVersion(middleItemDo.getTaskVersion());
        middleItemVo.setItemType(middleItemDo.getTaskType());
        middleItemVo.setProjectType(middleItemDo.getProjectType());
        middleItemVo.setLastModificationTime(middleItemDo.getLastModificationTime());
        middleItemVo.setSituationCode(middleItemDo.getSituationCode());
        middleItemVo.setSituationName(middleItemDo.getSituationName());

        //构建材料
        List<MiddleMaterialVo> middleMaterialVoList = Lists.newArrayList();
        for (MiddleItemMaterialDo middleItemMaterialDo : middleItemMaterialDoList) {
            MiddleMaterialVo middleMaterialVo = new MiddleMaterialVo();
            middleMaterialVo.setMaterialName(middleItemMaterialDo.getMaterialName());
            middleMaterialVo.setMaterialType(middleItemMaterialDo.getMaterialType());
            middleMaterialVo.setLicenseCode(middleItemMaterialDo.getLicenseCode());
            middleMaterialVo.setLicenseName(middleItemMaterialDo.getLicenseName());
            middleMaterialVo.setMaterialBizId(middleItemMaterialDo.getMaterialId());
            middleMaterialVo.setSubmissionMeasure(middleItemMaterialDo.getSubmissionMeasure());
            //根据材料附件类型分离附件
            List<MaterialAttachmentVo> sampleFileList = Lists.newArrayList();
            List<MaterialAttachmentVo> blankFileList = Lists.newArrayList();
            for (MaterialAttachmentDo materialAttachmentDo : materialAttachmentDoList) {
                MaterialAttachmentVo materialAttachmentVo = new MaterialAttachmentVo();
                if (middleItemMaterialDo.getMaterialId().equals(materialAttachmentDo.getMaterialId())) {
                    BeanCopyUtils.copy(materialAttachmentDo, materialAttachmentVo);
                    if (AttachmentTypeEnum.SAMPLE_FILE.equals(materialAttachmentDo.getAttachmentType())) {
                        sampleFileList.add(materialAttachmentVo);
                        continue;
                    }
                    blankFileList.add(materialAttachmentVo);
                }
            }
            middleMaterialVo.setSampleFileList(sampleFileList);
            middleMaterialVo.setBlankFileList(blankFileList);
            middleMaterialVoList.add(middleMaterialVo);
        }
        synchronizeDetailsResponse.setMiddleItemVo(middleItemVo);
        synchronizeDetailsResponse.setMiddleMaterialVoList(middleMaterialVoList);
    }

    /**
     * 同步事项存在当前系统并且事项状态不为"取消"的数据(不完全一致)
     *
     * @param context 上下文
     */
    private void existItemSynchronizeDiff(SynchronizeDataContext context) {
        if (context.getMiddleItemMaterialDoList().size() != context.getItemMaterialDoList().size()) {
            //事项状态为待清理
            itemClearStatusWait(context);
            //事项状态为已清理
            existItemSynchronizeDiffNoWait(context);
        }
    }

    /**
     * 同步事项存在当前系统并且事项状态不为"取消"的数据(完全一致)
     *
     * @param context 上下文
     */
    private void existItemSynchronizeSame(SynchronizeDataContext context) {
        if (context.getMiddleItemMaterialDoList().size() == context.getItemMaterialDoList().size()) {
            //事项清理状态为待清理
            itemClearStatusWait(context);
            //事项清理状态为已清理
            existItemSynchronizeSameNoWait(context);
        }
    }

    /**
     * 事项状态为待清理时的同步操作
     *
     * @param context 上下文
     */
    private void itemClearStatusWait(SynchronizeDataContext context) {
        if (ItemProofStatusEnum.WAIT_FOR_CLEAN.equals(context.getItemProofRelation().getItemProofStatus())) {
            synchronizeItemAndMaterial(context);
            context.getSynchronizeLogDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_SUCCESS);
        }
    }

    /**
     * 同步事项存在当前系统并且事项状态不为"取消"的数据(完全一致)----事项状态不是待清理时
     *
     * @param context 上下文
     */
    private void existItemSynchronizeSameNoWait(SynchronizeDataContext context) {
        if (!ItemProofStatusEnum.WAIT_FOR_CLEAN.equals(context.getItemProofRelation().getItemProofStatus())) {
            //事项系统与无证明城市系统的代替取消方式为电子证照时必须相同
            List<MiddleItemMaterialDo> middleItemMaterialDoList = context.getMiddleItemMaterialDoList();
            List<ProofListDo> proofListDoList = context.getProofListDoList();
            List<ProofListDo> proofListDoListUpdate = Lists.newArrayList();
            List<ReplaceLicenseDo> replaceLicenseDoList = context.getReplaceLicenseDoList();

            //将材料id赋予中间表材料
            List<ItemMaterialDo> itemMaterialDoList = context.getItemMaterialDoList();
            List<MiddleItemMaterialDo> middleItemMaterialDoListUpdate = Lists.newArrayList();
            for (MiddleItemMaterialDo middleItemMaterialDo : middleItemMaterialDoList) {
                for (ItemMaterialDo itemMaterialDo : itemMaterialDoList) {
                    if (middleItemMaterialDo.getMaterialId().equals(itemMaterialDo.getMaterialBizId())) {
                        middleItemMaterialDo.setMaterialRowGuid(itemMaterialDo.getId());
                        middleItemMaterialDoListUpdate.add(middleItemMaterialDo);
                    }
                }
            }
            boolean flag = false;
            for (ProofListDo proofListDo : proofListDoList) {
                MiddleItemMaterialDo materialDo = null;
                for (MiddleItemMaterialDo middleItemMaterialDo : middleItemMaterialDoListUpdate) {
                    //根据材料id确定材料和清单关联
                    if (proofListDo.getMaterialId().equals(middleItemMaterialDo.getMaterialRowGuid())) {
                        if (!verifyString(proofListDo.getReplaceCancelWay()).contains("0") && verifyString(middleItemMaterialDo.getSubmissionMeasure()).contains("3")) {
                            existItemSynchronizeLicenseDiff(context);
                            return;
                        }
                        if (verifyString(proofListDo.getReplaceCancelWay()).contains("0") && !verifyString(middleItemMaterialDo.getSubmissionMeasure()).contains("3")) {
                            existItemSynchronizeLicenseDiff(context);
                            return;
                        }
                        //代替方式都是电子证照，但是证照编码相同和不同的情况
                        if (verifyString(proofListDo.getReplaceCancelWay()).contains("0") && verifyString(middleItemMaterialDo.getSubmissionMeasure()).contains("3")) {
                            materialDo = middleItemMaterialDo;
                        }
                        //更新材料
                        proofListDo.setItemCode(context.getItemDo().getItemCode());
                        proofListDo.setProofName(middleItemMaterialDo.getMaterialName());
                        proofListDo.setItemMaterialSource(ItemMaterialSourceEnum.STANDARD_MATERIAL);
                    }
                }
                //判断电子证照编码是否一致
                for (ReplaceLicenseDo replaceLicenseDo : replaceLicenseDoList) {
                    if (proofListDo.getId().equals(replaceLicenseDo.getProofListId())) {
                        if (Objects.nonNull(materialDo)) {
                            if (!materialDo.getLicenseCode().equals(replaceLicenseDo.getLicenseCode())) {
                                flag = true;
                            }
                        }
                    }
                }
                proofListDoListUpdate.add(proofListDo);
            }

            //电子证照编码一致
            if (!flag) {

                synchronizeItemAndMaterial(context);
                context.getSynchronizeLogDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_SUCCESS);
                context.setProofListDoList(proofListDoListUpdate);
            }
            //电子证照编码不一致
            if (flag) {
                synchronizeItemAndMaterial(context);
                context.getSynchronizeLogDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_FAILED);
                context.getSynchronizeLogDo().setFailedResult("关联的电子证照有变更");
            }
        }
    }

    /**
     * 同步事项存在当前系统并且事项状态不为"取消"的数据(完全不一致)----事项状态不是待清理时
     *
     * @param context 上下文
     */
    private void existItemSynchronizeDiffNoWait(SynchronizeDataContext context) {
        if (!ItemProofStatusEnum.WAIT_FOR_CLEAN.equals(context.getItemProofRelation().getItemProofStatus())) {
            synchronizeItemAndMaterial(context);
            SynchronizeLogDo synchronizeLogDo = context.getSynchronizeLogDo();
            synchronizeLogDo.setFailedResult("事项下增加或删除了证明，需要重新梳理");
            synchronizeLogDo.setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_FAILED);
            context.setSynchronizeLogDo(synchronizeLogDo);
        }
    }

    /**
     * 代替取消方式中有一个不存在电子证照
     *
     * @param context 上下文
     */
    private void existItemSynchronizeLicenseDiff(SynchronizeDataContext context) {
        synchronizeItemAndMaterial(context);
        context.getSynchronizeLogDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_FAILED);
        context.getSynchronizeLogDo().setFailedResult("免证替换的电子证照方式有变更");
    }

    /**
     * 同步事项以及下面的材料到无证明城市系统中
     *
     * @param context 上下文数据
     */
    private void synchronizeItemAndMaterial(SynchronizeDataContext context) {
        MiddleItemDo middleItemDo = context.getMiddleItemDo();
        SynchronizeLogDo synchronizeLogDo = context.getSynchronizeLogDo();
        List<ItemChangeLogDo> itemChangeLogDoList = Lists.newArrayList();
        if (Objects.isNull(synchronizeLogDo)) {
            synchronizeLogDo = new SynchronizeLogDo();
        }
        ItemDo itemDo = new ItemDo();
        buildItemDo(itemDo, middleItemDo);
        itemDo.setId(context.getItemDo().getId());
        ItemProofRelationDo itemProofRelation = context.getItemProofRelation();
        if (Objects.nonNull(itemProofRelation)) {
            itemProofRelation.setItemProofStatus(itemProofRelation.getItemProofStatus());
        } else {
            throw new RuntimeException("synchronizeItemAndMaterial itemProofRelation is null");
        }
        context.setItemProofRelation(itemProofRelation);
        //校验事项是否更新
        verifyItemChangeLog(itemChangeLogDoList, context.getItemDo(), itemDo);

        buildSynchronizeLog(synchronizeLogDo, middleItemDo);
        //更新材料
        List<ItemMaterialDo> itemMaterialDoList = Lists.newArrayList();
        List<ItemMaterialDo> itemMaterialDoListUpdate = Lists.newArrayList();
        for (MiddleItemMaterialDo middleItemMaterialDo : context.getMiddleItemMaterialDoList()) {
            ItemMaterialDo itemMaterialDo = new ItemMaterialDo();
            itemMaterialDo.setItemCode(itemDo.getItemCode());
            buildMaterial(itemMaterialDo, middleItemMaterialDo);

            itemMaterialDo.setItemMaterialSource(ItemMaterialSourceEnum.STANDARD_MATERIAL);
            itemMaterialDoList.add(itemMaterialDo);
            //记录事项所关联材料的获取时间，只记录最大值
            if (synchronizeLogDo.getMiddleMaterialTime() == null || middleItemMaterialDo.getCreationTime().after(synchronizeLogDo.getMiddleMaterialTime())) {
                synchronizeLogDo.setMiddleMaterialTime(middleItemMaterialDo.getCreationTime());
            }
        }
        //已存在无证明系统的材料赋予材料id
        for (ItemMaterialDo noExistItemMaterialDo : itemMaterialDoList) {
            for (ItemMaterialDo existItemMaterialDo : context.getItemMaterialDoList()) {
                if (existItemMaterialDo.getMaterialBizId().equals(noExistItemMaterialDo.getMaterialBizId())) {
                    noExistItemMaterialDo.setId(existItemMaterialDo.getId());
                    noExistItemMaterialDo.setItemClearStatus(existItemMaterialDo.getItemClearStatus());
                    verifyMaterialChangeLog(itemChangeLogDoList, existItemMaterialDo, noExistItemMaterialDo, itemDo.getId());
                    itemMaterialDoListUpdate.add(noExistItemMaterialDo);
                }
            }
        }
        context.setItemDo(itemDo);
        context.setItemMaterialDoList(itemMaterialDoListUpdate);
        context.setSynchronizeLogDo(synchronizeLogDo);
        context.setItemChangeLogDoList(itemChangeLogDoList);
    }

    /**
     * 构建当前系统事项对象
     *
     * @param itemDo       需要同步的事项
     * @param middleItemDo 待同步的事项
     */
    private void buildItemDo(ItemDo itemDo, MiddleItemDo middleItemDo) {
        itemDo.setSyncItemId(middleItemDo.getItemId());
        itemDo.setItemCode(middleItemDo.getTaskCode());
        itemDo.setItemName(middleItemDo.getTaskName());
        itemDo.setCreditCode(middleItemDo.getTongYiCode());
        itemDo.setImplOrgName(middleItemDo.getDeptName());
        itemDo.setDivisionCode(middleItemDo.getAreaCode());
        itemDo.setItemType(middleItemDo.getTaskType());
        itemDo.setItemSource(middleItemDo.getItemSource());
        itemDo.setItemStatus(middleItemDo.getTaskState());
        itemDo.setProjectType(middleItemDo.getProjectType());
    }

    /**
     * 构建同步记录对象
     *
     * @param synchronizeLogDo 需要同步的记录
     * @param middleItemDo     待同步的记录
     */
    private void buildSynchronizeLog(SynchronizeLogDo synchronizeLogDo, MiddleItemDo middleItemDo) {
        //记录同步日志
        synchronizeLogDo.setItemCode(middleItemDo.getTaskCode());
        synchronizeLogDo.setItemName(middleItemDo.getTaskName());
        if (StringUtils.isNotBlank(middleItemDo.getSituationCode())) {
            synchronizeLogDo.setItemCode(middleItemDo.getSituationCode());
            synchronizeLogDo.setItemName(middleItemDo.getSituationName());
        }
        synchronizeLogDo.setImplOrgName(middleItemDo.getDeptName());
        synchronizeLogDo.setItemType(middleItemDo.getTaskType());
        synchronizeLogDo.setItemStatus(middleItemDo.getTaskState());
        synchronizeLogDo.setItemGuid(middleItemDo.getRowGuid());
        synchronizeLogDo.setMiddleItemTime(middleItemDo.getCreationTime());
        synchronizeLogDo.setLogSource(true);
    }

    /**
     * 构建材料对象
     *
     * @param itemMaterialDo       需要同步的对象
     * @param middleItemMaterialDo 待同步的对象
     */
    private void buildMaterial(ItemMaterialDo itemMaterialDo, MiddleItemMaterialDo middleItemMaterialDo) {
        itemMaterialDo.setMaterialName(middleItemMaterialDo.getMaterialName());
        itemMaterialDo.setMaterialType(middleItemMaterialDo.getMaterialType());
        itemMaterialDo.setLicenseCode(middleItemMaterialDo.getLicenseCode());
        itemMaterialDo.setLicenseName(middleItemMaterialDo.getLicenseName());
        itemMaterialDo.setMaterialSource(null);
        if (Objects.nonNull(middleItemMaterialDo.getSourceType())) {
            final String sourceTypeSelfProvided = "10";
            final String sourceTypeGovernmentProvided = "20";
            final String sourceTypeOther = "99";
            switch (middleItemMaterialDo.getSourceType().getCode()) {
                case sourceTypeSelfProvided:
                    String selfProvided = "01";
                    itemMaterialDo.setMaterialSource(selfProvided);
                    break;
                case sourceTypeGovernmentProvided:
                    String governmentProvided = "02";
                    itemMaterialDo.setMaterialSource(governmentProvided);
                    break;
                case sourceTypeOther:
                    String other = "03";
                    itemMaterialDo.setMaterialSource(other);
                    break;
                default:
                    break;
            }
        }
        itemMaterialDo.setMaterialSourceRemark(middleItemMaterialDo.getSourceExplain());
        itemMaterialDo.setNotCommit(middleItemMaterialDo.isSubmissionRequired());
        itemMaterialDo.setMaterialBizId(middleItemMaterialDo.getMaterialId());
        itemMaterialDo.setOrderNum(middleItemMaterialDo.getOrderNum());
    }

    /**
     * 校验事项是否变更，变更则添加到日志中
     *
     * @param itemChangeLogDoList 事项变更日志
     * @param oldItemDo           旧的事项
     * @param newItemDo           新的事项
     */
    private void verifyItemChangeLog(List<ItemChangeLogDo> itemChangeLogDoList, ItemDo oldItemDo, ItemDo newItemDo) {
        settingChangeLog(() -> !verifyString(oldItemDo.getItemCode()).equals(verifyString(newItemDo.getItemCode())), itemChangeLogDoList, newItemDo.getId(), oldItemDo.getItemCode(), newItemDo.getItemCode(), "事项编码：", EDIT_OPERATION_STR);
        settingChangeLog(() -> !verifyString(oldItemDo.getItemName()).equals(verifyString(newItemDo.getItemName())), itemChangeLogDoList, newItemDo.getId(), oldItemDo.getItemName(), newItemDo.getItemName(), "事项名称：", EDIT_OPERATION_STR);
        settingChangeLog(() -> !verifyString(oldItemDo.getCreditCode()).equals(verifyString(newItemDo.getCreditCode())), itemChangeLogDoList, newItemDo.getId(), oldItemDo.getCreditCode(), newItemDo.getCreditCode(), "实施机构的统一社会信用代码代码：", EDIT_OPERATION_STR);
        settingChangeLog(() -> !verifyString(oldItemDo.getImplOrgName()).equals(verifyString(newItemDo.getImplOrgName())), itemChangeLogDoList, newItemDo.getId(), oldItemDo.getImplOrgName(), newItemDo.getImplOrgName(), "实施机构：", EDIT_OPERATION_STR);
        settingChangeLog(() -> !verifyString(oldItemDo.getDivisionCode()).equals(verifyString(newItemDo.getDivisionCode())), itemChangeLogDoList, newItemDo.getId(), oldItemDo.getDivisionCode(), newItemDo.getDivisionCode(), "实施机构的行政区划代码：", EDIT_OPERATION_STR);
        settingChangeLog(() -> !oldItemDo.getItemType().equals(newItemDo.getItemType()), itemChangeLogDoList, newItemDo.getId(), oldItemDo.getDivisionCode(), newItemDo.getDivisionCode(), "事项类型：", EDIT_OPERATION_STR);
        String updateItemOperationStr = "更新事项";
        settingChangeLog(() -> !oldItemDo.getItemStatus().equals(newItemDo.getItemStatus()), itemChangeLogDoList, newItemDo.getId(), verifyObject(oldItemDo.getItemStatus()), verifyObject(newItemDo.getDivisionCode()), "事项状态：", updateItemOperationStr);
        settingChangeLog(() -> !oldItemDo.getProjectType().equals(newItemDo.getProjectType()), itemChangeLogDoList, newItemDo.getId(), verifyObject(oldItemDo.getProjectType()), verifyObject(newItemDo.getProjectType()), "办件类型：", updateItemOperationStr);
    }

    /**
     * 校验材料是否更新，更新则保存到更新日志中
     *
     * @param itemChangeLogDoList 更新日志
     * @param oldItemMaterialDo   旧的材料
     * @param newItemMaterialDo   新的材料
     * @param itemId              事项的id
     */
    private void verifyMaterialChangeLog(List<ItemChangeLogDo> itemChangeLogDoList, ItemMaterialDo oldItemMaterialDo, ItemMaterialDo newItemMaterialDo, String itemId) {
        settingChangeLog(() -> !verifyString(oldItemMaterialDo.getMaterialName()).equals(verifyString(newItemMaterialDo.getMaterialName())), itemChangeLogDoList, itemId, oldItemMaterialDo.getMaterialName(), newItemMaterialDo.getMaterialName(), "材料管理-材料名称：", EDIT_OPERATION_STR);
        settingChangeLog(() -> !oldItemMaterialDo.getMaterialType().equals(newItemMaterialDo.getMaterialType()), itemChangeLogDoList, itemId, verifyEnum(oldItemMaterialDo.getMaterialType()), verifyEnum(newItemMaterialDo.getMaterialType()), "材料管理-材料类型：", EDIT_OPERATION_STR);
        settingChangeLog(() -> !verifyString(oldItemMaterialDo.getLicenseCode()).equals(verifyString(newItemMaterialDo.getLicenseCode())), itemChangeLogDoList, itemId, oldItemMaterialDo.getLicenseCode(), newItemMaterialDo.getLicenseCode(), "材料管理-关联的电子证照的目录编码：", EDIT_OPERATION_STR);
        settingChangeLog(() -> !verifyString(oldItemMaterialDo.getLicenseName()).equals(verifyString(newItemMaterialDo.getLicenseName())), itemChangeLogDoList, itemId, oldItemMaterialDo.getLicenseName(), newItemMaterialDo.getLicenseName(), "材料管理-关联的电子证照的名称：", EDIT_OPERATION_STR);
        settingChangeLog(() -> !verifyString(oldItemMaterialDo.getMaterialSource()).equals(verifyString(newItemMaterialDo.getMaterialSource())), itemChangeLogDoList, itemId, oldItemMaterialDo.getMaterialSource(), newItemMaterialDo.getMaterialSource(), "材料管理-来源渠道：", EDIT_OPERATION_STR);
        settingChangeLog(() -> !verifyString(oldItemMaterialDo.getMaterialSourceRemark()).equals(verifyString(newItemMaterialDo.getMaterialSourceRemark())), itemChangeLogDoList, itemId, oldItemMaterialDo.getMaterialSourceRemark(), newItemMaterialDo.getMaterialSourceRemark(), "材料管理-来源渠道说明：", EDIT_OPERATION_STR);
        settingChangeLog(() -> !oldItemMaterialDo.getNotCommit().equals(newItemMaterialDo.getNotCommit()), itemChangeLogDoList, itemId, verifyObject(oldItemMaterialDo.getNotCommit()), verifyObject(newItemMaterialDo.getNotCommit()), "材料管理-是否免提交：", EDIT_OPERATION_STR);
        settingChangeLog(() -> {
            boolean isNotEmpty = Objects.nonNull(oldItemMaterialDo.getOrderNum()) && Objects.nonNull(newItemMaterialDo.getOrderNum());
            return isNotEmpty && !oldItemMaterialDo.getOrderNum().equals(newItemMaterialDo.getOrderNum());
        }, itemChangeLogDoList, itemId, verifyObject(oldItemMaterialDo.getOrderNum()), verifyObject(newItemMaterialDo.getOrderNum()), "材料管理-材料排序：", EDIT_OPERATION_STR);
    }

    /**
     * 根据判断条件，进行生成itemChangeLog与设值
     *
     * @param condition           条件接口，实现由调用方决定，返回布尔值，true才工作
     * @param itemChangeLogDoList 存储的数据
     * @param itemId              事项id
     * @param oldElementStr       旧数据字符串
     * @param newElementStr       新数据字符串
     * @param changeElementDesc   更变前缀描述
     * @param operationName       operationName
     */
    private void settingChangeLog(Supplier<Boolean> condition, List<ItemChangeLogDo> itemChangeLogDoList, String itemId, String oldElementStr, String newElementStr, String changeElementDesc, String operationName) {
        if (condition.get()) {
            ItemChangeLogDo materialChangeLogDo = new ItemChangeLogDo();
            materialChangeLogDo.setChangePrefix(changeElementDesc + oldElementStr);
            materialChangeLogDo.setChangePost(changeElementDesc + newElementStr);
            materialChangeLogDo.setOperationName(operationName);
            materialChangeLogDo.setItemId(itemId);
            materialChangeLogDo.setAccount(UserUtils.getUserName());
            materialChangeLogDo.setAccountName(UserUtils.getUserName());
            itemChangeLogDoList.add(materialChangeLogDo);
        }
    }

    /**
     * 校验字符串的值，为null则返回""
     *
     * @param str 字符串
     * @return 返回值
     */
    private String verifyString(String str) {
        if (str == null) {
            return "";
        }
        return str;
    }

    /**
     * 校验字符串的值，为null则返回""
     *
     * @param enumItem 枚举类
     * @return 返回值
     */
    private static String verifyEnum(Enum enumItem) {
        if (enumItem == null) {
            return "";
        }
        return enumItem.name();
    }

    /**
     * 校验字符串的值，为null则返回""
     *
     * @param obj 对象
     * @return 返回值
     */
    private static String verifyObject(Object obj) {
        if (obj == null) {
            return "";
        }
        return obj.toString();
    }

    public <T> Specification<T> setSpecification(ListCondition listCondition) {
        MiddleItemListRequest listVo = (MiddleItemListRequest) listCondition;
        PredicateBuilder<T> spec = Specifications.<T>and()
                .like(StringUtils.isNotBlank(listVo.getItemName()), "taskName", "%" + listVo.getItemName() + "%")
                .like(StringUtils.isNotBlank(listVo.getDeptName()), "deptName", "%" + listVo.getDeptName() + "%")
                .in(CollectionUtils.isNotEmpty(listVo.getSynchronizeStatus()), "synchronizeStatus", Collections.singletonList(listVo.getSynchronizeStatus()))
                .eq(StringUtils.isNotBlank(listVo.getItemCode()), "taskCode", listVo.getItemCode());
        return spec.build();
    }

    public <T, I extends Serializable> Slice<T> findAllNoCount(final Specification<T> spec, final Pageable pageable,
                                                               final Class<T> domainClass) {
        final SimpleJpaNoCountRepository<T, I> noCountDao = new SimpleJpaNoCountRepository<>(domainClass, em);
        return noCountDao.findAll(spec, pageable);
    }

   /* public static class SimpleJpaNoCountRepository<T, ID extends Serializable> extends SimpleJpaRepository<T, ID> {

        public SimpleJpaNoCountRepository(Class<T> domainClass, EntityManager em) {
            super(domainClass, em);
        }

        @Override
        protected <S extends T> Page<S> readPage(TypedQuery<S> query, Class<S> domainClass, Pageable pageable, Specification<S> spec) {
            query.setFirstResult((int) pageable.getOffset());
            query.setMaxResults(pageable.getPageSize());
            final List<S> content = query.getResultList();
            return new PageImpl<>(content, pageable, content.size());
        }
    }*/
}
