package gov.derate.proof.synchronous.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.synchronous.entity.vo.MiddleItemVo;
import gov.derate.proof.synchronous.entity.vo.MiddleMaterialVo;
import gov.license.common.api.resp.BaseResponse;

import java.io.Serializable;
import java.util.List;

/**
 * 同步数据详情响应体
 *
 * <AUTHOR>
 * @date 2022/4/25.
 */
public class SynchronizeDetailsResponse extends BaseResponse implements Serializable {

    private static final long serialVersionUID = -5398866454171669999L;

    /**
     * 事项
     */
    @JsonProperty("item")
    private MiddleItemVo middleItemVo;

    /**
     * 证明材料
     */
    @JsonProperty("material_list")
    private List<MiddleMaterialVo> middleMaterialVoList;

    /**
     * 同步失败原因
     */
    @JsonProperty("fail_result")
    private String failResult;

    /**
     * 变动详情
     */
    @JsonProperty("change_details")
    private String changeDetails;

    public MiddleItemVo getMiddleItemVo() {
        return middleItemVo;
    }

    public void setMiddleItemVo(MiddleItemVo middleItemVo) {
        this.middleItemVo = middleItemVo;
    }

    public List<MiddleMaterialVo> getMiddleMaterialVoList() {
        return middleMaterialVoList;
    }

    public void setMiddleMaterialVoList(List<MiddleMaterialVo> middleMaterialVoList) {
        this.middleMaterialVoList = middleMaterialVoList;
    }

    public String getFailResult() {
        return failResult;
    }

    public void setFailResult(String failResult) {
        this.failResult = failResult;
    }

    public String getChangeDetails() {
        return changeDetails;
    }

    public void setChangeDetails(String changeDetails) {
        this.changeDetails = changeDetails;
    }
}
