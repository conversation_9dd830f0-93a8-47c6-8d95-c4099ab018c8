package gov.derate.proof.synchronous.service.impl;

import com.google.common.collect.Lists;
import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.item.bo.ItemBo;
import gov.derate.proof.item.exception.ItemServiceException;
import gov.derate.proof.list.service.ProofState;
import gov.derate.proof.list.service.ProofStateManager;
import gov.derate.proof.synchronous.entity.MiddleItemDo;
import gov.derate.proof.synchronous.repository.MiddleItemRepository;
import gov.derate.proof.synchronous.resp.SynchronousResponseCode;
import gov.derate.proof.synchronous.service.SynchronizeDataService;
import gov.derate.proof.synchronous.service.SynchronizeItemService;
import gov.license.common.api.resp.BaseResponseCode;
import gov.license.common.api.utils.Assert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * <p>
 * 基础框架令牌服务包实现类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2020-08-10
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2020-08-10;
 */
@Service
public class SynchronizeItemServiceImpl implements SynchronizeItemService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SynchronizeItemServiceImpl.class);

    @Autowired
    private MiddleItemRepository middleItemRepository;
    /**
     * 清单状态管理器
     */
    @Autowired
    private ProofStateManager proofStateManager;

    /**
     * 中间表数据同步服务
     */
    @Autowired
    private SynchronizeDataService synchronizeDataService;

    @Override
    public SynchronizeStatusEnum synchronousData(String middleItemId) {
        long startTime = System.currentTimeMillis();
        //获取事项中间表
        Optional<MiddleItemDo> middleItemDo = middleItemRepository.findById(middleItemId);
        if (!middleItemDo.isPresent()
                || middleItemDo.get().getSynchronizeStatus() != SynchronizeStatusEnum.WAIT_SYNCHRONIZE) {
            throw new ItemServiceException(new BaseResponseCode(BaseResponseCode.SERVICE_ERROR.getCode(), "同步事项状态非待同步状态，不允许同步", true));
        }
        synchronizeDataService.syncMiddleItemDoList(Lists.newArrayList(middleItemDo.get()));
        LOGGER.debug("synchronousConsolidationOfData time [{}]", System.currentTimeMillis() - startTime);
        return middleItemDo.get().getSynchronizeStatus();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSynchronize(String middleItemId) throws Exception {
        //获取事项中间表
        Optional<MiddleItemDo> middleItemDoOptional = middleItemRepository.findById(middleItemId);
        MiddleItemDo middleItemDo = middleItemDoOptional.orElseThrow(() -> {
            LOGGER.warn("MiddleItemDo is null or synchronizeStatus is not syn fail");
            return new ItemServiceException(SynchronousResponseCode.MIDDLE_EMPTY_ERROR);
        });
        Assert.isTrue(middleItemDo.getSynchronizeStatus() == SynchronizeStatusEnum.SYNCHRONIZE_FAILED, SynchronousResponseCode.MIDDLE_SYNC_STATUS_NOT_FAILURE_ERROR);
        // 判断事项是否在审核通过，若是，则扭转到待清理保存后，再处理同步逻辑。
        Optional<ItemBo> itemBo = ItemBo.buildItemBo(middleItemDo.getTaskCode());
        if (itemBo.isPresent() && itemBo.get().getItemProofRelationDo().getItemProofStatus() == ItemProofStatusEnum.APPROVED) {
            ItemBo item = itemBo.get();
            ProofState proofState = proofStateManager.get(ItemProofStatusEnum.WAIT_FOR_CLEAN);
            proofState.doTransition(item.convertItemDo());
        }
        synchronizeDataService.syncMiddleItemDoList(Lists.newArrayList(middleItemDo));
    }


}
