package gov.derate.proof.synchronous.service;

import gov.derate.proof.common.repository.JpaBizCommonConditionInterface;
import gov.derate.proof.synchronous.entity.MiddleItemDo;
import gov.derate.proof.synchronous.req.MiddleItemListRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 事项中间表服务
 *
 * <AUTHOR>
 * @date 2022/4/24.
 */
public interface MiddleItemService extends JpaBizCommonConditionInterface {

    /**
     * 分页查询事项中间表数据
     *
     * @param listVo 查询条件
     * @param page   分页参数
     * @return 数据
     */
    Page<MiddleItemDo> queryMiddleItemList(MiddleItemListRequest listVo, Pageable page);
}
