package gov.derate.proof.synchronous.service;

import gov.derate.proof.common.enums.SynchronizeStatusEnum;

/**
 * 同步数据服务
 *
 * <AUTHOR>
 * @date 2022/4/24.
 */
public interface SynchronizeItemService {

    /**
     * 同步整合事项中间表到本地
     *
     * @param middleItemId 事项中间表id
     * @return 同步状态
     */
    SynchronizeStatusEnum synchronousData(String middleItemId);

    /**
     * 更新同步事项
     *
     * @param middleItemId 中间表事项id
     * @throws Exception 异常
     */
    void updateSynchronize(String middleItemId) throws Exception;
}
