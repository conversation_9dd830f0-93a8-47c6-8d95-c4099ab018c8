package gov.derate.proof.synchronous.repository;

import gov.derate.proof.synchronous.entity.SynchronizeLogDo;
import gov.license.common.api.repository.BaseRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 同步记录数据访问
 *
 * <AUTHOR>
 * @date 2022/4/24.
 */
@Repository
@Deprecated
public interface SynchronizeLogRepository extends BaseRepository<SynchronizeLogDo, String> {

    /**
     * 根据事项guid查询同步记录
     *
     * @param itemGuidList 事项guid列表
     * @return 同步记录
     */
    List<SynchronizeLogDo> findAllByItemGuidIn(List<String> itemGuidList);

    /**
     * 根据事项guid查询同步记录
     *
     * @param itemGuid 事项guid
     * @return 同步记录
     */
    SynchronizeLogDo findAllByItemGuid(String itemGuid);
}
