package gov.derate.proof.synchronous.service.impl;

import gov.license.jpa.Specifications;
import gov.derate.proof.common.query.ListCondition;
import gov.derate.proof.synchronous.entity.MiddleItemDo;
import gov.derate.proof.synchronous.repository.MiddleItemRepository;
import gov.derate.proof.synchronous.req.MiddleItemListRequest;
import gov.derate.proof.synchronous.service.MiddleItemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

/**
 * 事项中间表服务
 *
 * <AUTHOR>
 * @date 2022/4/24.
 */
@Service
public class MiddleItemServiceImpl implements MiddleItemService {

    @Autowired
    private MiddleItemRepository middleItemRepository;

    @Override
    public <T> Specification<T> setSpecification(ListCondition listCondition) {
        MiddleItemListRequest listVo = (MiddleItemListRequest) listCondition;
        return Specifications.<T>and()
                .like(StringUtils.isNotBlank(listVo.getItemName()), "taskName", "%" + listVo.getItemName() + "%")
                .like(StringUtils.isNotBlank(listVo.getDeptName()), "deptName", "%" + listVo.getDeptName() + "%")
                .build();
    }

    @Override
    public Page<MiddleItemDo> queryMiddleItemList(MiddleItemListRequest listVo, Pageable page) {
        Specification<MiddleItemDo> specification = setSpecification(listVo);
        return middleItemRepository.findAll(specification, page);
    }
}
