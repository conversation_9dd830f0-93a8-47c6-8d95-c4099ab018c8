package gov.derate.proof.synchronous.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.enums.ItemStatus;
import gov.derate.proof.common.enums.ItemTypeEnum;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.license.common.api.resp.BaseResponse;

import java.io.Serializable;
import java.util.Date;

/**
 * 同步数据列表
 *
 * <AUTHOR>
 * @date 2022/4/24.
 */
public class SynchronizeListResponse extends BaseResponse implements Serializable {
    private static final long serialVersionUID = 218901633609623271L;


    /**
     * 中间表事项id
     */
    @JsonProperty("middle_item_id")
    private String middleItemId;

    /**
     * 事项编码
     */
    @JsonProperty("item_code")
    private String itemCode;

    /**
     * 事项名称
     */
    @JsonProperty("item_name")
    private String itemName;

    /**
     * 实施机构
     */
    @JsonProperty("dept_name")
    private String deptName;

    /**
     * 事项类型（事项类型）
     */
    @JsonProperty("item_type")
    private ItemTypeEnum itemType;

    /**
     * 事项状态（事项状态：1在用 2暂停 3取消，默认在用）
     */
    @JsonProperty("item_status")
    private ItemStatus itemStatus;

    /**
     * 同步失败的原因
     */
    @JsonProperty("failed_result")
    private String failedResult;

    /**
     * 同步状态（同步成功0,同步失败1,未同步2）
     */
    @JsonProperty("synchronize_status")
    private SynchronizeStatusEnum synchronizeStatus;

    /**
     * 最新更新时间
     */
    @JsonProperty(value = "last_modification_time")
    private Date lastModificationTime;

    /**
     * 办理项
     */
    @JsonProperty("situation_name")
    private String situationName;
    /**
     * 受理条件
     */
    @JsonProperty("accept_condition")
    private String acceptCondition;

    public String getMiddleItemId() {
        return middleItemId;
    }

    public void setMiddleItemId(String middleItemId) {
        this.middleItemId = middleItemId;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public ItemTypeEnum getItemType() {
        return itemType;
    }

    public void setItemType(ItemTypeEnum itemType) {
        this.itemType = itemType;
    }

    public ItemStatus getItemStatus() {
        return itemStatus;
    }

    public void setItemStatus(ItemStatus itemStatus) {
        this.itemStatus = itemStatus;
    }

    public String getFailedResult() {
        return failedResult;
    }

    public void setFailedResult(String failedResult) {
        this.failedResult = failedResult;
    }

    public SynchronizeStatusEnum getSynchronizeStatus() {
        return synchronizeStatus;
    }

    public void setSynchronizeStatus(SynchronizeStatusEnum synchronizeStatus) {
        this.synchronizeStatus = synchronizeStatus;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    public String getSituationName() {
        return situationName;
    }

    public void setSituationName(String situationName) {
        this.situationName = situationName;
    }

    public String getAcceptCondition() {
        return acceptCondition;
    }

    public void setAcceptCondition(String acceptCondition) {
        this.acceptCondition = acceptCondition;
    }

    @Override
    public String toString() {
        return "SynchronizeListResponse{" +
                "middleItemId='" + middleItemId + '\'' +
                ", itemCode='" + itemCode + '\'' +
                ", itemName='" + itemName + '\'' +
                ", deptName='" + deptName + '\'' +
                ", itemType=" + itemType +
                ", itemStatus=" + itemStatus +
                ", failedResult='" + failedResult + '\'' +
                ", synchronizeStatus=" + synchronizeStatus +
                ", lastModificationTime=" + lastModificationTime +
                ", situationName='" + situationName + '\'' +
                ", acceptCondition='" + acceptCondition + '\'' +
                '}';
    }
}
