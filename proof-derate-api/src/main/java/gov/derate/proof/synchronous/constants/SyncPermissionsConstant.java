package gov.derate.proof.synchronous.constants;

/**
 * 事项同步管理 权限代码常量类
 *
 * <AUTHOR>
 * @date 2023/12/27
 */
public class SyncPermissionsConstant {

    /**
     * 证明管理:事项管理:事项同步管理:查询
     */
    public static final String CATALOG_ITEM_SYNCHRONIZE_LIST = "catalog:item:synchronize:list";

    /**
     * 证明管理:事项管理:事项同步管理:详情
     */
    public static final String CATALOG_ITEM_SYNCHRONIZE_INFO = "catalog:item:synchronize:info";

    /**
     * 证明管理:事项管理:事项同步管理:同步
     */
    public static final String CATALOG_ITEM_SYNCHRONIZE_SYNC = "catalog:item:synchronize:sync";

    /**
     * 证明管理:事项管理:事项同步管理:更新
     */
    public static final String CATALOG_ITEM_SYNCHRONIZE_UPDATE = "catalog:item:synchronize:update";
    /**
     * 证明管理:事项管理:事项同步管理:忽略
     */
    public static final String CATALOG_ITEM_SYNCHRONIZE_IGNORE = "catalog:item:synchronize:ignore";

    /**
     * 证明管理:事项管理:事项同步管理:查看清理办件
     */
    public static final String CATALOG_ITEM_SYNCHRONIZE_VIEW = "catalog:item:synchronize:view";



}
