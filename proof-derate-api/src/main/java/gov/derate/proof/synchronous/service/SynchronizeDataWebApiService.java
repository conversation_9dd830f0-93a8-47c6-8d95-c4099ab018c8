package gov.derate.proof.synchronous.service;

import gov.derate.proof.common.enums.OperatorLogEnum;
import gov.derate.proof.synchronous.entity.vo.ProofListProcedureVo;
import gov.derate.proof.synchronous.req.MiddleItemListRequest;
import gov.derate.proof.synchronous.req.SynchronizeDetailsRequest;
import gov.derate.proof.synchronous.resp.SynchronizeDetailsResponse;
import gov.derate.proof.synchronous.resp.SynchronizeListResponse;
import gov.license.common.api.resp.data.BasePageRespData;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 同步数据服务
 *
 * <AUTHOR>
 * @date 2022/4/24.
 */
public interface SynchronizeDataWebApiService {

    /**
     * 查询同步数据列表
     *
     * @param listVo 查询条件
     * @param page   分页
     * @return 列表
     */
    BasePageRespData<List<SynchronizeListResponse>> querySynchronizeList(MiddleItemListRequest listVo, Pageable page);

    /**
     * 查询同步数据列表
     *
     * @param listVo 查询条件
     * @param page   分页
     * @return 数量
     */
    BasePageRespData<Long> querySynchronizeListCount(MiddleItemListRequest listVo, Pageable page);

    /**
     * 查询同步数据详情
     *
     * @param listVo 查询条件
     * @return 详情
     */
    SynchronizeDetailsResponse querySynchronizeDetails(SynchronizeDetailsRequest listVo);

    /**
     * 忽略中间表的事项
     *
     * @param middleItemId 中间表事项id
     */
    void ignoreSynchronize(String middleItemId);

    /**
     * 根据事项编码和操作类型获取操作日志
     *
     * @param itemCode        事项编码
     * @param operatorLogEnum 操作类型
     * @return 操作日志
     */
    ProofListProcedureVo getSynchronizeOperatorLog(String itemCode, OperatorLogEnum operatorLogEnum);
}
