package gov.derate.proof.synchronous.webapi;

import gov.derate.proof.common.enums.OperatorLogEnum;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.common.service.OrganizationFilterService;
import gov.derate.proof.synchronous.constants.SyncPermissionsConstant;
import gov.derate.proof.synchronous.req.MiddleItemListRequest;
import gov.derate.proof.synchronous.req.SynchronizeDetailsRequest;
import gov.derate.proof.synchronous.resp.SynchronizeDetailsResponse;
import gov.derate.proof.synchronous.resp.SynchronizeListResponse;
import gov.derate.proof.synchronous.service.SynchronizeDataWebApiService;
import gov.derate.proof.synchronous.service.SynchronizeItemService;
import gov.licc.func.api.amp.annotation.ULog;
import gov.license.common.api.controller.BaseController;
import gov.license.common.api.exception.ApplicationGlobalRuntimeException;
import gov.license.common.api.resp.BaseResponseCode;
import gov.license.common.api.resp.ResponseHelper;
import gov.license.common.api.resp.ResponseResult;
import gov.license.common.api.resp.data.BasePageRespData;
import org.apache.shiro.authz.annotation.RequiresAuthentication;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import java.util.List;

/**
 * 同步事项系统数据
 *
 * <AUTHOR>
 * @date 2022/4/24.
 */
@RestController
public class SynchronousDataWebApi extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(SynchronousDataWebApi.class);

    @Autowired
    private SynchronizeDataWebApiService synchronizeDataService;
    @Autowired
    private SynchronizeItemService synchronizeItemService;
    @Autowired
    private OrganizationFilterService organizationFilterService;

    /**
     * 根据请求参数分页查询列表
     *
     * @param itemName            事项名称
     * @param deptName            实施机构
     * @param pageDirection       分页排序
     * @param pageNumber          页码
     * @param pageSize            分页大小
     * @param itemCode            事项编码
     * @param searchAllVersion    查询全部版本
     * @param searchMaxNewVersion 查询最新版本
     * @return 列表
     */
    @GetMapping("/catalog/webapi/v1/synchronize/list")
    @RequiresPermissions(SyncPermissionsConstant.CATALOG_ITEM_SYNCHRONIZE_LIST)
    @ULog(module = "事项同步管理", event = "查看事项同步管理列表")
    public ResponseResult<BasePageRespData<List<SynchronizeListResponse>>> listPage(@RequestParam(value = "item_name", required = false) String itemName,
                                                                                    @RequestParam(value = "dept_name", required = false) String deptName,
                                                                                    @RequestParam(value = "synchronize_status", required = false) List<SynchronizeStatusEnum> synchronizeStatus,
                                                                                    @RequestParam(value = "item_type", required = false) String itemTypeStr,
                                                                                    @RequestParam("page_direction") String pageDirection,
                                                                                    @RequestParam("page_number") int pageNumber,
                                                                                    @RequestParam("page_size") @Valid @Max(value = 1000, message = "分页大小小于1000") int pageSize,
                                                                                    @RequestParam(value = "item_code", required = false) String itemCode,
                                                                                    @RequestParam(value = "search_all_version", required = false) Boolean searchAllVersion,
                                                                                    @RequestParam(value = "search_max_new_version", required = false) Boolean searchMaxNewVersion
    ) {

        try {
            //数据范围条件过滤
            List<String> creditCodeList =
                    organizationFilterService.getCreditCodesByPermissionCode(SyncPermissionsConstant.CATALOG_ITEM_SYNCHRONIZE_LIST);

            MiddleItemListRequest middleItemListRequest = new MiddleItemListRequest();
            middleItemListRequest.setItemName(itemName);
            middleItemListRequest.setItemName(itemName);
            middleItemListRequest.setDeptName(deptName);
            middleItemListRequest.setSynchronizeStatus(synchronizeStatus);
            middleItemListRequest.setItemCode(itemCode);
            middleItemListRequest.setSearchAllVersion(searchAllVersion);
            middleItemListRequest.setSearchMaxNewVersion(searchMaxNewVersion);
            middleItemListRequest.setItemTypeList(itemTypeStr);
            middleItemListRequest.setCreditCodeList(creditCodeList);
            pageDirection = pageDirection.toUpperCase();
            Sort.Direction direction = Enum.valueOf(Sort.Direction.class, pageDirection);
            //如果是查最新版本，将page排序字段转为nativeQuery原生查询字段
            Pageable pageable = PageRequest.of(pageNumber - 1, pageSize, Sort.by(direction, "lastModificationTime"));
            BasePageRespData<List<SynchronizeListResponse>> respData
                    = synchronizeDataService.querySynchronizeList(middleItemListRequest, pageable);
            return ResponseHelper.success(respData);
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("ApplicationGlobalRuntimeException error", e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("SynchronousDataWebApi listPage error", e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        }
    }

    /**
     * 根据请求参数查询总条数
     *
     * @param itemName            事项名称
     * @param deptName            实施机构
     * @param itemCode            事项编码
     * @param searchAllVersion    查询全部版本
     * @param searchMaxNewVersion 查询最新版本
     * @return 列表
     */
    @GetMapping("/catalog/webapi/v1/synchronize/listCount")
    @RequiresPermissions(SyncPermissionsConstant.CATALOG_ITEM_SYNCHRONIZE_LIST)
    public ResponseResult<BasePageRespData<Long>> listCount(@RequestParam(value = "item_name", required = false) String itemName,
                                                            @RequestParam(value = "dept_name", required = false) String deptName,
                                                            @RequestParam(value = "synchronize_status", required = false) List<SynchronizeStatusEnum> synchronizeStatus,
                                                            @RequestParam(value = "item_type", required = false) String itemTypeStr,
                                                            @RequestParam("page_direction") String pageDirection,
                                                            @RequestParam("page_number") int pageNumber,
                                                            @RequestParam("page_size") @Valid @Max(value = 1000, message = "分页大小小于1000") int pageSize,
                                                            @RequestParam(value = "item_code", required = false) String itemCode,
                                                            @RequestParam(value = "search_all_version", required = false) Boolean searchAllVersion,
                                                            @RequestParam(value = "search_max_new_version", required = false) Boolean searchMaxNewVersion
    ) {
        try {
            //数据范围条件过滤
            List<String> creditCodeList =
                    organizationFilterService.getCreditCodesByPermissionCode(SyncPermissionsConstant.CATALOG_ITEM_SYNCHRONIZE_LIST);

            MiddleItemListRequest middleItemListRequest = new MiddleItemListRequest();
            middleItemListRequest.setItemName(itemName);
            middleItemListRequest.setDeptName(deptName);
            middleItemListRequest.setSynchronizeStatus(synchronizeStatus);
            middleItemListRequest.setItemCode(itemCode);
            middleItemListRequest.setItemTypeList(itemTypeStr);
            middleItemListRequest.setSearchAllVersion(searchAllVersion);
            middleItemListRequest.setSearchMaxNewVersion(searchMaxNewVersion);
            middleItemListRequest.setCreditCodeList(creditCodeList);
            pageDirection = pageDirection.toUpperCase();
            Sort.Direction direction = Enum.valueOf(Sort.Direction.class, pageDirection);
            Pageable pageable = PageRequest.of(pageNumber - 1, pageSize, Sort.by(direction, "lastModificationTime")
                    .and(Sort.by(Sort.Direction.ASC, "taskCode")));
            //如果是查最新版本，将page排序字段转为nativeQuery原生查询字段
            BasePageRespData<Long> longs = synchronizeDataService.querySynchronizeListCount(middleItemListRequest, pageable);
            return ResponseHelper.success(longs);
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("ApplicationGlobalRuntimeException error", e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("SynchronousDataWebApi listCount error", e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        }
    }

    /**
     * 同步信息详情
     *
     * @param itemCode          事项编码
     * @param syncItemId        同步系统事项id
     * @param synchronizeStatus 同步状态
     * @return 详情
     */
    @GetMapping("/catalog/webapi/v1/synchronize/details")
    @RequiresPermissions(SyncPermissionsConstant.CATALOG_ITEM_SYNCHRONIZE_INFO)
    @ULog(module = "事项同步管理", event = "查看事项同步管理详情")
    public ResponseResult<SynchronizeDetailsResponse> details(@RequestParam(value = "item_code") String itemCode,
                                                              @RequestParam(value = "middle_item_id") String syncItemId,
                                                              @RequestParam(value = "synchronize_status") SynchronizeStatusEnum synchronizeStatus) {
        try {
            SynchronizeDetailsRequest request = new SynchronizeDetailsRequest();
            request.setItemCode(itemCode);
            request.setMiddleItemId(syncItemId);
            request.setSynchronizeStatus(synchronizeStatus);
            return ResponseHelper.success(synchronizeDataService.querySynchronizeDetails(request));
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("ApplicationGlobalRuntimeException error", e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("SynchronousDataWebApi details error", e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        }
    }

    /**
     * 同步中间表数据
     *
     * @param middleItemId 同步系统事项id
     * @return 详情
     */
    @PostMapping("/catalog/webapi/v1/synchronize/operate")
    @RequiresPermissions(SyncPermissionsConstant.CATALOG_ITEM_SYNCHRONIZE_SYNC)
    @ULog(module = "事项同步管理", event = "同步")
    public ResponseResult<Object> synchronizeOperate(@RequestParam(value = "middle_item_id") String middleItemId) {
        try {
            return ResponseHelper.success(synchronizeItemService.synchronousData(middleItemId));
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("SynchronousDataWebApi synchronizeOperate error", e);
            return ResponseHelper.failure(e);
        } catch (Exception e) {
            LOGGER.error("SynchronousDataWebApi synchronizeOperate error", e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        }
    }

    /**
     * 忽略该事项，不进行同步
     *
     * @param middleItemId 同步系统事项id
     * @return 详情
     */
    @PostMapping("/catalog/webapi/v1/synchronize/ignore")
    @RequiresPermissions(SyncPermissionsConstant.CATALOG_ITEM_SYNCHRONIZE_IGNORE)
    @ULog(module = "事项同步管理", event = "忽略事项")
    public ResponseResult<Object> synchronizeIgnore(@RequestParam(value = "middle_item_id") String middleItemId) {
        try {
            synchronizeDataService.ignoreSynchronize(middleItemId);
            return ResponseHelper.success();
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("ApplicationGlobalRuntimeException error", e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("synchronizeOperate synchronizeIgnore error", e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        }
    }

    /**
     * 更新该事项
     *
     * @param middleItemId 同步系统事项id
     * @return 详情
     */
    @PostMapping("/catalog/webapi/v1/synchronize/update")
    @RequiresPermissions(SyncPermissionsConstant.CATALOG_ITEM_SYNCHRONIZE_UPDATE)
    @ULog(module = "事项同步管理", event = "更新事项")
    public ResponseResult<Object> synchronizeUpdate(@RequestParam(value = "middle_item_id") String middleItemId) throws Exception {
        try {
            synchronizeItemService.updateSynchronize(middleItemId);
            return ResponseHelper.success();
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("SynchronousDataWebApi synchronizeUpdate error", e);
            return ResponseHelper.failure(e);
        } catch (Exception e) {
            LOGGER.error("SynchronousDataWebApi synchronizeUpdate error", e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        }
    }

    /**
     * 获取操作日志
     *
     * @param itemCode    事项编码
     * @param operatorLog 操作类型
     * @return 操作日志
     */
    @GetMapping("/catalog/webapi/v1/synchronize/operator/journal")
    @RequiresAuthentication
    public ResponseResult<Object> synchronizeGetLog(@RequestParam(value = "item_code") String itemCode,
                                                    @RequestParam(value = "operator_log") OperatorLogEnum operatorLog) {
        try {
            return ResponseHelper.success(synchronizeDataService.getSynchronizeOperatorLog(itemCode, operatorLog));
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("ApplicationGlobalRuntimeException error", e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("SynchronousDataWebApi synchronizeGetLog error", e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        }
    }
}
