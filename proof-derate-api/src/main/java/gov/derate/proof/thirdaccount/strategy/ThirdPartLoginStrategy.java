package gov.derate.proof.thirdaccount.strategy;

import gov.derate.proof.thirdaccount.dto.ThirdPartyLoginContextDto;
import gov.derate.proof.thirdaccount.enums.ThirdAccountTypeEnum;
import gov.licc.func.api.auth.dto.LoginDto;

/**
 * 第三方登录策略类
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
public interface ThirdPartLoginStrategy {


    /**
     * 登录过程
     *
     * @param loginContext 登录上下文信息
     * @return 登录对象
     */
    LoginDto loginProcess(ThirdPartyLoginContextDto loginContext);

    /**
     * 根据请求获取认证授权码
     *
     * @param loginParam 请求信息
     * @return 授权码
     */
    void authorizationCodeProcess(ThirdPartyLoginContextDto loginParam);

    /**
     * 根据认证授权码获取Token信息
     *
     * @param request 请求信息
     * @return 授权码
     */
    void tokenProcess(ThirdPartyLoginContextDto request);

    /**
     * 获取第三方登录用户信息
     *
     * @param loginContext 登录信息
     * @return 第三方登录用户信息
     */
    LoginDto userInfoProcess(ThirdPartyLoginContextDto loginContext);

    /**
     * 获取登录类型类型
     *
     * @return 类型
     */
    ThirdAccountTypeEnum getLoginType();
}
