package gov.derate.proof.thirdaccount.strategy;

import com.google.common.collect.Maps;
import gov.derate.proof.account.dto.AccountInfoDto;
import gov.derate.proof.account.entity.AccountViewDo;
import gov.derate.proof.account.exception.AccountInfoException;
import gov.derate.proof.account.repository.AccountViewRepository;
import gov.derate.proof.account.resp.AccountInfoResponseCode;
import gov.derate.proof.account.service.AccountInfoService;
import gov.derate.proof.common.constant.CacheKeyBuilder;
import gov.derate.proof.thirdaccount.code.ThirdAccountGatewayResponseCode;
import gov.derate.proof.thirdaccount.constants.ThirdAccountDictConstant;
import gov.derate.proof.thirdaccount.dto.ThirdPartyLoginContextDto;
import gov.derate.proof.thirdaccount.dto.YzySecurityTokenResponseDto;
import gov.derate.proof.thirdaccount.dto.YzySecurityUserInfoResponseDto;
import gov.derate.proof.thirdaccount.dto.YzyThirdPartLoginParamDto;
import gov.derate.proof.thirdaccount.enums.ThirdAccountTypeEnum;
import gov.derate.proof.thirdaccount.exception.ThirdAccountGatewayException;
import gov.derate.proof.thirdaccount.exception.ThirdPartyLoginException;
import gov.derate.proof.thirdaccount.util.HttpRequestUtils;
import gov.licc.func.api.amp.dto.DictDetailDto;
import gov.licc.func.api.amp.dto.DictDto;
import gov.licc.func.api.auth.dto.LoginDto;
import gov.license.cache.CacheKey;
import gov.license.cache.service.CacheService;
import gov.license.common.api.utils.Assert;
import gov.license.common.tools.http.HttpUtil;
import gov.license.common.tools.jackson.JacksonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 粤政易登录策略
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
@Service
public class YzyLoginStrategy extends AbstractThirdPartyLoginStrategy {

    private static final Logger LOGGER = LoggerFactory.getLogger(YzyLoginStrategy.class);

    @Autowired
    private CacheService cacheService;
    @Autowired
    private AccountInfoService accountInfoService;
    @Autowired
    private AccountViewRepository accountViewRepository;

    @Override
    public void authorizationCodeProcess(ThirdPartyLoginContextDto loginContext) {
        try {
            YzyThirdPartLoginParamDto loginDTO =
                    JacksonUtil.toBean(JacksonUtil.toJsonStr(loginContext.getLoginRequest()),
                            YzyThirdPartLoginParamDto.class);
            loginContext.setAuthorizationCode(loginDTO.getAuthorizationCode());
        } catch (Exception e) {
            throw new ThirdPartyLoginException(ThirdAccountGatewayResponseCode.PARAM_THIRD_PARTY_YZY_ERROR, e);
        }
    }

    @Override
    public void tokenProcess(ThirdPartyLoginContextDto loginContext) {
        String token = getToken(loginContext.getAuthorizationCode(), loginContext.getTargetDict());
        loginContext.setToken(token);
    }

    @Override
    public LoginDto userInfoProcess(ThirdPartyLoginContextDto loginContext) {
        LoginDto loginDto = new LoginDto();
        loginDto.setIp(HttpRequestUtils.getRequestAddress());
        loginDto.setBrowser(HttpRequestUtils.getRequestBrowser());
        //获取访问令牌 access_token
        String accessToken = loginContext.getToken();
        Assert.hasText(accessToken, new ThirdAccountGatewayException(ThirdAccountGatewayResponseCode.YZY_ACCESS_TOKEN_IS_NULL));

        //根据access_token获取账号详细信息
        YzySecurityUserInfoResponseDto userInfo = getUserDetail(accessToken, loginContext.getTargetDict());
        Assert.notNull(userInfo, new ThirdAccountGatewayException(ThirdAccountGatewayResponseCode.YZY_USER_DETAIL_IS_NULL));
        Assert.notNull(userInfo.getMobile(), new ThirdAccountGatewayException(ThirdAccountGatewayResponseCode.YZY_USER_MOBILE_IS_NULL));
        List<AccountInfoDto> accountInfoDtos = accountInfoService.queryAccountInfoByMobilePhone(userInfo.getMobile());

        // 账户是否异常进行判断
        if (CollectionUtils.isEmpty(accountInfoDtos)) {
            throw new ThirdAccountGatewayException(ThirdAccountGatewayResponseCode.YZY_USER_ACCOUNT_IS_NULL);
        } else if (accountInfoDtos.size() != 1) {
            throw new ThirdAccountGatewayException(ThirdAccountGatewayResponseCode.YZY_USER_ACCOUNT_MULTI);
        }
        // 登录系统
        AccountViewDo accountViewDo = accountViewRepository.findById(accountInfoDtos.get(0).getAccountId()).orElseThrow(() ->
                new AccountInfoException(AccountInfoResponseCode.ACCOUNT_YZY_LOGIN_NOT_EXISTS_ERROR));
        loginDto.setAccount(accountViewDo.getAccount());

        //记录粤政易登录状态
        CacheKey cacheKey = CacheKeyBuilder.YZY_SECURITY_LOGIN_FLAG.getCacheKey(loginDto.getAccount());
        cacheService.set(cacheKey, loginDto.getAccount());
        return loginDto;
    }

    @Override
    public ThirdAccountTypeEnum getLoginType() {
        return ThirdAccountTypeEnum.PROVINCE_GD_AFFAIRS;
    }

    /**
     * 根据授权码获取token
     *
     * @param code       授权code
     * @param targetDict 目标字典数据
     * @return token字符串
     */
    private String getToken(String code, DictDto targetDict) {
        try {
            //从字典获取参数
            String clientId =
                    targetDict.getDictDetailList().stream().filter(dictDetail -> ThirdAccountDictConstant.DICT_DETAIL_YZY_CLIENT_ID.equals(dictDetail.getKey())).map(DictDetailDto::getValue).findFirst().orElse("");
            String clientSecret =
                    targetDict.getDictDetailList().stream().filter(dictDetail -> ThirdAccountDictConstant.DICT_DETAIL_YZY_CLIENT_SECRET.equals(dictDetail.getKey())).map(DictDetailDto::getValue).findFirst().orElse("");
            String getTokenUrl =
                    targetDict.getDictDetailList().stream().filter(dictDetail -> ThirdAccountDictConstant.DICT_DETAIL_YZY_GET_TOKEN_URL.equals(dictDetail.getKey())).map(DictDetailDto::getValue).findFirst().orElse("");
            String grantType =
                    targetDict.getDictDetailList().stream().filter(dictDetail -> ThirdAccountDictConstant.DICT_DETAIL_YZY_GRANT_TYPE.equals(dictDetail.getKey())).map(DictDetailDto::getValue).findFirst().orElse("");
            String redirectUri =
                    targetDict.getDictDetailList().stream().filter(dictDetail -> ThirdAccountDictConstant.DICT_DETAIL_YZY_REDIRECT_URI.equals(dictDetail.getKey())).map(DictDetailDto::getValue).findFirst().orElse("");

            //生成所需request实体
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("client_id", clientId);
            paramMap.put("grant_type", grantType);
            paramMap.put("redirect_uri", redirectUri);
            paramMap.put("code", code);
            paramMap.put("client_secret", clientSecret);
            LOGGER.debug("yzyLogin request url[{}],param[{}]",getTokenUrl,paramMap);
            //请求目标资源
            HttpUtil.HttpResp resp = HttpUtil.doPostForm(getTokenUrl, paramMap);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("getToken paramMap:{}", JacksonUtil.toJsonStr(paramMap));
                LOGGER.debug("getToken statusCode:{}", resp.getStatusCode());
                LOGGER.debug("getToken resp:{}", resp.getResponseString());
            }
            if (resp.getStatusCode().equals(HttpStatus.OK)) {
                Map<String, Object> resultMap = JacksonUtil.toMap(resp.getResponseString(), Object.class);
                YzySecurityTokenResponseDto tokenResponseDTO = JacksonUtil.toBean(JacksonUtil.toJsonStr(resultMap.get("data")),
                        YzySecurityTokenResponseDto.class);
                return tokenResponseDTO.getAccessToken();
            }
        } catch (Exception e) {
            throw new ThirdPartyLoginException(ThirdAccountGatewayResponseCode.THIRD_PARTY_YZY_TOKEN_ERROR, e);
        }
        return null;
    }

    /**
     * 根据授权token获取用户信息
     *
     * @param token      授权token信息
     * @param targetDict 目标字典数据
     * @return 用户信息过程返回值
     */
    private YzySecurityUserInfoResponseDto getUserDetail(String token, DictDto targetDict) {
        try {
            //从字典获取参数
            String userInfoDetailUrl =
                    targetDict.getDictDetailList().stream().filter(dictDetail -> ThirdAccountDictConstant.DICT_DETAIL_YZY_GET_USER_INFO_DETAIL_URL.equals(dictDetail.getKey())).map(DictDetailDto::getValue).findFirst().orElse("");

            //生成所需request实体
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("access_token", token);
            //请求目标资源
            HttpUtil.HttpResp resp = HttpUtil.doPostForm(userInfoDetailUrl, paramMap);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("getUserInfoDetail paramMap:{}", JacksonUtil.toJsonStr(paramMap));
                LOGGER.debug("getUserInfoDetail statusCode:{}", resp.getStatusCode());
                LOGGER.debug("getUserInfoDetail resp:{}", resp.getResponseString());
            }
            if (resp.getStatusCode().equals(HttpStatus.OK)) {
                Map<String, Object> resultMap = JacksonUtil.toMap(resp.getResponseString(), Object.class);
                return JacksonUtil.toBean(JacksonUtil.toJsonStr(resultMap.get("data")), YzySecurityUserInfoResponseDto.class);
            }
        } catch (Exception e) {
            throw new ThirdPartyLoginException(ThirdAccountGatewayResponseCode.THIRD_PARTY_YZY_USER_DETAIL_ERROR, e);
        }
        return null;
    }


}
