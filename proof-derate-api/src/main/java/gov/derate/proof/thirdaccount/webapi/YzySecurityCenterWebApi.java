package gov.derate.proof.thirdaccount.webapi;

import gov.derate.proof.thirdaccount.YzySecurityCenterService;
import gov.derate.proof.thirdaccount.exception.ThirdAccountGatewayException;
import gov.license.common.api.exception.ApplicationGlobalRuntimeException;
import gov.license.common.api.resp.BaseResponseCode;
import gov.license.common.api.resp.ResponseHelper;
import gov.license.common.api.resp.ResponseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * 粤政易登录
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
@RestController
public class YzySecurityCenterWebApi {

    private static final Logger LOGGER = LoggerFactory.getLogger(YzySecurityCenterWebApi.class);

    @Autowired
    private YzySecurityCenterService yzySecurityCenterService;

    /**
     * 获取粤政易登出地址
     */
    @GetMapping("/auth/webapi/v1/yzy/security_center/logout/{account}")
    public ResponseResult<String> logout(@PathVariable("account") String account) {
        try {
            String yzyLogoutUrl = yzySecurityCenterService.getYzyLogoutUrl(account);
            return ResponseHelper.success(yzyLogoutUrl);
        } catch (ThirdAccountGatewayException e) {
            LOGGER.error("ThirdAccountGateway error responseCode [{}|{}]", e.getResponseCode(),
                    e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (ApplicationGlobalRuntimeException e) {
            LOGGER.error("ApplicationGlobalRuntimeException error", e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("Authorization login error", e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        }
    }

}
