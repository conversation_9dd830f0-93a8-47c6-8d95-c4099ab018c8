package gov.derate.proof.thirdaccount;

import gov.derate.proof.common.constant.CacheKeyBuilder;
import gov.derate.proof.thirdaccount.code.ThirdAccountGatewayResponseCode;
import gov.derate.proof.thirdaccount.constants.ThirdAccountDictConstant;
import gov.derate.proof.thirdaccount.exception.ThirdAccountGatewayException;
import gov.licc.func.api.amp.dto.DictDetailDto;
import gov.licc.func.api.amp.dto.DictDto;
import gov.licc.func.api.amp.service.DictPublicService;
import gov.license.cache.CacheKey;
import gov.license.cache.service.CacheService;
import gov.license.common.api.utils.Assert;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.Optional;


/**
 * <AUTHOR>
 * @Description: 粤政易登录中心ServiceImpl
 * @ClassName YzySecurityCenterServiceImpl
 * @date 2023/11/13 14:44
 */
@Service
public class YzySecurityCenterServiceImpl implements YzySecurityCenterService {

    private static final Logger LOGGER = LoggerFactory.getLogger(YzySecurityCenterServiceImpl.class);

    @Autowired
    private DictPublicService dictPublicService;
    @Autowired
    private CacheService cacheService;

    @Override
    public String getYzyLogoutUrl(String account) throws UnsupportedEncodingException {

        CacheKey cacheKey = CacheKeyBuilder.YZY_SECURITY_LOGIN_FLAG.getCacheKey(account);

        //粤政易登录需要返回粤政易退出地址
        if(StringUtils.isNotEmpty(cacheService.get(cacheKey))){
            Optional<DictDto> optionalDict = dictPublicService.findDictAndDetailByName(ThirdAccountDictConstant.DICT_DETAIL_YZY_CONFIG);
            Assert.isTrue(optionalDict.isPresent(), new ThirdAccountGatewayException(ThirdAccountGatewayResponseCode.YZY_DICT_PUBLIC_CONFIG_IS_NULL));

            DictDto targetDict = optionalDict.get();

            //从字典获取参数
            String clientId =
                    targetDict.getDictDetailList().stream().filter(dictDetail -> ThirdAccountDictConstant.DICT_DETAIL_YZY_CLIENT_ID.equals(dictDetail.getKey())).map(DictDetailDto::getValue).findFirst().orElse("");
            String logoutUrl =
                    targetDict.getDictDetailList().stream().filter(dictDetail -> ThirdAccountDictConstant.DICT_DETAIL_YZY_LOGOUT_URL.equals(dictDetail.getKey())).map(DictDetailDto::getValue).findFirst().orElse("");
            String backUrl =
                    targetDict.getDictDetailList().stream().filter(dictDetail -> ThirdAccountDictConstant.DICT_DETAIL_YZY_BACK_URL.equals(dictDetail.getKey())).map(DictDetailDto::getValue).findFirst().orElse("");

            String yzyLogoutUrl = String.format("%s?client_id=%s&backurl=%s",logoutUrl,clientId,backUrl);
            //清除状态
            cacheService.del(cacheKey);
            return yzyLogoutUrl;
        }else{
            return "";
        }
    }
}
