package gov.derate.proof.thirdaccount.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 粤政易登录获取token过程返回值DTO
 *
 * <AUTHOR>
 * @date 2023-11-9
 */
public class YzySecurityTokenResponseDto implements Serializable {

    private static final long serialVersionUID = -6195847433083767179L;

    /**
     * token 信息
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * token 信息
     */
    @JsonProperty("token_type")
    private String tokenType;

    /**
     * 有效时间（秒）
     */
    @JsonProperty("expires_in")
    private Long expiresIn;

    /**
     * token 信息
     */
    @JsonProperty("scope")
    private String scope;

    /**
     * token 信息
     */
    @JsonProperty("userid")
    private String userid;


    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }

    @Override
    public String toString() {
        return "YzySecurityTokenResponseDTO{" +
                "accessToken='" + accessToken + '\'' +
                ", tokenType='" + tokenType + '\'' +
                ", expiresIn=" + expiresIn +
                ", scope='" + scope + '\'' +
                ", userid='" + userid + '\'' +
                '}';
    }
}
