package gov.derate.proof.thirdaccount.dto;


import gov.licc.func.api.amp.dto.DictDto;

/**
 * 第三方登录上下文信息
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
public class ThirdPartyLoginContextDto {
    /**
     * 请求信息
     **/
    private Object loginRequest;

    /**
     * 所需系统字典信息
     **/
    private DictDto targetDict;

    /**
     * 授权码
     **/
    private String authorizationCode;

    /**
     * 认证token
     **/
    private String token;

    public Object getLoginRequest() {
        return loginRequest;
    }

    public void setLoginRequest(Object loginRequest) {
        this.loginRequest = loginRequest;
    }

    public DictDto getTargetDict() {
        return targetDict;
    }

    public void setTargetDict(DictDto targetDict) {
        this.targetDict = targetDict;
    }

    public String getAuthorizationCode() {
        return authorizationCode;
    }

    public void setAuthorizationCode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
