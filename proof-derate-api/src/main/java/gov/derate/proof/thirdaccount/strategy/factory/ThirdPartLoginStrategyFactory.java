package gov.derate.proof.thirdaccount.strategy.factory;

import gov.derate.proof.thirdaccount.code.ThirdAccountGatewayResponseCode;
import gov.derate.proof.thirdaccount.dto.AbstractParamThirdPartLoginDto;
import gov.derate.proof.thirdaccount.dto.ThirdPartyLoginContextDto;
import gov.derate.proof.thirdaccount.enums.ThirdAccountTypeEnum;
import gov.derate.proof.thirdaccount.exception.ThirdPartyLoginException;
import gov.derate.proof.thirdaccount.strategy.ThirdPartLoginStrategy;
import gov.licc.func.api.auth.dto.LoginDto;
import gov.license.common.tools.jackson.JacksonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 第三方登录策略工厂类
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
@Component
public class ThirdPartLoginStrategyFactory implements ApplicationContextAware {

    private static final Logger LOGGER = LoggerFactory.getLogger(ThirdPartLoginStrategyFactory.class);

    /**
     * 限流策略类集合
     */
    private static Map<String, ThirdPartLoginStrategy> thirdPartLoginStrategyMap;

    /**
     * 策略上下文对象委派具体的策略执行算法
     *
     * @param loginContext 登录处理上下文信息
     * @return 登录信息
     */
    public LoginDto loginHandler(ThirdPartyLoginContextDto loginContext) throws IOException {
        try {
            AbstractParamThirdPartLoginDto loginDTO =
                    JacksonUtil.toBean(JacksonUtil.toJsonStr(loginContext.getLoginRequest()),
                            AbstractParamThirdPartLoginDto.class);
            return getLoginStrategy(Objects.requireNonNull(loginDTO.getLoginType())).loginProcess(loginContext);
        } catch (Exception e) {
            throw new ThirdPartyLoginException(ThirdAccountGatewayResponseCode.THIRD_PARTY_HANDLER_ERROR, e);
        }
    }

    /**
     * 获取适用的策略处理类
     *
     * @param loginType 登录类型枚举类
     * @return 登录类型策略类
     */
    private ThirdPartLoginStrategy getLoginStrategy(ThirdAccountTypeEnum loginType) {
        return Optional.ofNullable(thirdPartLoginStrategyMap.get(loginType.name()))
                .orElseThrow(() -> new RuntimeException(String.format("not found loginType strategy , loginType is %s", loginType.name())));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ThirdPartLoginStrategy> strategyBeans = applicationContext.getBeansOfType(ThirdPartLoginStrategy.class);
        if (strategyBeans.size() == 0) {
            // 无可用的策略
            return;
        }
        thirdPartLoginStrategyMap = new HashMap<>(strategyBeans.size());
        for (ThirdPartLoginStrategy strategy : strategyBeans.values()) {
            thirdPartLoginStrategyMap.put(strategy.getLoginType().name(), strategy);
            LOGGER.info("register third part login strategy :{}", strategy.getLoginType());
        }
    }
}
