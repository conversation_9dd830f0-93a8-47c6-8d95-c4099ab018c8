package gov.derate.proof.thirdaccount.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * Http request 工具类
 *
 * <AUTHOR>
 * @date 2023-10-25
 */
public class HttpRequestUtils {

	/**
	 * 获取当前线程request请求
	 *
	 * @return
	 * @throws IllegalStateException
	 */
	public static HttpServletRequest getCurrentRequest() throws IllegalStateException {
		ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (attrs == null) {
			throw new IllegalStateException("当前线程中不存在 Request 上下文");
		}
		return attrs.getRequest();
	}

	/**
	 * 获取HttpServletRequest
	 *
	 * @return
	 */
	public static HttpServletRequest getHttpRequest() {

		return getCurrentRequest();
	}

	/**
	 * 获取 User Agent
	 *
	 * @return
	 */
	public static String getRequestBrowser() {

		return getRequestBrowser(getCurrentRequest());
	}

	/**
	 * 获取 User Agent
	 *
	 * @param request
	 * @return
	 */
	public static String getRequestBrowser(HttpServletRequest request) {
		if (request != null) {
			return request.getHeader("User-Agent");
		}
		return null;
	}

	/**
	 * 获取请求ip
	 *
	 * @return
	 */
	public static String getRequestAddress() {
		return getRequestAddress(getCurrentRequest());
	}

	/**
	 * 获取请求ip
	 *
	 * @param request
	 * @return
	 */
	public static String getRequestAddress(HttpServletRequest request) {
		if (request != null) {
			String[] headNames = new String[]{"X-Forwarded-For", "Proxy-Client-IP", "WL-Proxy-Client-IP"};
			String ip = null;
			for (int i = 0; i < headNames.length; i++) {
				ip = request.getHeader(headNames[i]);
				if (!StringUtils.isEmpty(ip)) {
					break;
				}
			}
			return StringUtils.isEmpty(ip) ? request.getRemoteAddr() : ip;
		}
		return null;
	}

	/**
	 * 获取服务器IP地址
	 *
	 * @return
	 */
	public static String getServerAddress(HttpServletRequest request) {
		if (request != null) {
			return request.getLocalAddr();
		}
		return null;
	}

}
