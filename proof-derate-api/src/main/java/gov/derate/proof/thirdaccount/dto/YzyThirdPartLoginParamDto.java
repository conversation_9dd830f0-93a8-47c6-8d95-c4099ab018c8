package gov.derate.proof.thirdaccount.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 第三方登录请求抽象类
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
public class YzyThirdPartLoginParamDto extends AbstractParamThirdPartLoginDto {

    private static final long serialVersionUID = 449463651547112777L;
    /**
     * 授权码
     **/
    @JsonProperty("code")
    private String authorizationCode;

    public String getAuthorizationCode() {
        return authorizationCode;
    }

    public void setAuthorizationCode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
    }
}
