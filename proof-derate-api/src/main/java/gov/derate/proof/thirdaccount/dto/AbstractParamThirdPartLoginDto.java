package gov.derate.proof.thirdaccount.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.thirdaccount.enums.ThirdAccountTypeEnum;

import java.io.Serializable;

/**
 * 第三方登录请求抽象类
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
public class AbstractParamThirdPartLoginDto implements Serializable {

    private static final long serialVersionUID = 6588861263549031067L;

    /**
     * 登录类型
     **/
    @JsonProperty("login_type")
    private ThirdAccountTypeEnum loginType;

    public ThirdAccountTypeEnum getLoginType() {
        return loginType;
    }

    public void setLoginType(ThirdAccountTypeEnum loginType) {
        this.loginType = loginType;
    }
}
