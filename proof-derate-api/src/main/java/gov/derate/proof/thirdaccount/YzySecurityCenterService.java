package gov.derate.proof.thirdaccount;

import java.io.UnsupportedEncodingException;

/**
 * 粤政易登录中心Service
 *
 * <AUTHOR>
 * @date 2023-11-13
 */
public interface YzySecurityCenterService {

    /**
     * 获取跳转粤政易登出地址
     *
     * @param account 账户
     * @return 获取登出路径
     * @throws UnsupportedEncodingException 异常
     */
    String getYzyLogoutUrl(String account) throws UnsupportedEncodingException;
}
