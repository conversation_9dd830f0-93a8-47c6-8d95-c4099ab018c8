package gov.derate.proof.thirdaccount.constants;


import gov.derate.proof.thirdaccount.enums.ThirdAccountTypeEnum;

/**
 * 外部账号字典常量类
 *
 * <AUTHOR>
 * @date 2023-10-24
 */
public class ThirdAccountDictConstant {


    /**
     * 广东粤政易登录配置
     */
    public static final String DICT_DETAIL_YZY_CONFIG =
            "THIRD_ACCOUNT_" + ThirdAccountTypeEnum.PROVINCE_GD_AFFAIRS.name() + "_CONFIG";

    /**
     * 广东粤政易登录配置GRANT_TYPE
     */
    public static final String DICT_DETAIL_YZY_GRANT_TYPE =
            "GRANT_TYPE";

    /**
     * 广东粤政易登录配置CLIENT_ID
     */
    public static final String DICT_DETAIL_YZY_CLIENT_ID =
            "CLIENT_ID";

    /**
     * 广东粤政易登录配置CLIENT_SECRET
     */
    public static final String DICT_DETAIL_YZY_CLIENT_SECRET =
            "CLIENT_SECRET";

    /**
     * 广东粤政易登录配置RESPONSE_TYPE
     */
    public static final String DICT_DETAIL_YZY_RESPONSE_TYPE =
            "RESPONSE_TYPE";

    /**
     * 广东粤政易登录配置SCOPE
     */
    public static final String DICT_DETAIL_YZY_SCOPE =
            "SCOPE";

    /**
     * 广东粤政易登录配置AUTHORIZE_URL
     */
    public static final String DICT_DETAIL_YZY_AUTHORIZE_URL =
            "AUTHORIZE_URL";

    /**
     * 广东粤政易登录-配置TOKEN信息URL
     */
    public static final String DICT_DETAIL_YZY_GET_TOKEN_URL =
            "GET_TOKEN_URL";

    /**
     * 广东粤政易登录配置-获取用户信息URL
     */
    public static final String DICT_DETAIL_YZY_GET_USER_INFO_DETAIL_URL =
            "GET_USER_INFO_DETAIL_URL";
    /**
     * 广东粤政易登录配置-回调地址
     */
    public static final String DICT_DETAIL_YZY_REDIRECT_URI =
            "REDIRECT_URI";

    /**
     * 广东粤政易登录配置-登出地址
     */
    public static final String DICT_DETAIL_YZY_LOGOUT_URL =
            "LOGOUT_URL";

    /**
     * 广东粤政易登录配置-登出回调地址
     */
    public static final String DICT_DETAIL_YZY_BACK_URL =
            "BACK_URL";

    /**
     * 广东粤政易登录配置-默认角色代码
     */
    public static final String DEFAULT_ROLE_CODE =
            "DEFAULT_ROLE_CODE";
}
