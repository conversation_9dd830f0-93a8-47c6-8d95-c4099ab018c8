package gov.derate.proof.thirdaccount.dto;

/**
 * 外部账号管理详情BO类
 *
 * <AUTHOR>
 * @date 2023/10/19
 */
public class ThirdAccountGatewayViewDto extends  gov.derate.proof.thirdaccount.dto.BaseDto {

    private static final long serialVersionUID = -1625816141276473949L;
    /**
     * 本地账号
     */
    private String localAccount;
    /**
     * 本地账号名称
     */
    private String localAccountName;
    /**
     * 外部账号
     */
    private String thirdAccount;

    public String getLocalAccount() {
        return localAccount;
    }

    public void setLocalAccount(String localAccount) {
        this.localAccount = localAccount;
    }

    public String getLocalAccountName() {
        return localAccountName;
    }

    public void setLocalAccountName(String localAccountName) {
        this.localAccountName = localAccountName;
    }

    public String getThirdAccount() {
        return thirdAccount;
    }

    public void setThirdAccount(String thirdAccount) {
        this.thirdAccount = thirdAccount;
    }
}
