package gov.derate.proof.thirdaccount.dto;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 基础Bo类
 *
 * <AUTHOR>
 * @date 2023-10-25
 */
public class BaseDto implements Serializable {
    private static final long serialVersionUID = -2603412777279270180L;
    /**
     * APP_KEY
     */
    @JsonProperty("id")
    private String id;
    /**
     * 创建人ID
     */
    @JsonProperty("creator_id")
    private String creatorId;
    /**
     * 创建时间
     */
    @JsonProperty("creation_time")
    private Date creationTime;
    /**
     * 最后修改人ID
     */
    @JsonProperty("last_modificator_id")
    private String lastModificatorId;
    /**
     * 最后修改人时间
     */
    @JsonProperty("last_modification_time")
    private Date lastModificationTime;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreatorId() {
        return this.creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreationTime() {
        return this.creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public String getLastModificatorId() {
        return this.lastModificatorId;
    }

    public void setLastModificatorId(String lastModificatorId) {
        this.lastModificatorId = lastModificatorId;
    }

    public Date getLastModificationTime() {
        return this.lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }
}
