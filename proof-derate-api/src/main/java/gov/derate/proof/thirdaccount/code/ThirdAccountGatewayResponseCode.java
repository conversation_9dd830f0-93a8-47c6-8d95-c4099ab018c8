package gov.derate.proof.thirdaccount.code;

import gov.license.common.api.resp.BaseResponseCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 外部账号管理自定义异常信息
 *
 * <AUTHOR>
 * @date 2023-10-19
 */

public class ThirdAccountGatewayResponseCode extends BaseResponseCode {

    private static final Logger LOGGER = LoggerFactory.getLogger(ThirdAccountGatewayResponseCode.class);

    /**
     * 外部账号管理-粤政易登录-token信息为空
     **/
    public static final ThirdAccountGatewayResponseCode YZY_ACCESS_TOKEN_IS_NULL = new ThirdAccountGatewayResponseCode(
            "3C010301000", "粤政易登录-token信息为空");
    /**
     * 外部账号管理-粤政易登录-用户信息为空
     **/
    public static final ThirdAccountGatewayResponseCode YZY_USER_DETAIL_IS_NULL = new ThirdAccountGatewayResponseCode(
            "3C010301001", "粤政易登录-用户信息为空");
    /**
     * 外部账号管理-粤政易登录-相关字典配置为空
     **/
    public static final ThirdAccountGatewayResponseCode YZY_DICT_PUBLIC_CONFIG_IS_NULL = new ThirdAccountGatewayResponseCode(
            "3C010301002", "粤政易登录-相关字典配置为空");

    /**
     * 外部账号管理-粤政易登录-手机号为空
     **/
    public static final ThirdAccountGatewayResponseCode YZY_USER_MOBILE_IS_NULL = new ThirdAccountGatewayResponseCode(
            "3C010301003", "粤政易登录-手机号为空");

    /**
     * 外部账号管理-粤政易登录-存在多个关联账户
     **/
    public static final ThirdAccountGatewayResponseCode YZY_USER_ACCOUNT_MULTI = new ThirdAccountGatewayResponseCode(
            "3C010301004", "粤政易登录-存在多个关联账户");
    /**
     * 外部账号管理-粤政易登录-存在多个关联账户
     **/
    public static final ThirdAccountGatewayResponseCode YZY_USER_ACCOUNT_IS_NULL = new ThirdAccountGatewayResponseCode(
            "3C010301005", "粤政易登录-账号不存在，请联系管理员");

    /**
     * 第三方登录-第三方登录系统异常
     **/
    public static final ThirdAccountGatewayResponseCode THIRD_PARTY_LOGIN_ERROR =
            new ThirdAccountGatewayResponseCode("3C010301006", "第三方登录系统异常");

    /**
     * 第三方登录-第三方登录处理异常
     **/
    public static final ThirdAccountGatewayResponseCode THIRD_PARTY_HANDLER_ERROR =
            new ThirdAccountGatewayResponseCode("3C010301007", "第三方登录处理异常");

    /**
     * 第三方登录-粤政易-参数解析异常
     **/
    public static final ThirdAccountGatewayResponseCode PARAM_THIRD_PARTY_YZY_ERROR =
            new ThirdAccountGatewayResponseCode("3C010301008", "参数解析异常");

    /**
     * 第三方登录-粤政易-获取Token过程异常
     **/
    public static final ThirdAccountGatewayResponseCode THIRD_PARTY_YZY_TOKEN_ERROR =
            new ThirdAccountGatewayResponseCode("3C010301009", "获取Token过程异常");

    /**
     * 第三方登录-粤政易-获取用户信息过程异常
     **/
    public static final ThirdAccountGatewayResponseCode THIRD_PARTY_YZY_USER_DETAIL_ERROR =
            new ThirdAccountGatewayResponseCode("3C010301010", "获取用户信息过程异常");

    public ThirdAccountGatewayResponseCode(String code, String message) {
        super(code, message, true);
        this.code = code;
        this.message = message;
    }

    private String code;
    private String message;

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

}
