package gov.derate.proof.thirdaccount.strategy;

import gov.derate.proof.thirdaccount.dto.ThirdPartyLoginContextDto;
import gov.derate.proof.thirdaccount.enums.ThirdAccountTypeEnum;
import gov.licc.func.api.auth.dto.LoginDto;
import gov.license.common.tools.jackson.JacksonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 第三方登录抽象模板方法类
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
public abstract class AbstractThirdPartyLoginStrategy implements ThirdPartLoginStrategy {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractThirdPartyLoginStrategy.class);

    @Override
    public LoginDto loginProcess(ThirdPartyLoginContextDto loginContext) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("loginProcess loginRequest:{}", JacksonUtil.toJsonStr(loginContext.getLoginRequest()));
        }
        authorizationCodeProcess(loginContext);

        tokenProcess(loginContext);

        return userInfoProcess(loginContext);
    }

    @Override
    public void authorizationCodeProcess(ThirdPartyLoginContextDto loginParam) {

    }

    @Override
    public void tokenProcess(ThirdPartyLoginContextDto request) {

    }

    @Override
    public LoginDto userInfoProcess(ThirdPartyLoginContextDto loginContext) {
        return null;
    }

    @Override
    public ThirdAccountTypeEnum getLoginType() {
        return null;
    }
}
