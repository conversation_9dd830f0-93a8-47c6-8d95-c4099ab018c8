package gov.derate.proof.thirdaccount.exception;

import gov.license.common.api.exception.ApplicationServiceException;
import gov.license.common.api.resp.BaseResponseCode;

/**
 * 外部账号管理业务异常
 *
 * <AUTHOR>
 * @date 2023-10-25
 */
public class ThirdAccountGatewayException extends ApplicationServiceException {
    private static final long serialVersionUID = -6750304052525416013L;
    private BaseResponseCode responseCode;

    public ThirdAccountGatewayException(BaseResponseCode causeResponseCode, Throwable throwable) {
        super(throwable);
        responseCode = causeResponseCode;
    }

    public ThirdAccountGatewayException(BaseResponseCode causeResponseCode) {
        super(causeResponseCode);
        responseCode = causeResponseCode;
    }

    public ThirdAccountGatewayException(String message) {
        super(message);
    }

    @Override
    public BaseResponseCode getResponseCode() {
        return responseCode;
    }

}
