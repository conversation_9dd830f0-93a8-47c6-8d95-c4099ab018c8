package gov.derate.proof.thirdaccount.enums;

/**
 * 第三方账号类型枚举类
 *
 * <AUTHOR>
 * @date 2023-10-23
 */
public enum ThirdAccountTypeEnum {


    /**
     * 粤政易
     */
    PROVINCE_GD_AFFAIRS(0, "粤政易");

    ThirdAccountTypeEnum(String desc) {
        this.desc = desc;
    }

    ThirdAccountTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private int code;

    private String desc;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static ThirdAccountTypeEnum getHolderType(int code) {
        ThirdAccountTypeEnum cHolderType = null;
        for (ThirdAccountTypeEnum value : ThirdAccountTypeEnum.values()) {
            if (code == value.code) {
                cHolderType = value;
            }
        }
        return cHolderType;
    }
}
