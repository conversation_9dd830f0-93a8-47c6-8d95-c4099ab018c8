package gov.derate.proof.thirdaccount.exception;

import gov.license.common.api.exception.ApplicationServiceException;
import gov.license.common.api.resp.BaseResponseCode;

/**
 * 第三方登录业务异常
 *
 * <AUTHOR>
 * @date 2023-10-25
 */
public class ThirdPartyLoginException extends ApplicationServiceException {
    private static final long serialVersionUID = -6750304052525416013L;
    private BaseResponseCode responseCode;

    public ThirdPartyLoginException(BaseResponseCode causeResponseCode, Throwable throwable) {
        super(throwable);
        responseCode = causeResponseCode;
    }

    public ThirdPartyLoginException(BaseResponseCode causeResponseCode) {
        super(causeResponseCode);
        responseCode = causeResponseCode;
    }

    public ThirdPartyLoginException(String message) {
        super(message);
    }

    @Override
    public BaseResponseCode getResponseCode() {
        return responseCode;
    }

}
