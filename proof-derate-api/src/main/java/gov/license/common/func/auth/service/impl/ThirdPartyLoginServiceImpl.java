package gov.license.common.func.auth.service.impl;

import gov.derate.proof.thirdaccount.code.ThirdAccountGatewayResponseCode;
import gov.derate.proof.thirdaccount.constants.ThirdAccountDictConstant;
import gov.derate.proof.thirdaccount.dto.ThirdPartyLoginContextDto;
import gov.derate.proof.thirdaccount.exception.ThirdAccountGatewayException;
import gov.derate.proof.thirdaccount.exception.ThirdPartyLoginException;
import gov.derate.proof.thirdaccount.strategy.factory.ThirdPartLoginStrategyFactory;
import gov.licc.func.api.amp.dto.DictDto;
import gov.licc.func.api.amp.service.DictPublicService;
import gov.licc.func.api.auth.dto.LoginDto;
import gov.licc.func.api.auth.service.AbstractThirdPartyAuthorizationLoginPublicServiceImpl;
import gov.license.common.api.utils.Assert;
import gov.license.common.tools.jackson.JacksonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Optional;


/**
 * <AUTHOR>
 * @Description: 粤政易Service实现类
 * @ClassName YzySecurityServiceimpl
 * @date 2023/11/9 11:37
 */
@Service
@Primary
public class ThirdPartyLoginServiceImpl extends AbstractThirdPartyAuthorizationLoginPublicServiceImpl {
    private static final Logger LOGGER = LoggerFactory.getLogger(ThirdPartyLoginServiceImpl.class);

    @Autowired
    private DictPublicService dictPublicService;

    @Autowired
    private ThirdPartLoginStrategyFactory thirdPartLoginStrategyFactory;

    @Override
    public LoginDto buildLoginParam(Object loginRequest) {
        LOGGER.debug("ThirdPartyLoginServiceImpl param loginRequest [{}]", JacksonUtil.toJsonStr(loginRequest));
        Optional<DictDto> optionalDict = dictPublicService.findDictAndDetailByName(ThirdAccountDictConstant.DICT_DETAIL_YZY_CONFIG);
        Assert.isTrue(optionalDict.isPresent(), new ThirdAccountGatewayException(ThirdAccountGatewayResponseCode.YZY_DICT_PUBLIC_CONFIG_IS_NULL));

        //执行第三方登录策略进行登录
        ThirdPartyLoginContextDto thirdPartLoginContext = new ThirdPartyLoginContextDto();
        thirdPartLoginContext.setLoginRequest(loginRequest);
        thirdPartLoginContext.setTargetDict(optionalDict.get());
        try {
            LOGGER.debug("ThirdPartyLoginServiceImpl thirdPartLoginContext[{}]", thirdPartLoginContext);

            return thirdPartLoginStrategyFactory.loginHandler(thirdPartLoginContext);
        } catch (Exception e) {
            LOGGER.error("ThirdPartyLoginServiceImpl buildLoginParam error: loginRequest is [{}], thirdPartLoginContext [{}]", JacksonUtil.toJsonStr(loginRequest), thirdPartLoginContext, e);
            throw new ThirdPartyLoginException(ThirdAccountGatewayResponseCode.THIRD_PARTY_LOGIN_ERROR, e);
        }
    }
}
