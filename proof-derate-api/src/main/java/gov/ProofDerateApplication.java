package gov;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <p>
 * proof derate api启动类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-07-28
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-07-28;
 */

@SpringBootApplication
@ServletComponentScan
@EnableJpaAuditing
@EnableScheduling
//@EnableCaching
public class ProofDerateApplication {

    public static void main(String[] args) throws Exception {
        SpringApplication.run(ProofDerateApplication.class, args);
    }

}
