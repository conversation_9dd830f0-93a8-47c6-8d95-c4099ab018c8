<?xml version="1.0" encoding="UTF-8"?>
<!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
<configuration status="ERROR" monitorInterval="1800">

    <Properties>
        <property name="console_log_pattern">%style{%d}{bright,blue} %highlight{%-5level}{ERROR=Bright RED, WARN=Bright
            Yellow, INFO=Bright Green, DEBUG=Bright Cyan, TRACE=Bright White} %style{[%t]}{magenta}
            %style{%c{3.}.%M(%L)}{cyan}: %msg%n
        </property>
        <!-- ==============================================公共配置============================================== -->
        <property name="basePath">logs/</property>

        <!-- ============================================控制台显示控制============================================ -->
        <property name="console_print_level">ERROR</property>
        <property name="console_log_pattern">%d{MM-dd HH:mm:ss.SSS} [%t] %-5p [%c{3}] %m%n</property>

        <property name="output_log_level">DEBUG</property>

        <!-- ============================================日志文件配置============================================ -->
        <property name="every_file_size">200MB</property>
        <property name="log_pattern">%d{MM-dd HH:mm:ss.SSS} [%t] %-5p [%c{3}] %m%n</property>

        <!-- ===========================================所有级别日志文件配置=========================================== -->
        <property name="default_fileName">${basePath}/default.log</property>
        <property name="default_filePattern">${basePath}/%d{yyyy-MM}/default-%d{yyyy-MM-dd}-%i.log.gz</property>
        <property name="default_max">500</property>
        <property name="default_timeInterval">1</property>
        <property name="default_timeModulate">true</property>

        <!-- ============================================Error级别日志文件配置============================================ -->
        <property name="error_fileName">${basePath}/error.log</property>
        <property name="error_filePattern">${basePath}/%d{yyyy-MM}/error-%d{yyyy-MM-dd}-%i.log.gz</property>
        <property name="error_max">500</property>
        <property name="error_timeInterval">1</property>
        <property name="error_timeModulate">true</property>

    </Properties>

    <appenders>
        <!-- =======================================用来定义输出到控制台的配置======================================= -->
        <Console name="Console" target="SYSTEM_OUT">
            <ThresholdFilter level="${console_print_level}" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${console_log_pattern}"/>
        </Console>

        <!-- ================================打印root中指定的level级别以上的日志到文件================================ -->
        <RollingFile name="DefaultFile" fileName="${default_fileName}" filePattern="${default_filePattern}">
            <PatternLayout pattern="${log_pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="${default_timeInterval}" modulate="${default_timeModulate}"/>
                <SizeBasedTriggeringPolicy size="${every_file_size}"/>
            </Policies>
            <DefaultRolloverStrategy max="${default_max}"/>
        </RollingFile>

        <!-- =======================================打印ERROR级别的日志到文件======================================= -->
        <RollingFile name="ErrorFile" fileName="${error_fileName}" filePattern="${error_filePattern}">
            <PatternLayout pattern="${log_pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="${error_timeInterval}" modulate="${error_timeModulate}"/>
                <SizeBasedTriggeringPolicy size="${every_file_size}"/>
            </Policies>
            <DefaultRolloverStrategy max="${error_max}"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingFile>

    </appenders>

    <loggers>
        <root level="${output_log_level}">
            <appender-ref ref="Console"/>
            <appender-ref ref="DefaultFile"/>
            <appender-ref ref="ErrorFile"/>
        </root>

        <logger name="java.sql" level="DEBUG" additivity="true"/>
        <logger name="amp.feign" level="INFO" additivity="true"/>
        <logger name="aam.feign" level="INFO" additivity="true"/>
        <logger name="common.feign" level="INFO" additivity="true"/>
        <logger name="org.springframework" level="WARN" additivity="true"/>

        <logger name="org" level="WARN" additivity="true"/>
        <logger name="com" level="WARN" additivity="true"/>
        <logger name="net" level="WARN" additivity="true"/>
        <logger name="io.netty" level="WARN" additivity="true"/>

        <logger name="httpclient.wire.content" level="INFO" additivity="true"/>
        <logger name="httpclient.wire.header" level="INFO" additivity="true"/>
        <logger name="net.sf.ehcache.store.disk" level="INFO" additivity="true"/>

    </loggers>

</configuration>