management.server.servlet.context-path=/actuator
management.server.health.mail.enabled=false
#集群配置
licc.func.clu.api.url=192.168.10.127:8888/licc-func-server
#免证办-集群版本url
licc.func.clu.derate_api.url=192.168.10.127:8889/license-derate-api
# 日志级别（可选：NONE, BASIC, HEADERS, FULL）
feign.client.config.default.loggerLevel=FULL
# 全局配置，也可以针对特定服务进行配置
# 连接超时时间（毫秒）
feign.client.config.default.connectTimeout:5000
# 读取超时时间（毫秒）
feign.client.config.default.readTimeout:5000
# 单机情况，该配置需要屏蔽
feign.client.config.default.request-interceptors[]:gov.licc.base.clu.interceptor.FeignTokenInterceptor,gov.licc.func.clu.api.FeignCurrenAccountInterceptor
feign.hystrix.enabled=false
feign.okhttp.enabled=true
# 设置最大连接数
feign.httpclient.max-connections=200
# 设置每个路由的最大连接数
feign.httpclient.max-connections-per-route=50