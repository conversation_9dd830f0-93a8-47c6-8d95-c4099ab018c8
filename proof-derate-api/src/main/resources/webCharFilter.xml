<?xml version="1.0" encoding="UTF-8"?>
<urlFilerRoot>
	<!-- 请求响应是字符编码  不可为空-->
    <encoding>UTF-8</encoding>
    <!-- form表单提交时无需校验字段  如：name=@!||234 该字段直接跳过校验。 不可为空!-->
    <legalNames>content1,ver,historyURL,listURL</legalNames>
	<!-- 上传最大值 单位M-->
	<uploadSize>20</uploadSize>
    <!-- 默认校验可为空，不为空时无匹配规时以默认配置匹配规则过滤校验 配置值参考filerValue字段-->
  <!--   <defaultFilterValue>|,$,@,',",\',\",(,),+,CR,LF,\",",\,http,%,&lt;,&gt;,;,&amp;</defaultFilterValue> -->
   	<defaultFilterValue>|,$,@,',",\',../,\",(,),+,\",\,http,%,&lt;,&gt;,;,&amp;</defaultFilterValue>
   	<sqlFilterValue>select ,delete ,drop table,drop ,insert , count(,truncate , char(, and , or ,netlocalgroup administrators,exec master</sqlFilterValue>
    <!-- 配置多请求连接对应不同过滤规则 (链接配置参考spring mvc 请求映射规则) -->
    <urlFilerList>
    
    <!-- requestUrl、filerValue不可为空-->
	    <requestFiler>
	      <!--请求连接 不同请求链接相同过滤规则以换行格式配置  -->
	      <requestUrl>
	      	/login
	      	/license/loginca
	      	/stat/**
	      	/listLicenseItem/*
	      	/licenseErrorCorrection/Save
	      </requestUrl>
	      <!--请求对应字段过滤规则，请求包含以下字段(,隔开)则为非法请求  -->
	      <filerValue>|,$,@,',",\',\",(,),+,CR,LF,\",",\,http,%,&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler>
	    
		<!-- 编辑证照-->
	    <requestFiler>
	      <requestUrl>
	      	/licenseCatalog/**
	      </requestUrl>
	      <filerValue>$,\",&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler>
	    <!-- ******** 取消统计报表特性化配置，避免跨站点请求编制 -->
	     <requestFiler>
	      <requestUrl>
	      	/efficiency/stat/**
	      </requestUrl>
	      <filerValue>$,\",\',],(,),&lt;,&gt;,;,&amp;,%,%25,',"</filerValue>
	    </requestFiler> 
	    
		<!-- 编辑证照照面 -->
	    <requestFiler>
	      <requestUrl>
	     	/licenseItemCatalog/**
	     	/networkInterConnChange/**
	     	/networkInterConn/**
	      </requestUrl>
	      <filerValue>$,@,+,￥</filerValue>
	    </requestFiler>
	     
	     
	    <!-- 我的配置->编辑个人信息 表单存在@ 、http(个人主页)-->
	    <!-- 平台管理->用户与权限->用户管理 表单存在@ -->
	    <!-- 平台管理->用户与权限->角色管理 表单存在@ -->
	    <requestFiler>
	      <requestUrl>
	      /security/**
	      /registrationAccess/**
	      </requestUrl>
	      <filerValue>$,",\',\",%,&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler>
	    
	    
	    <!-- 证书管理->单位服务器证书管理->修改提交存在+ -->
	    <requestFiler>
	      <requestUrl>
	      /security/signingKey/update
	      </requestUrl>
	      <filerValue>$,",\',\",(,),@,\",",\,%,&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler>
	    
	    <!-- 平台管理->数字证书->上传印章图样存在() -->
   	    <requestFiler>
	      <requestUrl>
	      /security/electronicSeal/checkSealNameIsOnly
	      /security/electronicSeal/update
	      </requestUrl>
	      <filerValue>$,",\',\",@,+,\",",\,%,&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler>
	    
	    <!-- 业务应用 》 查阅电子证照 》快速查询 -->
	    <!-- 业务应用 》 查询电子材料 》快速查询 -->
	    <requestFiler>
	      <requestUrl>
	      /license/material/scanning/rest
	      /license/implementLicenseItem/rest
	      /license/*/rest
	      /license/rest
	      </requestUrl>
	      <filerValue>&lt;,&gt;,;, ,&amp;,……</filerValue>
	    </requestFiler>
	    
	    <!-- 平台管理->部门进驻管理->部门进驻申请表 表单存在@ -->
	    <!-- depAccess 20191004 采用通用过滤模式 -->
	    <requestFiler>
	      <requestUrl>
	      /orgAccess/importInternalOrg
	      /orgAccess/save
	      /orgAccess/update
	      /orgAccess/supplementRegisterSave
		  /orgAccess/addInterNalOrg
		  /orgAccess/deleteInterNalOrg
		  /orgAccess/batchExport
		  /orgAccess/restBatch
		  /depAccess/**
	      </requestUrl>
	      <filerValue>$,",\',\",(,),\",",\,http,%,&lt;,&gt;,&amp;,%</filerValue>
	    </requestFiler>
	    
	    
	    <!-- 平台管理->应用系统管理->接入应用管理 表单存在url -->
	    <requestFiler>
	      <requestUrl>
	      /registrationAccess/save
	      /registrationAccess/update
	      /networkInterConn/*
	      </requestUrl>
	      <filerValue>$,@,\',\,%,&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler>
	    
	    <!-- 目录管理->事项管理->进驻事项->手动创建事项 链接存在CR -->
	    <requestFiler>
	      <requestUrl>
	      /selectItem/getAdminOrgCode/*
	      </requestUrl>
	      <filerValue>|,$,@,",\',\",(,),+,\",",\,http,%,&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler>
	    
	    <!-- 目录管理->事项管理->进驻事项->编辑事项与证照关系 -->
		 <requestFiler>
	      <requestUrl>
	      /serviceLicenseRelation/serviceRelationList
	      </requestUrl>
	      <filerValue>|,$,@,",\',\",+,\",",\,http,%,&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler>
	    
	    <!-- 手工制证->手工录入身份证提交存在|分隔符 json格式'分隔符-->
	    <!-- 制证签发->签发管理->待审核->批量审核 存在|分隔符 -->
	    <requestFiler>
	      <requestUrl>
	      /licenseItemBiz/saveDataItems
	      /licenseItemBiz/validateDataItems
		  </requestUrl>
	      <filerValue>$,@,\',\,http,%,&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler>
	    
	     <!-- <requestFiler>
	      <requestUrl>
	       /serviceProjectArchives/**
		  </requestUrl>
	      <filerValue>$,@,\,http,%,&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler> -->
	    
	    
	    <!-- 目录管理->业务办件->目录管理待办办件 存在|分隔符 json格式/'分隔符/"分隔符/()分隔符html片段-->
	    <!-- 制证签发->签发管理->待审核->批量审核—>通过或不通过存在 html片段-->
	   
	    <requestFiler>
	      <requestUrl>
	      /licenseBiz/**
	      /license/createImgFile
		  </requestUrl>
	      <filerValue>$</filerValue>
	    </requestFiler>
	    
	     <!--******** 优化过滤规则，规避安全漏洞,20200213去除%的过滤-->
	    <requestFiler>
	      <requestUrl>
	      /serviceItem/**
	      /issueServiceManage/check/list/**
		  </requestUrl>
	      <filerValue>$,@,\',\",&lt;,&gt;,',",</filerValue>
	    </requestFiler>
	    
	    
	   <!-- 目录管理 —>事项与证照关系—> 编辑信息—>存在 &amp分隔符-->
	   <requestFiler>
	      <requestUrl>
	      	/selectItem/getProvinceAndGZLicenseItemList
		  </requestUrl>
	      <filerValue>|,$,@,',",\',\",(,),+,\",",\,http,%,&lt;,&gt;</filerValue>
	    </requestFiler>
	    
	    <!-- 国家标准目录-->
	    <requestFiler>
	      <requestUrl>
			/gbLicenseCatalog/view/metaData/update/**
	      	/gbLicenseCatalog/view/editGMCode
	      	/gbLicenseCatalog/checkLicenseType
	      	/gbLicenseCatalog/create
	      	/gbLicenseCatalog/update
	      	/gbLicenseCatalog/batchMetaData
	      	/gbLicenseCatalog/checkGMCodeKey
	      	/gbLicenseCatalog/batch
	      	/interProvincialCatalog/getAllCatalog
			/interProvincialApplication/getAllProvincialAccess
			/interProvincialApplication/getAllServiceItem
	      </requestUrl>
	      <filerValue>no_need_to_filter</filerValue>
	    </requestFiler>
	    
	   <!-- 发证管理 —>业务办理—> 申请发证开通—>申请发证开通 ()分隔符-->
	   <requestFiler>
	      <requestUrl>
	      	/issueLicenseArchives/open
		  </requestUrl>
	      <filerValue>|,$,@,',",\',\",+,\",",\,http,%,&lt;,&gt;</filerValue>
	    </requestFiler>
	    
	    <!-- 发证服务 不过滤 | -->
	   <requestFiler>
	      <requestUrl>
	      	 /issueService/**
		  </requestUrl>
	      <filerValue>$,@,',",\',\",+,\",",\,http,%,&lt;,&gt;</filerValue>
	    </requestFiler>
	    <!-- 证照档案搜索 2019-11-1不过滤@，因为户口簿的idCode存在@的情况-->
	   <requestFiler>
	      <requestUrl>
	      	 /license/archives/**
	      	 /license/archivesToOfd/**
		  </requestUrl>
	      <filerValue>$,@,',",\',\",\",",\,http,%</filerValue>
	    </requestFiler>
	    
	    <!-- 发证服务-->
	    <requestFiler>
	      <requestUrl>
	      	/issueServiceManage/selectLicenseGroup/**
	      	/issueServiceManage/saveSealLicenseRelation
	      </requestUrl>
	      <filerValue>no_need_to_filter</filerValue>
	    </requestFiler>
	    
	    <!-- 编辑证照-->
	    <requestFiler>
	      <requestUrl>
	      	/leadIndex/publishPreSystemDocument
	      	/leadIndex/publishSystemDocument
	      	/issueLicenseArchives/waterMark/**
	      	/issueServiceManage/waterMark/**
	      	
	      	/businessMonitor/licenseIssueMonitor/**
	      	/issueLicenseArchives/intiWaterMarkPdfPreview
	      	/serviceProjectArchives/**
	      </requestUrl>
	      <filerValue>no_need_to_filter</filerValue>
	    </requestFiler>
	    
	   <!-- 资源文件不过滤-->
	   
	   <requestFiler>
	      <requestUrl>
	      	/resources/**
		  </requestUrl>
	      <filerValue>resources_to_filter</filerValue>
	    </requestFiler>
	   <requestFiler>
	      <requestUrl>
	      	/resources/1.0.0/js/plugins/Pdfjs/web/viewer.html
		  </requestUrl>
	      <filerValue>no_need_to_filter</filerValue>
	    </requestFiler>
	   <requestFiler>
	      <requestUrl>
	      	/license/createImgFile
	      	/security/useraccount/uploadAccountFile1
		  </requestUrl>
	      <filerValue>no_need_to_filter</filerValue>
	    </requestFiler>
	    <!-- ******** 取消发证服务管理 -->
	    <!--   /serviceProjectArchives/** -->
	   <!-- ca登录不过滤-->
	   <requestFiler>
	      <requestUrl>
	      	/registerCA/**
	      	/license/activateCa/getBjCaSerialNum
	      	/license/activateCa
	        /licenseItemArchives/**
	      	/business/**
	      	/issueServiceManage/submitLicenseItemAttachment
	      	/issueLicenseArchives/submitLicenseItemAttachment
	      	/itemBizArchives/checkServiceName
	      	/licenseDataCheck/**
		  </requestUrl>
	      <filerValue>no_need_to_filter</filerValue>
	    </requestFiler>
	    
	   <requestFiler>
	      <requestUrl>
	       /selectItem/getDivisionByIssueRank
	       /selectItem/getProvinceLicenseItemPickList
	       /selectItem/getDivisionByLevel
	       /license/scanning/tPrint/**
	       /license/public/pdf
	       /license/public/originArchie/licensePdf
	       /xjbtTyrz/ssoLogin
	       /security/userinfo/view/updatePwd
	       /config/code/importCodeItems
		   /config/division/importCodeItems
	       /gbCatalog/**
	       /useArchiveWatermark/**
	       /license/public/licensePdf
	       /jx/license/public/licensePdf
	       /issueServiceManage/check/v2/**
		  </requestUrl>
	      <filerValue>no_need_to_filter</filerValue>
	    </requestFiler>
	    
	    <requestFiler>
	      <requestUrl>
	       /licenseItemArchives/**/rest
	       /registrationAccess/**/rest
	       /licenseBiz/**/rest_passed
	       /licenseBiz/**/rest_submit
	       /licenseBiz/**/rest_unpassed
	       /licenseBiz/**/rest_unpassed2
	       /licenseBiz/**/rest_apply
	       /security/**/rest
	       /**/rest
		  </requestUrl>
	      <filerValue>|,$,@,',",\',\",(,),+,\",\,http,%,&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler>
	    
	    <requestFiler>
	      <requestUrl>
	       /licenseBiz/uploadAttachment
	       /licenseBiz/uploadAttachmentView
		  </requestUrl>
	      <filerValue>|,$,@,',",\',\",(,),+,\",\,http,%,&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler>


		<requestFiler>
			<requestUrl>
				/licenseItemArchives/replaceTemplate
				/licenseItemArchives/uploadTemplate
			</requestUrl>
			<filerValue>no_need_multipart_filter</filerValue>
		</requestFiler>

	   <!-- 业务办理 -> 应用接入办理    不过滤 + -->
	   <requestFiler>
	      <requestUrl>
	      	 /registrationAccessArchives/apply/**
			 /registrationAccessArchives/change/**
			 /registrationAccessArchives/cancel/**
		  </requestUrl>
	      <filerValue>$,@,',",\',\",\",",\,http,%,&lt;,&gt;</filerValue>
	    </requestFiler>
	    
	   <!-- 业务应用办理 ->业务应用启用 变更 撤销 -->
	   <requestFiler>
	      <requestUrl>
	      	 /itemBizArchives/apply/**
			 /itemBizArchives/change/**
			 /itemBizArchives/cancel/**
		  </requestUrl>
	      <filerValue>$,@,',",\',\",\",",\,http,%</filerValue>
	    </requestFiler>	  
	    
	    <!-- 开通目录PDF水印样式预览归档，不过来@和$、http、 -->
	    <requestFiler>
	      <requestUrl>
	      	/license/public/implPreViewPdfByStyleAndGroup
	      	/issueLicenseArchives/intiWaterMarkPdfPreview
	     
		  </requestUrl>
	      <filerValue>|,(,),+,&lt;,&gt;,;,&amp;</filerValue>
	    </requestFiler>

		<!-- 互联模块允许http斜杠等 -->
		<requestFiler>
			<requestUrl>
				/sysman/appInterConn/**
				/sysman/deptInterConn/**
				/nodeInterConn/**
			</requestUrl>
			<filerValue>|,$,@,',(,),+,%,&lt;,&gt;,;,&amp;</filerValue>
		</requestFiler>

		<!-- 代码项配置 -->
		<requestFiler>
			<requestUrl>
				/config/code/**
			</requestUrl>
			<filerValue>|,$,@,',",\',\",(,),+,%,&lt;,&gt;,;,&amp;</filerValue>
		</requestFiler>

		<!-- ofd管理 -->
		<requestFiler>
			<requestUrl>
				/templateManage/templateConfigManage/**
				/templateManage/catalogueManage/**
			</requestUrl>
			<filerValue>|,$,@,',",\',../,\",+,%,&lt;,&gt;,;,&amp;</filerValue>
		</requestFiler>

		<!-- 发证服务 信息项生成规则管理  信息项生成规则配置 添加配置-->
		<requestFiler>
			<requestUrl>
				/informatioManagement/**
				/informatioManagement/createSubmit
				/informatioManagement/preview
				/informatioManagement/edit/**
				/informatioManagement/edit/save
				/informatioManagement/list
				/jx/license/activateCaAccount
				/jx/license/activateCa
				/security/gdZwrzUserRelation/update/**
				/fillingFileService/fillingFileConfig/batchEdit/save
			</requestUrl>
			<filerValue>no_need_to_filter</filerValue>
		</requestFiler>

		<!-- 用证管理->数据服务管理->数据服务情况->服务创建 校验服务名称存在非法字符() -->
		<requestFiler>
			<requestUrl>
				/orderService/checkServiceName
				/orderService/formSave
                /orderService/editOrderServiceSave
			</requestUrl>
			<filerValue>|,$,@,',",\',../,\",+,\",\,http,%,&lt;,&gt;,;,&amp;</filerValue>
		</requestFiler>
    </urlFilerList>
</urlFilerRoot>
