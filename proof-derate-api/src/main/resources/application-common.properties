# 该配置文件为公共配置文件，子项目按需spring.profiles.include 进行引入
# springboot2.X文件上传大小限制
spring.servlet.multipart.max-file-size=1MB
spring.servlet.multipart.max-request-size=10MB
# HTTPS 之 请求头缺少HTTPOnly和Secure属性解决方案
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=true
# springbootV2.6.x 后需要增加该配置放开循环依赖
spring.main.allow-circular-references=true
#控制台是否打印
spring.jpa.show-sql=true
#格式化sql语句
spring.jpa.hibernate.format_sql=true
#指出是什么操作生成了该语句
#spring.jpa.properties.hibernate.use_sql_comments=true
# Hibernate时区配置
spring.jpa.properties.hibernate.jdbc.time_zone=Asia/Shanghai
# 在事务外也可以访问懒加载的数据
spring.jpa.open-in-view=false
# jpa是否启动时，根据类型创建表与修改表结构，建议第一次的时候开启此配置项，其余时间关闭。
spring.jpa.hibernate.ddl-auto=none
# 可选，配置了spring.cache.jcache.config属性会自动装配JCacheCacheManager
spring.cache.type=jcache
# 指定ehcache的配置文件所在的位置
spring.cache.jcache.config=classpath:ehcache3.xml
#jackson相关配置
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
#时区必须要设置
spring.jackson.time-zone=Asia/Shanghai
#ALWAYS的意思是即时属性为null，仍然也会输出这个key，对应yml里面的注释里面的类型
spring.jackson.default-property-inclusion=ALWAYS
# 某些类对象无法序列化的时候，是否报错
spring.jackson.serialization.fail-on-empty-beans=false
# json对象中有不存在的属性时候，是否报错
spring.jackson.deserialization.fail-on-unknown-properties=false
