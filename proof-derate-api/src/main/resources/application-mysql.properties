spring.datasource.druid.url=*****************************************************************************************************************************************
spring.datasource.druid.username=<PERSON><PERSON>(fe223bcc1b40be9c3c89b90f5fd9864d)
spring.datasource.druid.password=ENC(5f881b4308dc9ccbabaf1a698b84cc79)
# 链接有效性参数
spring.datasource.druid.validation-query=select 1
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
# 连接池参数
spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=205
spring.datasource.druid.max-active=300
spring.datasource.druid.max-wait=5000
spring.datasource.druid.poolPreparedStatements=true
spring.datasource.druid.maxOpenPreparedStatements=100
spring.jpa.database-platform=org.hibernate.dialect.MySQL5Dialect
spring.jpa.database=mysql