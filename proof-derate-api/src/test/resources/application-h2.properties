#数据源
#spring.datasource.druid.url=jdbc:h2:file:./db/test01

spring.datasource.druid.url=jdbc:h2:mem:xwj_db;MODE=MySQL;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.druid.username=sa
spring.datasource.druid.password=
spring.datasource.druid.driver-class-name=org.h2.Driver
spring.datasource.druid.initial-size=10
spring.datasource.druid.max-active=50
spring.datasource.druid.min-idle=5
spring.datasource.druid.max-wait=6000
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.max-open-prepared-statements=20
spring.datasource.druid.validation-query=select 1 from dual