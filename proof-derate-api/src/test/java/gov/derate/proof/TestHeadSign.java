package gov.derate.proof;

import gov.derate.proof.common.utils.Sm3Util;
import gov.license.common.crypto.sca.SMGenerator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.UUID;

/**
 * <p>
 * 测试第三方签名
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/1/12
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/1/12；
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class TestHeadSign {
    /**
     * 测试签名
     * requestId = 随机值，建议uuid
     * timestamp = 时间戳，5分钟内
     * appSecurity = 接入系统秘钥
     * signedValue = 需要计算拼接的字符串
     * signature = sm3计算后的字符串
     */
    @Test
    public void thirdPartSignTest() {
        long timestamp = System.currentTimeMillis();
        String requestId = UUID.randomUUID().toString();
        System.out.println("requestId=" + requestId);
        System.out.println("timestamp=" + timestamp);
        String appSecurity = "cbd4a59344cfeab1268faf3af4c136b3";
        System.out.println("appSecurity=" + appSecurity);
        String signedValue = requestId + timestamp + appSecurity;
        System.out.println("signedValue=" + signedValue);
        System.out.println("signature=" + SMGenerator.sm3().digestHex(signedValue));
    }

    /**
     * 测试签名
     * requestId = 随机值，建议uuid
     * timestamp = 时间戳，5分钟内
     * appSecurity = 接入系统秘钥
     * signedValue = 需要计算拼接的字符串
     * signature = sm3计算后的字符串
     */
    @Test
    public void thirdPartSignTest2() {
        String timeStamp = "1645017514176";
        String requestId = null;
        String userToken = null;
        String appkey = "32132121231232132";
        String appSecret = "204aa7643a93e7d1e4d15f83ab626c7d";
        System.out.println("signature=" + Sm3Util.sign(appkey, timeStamp, requestId, userToken, appSecret));
    }
}
