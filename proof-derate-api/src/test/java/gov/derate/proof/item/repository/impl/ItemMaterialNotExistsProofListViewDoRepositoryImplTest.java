package gov.derate.proof.item.repository.impl;

import gov.derate.proof.item.entity.ItemMaterialNotExistsProofListViewDo;
import gov.derate.proof.item.repository.ItemMaterialNotExistsProofListViewDoCustomRepository;
import gov.derate.proof.item.query.ItemMaterialListQuery;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

import static org.junit.Assert.assertFalse;

/**
 * <p>
 * ItemMaterialNotExistsProofListViewDoCustomRepositoryImplTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2016-11-23
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2021/10/11；
 */
@RunWith(SpringRunner.class)
@WebAppConfiguration
@PowerMockIgnore("javax.management.*")
@Rollback
@Transactional(rollbackFor = Exception.class)
@SpringBootTest
public class ItemMaterialNotExistsProofListViewDoRepositoryImplTest {
    @Autowired
    private ItemMaterialNotExistsProofListViewDoCustomRepository itemMaterialNotExistsProofListViewDoCustomRepository;

    @Test
    public void queryMaterialBoByItem() {
        ItemMaterialListQuery itemMaterialListQueryVo = new ItemMaterialListQuery();
//        itemMaterialListRequestVo.setItemCode("");
//        itemMaterialListRequestVo.setMaterialName("");
//        itemMaterialListRequestVo.setItemCodeFromUnionQuery("");

//        itemMaterialListRequestVo.setMaterialType(MaterialTypeEnum.CERTIFICATE_PROVE);
//        itemMaterialListRequestVo.setItemMaterialSource(false);
//        itemMaterialListRequestVo.setLicenseCode("");
//        itemMaterialListRequestVo.setLicenseName("");

        List<ItemMaterialNotExistsProofListViewDo> listViewList = itemMaterialNotExistsProofListViewDoCustomRepository.queryMaterialBoByItem(itemMaterialListQueryVo);
        assertFalse(CollectionUtils.isEmpty(listViewList));

    }

    @Test
    public void queryMaterialBoByItemTest2() {
        ItemMaterialListQuery itemMaterialListQueryVo = new ItemMaterialListQuery();
        itemMaterialListQueryVo.setItemCode("11441500MB2C9380303440712002006");
//        itemMaterialListRequestVo.setMaterialName("不动产权证书");
//        itemMaterialListRequestVo.setItemCodeFromUnionQuery("11441500MB2C9380303440712002006");

//        itemMaterialListRequestVo.setMaterialType(MaterialTypeEnum.CERTIFICATE_PROVE);
//        itemMaterialListRequestVo.setItemMaterialSource(false);
//        itemMaterialListRequestVo.setLicenseCode("");
//        itemMaterialListRequestVo.setLicenseName("");

        List<ItemMaterialNotExistsProofListViewDo> listViewList = itemMaterialNotExistsProofListViewDoCustomRepository.queryMaterialBoByItem(itemMaterialListQueryVo);
        assertFalse(CollectionUtils.isEmpty(listViewList));

    }

    @Test
    public void queryMaterialBoByItemTest3() {
        ItemMaterialListQuery itemMaterialListQueryVo = new ItemMaterialListQuery();
//        itemMaterialListRequestVo.setItemCode("11441500MB2C9380303440712002006");
        itemMaterialListQueryVo.setMaterialName("不动产权证书");
//        itemMaterialListRequestVo.setItemCodeFromUnionQuery("11441500MB2C9380303440712002006");

//        itemMaterialListRequestVo.setMaterialType(MaterialTypeEnum.CERTIFICATE_PROVE);
//        itemMaterialListRequestVo.setItemMaterialSource(false);
//        itemMaterialListRequestVo.setLicenseCode("");
//        itemMaterialListRequestVo.setLicenseName("");

        List<ItemMaterialNotExistsProofListViewDo> listViewList = itemMaterialNotExistsProofListViewDoCustomRepository.queryMaterialBoByItem(itemMaterialListQueryVo);
        assertFalse(CollectionUtils.isEmpty(listViewList));

    }

    @Test
    public void queryMaterialBoByItemTest5() {
        ItemMaterialListQuery itemMaterialListQueryVo = new ItemMaterialListQuery();
        itemMaterialListQueryVo.setItemCode("11441500MB2C9380303440712002006");
        itemMaterialListQueryVo.setMaterialName("不动产权证书");
//        itemMaterialListRequestVo.setItemCodeFromUnionQuery("11441500MB2C9380303440712002006");

//        itemMaterialListRequestVo.setMaterialType(MaterialTypeEnum.CERTIFICATE_PROVE);
//        itemMaterialListRequestVo.setItemMaterialSource(false);
//        itemMaterialListRequestVo.setLicenseCode("");
//        itemMaterialListRequestVo.setLicenseName("");

        List<ItemMaterialNotExistsProofListViewDo> listViewList = itemMaterialNotExistsProofListViewDoCustomRepository.queryMaterialBoByItem(itemMaterialListQueryVo);
        assertFalse(CollectionUtils.isEmpty(listViewList));

    }
}