package gov.derate.proof.item.repository;

import gov.derate.proof.base.BaseTestRepository;
import gov.derate.proof.item.entity.ItemAndItemMaterialViewDo;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class ItemAndItemMaterialViewDoRepositoryTest extends BaseTestRepository {
    @Autowired
    private ItemAndItemMaterialViewDoRepository itemAndItemMaterialViewDoRepository;

    @Test
    public void findByProofListIdTest() {
        List<ItemAndItemMaterialViewDo> all = itemAndItemMaterialViewDoRepository.findAll();
        Assert.assertNotNull(all);
    }
}