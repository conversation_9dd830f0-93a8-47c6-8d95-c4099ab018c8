package gov.derate.proof.item.repository;

import gov.derate.proof.item.entity.ItemMaterialExistsProofListViewDo;
import gov.derate.proof.item.query.ItemMaterialListQuery;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

import static org.junit.Assert.assertFalse;

@RunWith(SpringRunner.class)
@WebAppConfiguration
@PowerMockIgnore("javax.management.*")
@Rollback
@Transactional(rollbackFor = Exception.class)
@SpringBootTest
public class ItemMaterialExistsProofListViewDoCustomRepositoryTest {

    @Autowired
    private ItemMaterialExistsProofListViewDoCustomRepository itemMaterialExistsProofListViewDoCustomRepository;

    @Test
    public void queryMaterialBo() {
        ItemMaterialListQuery itemMaterialListQueryVo = new ItemMaterialListQuery();

//        itemMaterialListRequestVo.setMaterialName("经营场所使用权证明");
//        itemMaterialListRequestVo.setItemCode("11441500007240103U3442093023000");
//        itemMaterialListRequestVo.setMaterialType(MaterialTypeEnum.CERTIFICATE_PROVE);
        //以下条件用不上。
//        itemMaterialListRequestVo.setItemCodeFromUnionQuery("");
//        itemMaterialListRequestVo.setItemMaterialSource(false);
//        itemMaterialListRequestVo.setLicenseCode("");
//        itemMaterialListRequestVo.setLicenseName("");


        List<ItemMaterialExistsProofListViewDo> doList = itemMaterialExistsProofListViewDoCustomRepository.queryMaterialBo(itemMaterialListQueryVo);
        assertFalse(CollectionUtils.isEmpty(doList));
    }

    @Test
    public void queryMaterialBoTest2() {
        ItemMaterialListQuery itemMaterialListQueryVo = new ItemMaterialListQuery();

        itemMaterialListQueryVo.setMaterialName("经营场所使用权证明");
//        itemMaterialListRequestVo.setItemCode("11441500007240103U3442093023000");
//        itemMaterialListRequestVo.setMaterialType(MaterialTypeEnum.CERTIFICATE_PROVE);
        //以下条件用不上。
//        itemMaterialListRequestVo.setItemCodeFromUnionQuery("");
//        itemMaterialListRequestVo.setItemMaterialSource(false);
//        itemMaterialListRequestVo.setLicenseCode("");
//        itemMaterialListRequestVo.setLicenseName("");


        List<ItemMaterialExistsProofListViewDo> doList = itemMaterialExistsProofListViewDoCustomRepository.queryMaterialBo(itemMaterialListQueryVo);
        assertFalse(CollectionUtils.isEmpty(doList));
    }

    @Test
    public void queryMaterialBoTest3() {
        ItemMaterialListQuery itemMaterialListQueryVo = new ItemMaterialListQuery();

//        itemMaterialListRequestVo.setMaterialName("经营场所使用权证明");
        itemMaterialListQueryVo.setItemCode("11441500007240103U3442093023000");
//        itemMaterialListRequestVo.setMaterialType(MaterialTypeEnum.CERTIFICATE_PROVE);
        //以下条件用不上。
//        itemMaterialListRequestVo.setItemCodeFromUnionQuery("");
//        itemMaterialListRequestVo.setItemMaterialSource(false);
//        itemMaterialListRequestVo.setLicenseCode("");
//        itemMaterialListRequestVo.setLicenseName("");


        List<ItemMaterialExistsProofListViewDo> doList = itemMaterialExistsProofListViewDoCustomRepository.queryMaterialBo(itemMaterialListQueryVo);
        assertFalse(CollectionUtils.isEmpty(doList));
    }

    @Test
    public void queryMaterialBoTest4() {
        ItemMaterialListQuery itemMaterialListQueryVo = new ItemMaterialListQuery();

        itemMaterialListQueryVo.setMaterialName("经营场所使用权证明");
        itemMaterialListQueryVo.setItemCode("11441500007240103U3442093023000");
//        itemMaterialListRequestVo.setMaterialType(MaterialTypeEnum.CERTIFICATE_PROVE);
        //以下条件用不上。
//        itemMaterialListRequestVo.setItemCodeFromUnionQuery("");
//        itemMaterialListRequestVo.setItemMaterialSource(false);
//        itemMaterialListRequestVo.setLicenseCode("");
//        itemMaterialListRequestVo.setLicenseName("");


        List<ItemMaterialExistsProofListViewDo> doList = itemMaterialExistsProofListViewDoCustomRepository.queryMaterialBo(itemMaterialListQueryVo);
        assertFalse(CollectionUtils.isEmpty(doList));
    }
}