package gov.derate.proof.item.repository.impl;

import com.google.common.collect.Lists;
import gov.derate.proof.common.enums.ItemSourceEnum;
import gov.derate.proof.common.enums.ItemTypeEnum;
import gov.derate.proof.item.entity.NonstandardItemMaterialViewDo;
import gov.derate.proof.item.repository.NonstandardItemMaterialViewDoCustomRepository;
import gov.derate.proof.item.req.ItemListRequest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * NonstandardItemMaterialViewDoCustomRepositoryImplTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2016-11-23
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2021/10/11；
 */
@RunWith(SpringRunner.class)
@WebAppConfiguration
@PowerMockIgnore("javax.management.*")
@Rollback
@Transactional(rollbackFor = Exception.class)
@SpringBootTest
public class NonstandardItemMaterialViewDoRepositoryImplTest {
    @Autowired
    private NonstandardItemMaterialViewDoCustomRepository nonstandardItemMaterialViewDoCustomRepository;

    @Test
    public void queryNonstandardItemPageTest() {
        ItemListRequest listVo = new ItemListRequest();
       /* listVo.setMaterialName("");
        listVo.setItemCode("");
        listVo.setItemName("");
        listVo.setHandingItem("");
        listVo.setCreditCode("");
        listVo.setImplOrgName("");
        listVo.setDivisionCode("");
        listVo.setItemTypeLists(Lists.newArrayList());
        listVo.setItemSource(ItemSourceEnum.NOT_STANDARDIZATION);
        listVo.setItemSourceList(Lists.newArrayList());
        listVo.setItemClearStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);*/

        Page<NonstandardItemMaterialViewDo> nonstandardItemMaterialViewDos = nonstandardItemMaterialViewDoCustomRepository.queryNonstandardItemPage(listVo.buildQuery(), PageRequest.of(0, 2));
        Assert.assertNotNull(nonstandardItemMaterialViewDos);
    }

    @Test
    public void queryNonstandardItemPageTest2() {
        ItemListRequest listVo = new ItemListRequest();
        listVo.setMaterialName("经营场所使用权证明");
//        listVo.setItemCode("11441500007240103U3442093023000");
//        listVo.setItemName("单位内部设立印刷厂（所）登记");
//        listVo.setHandingItem("");
//        listVo.setCreditCode("11441500007240103U");
//        listVo.setImplOrgName("市委宣传部（市新闻出版局、市版权局）");
//        listVo.setDivisionCode("441500");
//        listVo.setItemTypeLists(Lists.newArrayList(ItemTypeEnum.OTHER_ADMINISTRATIVE_POWER));
//        listVo.setItemSource(ItemSourceEnum.STANDARDIZATION);
//        listVo.setItemSourceList(Lists.newArrayList());
//        listVo.setItemClearStatus(ItemProofStatusEnum.CLEAN);

        Page<NonstandardItemMaterialViewDo> nonstandardItemMaterialViewDos = nonstandardItemMaterialViewDoCustomRepository.queryNonstandardItemPage(listVo.buildQuery(), PageRequest.of(0, 1));
        Assert.assertNotNull(nonstandardItemMaterialViewDos);
    }

    @Test
    public void queryNonstandardItemPageTest3() {
        ItemListRequest listVo = new ItemListRequest();
//        listVo.setMaterialName("经营场所使用权证明");
        listVo.setItemCode("11441500007240103U3442093023000");
//        listVo.setItemName("单位内部设立印刷厂（所）登记");
//        listVo.setHandingItem("");
//        listVo.setCreditCode("11441500007240103U");
//        listVo.setImplOrgName("市委宣传部（市新闻出版局、市版权局）");
//        listVo.setDivisionCode("441500");
//        listVo.setItemTypeLists(Lists.newArrayList(ItemTypeEnum.OTHER_ADMINISTRATIVE_POWER));
//        listVo.setItemSource(ItemSourceEnum.STANDARDIZATION);
//        listVo.setItemSourceList(Lists.newArrayList());
//        listVo.setItemClearStatus(ItemProofStatusEnum.CLEAN);

        Page<NonstandardItemMaterialViewDo> nonstandardItemMaterialViewDos = nonstandardItemMaterialViewDoCustomRepository.queryNonstandardItemPage(listVo.buildQuery(), PageRequest.of(0, 1));
        Assert.assertNotNull(nonstandardItemMaterialViewDos);
    }

    @Test
    public void queryNonstandardItemPageTest4() {
        ItemListRequest listVo = new ItemListRequest();
//        listVo.setMaterialName("经营场所使用权证明");
//        listVo.setItemCode("11441500007240103U3442093023000");
        listVo.setItemName("单位内部设立印刷厂（所）登记");
//        listVo.setHandingItem("");
//        listVo.setCreditCode("11441500007240103U");
//        listVo.setImplOrgName("市委宣传部（市新闻出版局、市版权局）");
//        listVo.setDivisionCode("441500");
//        listVo.setItemTypeLists(Lists.newArrayList(ItemTypeEnum.OTHER_ADMINISTRATIVE_POWER));
//        listVo.setItemSource(ItemSourceEnum.STANDARDIZATION);
//        listVo.setItemSourceList(Lists.newArrayList());
//        listVo.setItemClearStatus(ItemProofStatusEnum.CLEAN);

        Page<NonstandardItemMaterialViewDo> nonstandardItemMaterialViewDos = nonstandardItemMaterialViewDoCustomRepository.queryNonstandardItemPage(listVo.buildQuery(), PageRequest.of(0, 1));
        Assert.assertNotNull(nonstandardItemMaterialViewDos);
    }

    @Test
    public void queryNonstandardItemPageTest5() {
        ItemListRequest listVo = new ItemListRequest();
//        listVo.setMaterialName("经营场所使用权证明");
//        listVo.setItemCode("11441500007240103U3442093023000");
//        listVo.setItemName("单位内部设立印刷厂（所）登记");
//        listVo.setHandingItem("");
        listVo.setCreditCode("11441500007240103U");
//        listVo.setImplOrgName("市委宣传部（市新闻出版局、市版权局）");
//        listVo.setDivisionCode("441500");
//        listVo.setItemTypeLists(Lists.newArrayList(ItemTypeEnum.OTHER_ADMINISTRATIVE_POWER));
//        listVo.setItemSource(ItemSourceEnum.STANDARDIZATION);
//        listVo.setItemSourceList(Lists.newArrayList());
//        listVo.setItemClearStatus(ItemProofStatusEnum.CLEAN);

        Page<NonstandardItemMaterialViewDo> nonstandardItemMaterialViewDos = nonstandardItemMaterialViewDoCustomRepository.queryNonstandardItemPage(listVo.buildQuery(), PageRequest.of(0, 1));
        Assert.assertNotNull(nonstandardItemMaterialViewDos);
    }

    @Test
    public void queryNonstandardItemPageTest6() {
        ItemListRequest listVo = new ItemListRequest();
//        listVo.setMaterialName("经营场所使用权证明");
//        listVo.setItemCode("11441500007240103U3442093023000");
//        listVo.setItemName("单位内部设立印刷厂（所）登记");
//        listVo.setHandingItem("");
//        listVo.setCreditCode("11441500007240103U");
        listVo.setImplOrgName("市委宣传部（市新闻出版局、市版权局）");
//        listVo.setDivisionCode("441500");
//        listVo.setItemTypeLists(Lists.newArrayList(ItemTypeEnum.OTHER_ADMINISTRATIVE_POWER));
//        listVo.setItemSource(ItemSourceEnum.STANDARDIZATION);
//        listVo.setItemSourceList(Lists.newArrayList());
//        listVo.setItemClearStatus(ItemProofStatusEnum.CLEAN);

        Page<NonstandardItemMaterialViewDo> nonstandardItemMaterialViewDos = nonstandardItemMaterialViewDoCustomRepository.queryNonstandardItemPage(listVo.buildQuery(), PageRequest.of(0, 1));
        Assert.assertNotNull(nonstandardItemMaterialViewDos);
    }

    @Test
    public void queryNonstandardItemPageTest7() {
        ItemListRequest listVo = new ItemListRequest();
//        listVo.setMaterialName("经营场所使用权证明");
//        listVo.setItemCode("11441500007240103U3442093023000");
//        listVo.setItemName("单位内部设立印刷厂（所）登记");
//        listVo.setHandingItem("");
//        listVo.setCreditCode("11441500007240103U");
//        listVo.setImplOrgName("市委宣传部（市新闻出版局、市版权局）");
        listVo.setDivisionCode("441500");
//        listVo.setItemTypeLists(Lists.newArrayList(ItemTypeEnum.OTHER_ADMINISTRATIVE_POWER));
//        listVo.setItemSource(ItemSourceEnum.STANDARDIZATION);
//        listVo.setItemSourceList(Lists.newArrayList());
//        listVo.setItemClearStatus(ItemProofStatusEnum.CLEAN);

        Page<NonstandardItemMaterialViewDo> nonstandardItemMaterialViewDos = nonstandardItemMaterialViewDoCustomRepository.queryNonstandardItemPage(listVo.buildQuery(), PageRequest.of(0, 1));
        Assert.assertNotNull(nonstandardItemMaterialViewDos);
    }

    @Test
    public void queryNonstandardItemPageTest8() {
        ItemListRequest listVo = new ItemListRequest();
//        listVo.setMaterialName("经营场所使用权证明");
//        listVo.setItemCode("11441500007240103U3442093023000");
//        listVo.setItemName("单位内部设立印刷厂（所）登记");
//        listVo.setHandingItem("");
//        listVo.setCreditCode("11441500007240103U");
//        listVo.setImplOrgName("市委宣传部（市新闻出版局、市版权局）");
//        listVo.setDivisionCode("441500");
        listVo.setItemTypeLists(Lists.newArrayList(ItemTypeEnum.OTHER_ADMINISTRATIVE_POWER));
//        listVo.setItemSource(ItemSourceEnum.STANDARDIZATION);
//        listVo.setItemSourceList(Lists.newArrayList());
//        listVo.setItemClearStatus(ItemProofStatusEnum.CLEAN);

        Page<NonstandardItemMaterialViewDo> nonstandardItemMaterialViewDos = nonstandardItemMaterialViewDoCustomRepository.queryNonstandardItemPage(listVo.buildQuery(), PageRequest.of(0, 1));
        Assert.assertNotNull(nonstandardItemMaterialViewDos);
    }

    @Test
    public void queryNonstandardItemPageTest9() {
        ItemListRequest listVo = new ItemListRequest();
//        listVo.setMaterialName("经营场所使用权证明");
//        listVo.setItemCode("11441500007240103U3442093023000");
//        listVo.setItemName("单位内部设立印刷厂（所）登记");
//        listVo.setHandingItem("");
//        listVo.setCreditCode("11441500007240103U");
//        listVo.setImplOrgName("市委宣传部（市新闻出版局、市版权局）");
//        listVo.setDivisionCode("441500");
//        listVo.setItemTypeLists(Lists.newArrayList(ItemTypeEnum.OTHER_ADMINISTRATIVE_POWER));
        listVo.setItemSource(ItemSourceEnum.STANDARDIZATION);
//        listVo.setItemSourceList(Lists.newArrayList());
//        listVo.setItemClearStatus(ItemProofStatusEnum.CLEAN);

        Page<NonstandardItemMaterialViewDo> nonstandardItemMaterialViewDos = nonstandardItemMaterialViewDoCustomRepository.queryNonstandardItemPage(listVo.buildQuery(), PageRequest.of(0, 1));
        Assert.assertNotNull(nonstandardItemMaterialViewDos);
    }

    @Test
    public void queryNonstandardItemPageTest10() {
        ItemListRequest listVo = new ItemListRequest();
//        listVo.setMaterialName("经营场所使用权证明");
//        listVo.setItemCode("11441500007240103U3442093023000");
//        listVo.setItemName("单位内部设立印刷厂（所）登记");
//        listVo.setHandingItem("");
//        listVo.setCreditCode("11441500007240103U");
//        listVo.setImplOrgName("市委宣传部（市新闻出版局、市版权局）");
//        listVo.setDivisionCode("441500");
//        listVo.setItemTypeLists(Lists.newArrayList(ItemTypeEnum.OTHER_ADMINISTRATIVE_POWER));
//        listVo.setItemSource(ItemSourceEnum.STANDARDIZATION);
        listVo.setItemSourceList(Lists.newArrayList(ItemSourceEnum.STANDARDIZATION));
//        listVo.setItemClearStatus(ItemProofStatusEnum.CLEAN);

        Page<NonstandardItemMaterialViewDo> nonstandardItemMaterialViewDos = nonstandardItemMaterialViewDoCustomRepository.queryNonstandardItemPage(listVo.buildQuery(), PageRequest.of(0, 1));
        Assert.assertNotNull(nonstandardItemMaterialViewDos);
    }

    @Test
    public void queryNonstandardItemPageTest11() {
        ItemListRequest listVo = new ItemListRequest();
//        listVo.setMaterialName("经营场所使用权证明");
//        listVo.setItemCode("11441500007240103U3442093023000");
//        listVo.setItemName("单位内部设立印刷厂（所）登记");
//        listVo.setHandingItem("");
//        listVo.setCreditCode("11441500007240103U");
//        listVo.setImplOrgName("市委宣传部（市新闻出版局、市版权局）");
//        listVo.setDivisionCode("441500");
//        listVo.setItemTypeLists(Lists.newArrayList(ItemTypeEnum.OTHER_ADMINISTRATIVE_POWER));
//        listVo.setItemSource(ItemSourceEnum.STANDARDIZATION);
//        listVo.setItemSourceList(Lists.newArrayList());
//        listVo.setItemClearStatus(ItemProofStatusEnum.CLEAN);

        Page<NonstandardItemMaterialViewDo> nonstandardItemMaterialViewDos = nonstandardItemMaterialViewDoCustomRepository.queryNonstandardItemPage(listVo.buildQuery(), PageRequest.of(0, 1));
        Assert.assertNotNull(nonstandardItemMaterialViewDos);
    }

    @Test
    public void queryNonstandardItemPageTest12() {
        ItemListRequest listVo = new ItemListRequest();
        listVo.setMaterialName("经营场所使用权证明");
        listVo.setItemCode("11441500007240103U3442093023000");
        listVo.setItemName("单位内部设立印刷厂（所）登记");
        listVo.setHandingItem("");
        listVo.setCreditCode("11441500007240103U");
        listVo.setImplOrgName("市委宣传部（市新闻出版局、市版权局）");
        listVo.setDivisionCode("441500");
        listVo.setItemTypeLists(Lists.newArrayList(ItemTypeEnum.OTHER_ADMINISTRATIVE_POWER, ItemTypeEnum.ADMINISTRATIVE_LICENSE));
        listVo.setItemSource(ItemSourceEnum.STANDARDIZATION);
        listVo.setItemSourceList(Lists.newArrayList(ItemSourceEnum.STANDARDIZATION, ItemSourceEnum.NOT_STANDARDIZATION));
//        listVo.setItemClearStatus(ItemProofStatusEnum.CLEAN);
//
        Page<NonstandardItemMaterialViewDo> nonstandardItemMaterialViewDos = nonstandardItemMaterialViewDoCustomRepository.queryNonstandardItemPage(listVo.buildQuery(), PageRequest.of(0, 10));
        Assert.assertNotNull(nonstandardItemMaterialViewDos);
    }
}
