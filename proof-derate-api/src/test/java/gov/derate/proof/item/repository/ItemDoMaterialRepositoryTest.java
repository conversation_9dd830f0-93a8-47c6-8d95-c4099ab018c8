package gov.derate.proof.item.repository;

import gov.derate.proof.item.entity.ItemMaterialDo;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/10.
 */
@RunWith(SpringRunner.class)
@WebAppConfiguration
@PowerMockIgnore("javax.management.*")
@Rollback
@Transactional(rollbackFor = Exception.class)
@SpringBootTest
public class ItemDoMaterialRepositoryTest {
    @Autowired
    private ItemMaterialRepository itemMaterialRepository;

    @Test
    public void findAllByItemCodeTest() {
        List<ItemMaterialDo> itemMaterialDoList = itemMaterialRepository.findAllByItemCode("1");
        Assert.assertTrue(CollectionUtils.isEmpty(itemMaterialDoList));
    }
}
