package gov.derate.proof.item.service.impl;

import gov.derate.proof.base.BaseTestService;
import gov.derate.proof.common.service.BeanFactoryUtils;
import gov.derate.proof.item.dto.ItemDto;
import gov.derate.proof.item.dto.ItemMaterialBo;
import gov.derate.proof.item.dto.ItemViewDto;
import gov.derate.proof.item.dto.NonstandardItemMaterialDto;
import gov.derate.proof.item.entity.ItemDo;
import gov.derate.proof.item.entity.ItemProofRelationDo;
import gov.derate.proof.item.query.ItemListQuery;
import gov.derate.proof.item.repository.ItemCustomRepository;
import gov.derate.proof.item.repository.ItemRepository;
import gov.derate.proof.item.service.ItemMaterialService;
import gov.derate.proof.list.entity.ReplaceLicenseDo;
import gov.derate.proof.list.repository.ItemProofRelationRepository;
import gov.derate.proof.list.repository.ReplaceLicenseRepository;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;
import java.util.Objects;

import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2021/9/10.
 */
@PowerMockIgnore("javax.management.*")
public class ItemServiceImplTest extends BaseTestService {

    /**
     * 注入实例
     */
    @InjectMocks
    ItemServiceImpl itemServiceImpl;

    @Mock
    private ItemRepository itemRepository;
    @Mock
    private ReplaceLicenseRepository replaceLicenseRepository;
    @Mock
    private ItemCustomRepository itemCustomRepository;
    @Mock
    private ItemProofRelationRepository itemProofRelationRepository;
    @Mock
    private ApplicationContext applicationContext;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        BeanFactoryUtils beanFactoryUtils = new BeanFactoryUtils();
        beanFactoryUtils.setApplicationContext(applicationContext);
    }

    @Test
    public void findByItemCodeTest() {
        PowerMockito.when(itemRepository.findByItemCode(Mockito.anyString())).thenReturn(null);
        ItemDto item = itemServiceImpl.findByItemCode("1");
        Assert.assertNull(item);
    }

    @Test
    public void getViewDataTest()  {
        ItemDo item = new ItemDo();
        item.setItemCode("1");
        PowerMockito.when(itemRepository.findByItemCode(Mockito.anyString())).thenReturn(item);
        List<ReplaceLicenseDo> replaceLicenseList = Lists.newArrayList();
        PowerMockito.when(replaceLicenseRepository.findByProofListIdIn(Mockito.anyList())).thenReturn(replaceLicenseList);
        List<ItemProofRelationDo> relationDoList = Lists.newArrayList();
        PowerMockito.when(itemProofRelationRepository.findAllByItemCode(Mockito.any())).thenReturn(relationDoList);
        ItemViewDto viewData = itemServiceImpl.getViewData("1", null, true);

        Assert.assertFalse(Objects.isNull(viewData));
    }


    @Test
    public void queryNonstandardItemPageTest()  {
        when(itemCustomRepository.queryNonstandardItemPage(Mockito.any(), Mockito.any())).thenReturn(null);

        ItemListQuery listVo = new ItemListQuery();
        PageRequest pageRequest = PageRequest.of(0, 10);
        Page<NonstandardItemMaterialDto> nonstandardItemMaterialVoPage = itemServiceImpl.queryNonstandardItemPage(listVo, pageRequest);

        Assert.assertNull(nonstandardItemMaterialVoPage);
    }

    @Test
    public void downImportTemplateTest() {
        String bytes = itemServiceImpl.downImportTemplate();
        Assert.assertNotNull(bytes);

    }
}
