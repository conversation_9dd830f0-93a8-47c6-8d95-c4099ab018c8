package gov.derate.proof.item.api;


import gov.derate.proof.item.req.ItemMaterialCreateRequest;
import gov.license.common.tools.jackson.JacksonUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;


/**
 * <AUTHOR>
 * @date 2021/9/26.
 */

@Rollback
@Transactional(rollbackFor = Exception.class)
@WebAppConfiguration
//SpringBoot1.4版本之前用的是SpringJUnit4ClassRunner.class
@RunWith(SpringRunner.class)
@SpringBootTest
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = true)  //这里的事务关联到项目的配置文件中的事务控制器（"transactionManager"），同时指定事物自动回滚（defaultRollback= true），以此对数据库不会产生数据污染。
@PowerMockIgnore("javax.management.*")
public class ItemMaterialWebApiMvcMockTest {

    protected MockMvc mockMvc;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Before
    public void setUp() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    public void listPage() throws Exception {
        PageRequest pageable = PageRequest.of(1, 2);
        String strJson = JacksonUtil.toJsonStr(pageable);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/item_material/page")
                        .accept(MediaType.parseMediaType("application/json;charset=utf-8"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(strJson)
                        .param("page_direction", "DESC")
                        .param("page_number", "1")
                        .param("page_size", "10")

                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void listTest() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/item_material/list")

        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void createTest() throws Exception {

        ItemMaterialCreateRequest createVo = new ItemMaterialCreateRequest();
        String strJson = JacksonUtil.toJsonStr(createVo);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/item_material/create")
                .accept(MediaType.parseMediaType("application/json;charset=utf-8"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(strJson)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

    @Test
    public void createByItemIdTest() throws Exception {

        ItemMaterialCreateRequest createVo = new ItemMaterialCreateRequest();
        String strJson = JacksonUtil.toJsonStr(createVo);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/item_material/create_by_item_id")
                .accept(MediaType.parseMediaType("application/json;charset=utf-8"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(strJson)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void edit() throws Exception {
        ItemMaterialCreateRequest editVo = new ItemMaterialCreateRequest();
        String strJson = JacksonUtil.toJsonStr(editVo);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/item_material/edit/123")
                .accept(MediaType.parseMediaType("application/json;charset=utf-8"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(strJson)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

    @Test
    public void delete() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/item_material/delete/123")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }


}
