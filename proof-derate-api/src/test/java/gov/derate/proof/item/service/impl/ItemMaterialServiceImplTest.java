package gov.derate.proof.item.service.impl;

import gov.derate.proof.item.dto.ItemMaterialBo;
import gov.derate.proof.item.entity.ItemMaterialDo;
import gov.derate.proof.item.repository.ItemMaterialExistsProofListViewDoCustomRepository;
import gov.derate.proof.item.repository.ItemMaterialNotExistsProofListViewDoCustomRepository;
import gov.derate.proof.item.repository.ItemMaterialRepository;
import gov.derate.proof.item.query.ItemMaterialListQuery;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/10.
 */
@RunWith(SpringRunner.class)
@PowerMockIgnore("javax.management.*")
public class ItemMaterialServiceImplTest {

    /**
     * 注入实例
     */
    @InjectMocks
    private ItemMaterialServiceImpl itemMaterialServiceImpl;

    @Mock
    private ItemMaterialRepository itemMaterialRepository;

    @Mock
    private ItemMaterialExistsProofListViewDoCustomRepository itemMaterialExistsProofListViewDoCustomRepository;
    @Mock
    private ItemMaterialNotExistsProofListViewDoCustomRepository itemMaterialNotExistsProofListViewDoCustomRepository;



    @Test
    public void queryByItemCodeTest() {
        PowerMockito.when(itemMaterialRepository.findAllByItemCode(Mockito.anyString())).thenReturn(null);

        List<ItemMaterialDo> itemMaterialList = itemMaterialServiceImpl.queryByItemCode("1");

        Assert.assertNull(itemMaterialList);
    }
}
