package gov.derate.proof.item.repository;

import gov.derate.proof.item.entity.ItemDo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2021/9/10.
 */
@RunWith(SpringRunner.class)
@WebAppConfiguration
@PowerMockIgnore("javax.management.*")
@Rollback
@SpringBootTest
@Transactional(rollbackFor = Exception.class)
public class ItemDoRepositoryTest {

    @Autowired
    private ItemRepository itemRepository;

    @Test
    public void findByItemCodeTest() {
        ItemDo itemDo = itemRepository.findByItemCode("1");
        Assert.assertNull(itemDo);
    }
}
