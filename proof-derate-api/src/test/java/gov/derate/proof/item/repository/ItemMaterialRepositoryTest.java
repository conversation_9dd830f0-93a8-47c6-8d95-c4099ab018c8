package gov.derate.proof.item.repository;

import com.google.common.collect.Lists;
import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.enums.ItemStatus;
import gov.derate.proof.item.entity.ItemMaterialDo;
import gov.derate.proof.stat.query.ItemStatQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/10.
 */
@RunWith(SpringRunner.class)
@WebAppConfiguration
@PowerMockIgnore("javax.management.*")
@Rollback
@Transactional(rollbackFor = Exception.class)
@SpringBootTest
public class ItemMaterialRepositoryTest {
    @Autowired
    private ItemMaterialRepository itemMaterialRepository;

    @Test
    public void findAllByItemCodeTest() {
        List<ItemMaterialDo> itemMaterialList = itemMaterialRepository.findAllByItemCode("1");
        Assert.assertTrue(CollectionUtils.isEmpty(itemMaterialList));
    }

    @Test
    public void countMaterialTest() {
        ItemStatQuery query = new ItemStatQuery();
        query.setDivisionCodeLeLength(6);
        query.setItemProofStatusList(Lists.newArrayList(ItemProofStatusEnum.APPROVED));
        query.setItemStatusList(Lists.newArrayList(ItemStatus.WORK));
        Long aLong = itemMaterialRepository.countMaterial(query);
        Assert.assertNotNull(aLong);
    }
}
