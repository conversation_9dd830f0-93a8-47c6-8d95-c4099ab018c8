package gov.derate.proof.item.webapi;

import gov.derate.proof.common.bo.UploadFileAttachmentBo;
import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.enums.ItemSourceEnum;
import gov.derate.proof.common.enums.ItemStatus;
import gov.derate.proof.common.service.OrganizationFilterService;
import gov.derate.proof.common.service.UploadFileAttachmentService;
import gov.derate.proof.common.service.UserCommonService;
import gov.derate.proof.item.bo.ItemAndItemMaterialViewBo;
import gov.derate.proof.item.bo.ItemChangeLogViewBo;
import gov.derate.proof.item.bo.ItemManagerViewBo;
import gov.derate.proof.item.dto.ItemDto;
import gov.derate.proof.item.dto.ItemViewDto;
import gov.derate.proof.item.dto.NonstandardItemMaterialDto;
import gov.derate.proof.item.req.*;
import gov.derate.proof.item.resp.ItemPageResp;
import gov.derate.proof.item.resp.ItemResponse;
import gov.derate.proof.item.service.ItemService;
import gov.license.common.api.resp.ResponseResult;
import gov.license.common.api.resp.data.BasePageRespData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ItemWebApiTest {
    @Mock
    Logger LOGGER;
    @Mock
    ItemService itemService;
    @Mock
    UserCommonService userCommonService;
    @Mock
    UploadFileAttachmentService uploadFileAttachmentService;
    @Mock
    OrganizationFilterService organizationFilterService;
    @InjectMocks
    ItemWebApi itemWebApi;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testWaitForCleanListPage() throws Exception {
        when(itemService.queryNewWay(any(), any())).thenReturn(null);
        when(organizationFilterService.getCreditCodesByPermissionCode(anyString())).thenReturn(Arrays.<String>asList("String"));

        ItemWaitForCleanListPageReq req = new ItemWaitForCleanListPageReq();
        ResponseResult<BasePageRespData<List<ItemPageResp>>> result = itemWebApi.waitForCleanListPage(req);
        Assert.assertEquals("200", result.getMeta().getCode());
    }

    @Test
    public void testCardingUnconfirmedListPage() throws Exception {
        when(itemService.queryNewWay(any(), any())).thenReturn(null);
        when(organizationFilterService.getCreditCodesByPermissionCode(anyString())).thenReturn(Arrays.<String>asList("String"));

        ResponseResult<BasePageRespData<List<ItemPageResp>>> result = itemWebApi.cardingUnconfirmedListPage(new ItemWaitForCleanListPageReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testCardingConfirmedListPage() throws Exception {
        when(itemService.queryNewWay(any(), any())).thenReturn(null);
        when(organizationFilterService.getCreditCodesByPermissionCode(anyString())).thenReturn(Arrays.<String>asList("String"));

        ResponseResult<BasePageRespData<List<ItemPageResp>>> result = itemWebApi.cardingConfirmedListPage(new ItemWaitForCleanListPageReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testProofListListPage() throws Exception {
        when(itemService.queryNewWay(any(), any())).thenReturn(null);
        when(organizationFilterService.getCreditCodesByPermissionCode(anyString())).thenReturn(Arrays.<String>asList("String"));

        ResponseResult<BasePageRespData<List<ItemPageResp>>> result = itemWebApi.proofListListPage(new ItemWaitForCleanListPageReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testWaitForCleanPageCount() throws Exception {
        when(itemService.countNewWay(any(), any())).thenReturn(null);
        when(organizationFilterService.getCreditCodesByPermissionCode(anyString())).thenReturn(Arrays.<String>asList("String"));

        ResponseResult<BasePageRespData<List<Long>>> result = itemWebApi.waitForCleanPageCount(new ItemWaitForCleanListPageReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testCardingUnconfirmedPageCount() throws Exception {
        when(itemService.countNewWay(any(), any())).thenReturn(null);
        when(organizationFilterService.getCreditCodesByPermissionCode(anyString())).thenReturn(Arrays.<String>asList("String"));

        ResponseResult<BasePageRespData<List<Long>>> result = itemWebApi.cardingUnconfirmedPageCount(new ItemWaitForCleanListPageReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testCardingConfirmedPageCount() throws Exception {
        when(itemService.countNewWay(any(), any())).thenReturn(null);
        when(organizationFilterService.getCreditCodesByPermissionCode(anyString())).thenReturn(Arrays.<String>asList("String"));

        ResponseResult<BasePageRespData<List<Long>>> result = itemWebApi.cardingConfirmedPageCount(new ItemWaitForCleanListPageReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testProofListPageCount() throws Exception {
        when(itemService.countNewWay(any(), any())).thenReturn(null);
        when(organizationFilterService.getCreditCodesByPermissionCode(anyString())).thenReturn(Arrays.<String>asList("String"));

        ResponseResult<BasePageRespData<List<Long>>> result = itemWebApi.proofListPageCount(new ItemWaitForCleanListPageReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testList() throws Exception {
        when(itemService.queryList(any())).thenReturn(Arrays.<ItemDto>asList(new ItemDto()));
        when(organizationFilterService.getCreditCodesByPermissionCode(anyString())).thenReturn(Arrays.<String>asList("String"));

        ResponseResult<List<ItemResponse>> result = itemWebApi.list("itemName", "handingItem", "creditCode", "divisionCode", Arrays.<ItemStatus>asList(ItemStatus.WORK), "itemTypeStr", ItemSourceEnum.NOT_STANDARDIZATION, "itemCode", ItemProofStatusEnum.WAIT_FOR_CLEAN);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testEdit() throws Exception {
        when(itemService.findById(anyString())).thenReturn(new ItemDto());

        ResponseResult<Object> result = itemWebApi.edit(new ItemRequest(), "id");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testView() throws Exception {
        when(itemService.getViewData(anyString(), any(), anyBoolean())).thenReturn(new ItemViewDto());

        ResponseResult<ItemViewDto> result = itemWebApi.view("itemCode", ItemProofStatusEnum.WAIT_FOR_CLEAN, Boolean.TRUE);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetItemMaterialPage() throws Exception {
        when(itemService.queryNonstandardItemPage(any(), any())).thenReturn(null);

        ResponseResult<BasePageRespData<List<NonstandardItemMaterialDto>>> result = itemWebApi.getItemMaterialPage(new ItemMaterialPageReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testItemMaterialPageList() throws Exception {
        when(itemService.managerItemPageQuery(any())).thenReturn(null);
        when(organizationFilterService.getCreditCodesByPermissionCode(anyString())).thenReturn(Arrays.<String>asList("String"));

        ResponseResult<BasePageRespData<List<ItemAndItemMaterialViewBo>>> result = itemWebApi.itemMaterialPageList(new ItemMaterialManagerPageReq());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testImportByExcel() throws Exception {
        when(itemService.importByExcel(any(), anyString(), anyLong())).thenReturn("importByExcelResponse");

        ResponseResult<Object> result = itemWebApi.importByExcel(null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testDownImportTemplate() throws Exception {
        when(itemService.downImportTemplate()).thenReturn("downImportTemplateResponse");

        ResponseResult<String> result = itemWebApi.downImportTemplate();
        Assert.assertEquals(null, result);
    }

    @Test
    public void testManagerCreate() throws Exception {
        when(itemService.createItemAndItemMaterial(any())).thenReturn("createItemAndItemMaterialResponse");
        when(userCommonService.validateUserOrgPermission(anyString(), anyString())).thenReturn(Boolean.TRUE);

        ResponseResult<String> result = itemWebApi.managerCreate(new ItemManagerCreateRequest());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testManagerEdit() throws Exception {
        when(itemService.managerEdit(any(), anyString())).thenReturn("managerEditResponse");
        when(userCommonService.validateUserOrgPermission(anyString(), anyString())).thenReturn(Boolean.TRUE);

        ResponseResult<String> result = itemWebApi.managerEdit(new ItemManagerEditRequest(), "itemId");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testFindInManagerByItemId() throws Exception {
        when(itemService.findItemAndItemMaterialByItemId(anyString())).thenReturn(new ItemManagerViewBo());

        ResponseResult<ItemManagerViewBo> result = itemWebApi.findInManagerByItemId("itemId");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testManagerItemMaterialCommitAttachment() throws Exception {
        when(uploadFileAttachmentService.save(anyString(), any())).thenReturn(new UploadFileAttachmentBo());

        ResponseResult<Map<String, String>> result = itemWebApi.managerItemMaterialCommitAttachment(null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testDownloadItemMaterialSampleFile() throws Exception {
        when(uploadFileAttachmentService.findById(anyString())).thenReturn(new UploadFileAttachmentBo());

        ResponseResult<String> result = itemWebApi.downloadItemMaterialSampleFile("attachmentId");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testDownloadItemMaterialBlankFile() throws Exception {
        when(uploadFileAttachmentService.findById(anyString())).thenReturn(new UploadFileAttachmentBo());

        ResponseResult<String> result = itemWebApi.downloadItemMaterialBlankFile("attachmentId");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testFindInManagerChangeLogByItemId() throws Exception {
        when(itemService.findItemAndItemMaterialChangeLogByItemId(anyString())).thenReturn(Arrays.<ItemChangeLogViewBo>asList(new ItemChangeLogViewBo()));

        ResponseResult<List<ItemChangeLogViewBo>> result = itemWebApi.findInManagerChangeLogByItemId("itemId");
        Assert.assertEquals(null, result);
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme