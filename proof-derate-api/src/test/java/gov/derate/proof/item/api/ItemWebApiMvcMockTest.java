package gov.derate.proof.item.api;

import com.google.common.collect.Maps;
import gov.derate.proof.base.ControllerTestBase;
import gov.derate.proof.common.enums.*;
import gov.derate.proof.item.entity.ItemDo;
import gov.derate.proof.item.entity.ItemMaterialDo;
import gov.derate.proof.item.repository.ItemMaterialRepository;
import gov.derate.proof.item.repository.ItemRepository;
import gov.derate.proof.item.req.ItemRequest;
import gov.license.common.tools.jackson.JacksonUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Date;
import java.util.HashMap;
import java.util.UUID;


/**
 * <AUTHOR>
 * @date 2021/9/26.
 */
public class ItemWebApiMvcMockTest extends ControllerTestBase {


    protected MockMvc mockMvc;

    @Autowired
    private WebApplicationContext webApplicationContext;
    @Autowired
    private ItemRepository itemRepository;
    @Autowired
    private ItemMaterialRepository itemMaterialRepository;

    @Before
    public void setUp() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    public void listPage() throws Exception {
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/item/page")
                .param("item_name", "建筑业企业资质证书")
                .param("handing_item", "个体商户注册")
                .param("credit_code", "123454678")
                .param("division_code", "广东汕尾红海湾经济开发区财政局")
                .param("item_type", "ADMINISTRATIVE_LICENSE")
                .param("item_source", "NOT_STANDARDIZATION")
                .param("item_clear_status", "WAIT_FOR_CLEAN")
                .param("page_direction", "DESC")
                .param("page_number", "0")
                .param("page_size", "10")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);


    }

    @Test
    public void listTest() throws Exception {
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/item/list")
                .param("item_name", "建筑业企业资质证书")
                .param("handing_item", "个体商户注册")
                .param("credit_code", "123454678")
                .param("division_code", "广东汕尾红海湾经济开发区财政局")
                .param("item_type", "ADMINISTRATIVE_LICENSE")
                .param("item_source", "NOT_STANDARDIZATION")
                .param("item_clear_status", "WAIT_FOR_CLEAN")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void createTest() throws Exception {

        ItemRequest createVo = new ItemRequest();
        String strJson = JacksonUtil.toJsonStr(createVo);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/item/create")
                .accept(MediaType.parseMediaType("application/json;charset=utf-8"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(strJson)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void edit() throws Exception {

        ItemRequest createVo = new ItemRequest();

        String strJson = JacksonUtil.toJsonStr(createVo);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/item/edit/123")
                        .accept(MediaType.parseMediaType("application/json;charset=utf-8"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(strJson)
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void delete() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/item/delete/123")

        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

    @Test
    @Rollback(true)
    public void itemMaterialPageListTest() throws Exception {
        setData();
//        Mockito.when(taskService.listPage(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(new PageImpl<>(Lists.newArrayList(taskListPageBo), PageRequest.of(0,1),0));
        HashMap<String, String> paramMap = Maps.newHashMap();
        paramMap.put("page_direction", "desc");
        paramMap.put("page_number", "1");
        paramMap.put("page_size", "10");
        get("/task/manager/page",
                paramMap,
                new HashMap<String, Object>() {{
                    put("$.meta.code", "200");
                }}
        );
    }

    /**
     * 测试数据，临时保存，由测试方法回滚。
     */
    private void setData() {
        String itemId = UUID.randomUUID().toString();
        ItemDo itemDo = new ItemDo();
        itemDo.setItemCode("123");
        itemDo.setItemName("测试事项");
        itemDo.setHandingItem("测试");
        itemDo.setCreditCode("123456");
        itemDo.setImplOrgName("广东省民政局");
        itemDo.setDivisionCode("440000");
        itemDo.setItemType(ItemTypeEnum.ADMINISTRATIVE_LICENSE);
        itemDo.setItemSource(ItemSourceEnum.NOT_STANDARDIZATION);
//        itemDo.setItemClearStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);
//        itemDo.setItemStatus(false);
        itemDo.setId(itemId);
        itemDo.setCreatorId(itemId);
        itemDo.setCreationTime(new Date());
        itemDo.setLastModificatorId(itemId);
        itemDo.setLastModificationTime(new Date());
        itemRepository.save(itemDo);

        ItemMaterialDo itemMaterialDo = new ItemMaterialDo();
        itemMaterialDo.setItemCode("123");
        itemMaterialDo.setItemMaterialSource(ItemMaterialSourceEnum.NOT_STANDARD_MATERIAL);
        itemMaterialDo.setMaterialName("测试事项-材料");
        itemMaterialDo.setMaterialType(MaterialTypeEnum.CERTIFICATE_PROVE);
        itemMaterialDo.setLicenseCode("123456,123456");
        itemMaterialDo.setLicenseName("证照名称，证照名称2");
//        itemMaterialDo.setMaterialSource("材料来源");
        itemMaterialDo.setMaterialSourceRemark("材料来源备注");
        itemMaterialDo.setNotCommit(false);
        itemMaterialDo.setItemClearStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);
        itemMaterialDo.setId(UUID.randomUUID().toString());
        itemMaterialDo.setCreatorId(UUID.randomUUID().toString());
        itemMaterialDo.setCreationTime(new Date());
        itemMaterialDo.setLastModificatorId(UUID.randomUUID().toString());
        itemMaterialDo.setLastModificationTime(new Date());
        itemMaterialRepository.save(itemMaterialDo);

    }


    @Override
    public void beforeTest() {

    }

    @Override
    public void afterTest() {

    }
}
