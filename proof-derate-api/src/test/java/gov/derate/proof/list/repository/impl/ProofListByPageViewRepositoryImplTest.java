package gov.derate.proof.list.repository.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import gov.derate.proof.common.enums.ItemMaterialSourceEnum;
import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.enums.ItemSourceEnum;
import gov.derate.proof.list.entity.ProofListByPageViewDo;
import gov.derate.proof.list.query.ProofListByPageViewQuery;
import gov.derate.proof.list.repository.ProofListByPageViewCustomRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Map;

import static org.junit.Assert.assertFalse;

/**
 * <p>
 * ProofListByPageViewCustomRepositoryImplTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2016-11-23
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2021/10/11；
 */
@RunWith(SpringRunner.class)
@WebAppConfiguration
@PowerMockIgnore("javax.management.*")
@Rollback
@Transactional(rollbackFor = Exception.class)
@SpringBootTest
public class ProofListByPageViewRepositoryImplTest {
    @Autowired
    private ProofListByPageViewCustomRepository proofListByPageViewCustomRepository;

    @Test
    public void queryProofListByPage() {
        ProofListByPageViewQuery proofListRequest = new ProofListByPageViewQuery();
//        proofListRequest.setItemCode("11441500007240103U3440193015003");
//        proofListRequest.setMaterialId("");
//        proofListRequest.setItemProofStatusListByUnionQuery(Lists.newArrayList(ItemProofStatusEnum.APPROVED));
//        proofListRequest.setItemName("音像制作单位的变更审批");
//        proofListRequest.setHandingItem("");
//        proofListRequest.setProofNameByUnionQuery("主要制作技术人员专业技术资格证明或学历证明及居民身份证（不少于5人）");
//        proofListRequest.setDivisionCode("441500");
//        proofListRequest.setCreditCode("11441500007240103U");
//        proofListRequest.setItemSourceList(Lists.newArrayList(ItemSourceEnum.STANDARDIZATION));
//        proofListRequest.setItemMaterialSourceListByUnionQuery(Lists.newArrayList(ItemMaterialSourceEnum.STANDARD_MATERIAL));

        Pageable page = PageRequest.of(0, 10);
        Map<String, String> hints = Maps.newHashMap();
        Page<ProofListByPageViewDo> listPage = proofListByPageViewCustomRepository.queryProofListByPage(proofListRequest, page, hints);
        assertFalse(CollectionUtils.isEmpty(listPage.getContent()));
    }

    @Test
    public void queryProofListByPageTest1() {
        ProofListByPageViewQuery proofListRequest = new ProofListByPageViewQuery();
        proofListRequest.setItemCode("11441500007240103U3440193015003");
//        proofListRequest.setMaterialId("");
//        proofListRequest.setItemProofStatusListByUnionQuery(Lists.newArrayList(ItemProofStatusEnum.APPROVED));
//        proofListRequest.setItemName("音像制作单位的变更审批");
//        proofListRequest.setHandingItem("");
//        proofListRequest.setProofNameByUnionQuery("主要制作技术人员专业技术资格证明或学历证明及居民身份证（不少于5人）");
//        proofListRequest.setDivisionCode("441500");
//        proofListRequest.setCreditCode("11441500007240103U");
//        proofListRequest.setItemSourceList(Lists.newArrayList(ItemSourceEnum.STANDARDIZATION));
//        proofListRequest.setItemMaterialSourceListByUnionQuery(Lists.newArrayList(ItemMaterialSourceEnum.STANDARD_MATERIAL));

        Pageable page = PageRequest.of(0, 10);
        Map<String, String> hints = Maps.newHashMap();
        Page<ProofListByPageViewDo> listPage = proofListByPageViewCustomRepository.queryProofListByPage(proofListRequest, page, hints);
        assertFalse(CollectionUtils.isEmpty(listPage.getContent()));
    }

    @Test
    public void queryProofListByPageTest2() {
        ProofListByPageViewQuery proofListRequest = new ProofListByPageViewQuery();
//        proofListRequest.setItemCode("11441500007240103U3440193015003");
//        proofListRequest.setMaterialId("");
        proofListRequest.setItemProofStatusListByUnionQuery(Lists.newArrayList(ItemProofStatusEnum.APPROVED));
//        proofListRequest.setItemName("音像制作单位的变更审批");
//        proofListRequest.setHandingItem("");
//        proofListRequest.setProofNameByUnionQuery("主要制作技术人员专业技术资格证明或学历证明及居民身份证（不少于5人）");
//        proofListRequest.setDivisionCode("441500");
//        proofListRequest.setCreditCode("11441500007240103U");
//        proofListRequest.setItemSourceList(Lists.newArrayList(ItemSourceEnum.STANDARDIZATION));
//        proofListRequest.setItemMaterialSourceListByUnionQuery(Lists.newArrayList(ItemMaterialSourceEnum.STANDARD_MATERIAL));

        Pageable page = PageRequest.of(0, 10);
        Map<String, String> hints = Maps.newHashMap();
        Page<ProofListByPageViewDo> listPage = proofListByPageViewCustomRepository.queryProofListByPage(proofListRequest, page, hints);
        assertFalse(CollectionUtils.isEmpty(listPage.getContent()));
    }

    @Test
    public void queryProofListByPageTest3() {
        ProofListByPageViewQuery proofListRequest = new ProofListByPageViewQuery();
//        proofListRequest.setItemCode("11441500007240103U3440193015003");
//        proofListRequest.setMaterialId("");
//        proofListRequest.setItemProofStatusListByUnionQuery(Lists.newArrayList(ItemProofStatusEnum.APPROVED));
        proofListRequest.setItemName("音像制作单位的变更审批");
//        proofListRequest.setHandingItem("");
//        proofListRequest.setProofNameByUnionQuery("主要制作技术人员专业技术资格证明或学历证明及居民身份证（不少于5人）");
//        proofListRequest.setDivisionCode("441500");
//        proofListRequest.setCreditCode("11441500007240103U");
//        proofListRequest.setItemSourceList(Lists.newArrayList(ItemSourceEnum.STANDARDIZATION));
//        proofListRequest.setItemMaterialSourceListByUnionQuery(Lists.newArrayList(ItemMaterialSourceEnum.STANDARD_MATERIAL));

        Pageable page = PageRequest.of(0, 10);
        Map<String, String> hints = Maps.newHashMap();
        Page<ProofListByPageViewDo> listPage = proofListByPageViewCustomRepository.queryProofListByPage(proofListRequest, page, hints);
        assertFalse(CollectionUtils.isEmpty(listPage.getContent()));
    }

    @Test
    public void queryProofListByPageTest4() {
        ProofListByPageViewQuery proofListRequest = new ProofListByPageViewQuery();
//        proofListRequest.setItemCode("11441500007240103U3440193015003");
//        proofListRequest.setMaterialId("");
//        proofListRequest.setItemProofStatusListByUnionQuery(Lists.newArrayList(ItemProofStatusEnum.APPROVED));
//        proofListRequest.setItemName("音像制作单位的变更审批");
//        proofListRequest.setHandingItem("");
        proofListRequest.setProofNameByUnionQuery("主要制作技术人员专业技术资格证明或学历证明及居民身份证（不少于5人）");
//        proofListRequest.setDivisionCode("441500");
//        proofListRequest.setCreditCode("11441500007240103U");
//        proofListRequest.setItemSourceList(Lists.newArrayList(ItemSourceEnum.STANDARDIZATION));
//        proofListRequest.setItemMaterialSourceListByUnionQuery(Lists.newArrayList(ItemMaterialSourceEnum.STANDARD_MATERIAL));

        Pageable page = PageRequest.of(0, 10);
        Map<String, String> hints = Maps.newHashMap();
        Page<ProofListByPageViewDo> listPage = proofListByPageViewCustomRepository.queryProofListByPage(proofListRequest, page, hints);
        assertFalse(CollectionUtils.isEmpty(listPage.getContent()));
    }

    @Test
    public void queryProofListByPageTest5() {
        ProofListByPageViewQuery proofListRequest = new ProofListByPageViewQuery();
//        proofListRequest.setItemCode("11441500007240103U3440193015003");
//        proofListRequest.setMaterialId("");
//        proofListRequest.setItemProofStatusListByUnionQuery(Lists.newArrayList(ItemProofStatusEnum.APPROVED));
//        proofListRequest.setItemName("音像制作单位的变更审批");
//        proofListRequest.setHandingItem("");
//        proofListRequest.setProofNameByUnionQuery("主要制作技术人员专业技术资格证明或学历证明及居民身份证（不少于5人）");
        proofListRequest.setDivisionCode("441500");
//        proofListRequest.setCreditCode("11441500007240103U");
//        proofListRequest.setItemSourceList(Lists.newArrayList(ItemSourceEnum.STANDARDIZATION));
//        proofListRequest.setItemMaterialSourceListByUnionQuery(Lists.newArrayList(ItemMaterialSourceEnum.STANDARD_MATERIAL));

        Pageable page = PageRequest.of(0, 10);
        Map<String, String> hints = Maps.newHashMap();
        Page<ProofListByPageViewDo> listPage = proofListByPageViewCustomRepository.queryProofListByPage(proofListRequest, page, hints);
        assertFalse(CollectionUtils.isEmpty(listPage.getContent()));
    }

    @Test
    public void queryProofListByPageTest6() {
        ProofListByPageViewQuery proofListRequest = new ProofListByPageViewQuery();
//        proofListRequest.setItemCode("11441500007240103U3440193015003");
//        proofListRequest.setMaterialId("");
//        proofListRequest.setItemProofStatusListByUnionQuery(Lists.newArrayList(ItemProofStatusEnum.APPROVED));
//        proofListRequest.setItemName("音像制作单位的变更审批");
//        proofListRequest.setHandingItem("");
//        proofListRequest.setProofNameByUnionQuery("主要制作技术人员专业技术资格证明或学历证明及居民身份证（不少于5人）");
//        proofListRequest.setDivisionCode("441500");
        proofListRequest.setCreditCode("11441500007240103U");
//        proofListRequest.setItemSourceList(Lists.newArrayList(ItemSourceEnum.STANDARDIZATION));
//        proofListRequest.setItemMaterialSourceListByUnionQuery(Lists.newArrayList(ItemMaterialSourceEnum.STANDARD_MATERIAL));

        Pageable page = PageRequest.of(0, 10);
        Map<String, String> hints = Maps.newHashMap();
        Page<ProofListByPageViewDo> listPage = proofListByPageViewCustomRepository.queryProofListByPage(proofListRequest, page, hints);
        assertFalse(CollectionUtils.isEmpty(listPage.getContent()));
    }

    @Test
    public void queryProofListByPageTest7() {
        ProofListByPageViewQuery proofListRequest = new ProofListByPageViewQuery();
//        proofListRequest.setItemCode("11441500007240103U3440193015003");
//        proofListRequest.setMaterialId("");
//        proofListRequest.setItemProofStatusListByUnionQuery(Lists.newArrayList(ItemProofStatusEnum.APPROVED));
//        proofListRequest.setItemName("音像制作单位的变更审批");
//        proofListRequest.setHandingItem("");
//        proofListRequest.setProofNameByUnionQuery("主要制作技术人员专业技术资格证明或学历证明及居民身份证（不少于5人）");
//        proofListRequest.setDivisionCode("441500");
//        proofListRequest.setCreditCode("11441500007240103U");
        proofListRequest.setItemSourceList(Lists.newArrayList(ItemSourceEnum.STANDARDIZATION));
//        proofListRequest.setItemMaterialSourceListByUnionQuery(Lists.newArrayList(ItemMaterialSourceEnum.STANDARD_MATERIAL));

        Pageable page = PageRequest.of(0, 10);
        Map<String, String> hints = Maps.newHashMap();
        Page<ProofListByPageViewDo> listPage = proofListByPageViewCustomRepository.queryProofListByPage(proofListRequest, page, hints);
        assertFalse(CollectionUtils.isEmpty(listPage.getContent()));
    }

    @Test
    public void queryProofListByPageTest8() {
        ProofListByPageViewQuery proofListRequest = new ProofListByPageViewQuery();
//        proofListRequest.setItemCode("11441500007240103U3440193015003");
//        proofListRequest.setMaterialId("");
//        proofListRequest.setItemProofStatusListByUnionQuery(Lists.newArrayList(ItemProofStatusEnum.APPROVED));
//        proofListRequest.setItemName("音像制作单位的变更审批");
//        proofListRequest.setHandingItem("");
//        proofListRequest.setProofNameByUnionQuery("主要制作技术人员专业技术资格证明或学历证明及居民身份证（不少于5人）");
//        proofListRequest.setDivisionCode("441500");
//        proofListRequest.setCreditCode("11441500007240103U");
//        proofListRequest.setItemSourceList(Lists.newArrayList(ItemSourceEnum.STANDARDIZATION));
        proofListRequest.setItemMaterialSourceListByUnionQuery(Lists.newArrayList(ItemMaterialSourceEnum.STANDARD_MATERIAL));

        Pageable page = PageRequest.of(0, 10);
        Map<String, String> hints = Maps.newHashMap();
        Page<ProofListByPageViewDo> listPage = proofListByPageViewCustomRepository.queryProofListByPage(proofListRequest, page, hints);
        assertFalse(CollectionUtils.isEmpty(listPage.getContent()));
    }

    @Test
    public void queryProofListByPageTest9() {
        ProofListByPageViewQuery proofListRequest = new ProofListByPageViewQuery();
        proofListRequest.setItemCode("11441500007240103U3440193015003");
//        proofListRequest.setMaterialId("");
        proofListRequest.setItemProofStatusListByUnionQuery(Lists.newArrayList(ItemProofStatusEnum.APPROVED));
        proofListRequest.setItemName("音像制作单位的变更审批");
//        proofListRequest.setHandingItem("");
        proofListRequest.setProofNameByUnionQuery("主要制作技术人员专业技术资格证明或学历证明及居民身份证（不少于5人）");
        proofListRequest.setDivisionCode("441500");
        proofListRequest.setCreditCode("11441500007240103U");
        proofListRequest.setItemSourceList(Lists.newArrayList(ItemSourceEnum.STANDARDIZATION));
        proofListRequest.setItemMaterialSourceListByUnionQuery(Lists.newArrayList(ItemMaterialSourceEnum.STANDARD_MATERIAL));

        Pageable page = PageRequest.of(0, 10);
        Map<String, String> hints = Maps.newHashMap();
        Page<ProofListByPageViewDo> listPage = proofListByPageViewCustomRepository.queryProofListByPage(proofListRequest, page, hints);
        assertFalse(CollectionUtils.isEmpty(listPage.getContent()));
    }
}