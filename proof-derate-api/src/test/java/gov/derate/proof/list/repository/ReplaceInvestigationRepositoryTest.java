package gov.derate.proof.list.repository;

import gov.derate.proof.list.entity.ReplaceInvestigationDo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2021/9/10.
 */
@RunWith(SpringRunner.class)
@WebAppConfiguration
@PowerMockIgnore("javax.management.*")
@Rollback
@Transactional(rollbackFor = Exception.class)
@SpringBootTest
public class ReplaceInvestigationRepositoryTest {
    @Autowired
    private ReplaceInvestigationRepository replaceInvestigationRepository;

    @Test
    public void findByProofListIdTest() {
        ReplaceInvestigationDo replaceInvestigation = replaceInvestigationRepository.findByProofListId("1");
        Assert.assertNull(replaceInvestigation);
    }
}
