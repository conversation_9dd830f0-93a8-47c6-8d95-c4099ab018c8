package gov.derate.proof.list.api;

import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2021/9/14.
 */
@Rollback
@Transactional(rollbackFor = Exception.class)
@WebAppConfiguration
@RunWith(SpringRunner.class)
@SpringBootTest
@PowerMockIgnore("javax.management.*")
public class ProofListSubmitControllerMvcMockTest {
}
