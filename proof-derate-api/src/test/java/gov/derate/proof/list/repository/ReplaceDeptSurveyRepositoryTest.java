package gov.derate.proof.list.repository;

import gov.derate.proof.list.entity.ReplaceDeptSurveyDo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2021/9/10.
 */
@RunWith(SpringRunner.class)
@WebAppConfiguration
@PowerMockIgnore("javax.management.*")
@Rollback
@Transactional(rollbackFor = Exception.class)
@SpringBootTest
public class ReplaceDeptSurveyRepositoryTest {
    @Autowired
    private ReplaceDeptSurveyRepository replaceDeptSurveyRepository;

    @Test
    public void findByProofListIdTest() {
        ReplaceDeptSurveyDo replaceDeptSurvey = replaceDeptSurveyRepository.findByProofListId("1");
        Assert.assertNull(replaceDeptSurvey);
    }
}
