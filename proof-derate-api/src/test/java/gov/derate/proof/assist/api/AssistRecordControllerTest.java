package gov.derate.proof.assist.api;


import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

/**
 * <AUTHOR>
 * @date 2021/9/30.
 */

@Rollback
//@Transactional(rollbackFor = Exception.class)
@WebAppConfiguration
//SpringBoot1.4版本之前用的是SpringJUnit4ClassRunner.class
@RunWith(SpringRunner.class)
@SpringBootTest
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = true)  //这里的事务关联到项目的配置文件中的事务控制器（"transactionManager"），同时指定事物自动回滚（defaultRollback= true），以此对数据库不会产生数据污染。
@PowerMockIgnore("javax.management.*")
public class AssistRecordControllerTest {

    protected MockMvc mockMvc;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Before
    public void setUp() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    public void assistDetailTest() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/assist/detail")
                .param("assist_serial_number", "16424086201259ad76")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

    @Test
    public void assistHistoryDetailTest() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/assist/history/detail")
                .param("assist_serial_number", "16424086201259ad76")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

    @Test
    public void assistHistoryTest() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/assist/history")
                .param("assist_serial_number", "16424086201259ad76")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

    @Test
    public void assistHistoryPageTest() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/assist/history_page")
                .param("token", "1111111113333333")
                .param("assist_result", "WAIT")
                .param("page_direction", "DESC")
                .param("page_number", "1")
                .param("page_size", "10")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

    @Test
    public void assistHistoryPageTest2() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/assist/history_page")
                        .param("token","1111111113333333")
                        .param("assist_result","WAIT")
                        .param("page_direction","DESC")
                        .param("page_number","1")
                        .param("page_size","10")
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

    @Test
    public void assistAuditTest() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/assist/audit")
                .param("assist_serial_number", "16424086201259ad76")
                .param("assist_comment", "test")
                .param("audit_status", "SUCCESS")
                .param("audit_user_id", "")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }
}
