package gov.derate.proof.assist.api;

import com.google.common.collect.Maps;
import gov.derate.proof.assist.bo.AssistRecordAuditDto;
import gov.derate.proof.assist.bo.AssistRecordDto;
import gov.derate.proof.assist.query.AssistRecordBaseQuery;
import gov.derate.proof.assist.query.AssistRecordPageQuery;
import gov.derate.proof.assist.req.AssistRecordSaveRequest;
import gov.derate.proof.assist.service.AssistRecordService;
import gov.derate.proof.assist.webapi.AssistInvestigateApplyWebApi;
import gov.derate.proof.common.enums.*;
import gov.derate.proof.item.dto.ItemDto;
import gov.derate.proof.item.service.ItemService;
import gov.derate.proof.list.entity.ProofListByPageViewDo;
import gov.derate.proof.list.service.ProofListService;
import gov.license.common.tools.jackson.JacksonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@RunWith(SpringRunner.class)
@WebMvcTest(AssistInvestigateApplyWebApi.class)
public class AssistInvestigateApplyControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AssistRecordService mockAssistRecordService;
    @MockBean
    private ProofListService mockProofListService;
    @MockBean
    private ItemService mockItemService;

    @Test
    public void testPage() throws Exception {
        // Setup
        // Configure AssistRecordService.queryAssistRecordProofRelationViewPage(...).
        final AssistRecordDto assistInvestigateRecordBaseBo = new AssistRecordDto();
        assistInvestigateRecordBaseBo.setHandleAffairsName("handleAffairsName");
        assistInvestigateRecordBaseBo.setHandleAffairsAssistIdentityType(AssistIdentityType.IDENTITY);
        assistInvestigateRecordBaseBo.setHandleAffairsIdentityNumber("handleAffairsIdentityNumber");
        assistInvestigateRecordBaseBo.setToAssistOrgName("toAssistOrgName");
        assistInvestigateRecordBaseBo.setToAssistCreditCode("toAssistCreditCode");
        assistInvestigateRecordBaseBo.setFromAssistOrgName("fromAssistOrgName");
        assistInvestigateRecordBaseBo.setFromAssistCreditCode("fromAssistCreditCode");
        assistInvestigateRecordBaseBo.setBusinessSerialNumber("businessSerialNumber");
        assistInvestigateRecordBaseBo.setAssistSerialNumber("assistSerialNumber");
        assistInvestigateRecordBaseBo.setAuditSuggestion("auditSuggestion");
        final Page<AssistRecordDto> assistInvestigateRecordBaseBos = new PageImpl<>(Arrays.asList(assistInvestigateRecordBaseBo));
        when(mockAssistRecordService.queryAssistRecordProofRelationViewPage(any(AssistRecordPageQuery.class), PageRequest.of(0, 10))).thenReturn(new PageImpl<>(Collections.emptyList()));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/page")
                        .param("handle_affairs_name", "handleAffairsName")
                        .param("material_name", "materialName")
                        .param("assist_result", "assistResult")
                        .param("fromAssistCreditCode", "fromAssistCreditCode")
                        .param("page_direction", "desc")
                        .param("page_number", "1")
                        .param("page_size", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertTrue(response.getContentAsString().contains("handleAffairsName"));

    }

    @Test
    public void testPage_AssistRecordServiceReturnsNoItems() throws Exception {
        when(mockAssistRecordService.queryAssistRecordProofRelationViewPage(any(AssistRecordPageQuery.class), PageRequest.of(0, 10))).thenReturn(new PageImpl<>(Collections.emptyList()));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/page")
                        .param("handle_affairs_name", "handleAffairsName")
                        .param("material_name", "materialName")
                        .param("assist_result", "assistResult")
                        .param("fromAssistCreditCode", "fromAssistCreditCode")
                        .param("page_direction", "desc")
                        .param("page_number", "1")
                        .param("page_size", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertTrue(response.getContentAsString().contains("[]"));

    }

    @Test
    public void testPage_AssistRecordServiceThrowsIllegalAccessException() throws Exception {
        // Setup
        when(mockAssistRecordService.queryAssistRecordProofRelationViewPage(any(AssistRecordPageQuery.class), PageRequest.of(0, 10))).thenThrow(IllegalAccessException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/page")
                        .param("handle_affairs_name", "handleAffairsName")
                        .param("material_name", "materialName")
                        .param("assist_result", "assistResult")
                        .param("fromAssistCreditCode", "fromAssistCreditCode")
                        .param("page_direction", "desc")
                        .param("page_number", "1")
                        .param("page_size", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertFalse(response.getContentAsString().contains("\"code\":\"200\""));

    }

    @Test
    public void testPage_AssistRecordServiceThrowsInstantiationException() throws Exception {
        // Setup
        when(mockAssistRecordService.queryAssistRecordProofRelationViewPage(any(AssistRecordPageQuery.class), PageRequest.of(0, 10))).thenThrow(InstantiationException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/page")
                        .param("handle_affairs_name", "handleAffairsName")
                        .param("material_name", "materialName")
                        .param("assist_result", "assistResult")
                        .param("fromAssistCreditCode", "fromAssistCreditCode")
                        .param("page_direction", "desc")
                        .param("page_number", "1")
                        .param("page_size", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertFalse(response.getContentAsString().contains("\"code\":\"200\""));

    }

    @Test
    public void testGetItemMaterial() throws Exception {
        // Setup
        // Configure ProofListService.getProofListDosByItemCode(...).
        final List<ProofListByPageViewDo> proofListByPageViewDos = Arrays.asList(new ProofListByPageViewDo("itemCode", "itemName", "implOrgName", "handingItem", "proofName", ItemProofStatusEnum.WAIT_FOR_CLEAN, "proofListId"));
        when(mockProofListService.getProofListArtificalAndLicenseItemWayByItemCode("itemCode")).thenReturn(proofListByPageViewDos);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/item_material")
                        .param("item_code", "itemCode")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertTrue(response.getContentAsString().contains("\"code\":\"200\""));
        assertTrue(response.getContentAsString().contains("proofName"));

    }

    @Test
    public void testGetItemMaterial_ProofListServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockProofListService.getProofListArtificalAndLicenseItemWayByItemCode("itemCode")).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/item_material")
                        .param("item_code", "itemCode")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertTrue(response.getContentAsString().contains("\"code\":\"200\""));
        assertTrue(response.getContentAsString().contains("[]"));
    }

    @Test
    public void testGetAssistOrg() throws Exception {
        // Setup
        HashMap<String, String> objectObjectHashMap = Maps.newHashMap();
        objectObjectHashMap.put("name", "code");
        when(mockProofListService.getAssistOrgByMaterialId("materialId")).thenReturn(objectObjectHashMap);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/assist_org")
                        .param("material_id", "materialId")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertTrue(response.getContentAsString().contains("\"code\":\"200\""));
        assertTrue(response.getContentAsString().contains("name"));
    }

    @Test
    public void testItemPage() throws Exception {
        // Setup
        // Configure ItemService.queryPageItemResponse(...).
        final ItemDto itemResponse = new ItemDto();
        itemResponse.setItemCode("itemCode");
        itemResponse.setItemName("itemName");
        itemResponse.setHandingItem("handingItem");
        itemResponse.setCreditCode("creditCode");
        itemResponse.setImplOrgName("implOrgName");
        itemResponse.setDivisionCode("divisionCode");
        itemResponse.setItemType(ItemTypeEnum.ADMINISTRATIVE_LICENSE);
        itemResponse.setItemSource(ItemSourceEnum.NOT_STANDARDIZATION);
        itemResponse.setItemClearStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);
        itemResponse.setId("id");
        final Page<ItemDto> itemResponses = new PageImpl<>(Arrays.asList(itemResponse));
        when(mockItemService.queryPageItemResponse(eq("itemName"), eq("creditCode"), any(Pageable.class), any())).thenReturn(itemResponses);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/item_page")
                        .param("item_name", "itemName")
                        .param("credit_code", "creditCode")
                        .param("page_direction", "desc")
                        .param("page_number", "1")
                        .param("page_size", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertTrue(response.getContentAsString().contains("\"code\":\"200\""));
        assertTrue(response.getContentAsString().contains("itemCode"));
    }

    @Test
    public void testItemPage_ItemServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockItemService.queryPageItemResponse(eq("itemName"), eq("creditCode"), any(Pageable.class), any())).thenReturn(new PageImpl<>(Collections.emptyList()));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/item_page")
                        .param("item_name", "itemName")
                        .param("credit_code", "creditCode")
                        .param("page_direction", "desc")
                        .param("page_number", "1")
                        .param("page_size", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertTrue(response.getContentAsString().contains("\"code\":\"200\""));
        assertTrue(response.getContentAsString().contains("[]"));
    }

    @Test
    public void testItemPage_ItemServiceThrowsIllegalAccessException() throws Exception {
        // Setup
        when(mockItemService.queryPageItemResponse(eq("itemName"), eq("creditCode"), any(Pageable.class), any())).thenThrow(IllegalAccessException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/item_page")
                        .param("item_name", "itemName")
                        .param("credit_code", "creditCode")
                        .param("page_direction", "desc")
                        .param("page_number", "1")
                        .param("page_size", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertFalse(response.getContentAsString().contains("\"code\":\"200\""));
    }

    @Test
    public void testItemPage_ItemServiceThrowsInstantiationException() throws Exception {
        // Setup
        when(mockItemService.queryPageItemResponse(eq("itemName"), eq("creditCode"), any(Pageable.class), any())).thenThrow(InstantiationException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/item_page")
                        .param("item_name", "itemName")
                        .param("credit_code", "creditCode")
                        .param("page_direction", "desc")
                        .param("page_number", "1")
                        .param("page_size", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertFalse(response.getContentAsString().contains("\"code\":\"200\""));

    }

    @Test
    public void testSave() throws Exception {
        // Setup
        // Configure AssistRecordService.apply(...).
        final AssistRecordDto assistInvestigateRecordBaseBo = new AssistRecordDto();
        assistInvestigateRecordBaseBo.setHandleAffairsName("handleAffairsName");
        assistInvestigateRecordBaseBo.setHandleAffairsAssistIdentityType(AssistIdentityType.IDENTITY);
        assistInvestigateRecordBaseBo.setHandleAffairsIdentityNumber("handleAffairsIdentityNumber");
        assistInvestigateRecordBaseBo.setToAssistOrgName("toAssistOrgName");
        assistInvestigateRecordBaseBo.setToAssistCreditCode("toAssistCreditCode");
        assistInvestigateRecordBaseBo.setFromAssistOrgName("fromAssistOrgName");
        assistInvestigateRecordBaseBo.setFromAssistCreditCode("fromAssistCreditCode");
        assistInvestigateRecordBaseBo.setBusinessSerialNumber("businessSerialNumber");
        assistInvestigateRecordBaseBo.setAssistSerialNumber("assistSerialNumber");
        assistInvestigateRecordBaseBo.setAuditSuggestion("auditSuggestion");
        when(mockAssistRecordService.createAssist(any(AssistRecordSaveRequest.class))).thenReturn(assistInvestigateRecordBaseBo);

        // Run the test
        AssistRecordSaveRequest request = buildAssistRecordSaveRequest();

        final MockHttpServletResponse response = mockMvc.perform(post("/assist_investigate/apply/save")
                        .content(JacksonUtil.toJsonStr(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertTrue(response.getContentAsString().contains("true"));
        verify(mockAssistRecordService).createAssist(any(AssistRecordSaveRequest.class));
    }

    @Test
    public void testSave_AssistRecordServiceThrowsInstantiationException() throws Exception {
        // Setup
        when(mockAssistRecordService.createAssist(any(AssistRecordSaveRequest.class))).thenThrow(InstantiationException.class);
        AssistRecordSaveRequest request = buildAssistRecordSaveRequest();
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/assist_investigate/apply/save")
                        .content(JacksonUtil.toJsonStr(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertFalse(response.getContentAsString().contains("\"code\":\"200\""));
    }

    private AssistRecordSaveRequest buildAssistRecordSaveRequest() {
        AssistRecordSaveRequest request = new AssistRecordSaveRequest();
        request.setHandleAffairsType(AssistHandleAffairsTypeEnum.LEGAL_PERSON);
        request.setFromDemand("fromDemand");
        request.setAssistSerialNumber("assistSerialNumber");
        request.setBusinessSerialNumber("businessSerialNumber");
        request.setLegalPersonIdentityType(LegalPersonIdentityType.WORKERS_AND_LEGAL_PERSON_QUALIFICATION);
        request.setHandleAffairsName("handleAffairsName");
        request.setHandleAffairsAssistIdentityType(AssistIdentityType.IDENTITY);
        request.setHandleAffairsIdentityNumber("handleAffairsIdentityNumber");
        request.setToAssistOrgName("toAssistOrgName");
        request.setToAssistCreditCode("toAssistCreditCode");
        request.setFromAssistOrgName("fromAssistOrgName");
        request.setFromAssistCreditCode("fromAssistCreditCode");
        request.setBusinessSerialNumber("businessSerialNumber");
        request.setFromAssistUserName("fromAssistUserName");
        request.setHistoryAssistSerialNumber("historyAssistSerialNumber");
        request.setItemCode("itemCode");
        request.setItemName("itemName");
        request.setMaterialId("materialId");
        request.setFromAssistContain("fromAssistContain");
        request.setAssistTime(new Date());
        return request;
    }

    @Test
    public void testSave_AssistRecordServiceThrowsIllegalAccessException() throws Exception {
        // Setup
        when(mockAssistRecordService.createAssist(any(AssistRecordSaveRequest.class))).thenThrow(IllegalAccessException.class);
        AssistRecordSaveRequest request = buildAssistRecordSaveRequest();
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/assist_investigate/apply/save")
                        .content(JacksonUtil.toJsonStr(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertFalse(response.getContentAsString().contains("\"code\":\"200\""));
    }

    @Test
    public void testView() throws Exception {
        // Setup
        // Configure AssistRecordService.getAssistRecordBaseBo(...).
        final AssistRecordDto assistInvestigateRecordBaseBo = new AssistRecordDto();
        assistInvestigateRecordBaseBo.setHandleAffairsName("handleAffairsName");
        assistInvestigateRecordBaseBo.setHandleAffairsAssistIdentityType(AssistIdentityType.IDENTITY);
        assistInvestigateRecordBaseBo.setHandleAffairsIdentityNumber("handleAffairsIdentityNumber");
        assistInvestigateRecordBaseBo.setToAssistOrgName("toAssistOrgName");
        assistInvestigateRecordBaseBo.setToAssistCreditCode("toAssistCreditCode");
        assistInvestigateRecordBaseBo.setFromAssistOrgName("fromAssistOrgName");
        assistInvestigateRecordBaseBo.setFromAssistCreditCode("fromAssistCreditCode");
        assistInvestigateRecordBaseBo.setBusinessSerialNumber("businessSerialNumber");
        assistInvestigateRecordBaseBo.setAssistSerialNumber("assistSerialNumber");
        assistInvestigateRecordBaseBo.setAuditSuggestion("auditSuggestion");
        when(mockAssistRecordService.getAssistRecord("id")).thenReturn((AssistRecordAuditDto) assistInvestigateRecordBaseBo);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/view/{id}", "id")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertTrue(response.getContentAsString().contains("\"code\":\"200\""));
        assertTrue(response.getContentAsString().contains("handleAffairsName"));

    }

    @Test
    public void testView_AssistRecordServiceThrowsIllegalAccessException() throws Exception {
        // Setup
        when(mockAssistRecordService.getAssistRecord("id")).thenThrow(IllegalAccessException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/view/{id}", "id")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertFalse(response.getContentAsString().contains("\"code\":\"200\""));

    }

    @Test
    public void testView_AssistRecordServiceThrowsInstantiationException() throws Exception {
        // Setup
        when(mockAssistRecordService.getAssistRecord("id")).thenThrow(InstantiationException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/view/{id}", "id")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertFalse(response.getContentAsString().contains("\"code\":\"200\""));
    }

    @Test
    public void testGeneratorAssistSerialNumber() throws Exception {
        // Setup
        when(mockAssistRecordService.generatorAssistSerialNumber()).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_investigate/apply/assist_serial_number")
                        .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertTrue(response.getContentAsString().contains("\"code\":\"200\""));
        assertTrue(response.getContentAsString().contains("result"));
    }
}
