package gov.derate.proof.assist.manager;

import gov.derate.proof.assist.bo.GovEasyUserBo;
import gov.derate.proof.assist.req.GovEasyManagerSendMsgRequest;
import gov.derate.proof.assist.req.GovEasySendTextCardContextRequest;
import gov.derate.proof.base.ControllerTestBase;
import gov.derate.proof.common.utils.GovEasySm3Utils;
import gov.derate.proof.common.utils.SignatureUtil;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Base64Utils;

import java.util.Objects;
import java.util.UUID;

/**
 * <p>
 * 测试功能
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/1/13
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/1/13；
 */
public class GovEasyManagerTest extends ControllerTestBase {
    @Autowired
    private GovEasyManager govEasyManager;
    @Value("${digit.assistant.AgentID}")
    private String agentId;
    @Value("${digit.assistant.token}")
    private String token;


    @Value("${digit.assistant.sendTextTitle}")
    private String sendTextTitle;
    @Value("${digit.assistant.sendTextContent}")
    private String sendTextContent;
    @Value("${digit.assistant.sendTextRedirectUrl}")
    private String sendTextRedirectUrl;
    /**
     * #粤证易-根据粤证易获取到点击用户的code后，获取UserId GET请求 %s是待业务填充字符
     */
    @Value("${digit.assistant.PaaSID}")
    private String paasId;

    @Test
    public void sendMessageTest() throws Exception {
        GovEasyManagerSendMsgRequest sendMsgRequest = new GovEasyManagerSendMsgRequest();
        sendMsgRequest.setToUser("49v0535ecvqo1ikdteym2h");
        sendMsgRequest.setMsgType("textcard");
        sendMsgRequest.setAgentId(Integer.valueOf(agentId));
        GovEasySendTextCardContextRequest text = new GovEasySendTextCardContextRequest();
        text.setTitle(new String(Base64Utils.decodeFromString(sendTextTitle)));
        text.setDescription(new String(Base64Utils.decodeFromString(sendTextContent)));
        String url = "https://smcj.shanwei.gov.cn/smcjtest/smcj-yzy-web/?redirectURI=http://localhost:8080/proof-derate-api/api/v1/gov_easy_user_id?token=${token}";
        String includeAssistSeriCode = String.format(sendTextRedirectUrl, "123456");
        text.setUrl(url);
        sendMsgRequest.setTextCard(text);
        boolean success = govEasyManager.sendMessage(sendMsgRequest);
        Assert.assertTrue(success);
    }
    @Test
    public void sendMessageTest2() throws Exception {
        GovEasyManagerSendMsgRequest sendMsgRequest = new GovEasyManagerSendMsgRequest();
        sendMsgRequest.setToUser("49v0535ecvqo1ikdteym2h");
        sendMsgRequest.setMsgType("textcard");
        sendMsgRequest.setAgentId(Integer.valueOf(1002698));
        GovEasySendTextCardContextRequest text = new GovEasySendTextCardContextRequest();
        text.setTitle(new String(Base64Utils.decodeFromString(sendTextTitle)));
        text.setDescription(new String(Base64Utils.decodeFromString(sendTextContent)));
        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wld341060039&response_type=code&scope=snsapi_base&agentid=1002698&state=STATE&redirect_uri=http://*************:8080/proof-derate-api/api/v1/hello#wechat_redirect";
//        String url = "https://smcj.shanwei.gov.cn/smcjtest/smcj-yzy-web/?redirectURI=http://localhost:8080/proof-derate-api/api/v1/gov_easy_user_id?token=${token}";
        String includeAssistSeriCode = String.format(sendTextRedirectUrl, "123456");
        text.setUrl(url);
        sendMsgRequest.setTextCard(text);
        boolean success = govEasyManager.sendMessage(sendMsgRequest);
        Assert.assertTrue(success);
    }

    @Test
    public void sendMessageTest3() throws Exception {
        GovEasyManagerSendMsgRequest sendMsgRequest = new GovEasyManagerSendMsgRequest();
        sendMsgRequest.setToUser("49v0535ecvqo1ikdteym2h");
        sendMsgRequest.setMsgType("textcard");
        sendMsgRequest.setAgentId(Integer.valueOf(1002698));
        GovEasySendTextCardContextRequest text = new GovEasySendTextCardContextRequest();
        text.setTitle(new String(Base64Utils.decodeFromString(sendTextTitle)));
        text.setDescription(new String(Base64Utils.decodeFromString(sendTextContent)));
        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wld341060039&response_type=code&scope=snsapi_base&agentid=1002698&state=STATE&redirect_uri=http://*************:8080/proof-derate-api/api/v1/hello#wechat_redirect";
//        String url = "https://smcj.shanwei.gov.cn/smcjtest/smcj-yzy-web/?redirectURI=http://localhost:8080/proof-derate-api/api/v1/gov_easy_user_id?token=${token}";
        String includeAssistSeriCode = String.format(sendTextRedirectUrl, "123456");
        text.setUrl(url);
        sendMsgRequest.setTextCard(text);
        boolean success = govEasyManager.sendMessage(sendMsgRequest);
        Assert.assertTrue(success);
    }

    @Test
    public void signatureTest() throws Exception {
        // 调用网关的签名
        System.out.println("x-tif-paasid=" + paasId);
        long now = System.currentTimeMillis();
        String timestamp = Long.toString(now / 1000L);
        System.out.println("x-tif-timestamp=" + timestamp);
        String nonce = Long.toHexString(now) + "-" + Long.toHexString((long) Math.floor(Math.random() * 0xFFFFFF));
        System.out.println("x-tif-nonce=" + nonce);
        String signature = SignatureUtil.toSha256(timestamp + this.token + nonce + timestamp);
        System.out.println("x-tif-signature=" + signature);
    }

    @Test
    public void getUserInfoByUserIdTest() {
        GovEasyUserBo userInfoByUserId = govEasyManager.getUserInfoByUserId("49v0535ecvqo1ikdteym2h");
        Assert.assertTrue(Objects.nonNull(userInfoByUserId));
    }

    @Override
    public void beforeTest() {

    }

    @Override
    public void afterTest() {

    }

    @Value("${gov.easy.get_user.appkey}")
    private String govEasyAppKey;
    @Value("${gov.easy.get_user.secret}")
    private String govEasySecret;

    @Test
    public void getTokenTest() {
        String govzz_appkey = govEasyAppKey;
        String govzz_timestamp = String.valueOf(System.currentTimeMillis());
        String govzz_requestid = UUID.randomUUID().toString();
        String govzz_usertoken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJ6c29mdC1qd3QtaXNzIiwidXNlclR5cGUiOiJkZWZhdWx0IiwidXNlck5hbWUiOiIxNTUyMTEzNjMxNCIsImV4cCI6MTY0MjY2OTQ1NiwidXNlcklkIjoiZjIyNmMyNWMtNmFhNi00ZGRmLWIxZWYtZjYxYTViN2Q3NzgwIiwiaWF0IjoxNjQyNjY3OTU2LCJqdGkiOiIwNDk2MWZjNy1kMDQ0LTRhMTctODM5Yy1kMzFmZTdhZGZkZjYiLCJyZW5ld0V4cGlyZXNBdCI6MTY0MzUzMjI1Nn0.QXNhHugvyjn00hFVMY9hL9I3NG9vZwiFlj5RBIKORiM";
        String apptoken = govEasySecret;
        String sign = GovEasySm3Utils.sign(govzz_appkey, govzz_timestamp, govzz_requestid, govzz_usertoken, apptoken);
        System.out.println("govzz_appkey=" + govzz_appkey);
        System.out.println("govzz_timestamp=" + govzz_timestamp);
        System.out.println("govzz_requestid=" + govzz_requestid);
        System.out.println("govzz_usertoken=" + govzz_usertoken);
        System.out.println("apptoken=" + apptoken);
        System.out.println("sign=" + sign);
    }
}
