package gov.derate.proof.assist.api;

import gov.derate.proof.assist.service.AssistUserChangeLogService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * <p>
 * 测试协查用户更变日志Controller单元测试类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/5/11
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2023/5/11；
 */
@WebAppConfiguration
//SpringBoot1.4版本之前用的是SpringJUnit4ClassRunner.class
@RunWith(SpringRunner.class)
@SpringBootTest
public class AssistUserChangeLogWebApiTest {
    /**
     * 协查人员更变记录服务类
     */
    @Autowired
    private AssistUserChangeLogService assistUserChangeLogService;

    @Test
    void getChangeLogByAssistUserIdTest() {

    }
}