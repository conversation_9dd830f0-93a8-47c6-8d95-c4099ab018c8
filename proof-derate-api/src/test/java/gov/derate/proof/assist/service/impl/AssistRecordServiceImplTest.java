package gov.derate.proof.assist.service.impl;

import com.google.common.collect.Lists;
import gov.derate.proof.assist.bo.AssistRecordDetailPageBo;
import gov.derate.proof.assist.bo.AssistRecordDto;
import gov.derate.proof.assist.bo.AssistRecordHistoryPageBo;
import gov.derate.proof.assist.bo.GovEasyUserBo;
import gov.derate.proof.assist.dto.AssistLicenseProofRecordDto;
import gov.derate.proof.assist.dto.AssistRecordApiDto;
import gov.derate.proof.assist.dto.GetAssistLicenseProofRecordDto;
import gov.derate.proof.assist.dto.LicenseDerateAssistSignApiDto;
import gov.derate.proof.assist.entity.AssistLicenseProofRecordDo;
import gov.derate.proof.assist.entity.AssistRecordDo;
import gov.derate.proof.assist.entity.AssistUserProofRelationViewDo;
import gov.derate.proof.assist.exception.AssistRecordServiceException;
import gov.derate.proof.assist.manager.GovEasyManager;
import gov.derate.proof.assist.query.AssistLicenseProofRecordPageQuery;
import gov.derate.proof.assist.query.AssistRecordApiQuery;
import gov.derate.proof.assist.query.AssistRecordHistoryPageQuery;
import gov.derate.proof.assist.query.WaitAssistRecordApiQuery;
import gov.derate.proof.assist.repository.*;
import gov.derate.proof.assist.req.AssistRecordHandleApiRequest;
import gov.derate.proof.assist.req.AssistRecordSaveApiRequest;
import gov.derate.proof.assist.resp.AssistRecordListApiResponse;
import gov.derate.proof.assist.service.AssistAttachmentService;
import gov.derate.proof.assist.utils.GovEasyJwtUtil;
import gov.derate.proof.catalog.bo.ProofCatalogDetailDto;
import gov.derate.proof.catalog.resp.ProofCatalogResponse;
import gov.derate.proof.catalog.service.ProofCatalogService;
import gov.derate.proof.common.bo.UploadFileAttachmentBo;
import gov.derate.proof.common.enums.*;
import gov.derate.proof.common.service.NoticeService;
import gov.derate.proof.item.dto.ItemDto;
import gov.derate.proof.item.entity.ItemMaterialDo;
import gov.derate.proof.item.repository.ItemMaterialRepository;
import gov.derate.proof.item.service.ItemMaterialService;
import gov.derate.proof.item.service.ItemService;
import gov.derate.proof.license.enums.AckCode;
import gov.derate.proof.license.resp.LicenseAttachmentArchivingDataResponse;
import gov.derate.proof.license.resp.LicenseAttachmentArchivingResponse;
import gov.derate.proof.license.sdk.LicenseProofSdk;
import gov.derate.proof.list.bo.ProofListByPageViewBo;
import gov.derate.proof.list.service.ProofListService;
import gov.licc.func.api.amp.service.DictPublicService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import javax.validation.constraints.NotNull;
import java.util.*;

import static org.mockito.Mockito.*;

/**
 * AssistRecordServiceImplTest.java
 * <p>
 * Company: Zsoft
 * CreateDate:2024/8/22
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class AssistRecordServiceImplTest {
    @Mock
    Logger LOGGER;
    @Mock
    AssistRecordRepository assistRecordRepository;
    @Mock
    AssistInfoRepository assistInfoRepository;
    @Mock
    ItemMaterialRepository itemMaterialRepository;
    @Mock
    AssistInfoItemRepository assistInfoItemRepository;
    @Mock
    GovEasyManager govEasyManager;
    @Mock
    AssistUserRepository assistUserRepository;
    @Mock
    ProofListService proofListService;
    @Mock
    AssistUserProofRelationViewDoRepository assistUserProofRelationViewDoRepository;
    @Mock
    GovEasyJwtUtil govEasyJwtUtil;
    @Mock
    AssistUserDoRepository assistUserDoRepository;
    @Mock
    AssistAttachmentService assistAttachmentService;
    @Mock
    AssistRecordProofRelationViewDoRepository assistRecordProofRelationViewDoRepository;
    @Mock
    NoticeService noticeService;
    @Mock
    ProofCatalogService proofCatalogService;
    @Mock
    DictPublicService dictPublicService;
    @Mock
    ItemMaterialService itemMaterialService;
    @Mock
    ItemService itemService;
    @Mock
    AssistLicenseProofRecordRepository assistLicenseProofRecordRepository;
    @Mock
    LicenseProofSdk licenseProofSdk;
    @InjectMocks
    AssistRecordServiceImpl assistRecordServiceImpl;

    @Before
    public void setUp() {
//         MockitoAnnotations.openMocks(this);
    }


    @Test
    public void testWaitAssistPage() throws Exception {
        Page<AssistRecordApiDto> result = assistRecordServiceImpl.waitAssistPage(new WaitAssistRecordApiQuery(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testFindByAssistSerialNumber() throws Exception {
        AssistRecordApiDto result = assistRecordServiceImpl.findByAssistSerialNumber("assistSerialNumber");
        Assert.assertEquals(new AssistRecordApiDto(), result);
    }

    @Test
    public void testRemove() throws Exception {
        assistRecordServiceImpl.remove("id");
    }

    @Test
    public void testAssist() throws Exception {
        when(govEasyManager.getUserInfoByUserId(anyString())).thenReturn(new GovEasyUserBo());
        when(proofListService.queryProofList(any())).thenReturn(Arrays.<ProofListByPageViewBo>asList(new ProofListByPageViewBo("itemCode", "itemName", "implOrgName", "handingItem", "proofName", ItemProofStatusEnum.WAIT_FOR_CLEAN, "proofListId")));
        when(assistUserProofRelationViewDoRepository.findAllByOrgCreditCodeAndProofCodeLike(anyString(), anyString())).thenReturn(Arrays.<AssistUserProofRelationViewDo>asList(new AssistUserProofRelationViewDo()));
        when(noticeService.sendNoticeMsg(anyString(), anyString())).thenReturn(Boolean.TRUE);
        when(proofCatalogService.findById(anyString())).thenReturn(new ProofCatalogResponse());
        when(proofCatalogService.findArtificialByCatalogIdIn(any())).thenReturn(null);

        AssistRecordDo result = assistRecordServiceImpl.createAssistByApi(new AssistRecordSaveApiRequest());
        Assert.assertEquals(new AssistRecordDo(), result);
    }

    @Test
    public void testGetAssistUserByOrgCreditCodeAndItemCodeAndItemNameAndMaterialId() throws Exception {
        when(proofListService.queryProofList(any())).thenReturn(Arrays.<ProofListByPageViewBo>asList(new ProofListByPageViewBo("itemCode", "itemName", "implOrgName", "handingItem", "proofName", ItemProofStatusEnum.WAIT_FOR_CLEAN, "proofListId")));
        when(assistUserProofRelationViewDoRepository.findAllByOrgCreditCodeAndProofCodeLike(anyString(), anyString())).thenReturn(Arrays.<AssistUserProofRelationViewDo>asList(new AssistUserProofRelationViewDo()));

        AssistUserProofRelationViewDo result = assistRecordServiceImpl.getAssistUserByOrgCreditCodeAndItemCodeAndItemNameAndMaterialId("creditCode", "itemCode", "itemName", "materialId");
        Assert.assertEquals(new AssistUserProofRelationViewDo(), result);
    }

    @Test
    public void testGeneratorAssistSerialNumber() throws Exception {
        String result = assistRecordServiceImpl.generatorAssistSerialNumber();
        Assert.assertTrue(StringUtils.isNotBlank(result));
    }

    @Test
    public void testViewBySerialNumber() throws Exception {
        AssistRecordDto result = assistRecordServiceImpl.viewBySerialNumber("serialNumber");
        Assert.assertEquals(new AssistRecordDto(), result);
    }

    @Test
    public void testStatExemptInitiate() throws Exception {
        when(assistRecordProofRelationViewDoRepository.countRecord()).thenReturn(Long.valueOf(1));

        Long result = assistRecordServiceImpl.statExemptInitiate();
        Assert.assertEquals(Long.valueOf(1), result);
    }

    @Test
    public void testStatExemptFinishCount() throws Exception {
        when(assistRecordProofRelationViewDoRepository.countRecord(any())).thenReturn(Long.valueOf(1));

        Long result = assistRecordServiceImpl.statExemptFinishCount();
        Assert.assertEquals(Long.valueOf(1), result);
    }

    @Test
    public void testCreateLicenseDerateAssistSign() throws Exception {
        when(proofCatalogService.findDetailById(anyString())).thenReturn(new ProofCatalogDetailDto());
        when(itemMaterialService.findById(anyString())).thenReturn(new ItemMaterialDo());
        when(itemService.findByItemCode(anyString())).thenReturn(new ItemDto());

        String result = assistRecordServiceImpl.createLicenseDerateAssistSign(new LicenseDerateAssistSignApiDto());
        Assert.assertEquals("replaceMeWithExpectedResult", result);
    }

    @Test
    public void testQueryLicenseProofArchivesPage() throws Exception {
        AssistLicenseProofRecordDo assistLicenseProofRecordDo = buildAssistLicenseProofRecordDoNaturePersonMock(AssistLicenseProofResultEnum.signed);
        when(assistLicenseProofRecordRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(new PageImpl(Lists.newArrayList(assistLicenseProofRecordDo), PageRequest.of(0, 10), 1));
        AssistLicenseProofRecordPageQuery query = new AssistLicenseProofRecordPageQuery();
        query.setSerialNumber("");
        query.setLicenseName("");
        query.setProofResultList(Lists.newArrayList());
        query.setPageNumber(1);
        query.setPageSize(10);
        query.setOrgToAssistCreditCode(Lists.newArrayList());
        Page<AssistLicenseProofRecordDto> result = assistRecordServiceImpl.queryLicenseProofArchivesPage(query);
        Assert.assertTrue(result.hasContent());
        Assert.assertEquals(assistLicenseProofRecordDo.getSerialNumber(), result.getContent().get(0).getSerialNumber());
    }

    @Test
    public void testQueryLicenseProofArchivesPageEmpty() throws Exception {
        when(assistLicenseProofRecordRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(new PageImpl(Lists.newArrayList(), PageRequest.of(0, 10), 0));
        AssistLicenseProofRecordPageQuery query = new AssistLicenseProofRecordPageQuery();
        query.setSerialNumber("");
        query.setLicenseName("");
        query.setProofResultList(Lists.newArrayList());
        query.setPageNumber(1);
        query.setPageSize(10);
        query.setOrgToAssistCreditCode(Lists.newArrayList());
        Page<AssistLicenseProofRecordDto> result = assistRecordServiceImpl.queryLicenseProofArchivesPage(query);
        Assert.assertFalse(result.hasContent());
    }

    @Test
    public void testQueryLicenseProofArchivesView() throws Exception {
        AssistLicenseProofRecordDo assistLicenseProofRecordDo = buildAssistLicenseProofRecordDoNaturePersonMock(AssistLicenseProofResultEnum.signed);
        when(assistLicenseProofRecordRepository.findBySerialNumber(anyString())).thenReturn(Optional.of(assistLicenseProofRecordDo));
        Optional<AssistLicenseProofRecordDto> result = assistRecordServiceImpl.queryLicenseProofArchivesView("test");
        Assert.assertEquals(assistLicenseProofRecordDo.getSerialNumber(), result.get().getSerialNumber());
    }

    @Test
    public void testDownloadLicenseProofArchives() throws Exception {
        AssistLicenseProofRecordDo assistLicenseProofRecordDo = buildAssistLicenseProofRecordDoNaturePersonMock(AssistLicenseProofResultEnum.signed);
        LicenseAttachmentArchivingResponse licenseAttachmentArchivingResponse = buildLicenseAttachmentArchivingResponseMock();
        when(assistLicenseProofRecordRepository.findBySerialNumber(anyString())).thenReturn(Optional.of(assistLicenseProofRecordDo));
        when(licenseProofSdk.licenseAttachmentArchiving(anyString())).thenReturn(licenseAttachmentArchivingResponse);
        UploadFileAttachmentBo result = assistRecordServiceImpl.downloadLicenseProofArchives("test");
        Assert.assertEquals(licenseAttachmentArchivingResponse.getData().getFileName(), result.getFileName());
    }

    @Test
    public void testDownloadLicenseProofArchivesThrow() throws Exception {
        when(assistLicenseProofRecordRepository.findBySerialNumber(anyString())).thenReturn(Optional.empty());
        when(licenseProofSdk.licenseAttachmentArchiving(anyString())).thenReturn(new LicenseAttachmentArchivingResponse());
        Assert.assertThrows(AssistRecordServiceException.class, () -> {
            UploadFileAttachmentBo result = assistRecordServiceImpl.downloadLicenseProofArchives("test");
        });
    }

    @Test
    public void testGetLicenseDerateAssistBySerialNumberNaturePerson() throws Exception {
        AssistLicenseProofRecordDo mockObjByNaturePerson = buildAssistLicenseProofRecordDoNaturePersonMock(AssistLicenseProofResultEnum.not_sign);
        Optional<AssistLicenseProofRecordDo> optionalRecord = Optional.of(mockObjByNaturePerson);
        when(assistLicenseProofRecordRepository.findBySerialNumber(anyString())).thenReturn(optionalRecord);
        LicenseAttachmentArchivingResponse t = buildLicenseAttachmentArchivingResponseMock();
        when(licenseProofSdk.licenseAttachmentArchiving(anyString())).thenReturn(t);
        Optional<GetAssistLicenseProofRecordDto> resultOptional = assistRecordServiceImpl.getLicenseDerateAssistBySerialNumber("test");
        Assert.assertTrue(resultOptional.isPresent());
        GetAssistLicenseProofRecordDto result = resultOptional.get();
        Assert.assertEquals(mockObjByNaturePerson.getSerialNumber(), result.getSerialNumber());
        Assert.assertTrue(StringUtils.isBlank(result.getToUserName()));
        Assert.assertTrue(StringUtils.isBlank(result.getLicenseItemPdf()));
    }

    @Test
    public void testGetLicenseDerateAssistBySerialNumberThrow() throws Exception {
        when(assistLicenseProofRecordRepository.findBySerialNumber(anyString())).thenReturn(Optional.empty());
        Assert.assertThrows(AssistRecordServiceException.class, () -> {
            Optional<GetAssistLicenseProofRecordDto> resultOptional = assistRecordServiceImpl.getLicenseDerateAssistBySerialNumber("test");
        });
    }

    @Test
    public void testGetLicenseDerateAssistBySerialNumberNaturePersonSigned() throws Exception {
        AssistLicenseProofRecordDo mockObjByNaturePerson = buildAssistLicenseProofRecordDoNaturePersonMock(AssistLicenseProofResultEnum.signed);
        Optional<AssistLicenseProofRecordDo> optionalRecord = Optional.of(mockObjByNaturePerson);
        when(assistLicenseProofRecordRepository.findBySerialNumber(anyString())).thenReturn(optionalRecord);
        LicenseAttachmentArchivingResponse t = buildLicenseAttachmentArchivingResponseMock();
        when(licenseProofSdk.licenseAttachmentArchiving(anyString())).thenReturn(t);
        Optional<GetAssistLicenseProofRecordDto> resultOptional = assistRecordServiceImpl.getLicenseDerateAssistBySerialNumber("test");
        Assert.assertTrue(resultOptional.isPresent());
        GetAssistLicenseProofRecordDto result = resultOptional.get();
        Assert.assertEquals(mockObjByNaturePerson.getSerialNumber(), result.getSerialNumber());
        Assert.assertTrue(StringUtils.isNotBlank(result.getToUserName()));
        Assert.assertTrue(StringUtils.isNotBlank(result.getLicenseItemPdf()));
    }

    @NotNull
    private static LicenseAttachmentArchivingResponse buildLicenseAttachmentArchivingResponseMock() {
        LicenseAttachmentArchivingResponse t = new LicenseAttachmentArchivingResponse();
        LicenseAttachmentArchivingDataResponse fileData = new LicenseAttachmentArchivingDataResponse();
        fileData.setFileName("test.txt");
        fileData.setFileData("tesadfasdfasef");
        t.setData(fileData);
        t.setAckCode(AckCode.SUCCESS);
        return t;
    }

    @Test
    public void testGetLicenseDerateAssistBySerialNumberLegalPerson() throws Exception {
        AssistLicenseProofRecordDo mockObjByLegalPerson = buildAssistLicenseProofRecordDoLegalPersonMock(AssistLicenseProofResultEnum.not_sign);
        Optional<AssistLicenseProofRecordDo> optionalRecord = Optional.of(mockObjByLegalPerson);
        when(assistLicenseProofRecordRepository.findBySerialNumber(anyString())).thenReturn(optionalRecord);
        LicenseAttachmentArchivingResponse t = buildLicenseAttachmentArchivingResponseMock();
        when(licenseProofSdk.licenseAttachmentArchiving(anyString())).thenReturn(t);
        Optional<GetAssistLicenseProofRecordDto> resultOptional = assistRecordServiceImpl.getLicenseDerateAssistBySerialNumber("test");
        Assert.assertTrue(resultOptional.isPresent());
        GetAssistLicenseProofRecordDto result = resultOptional.get();
        Assert.assertEquals(mockObjByLegalPerson.getSerialNumber(), result.getSerialNumber());
        Assert.assertTrue(StringUtils.isBlank(result.getToUserName()));
        Assert.assertTrue(StringUtils.isBlank(result.getLicenseItemPdf()));
    }

    @NotNull
    private static AssistLicenseProofRecordDo buildAssistLicenseProofRecordDoNaturePersonMock(@NotNull AssistLicenseProofResultEnum proofResultEnum) {
        AssistLicenseProofRecordDo mockObj = new AssistLicenseProofRecordDo();
        mockObj.setHandleAffairsName("test");
        mockObj.setHandleAffairsAssistIdentityType(AssistIdentityType.IDENTITY);
        mockObj.setHandleAffairsIdentityNumber("3213213213213");
        mockObj.setToAssistOrgName("test");
        mockObj.setToAssistCreditCode("fasdfasdf");
        mockObj.setFromAssistOrgName("asdfasdf");
        mockObj.setFromAssistCreditCode("asdfasdfa");
        mockObj.setFromAssistUserName("asdfasdf");
        mockObj.setAssistTime(new Date());
        mockObj.setCallBack(false);
        mockObj.setToUserId("test");
        mockObj.setToUserName("test");
        mockObj.setAuditTime(new Date());
        mockObj.setItemCode("test");
        mockObj.setItemName("test");
        mockObj.setMaterialId("test");
        mockObj.setHandleAffairsType(AssistHandleAffairsTypeEnum.NATURAL_PERSON);
        mockObj.setFromDemand("test");
//        mockObj.setLegalPersonIdentityType(LegalPersonIdentityType.CREDIT_CODE);
        mockObj.setProofCatalogId("test");
        mockObj.setProofCatalogName("test");
        mockObj.setInvalidDate(new Date());
        mockObj.setSerialNumber("test");
        mockObj.setProofResult(proofResultEnum);
        mockObj.setToAssistContain("test");
        mockObj.setMaterialName("test");
        mockObj.setAuditResult(AssistResultEnum.WAIT);
        mockObj.setAuditSuggestion("test");
        mockObj.setCreateAuthCode("test");
        mockObj.setImplementCode("test");
        mockObj.setFromAssistContain("test");
//        mockObj.setBizOrgName("test");
//        mockObj.setBizOrgCreditCode("test");
        mockObj.setLicenseName("test");
        mockObj.setId("test");
        mockObj.setCreatorId("test");
        mockObj.setCreationTime(new Date());
        mockObj.setLastModificatorId("test");
        mockObj.setLastModificationTime(new Date());
        return mockObj;
    }

    @NotNull
    private static AssistLicenseProofRecordDo buildAssistLicenseProofRecordDoLegalPersonMock(@NotNull AssistLicenseProofResultEnum proofResultEnum) {
        AssistLicenseProofRecordDo mockObj = new AssistLicenseProofRecordDo();
        mockObj.setHandleAffairsName("test");
//        mockObj.setHandleAffairsAssistIdentityType(AssistIdentityType.IDENTITY);
//        mockObj.setHandleAffairsIdentityNumber("3213213213213");
        mockObj.setToAssistOrgName("test");
        mockObj.setToAssistCreditCode("fasdfasdf");
        mockObj.setFromAssistOrgName("asdfasdf");
        mockObj.setFromAssistCreditCode("asdfasdfa");
        mockObj.setFromAssistUserName("asdfasdf");
        mockObj.setAssistTime(new Date());
        mockObj.setCallBack(false);
        mockObj.setToUserId("test");
        mockObj.setToUserName("test");
        mockObj.setAuditTime(new Date());
        mockObj.setItemCode("test");
        mockObj.setItemName("test");
        mockObj.setMaterialId("test");
        mockObj.setHandleAffairsType(AssistHandleAffairsTypeEnum.NATURAL_PERSON);
        mockObj.setFromDemand("test");
        mockObj.setLegalPersonIdentityType(LegalPersonIdentityType.CREDIT_CODE);
        mockObj.setProofCatalogId("test");
        mockObj.setProofCatalogName("test");
        mockObj.setInvalidDate(new Date());
        mockObj.setSerialNumber("test");
        mockObj.setProofResult(proofResultEnum);
        mockObj.setToAssistContain("test");
        mockObj.setMaterialName("test");
        mockObj.setAuditResult(AssistResultEnum.WAIT);
        mockObj.setAuditSuggestion("test");
        mockObj.setCreateAuthCode("test");
        mockObj.setImplementCode("test");
        mockObj.setFromAssistContain("test");
        mockObj.setBizOrgName("test");
        mockObj.setBizOrgCreditCode("test");
        mockObj.setLicenseName("test");
        mockObj.setId("test");
        mockObj.setCreatorId("test");
        mockObj.setCreationTime(new Date());
        mockObj.setLastModificatorId("test");
        mockObj.setLastModificationTime(new Date());
        return mockObj;
    }

    @Test
    public void testAssistDetail() throws Exception {
//        List<AssistRecordDetailPageBo> result = assistRecordServiceImpl.assistDetailGovEasyApi(new AssistRecordApiQuery());
//        Assert.assertEquals(Arrays.<AssistRecordDetailPageBo>asList(new AssistRecordDetailPageBo()), result);
    }

    @Test
    public void testAssistHistoryPage() throws Exception {
        Page<AssistRecordHistoryPageBo> result = assistRecordServiceImpl.assistHistoryPage(new AssistRecordHistoryPageQuery(), null);
        Assert.assertEquals(null, result);
    }

    @Test
    public void testAssistDetailApi() throws Exception {
//        List<AssistRecordDetailPageBo> result = assistRecordServiceImpl.assistDetailApi(new AssistRecordApiQuery());
//        Assert.assertEquals(Arrays.<AssistRecordDetailPageBo>asList(new AssistRecordDetailPageBo()), result);
    }


    @Test
    public void testGetAssistRecordInvalidDate() throws Exception {
        when(proofCatalogService.findArtificialByCatalogIdIn(any())).thenReturn(null);

        Optional<Date> result = assistRecordServiceImpl.getAssistRecordInvalidDate("catalogId", new GregorianCalendar(2024, Calendar.AUGUST, 22, 15, 36).getTime());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetAssistRecordInvalidDate2ToDayMinus() throws Exception {
        when(proofCatalogService.findArtificialByCatalogIdIn(any())).thenReturn(null);

        Optional<Date> result = assistRecordServiceImpl.getAssistRecordInvalidDate2ToDayMinus("catalogId", new GregorianCalendar(2024, Calendar.AUGUST, 22, 15, 36).getTime(), new GregorianCalendar(2024, Calendar.AUGUST, 22, 15, 36).getTime());
        Assert.assertEquals(null, result);
    }


    @Test
    public void testQueryListApi2() throws Exception {
        List<AssistRecordListApiResponse> result = assistRecordServiceImpl.queryListApi("assistCreditCode", "toUserName", "assistStatus");
        Assert.assertEquals(Arrays.<AssistRecordListApiResponse>asList(new AssistRecordListApiResponse()), result);
    }

    @Test
    public void testHandleApi() throws Exception {
        when(assistRecordRepository.findByAssistSerialNumber(anyString())).thenReturn(new AssistRecordDo());

        assistRecordServiceImpl.handleApi(new AssistRecordHandleApiRequest());
    }

    @Test
    public void testCountItemAndAuditTimeIsNullByIndex() throws Exception {
        when(assistRecordRepository.countItemAndAuditTimeIsNullByIndex(any())).thenReturn(Arrays.<Map<String, Object>>asList(new HashMap<String, Object>() {{
            put("String", "Map");
        }}));
        when(assistRecordRepository.countItemAndAuditTimeIsNullByIndex()).thenReturn(Arrays.<Map<String, Object>>asList(new HashMap<String, Object>() {{
            put("String", "Map");
        }}));

        Long result = assistRecordServiceImpl.countItemAndAuditTimeIsNullByIndex(Arrays.<String>asList("String"));
        Assert.assertEquals(Long.valueOf(1), result);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme