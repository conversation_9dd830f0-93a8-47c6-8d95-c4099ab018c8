package gov.derate.proof.assist.api;

import gov.derate.proof.assist.bo.AssistOrgSelectItemBo;
import gov.derate.proof.assist.bo.AssistRecordDetailPageBo;
import gov.derate.proof.assist.bo.AssistRecordHistoryDto;
import gov.derate.proof.assist.dto.GetAssistLicenseProofRecordDto;
import gov.derate.proof.assist.entity.AssistRecordDo;
import gov.derate.proof.assist.exception.AssistRecordServiceException;
import gov.derate.proof.assist.req.AssistRecordSaveApiRequest;
import gov.derate.proof.assist.req.GetLicenseDerateAssistApiRequest;
import gov.derate.proof.assist.req.LicenseDerateAssistSignApiRequest;
import gov.derate.proof.assist.resp.LicenseDerateAssistSignApiResponse;
import gov.derate.proof.assist.service.AssistRecordService;
import gov.derate.proof.assist.service.AssistUserService;
import gov.derate.proof.catalog.bo.CatalogApiBo;
import gov.derate.proof.catalog.service.ProofCatalogService;
import gov.derate.proof.common.enums.AssistLicenseProofResultEnum;
import gov.license.common.api.resp.ResponseResult;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.mockito.Mockito.*;

/**
 * AssistRecordApiTest.java
 * <p>
 * Company: Zsoft
 * CreateDate:2024/8/22
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class AssistRecordApiTest {
    @Mock
    Logger LOGGER;
    @Mock
    AssistRecordService assistRecordService;
    @Mock
    AssistUserService assistUserService;
    @Mock
    ProofCatalogService proofCatalogService;
    @InjectMocks
    AssistRecordApi assistRecordApi;

    @Before
    public void setUp() {
//        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testCreateApi() throws Exception {
        when(assistRecordService.createAssistByApi(any())).thenReturn(new AssistRecordDo());

        ResponseResult<String> result = assistRecordApi.createAssistByApi(new AssistRecordSaveApiRequest());
        Assert.assertEquals(null, result);
    }

    @Test
    public void testAuthGovEasyUser() throws Exception {
        ResponseResult<String> result = assistRecordApi.authGovEasyUser("appKey");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetAssistOrgList() throws Exception {
        when(assistUserService.queryListGroupByOrgCode(any())).thenReturn(Arrays.<AssistOrgSelectItemBo>asList(new AssistOrgSelectItemBo("orgCreditCode", "orgName")));

        ResponseResult<List<AssistOrgSelectItemBo>> result = assistRecordApi.getAssistOrgList("creditCode", "orgName", "itemCode", "materialName", "materialId", "proofCatalogId");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testGetAuditSuccessProofCatalog() throws Exception {
        when(proofCatalogService.getAuditSuccessProofCatalog(any())).thenReturn(Arrays.<CatalogApiBo>asList(new CatalogApiBo()));

        ResponseResult<List<CatalogApiBo>> result = assistRecordApi.getAuditSuccessProofCatalog("itemCode", "materialName", "materialId");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testAssistDetailApi() throws Exception {
//        when(assistRecordService.assistDetailApi(any())).thenReturn(Arrays.<AssistRecordDetailPageBo>asList(new AssistRecordDetailPageBo()));

        ResponseResult<AssistRecordDetailPageBo> result = assistRecordApi.assistDetailApi("assistSerialNumber");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testAssistHistoryApi() throws Exception {
        when(assistRecordService.assistHistoryApi(anyString())).thenReturn(Arrays.<AssistRecordHistoryDto>asList(new AssistRecordHistoryDto()));

        ResponseResult<List<AssistRecordHistoryDto>> result = assistRecordApi.assistHistoryApi("assistSerialNumber");
        Assert.assertEquals(null, result);
    }

    @Test
    public void testCreateLicenseDerateAssistSignApi() throws Exception {
        when(assistRecordService.createLicenseDerateAssistSign(any())).thenReturn("createLicenseDerateAssistSignResponse");

        ResponseResult<LicenseDerateAssistSignApiResponse> result = assistRecordApi.createLicenseDerateAssistSignApi(new LicenseDerateAssistSignApiRequest());
        Assert.assertEquals("createLicenseDerateAssistSignResponse", result.getData().getSerialNumber());
    }

    @Test
    public void testGetLicenseDerateAssistApi() throws Exception {
        GetAssistLicenseProofRecordDto recordDto = new GetAssistLicenseProofRecordDto();
        recordDto.setSerialNumber("test");
        recordDto.setProofResult(AssistLicenseProofResultEnum.not_sign);
        recordDto.setToUserName("test");
        recordDto.setToAssistContain("test");
        recordDto.setToAssistCreditCode("test");
        recordDto.setToAssistOrgName("test");
        recordDto.setAuditTime(new Date());
        recordDto.setLicenseItemPdf("test");
        recordDto.setFromDemand("test");
        recordDto.setFromAssistUserName("test");
        recordDto.setFromAssistOrgName("test");
        recordDto.setFromAssistCreditCode("test");
        recordDto.setItemCode("test");
        recordDto.setItemName("test");
        recordDto.setMaterialName("test");
        recordDto.setMaterialId("test");
        when(assistRecordService.getLicenseDerateAssistBySerialNumber(anyString())).thenReturn(Optional.of(recordDto));
        GetLicenseDerateAssistApiRequest request = new GetLicenseDerateAssistApiRequest();
        request.setSerialNumber("test");
        ResponseResult<GetAssistLicenseProofRecordDto> result = assistRecordApi.getLicenseDerateAssistApi(request);
        Assert.assertEquals("200", result.getMeta().getCode());
        Assert.assertEquals(recordDto.getSerialNumber(), result.getData().getSerialNumber());

    }

    @Test
    public void testGetLicenseDerateAssistApiThrow() throws Exception {
        when(assistRecordService.getLicenseDerateAssistBySerialNumber(anyString())).thenReturn(null);
        Assert.assertThrows(AssistRecordServiceException.class, () -> {
            ResponseResult<GetAssistLicenseProofRecordDto> result = assistRecordApi.getLicenseDerateAssistApi(new GetLicenseDerateAssistApiRequest());

        });
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme