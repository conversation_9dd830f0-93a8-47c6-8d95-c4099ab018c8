package gov.derate.proof.assist.api;


import gov.derate.proof.assist.req.AssistUserSaveRequest;
import gov.derate.proof.common.enums.AssistIdentityType;
import gov.license.common.tools.jackson.JacksonUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

/**
 * <AUTHOR>
 * @date 2021/9/30.
 */

@Rollback
//@Transactional(rollbackFor = Exception.class)
@WebAppConfiguration
//SpringBoot1.4版本之前用的是SpringJUnit4ClassRunner.class
@RunWith(SpringRunner.class)
@SpringBootTest
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = true)  //这里的事务关联到项目的配置文件中的事务控制器（"transactionManager"），同时指定事物自动回滚（defaultRollback= true），以此对数据库不会产生数据污染。
@PowerMockIgnore("javax.management.*")
public class AssistUserWebApiTest {

    protected MockMvc mockMvc;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Before
    public void setUp() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Rollback
    @Test
    public void createTest() throws Exception {
        AssistUserSaveRequest createRequest = new AssistUserSaveRequest();
        createRequest.setAssistorName("申请人");
        createRequest.setAssistorIdentityType(AssistIdentityType.IDENTITY);
        createRequest.setAssistorIdentityNumber("111111222222");
        createRequest.setAssistorPhone("1111");
        createRequest.setUserId("222");
        createRequest.setProofCode("1111,2222");
        createRequest.setOrgCreditCode("toOrg");
        createRequest.setOrgName("toOrg");

        String strJson = JacksonUtil.toJsonStr(createRequest);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/assist_user/create")
                        .accept(MediaType.parseMediaType("application/json;charset=utf-8"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(strJson)
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

    @Rollback
    @Test
    public void editTest() throws Exception {
        AssistUserSaveRequest createRequest = new AssistUserSaveRequest();
        createRequest.setAssistorName("申请人");
        createRequest.setAssistorIdentityType(AssistIdentityType.IDENTITY);
        createRequest.setAssistorIdentityNumber("111111222222");
        createRequest.setAssistorPhone("1111");
        createRequest.setUserId("222");
        createRequest.setProofCode("333");
        createRequest.setOrgCreditCode("toOrg");
        createRequest.setOrgName("toOrg");

        String strJson = JacksonUtil.toJsonStr(createRequest);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/assist_user/edit/c997ada7-43fd-4e2b-99f8-6b9abcd94597")
                        .accept(MediaType.parseMediaType("application/json;charset=utf-8"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(strJson)
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

    @Test
    public void orgList() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/assist_user/orgList")
//                        .param("proof_code","1111")
                        .param("page_direction", "DESC")
                        .param("page_number", "1")
                        .param("page_size", "10")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void userPageList() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/assist_user/page")
                .param("proof_code", "1111")
                .param("page_direction", "DESC")
                .param("page_number", "1")
                .param("page_size", "10")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Rollback
    @Test
    public void getById() throws Exception {
        String id = "c997ada7-43fd-4e2b-99f8-6b9abcd94597";
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/assist_user/view/" + id)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Rollback
    @Test
    public void deleteById() throws Exception {
        String id = "00000";
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/assist_user/delete/" + id)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }
}
