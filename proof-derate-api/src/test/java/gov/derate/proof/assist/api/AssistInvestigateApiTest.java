package gov.derate.proof.assist.api;

import com.google.common.collect.Lists;
import gov.derate.proof.assist.bo.AssistAttachmentBo;
import gov.derate.proof.assist.req.AssistRecordHandleApiRequest;
import gov.derate.proof.assist.resp.AssistRecordListApiResponse;
import gov.derate.proof.assist.service.AssistRecordService;
import gov.derate.proof.common.AbstractControllerTestTemplate;
import gov.license.common.tools.jackson.JacksonUtil;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@WebMvcTest(AssistInvestigateApi.class)
public class AssistInvestigateApiTest extends AbstractControllerTestTemplate {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AssistRecordService mockAssistRecordService;

    /**
     * 测试协查列表controller
     * @throws Exception 异常
     */
    @Test
    public void testListApi() throws Exception {
        // Setup
        // Configure AssistRecordService.queryListApi(...).
        MockHttpServletRequestBuilder accept = get("/api/v1/assist/list")
                .param("assist_credit_code", "assistCreditCode")
                .param("to_user_name", "toUserName")
                .param("assist_status", "assistStatus")
                .accept(MediaType.APPLICATION_JSON);
        templateMethodResponseResult(accept,(resultActions -> {})
                , this::buildAssistRecordListApiResponses
                ,(preSetReturnData) ->{
                    when(mockAssistRecordService.queryListApi("assistCreditCode", "toUserName", "assistStatus")).thenReturn(preSetReturnData);
                    return preSetReturnData;
                }
        );
    }

    /**
     * 构造响应数据
     * @return 协查记录数据
     */
    private List<AssistRecordListApiResponse> buildAssistRecordListApiResponses() {
        final AssistRecordListApiResponse assistRecordListApiResponse = new AssistRecordListApiResponse();
        assistRecordListApiResponse.setAssistSerialNumber("assistSerialNumber");
        assistRecordListApiResponse.setMaterialName("materialName");
        assistRecordListApiResponse.setItemName("itemName");
        assistRecordListApiResponse.setHandleAffairsName("handleAffairsName");
        assistRecordListApiResponse.setFromAssistOrgName("fromAssistOrgName");
        assistRecordListApiResponse.setFromAssistCreditCode("fromAssistCreditCode");
        assistRecordListApiResponse.setAssistTime(new Date());
        assistRecordListApiResponse.setAssistStatus("assistStatus");
        return Lists.newArrayList(assistRecordListApiResponse);
    }

    @Test
    public void testListApi_AssistRecordServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockAssistRecordService.queryListApi("assistCreditCode", "toUserName", "assistStatus")).thenReturn(Collections.emptyList());

        // Run the test
        MockHttpServletRequestBuilder accept = get("/api/v1/assist/list")
                .param("assist_credit_code", "assistCreditCode")
                .param("to_user_name", "toUserName")
                .param("assist_status", "assistStatus")
                .accept(MediaType.APPLICATION_JSON);

        templateMethodResponseResult(accept,(resultActions -> {})
                , Lists::newArrayList
                ,(preSetReturnData) ->{
                    when(mockAssistRecordService.queryListApi("assistCreditCode", "toUserName", "assistStatus")).thenReturn(Lists.newArrayList());
                    return preSetReturnData;
                }
        );
    }

    @Test
    public void testHandleApi() throws Exception {
        // Setup
        // 无返回方法调用设值
        AssistRecordHandleApiRequest request = buildAssistRecordHandleApiRequest();

        MockHttpServletRequestBuilder accept = post("/api/v1/assist/handle")
                .content(JacksonUtil.toJsonStr(request)).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON);
        templateMethodResponseResult(accept,(resultActions -> {})
                , ()->null
                ,(preSetReturnData) ->{
                    Mockito.doAnswer(invocation -> {
                        Object[] arguments = invocation.getArguments();
                        return "called with arguments:" + Arrays.toString(arguments);
                    }).when(mockAssistRecordService).handleApi(request);
                    return preSetReturnData;
                }
        );
    }

    /**
     * 构造请求数据
     * @return 请求数据
     */
    private AssistRecordHandleApiRequest buildAssistRecordHandleApiRequest() {
        AssistRecordHandleApiRequest request = new AssistRecordHandleApiRequest();
        request.setAssistSerialNumber("assistSerialNumber");
//        request.setToUserId("toUserId");
//        request.setAssistStatus("assistStatus");
//        request.setAuditSuggestion("auditSuggestion");
        AssistAttachmentBo assistFile = new AssistAttachmentBo();
        assistFile.setFileName("fileName");
        assistFile.setFileData(new byte[0]);
//        request.setAssistFile(assistFile);
//        request.setToUserName("toUserName");
//        request.setToAssistCreditCode("toAssistCreditCode");
//        request.setToAssistOrgName("toAssistOrgName");
        return request;
    }

    @Override
    public Object mockData(Object preSetData) {
        return null;
    }

    @Override
    public Object preSet() {
        return null;
    }
}
