package gov.derate.proof.assist.api;

import gov.derate.proof.assist.bo.AssistAttachmentBo;
import gov.derate.proof.assist.service.AssistAttachmentService;
import gov.derate.proof.assist.webapi.AssistAttachmentWebApi;
import gov.derate.proof.common.bo.UploadFileAttachmentBo;
import gov.derate.proof.common.service.UploadFileAttachmentService;
import gov.derate.proof.common.utils.MD5Utils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;

@RunWith(SpringRunner.class)
@WebMvcTest(AssistAttachmentWebApi.class)
public class AssistAttachmentWebApiTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AssistAttachmentService mockAssistAttachmentService;
    @MockBean
    private UploadFileAttachmentService mockUploadFileAttachmentService;

    @Test
    public void testUploadFile() throws Exception {
        // Setup
        // Configure UploadFileAttachmentService.save(...).
        final UploadFileAttachmentBo uploadFileAttachmentBo = new UploadFileAttachmentBo();
        uploadFileAttachmentBo.setFileName("fileName.pdf");
        uploadFileAttachmentBo.setFileData("content".getBytes());
        uploadFileAttachmentBo.setHash(MD5Utils.getMd5("content".getBytes()));
        when(mockUploadFileAttachmentService.save(eq("fileName.pdf"), any(byte[].class))).thenReturn(uploadFileAttachmentBo);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(multipart("/assist_attachment/upload")
                .file(new MockMultipartFile("file", "fileName.pdf", MediaType.APPLICATION_FORM_URLENCODED_VALUE, "content".getBytes()))
                .param("relation", "relation")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertTrue(response.getContentAsString().contains("200"));
//        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockUploadFileAttachmentService).save(eq("fileName.pdf"), any(byte[].class));
        verify(mockAssistAttachmentService).uploadFile(eq("relation"), any(byte[].class), eq("fileName.pdf"));
    }

    @Test
    public void testDownload() throws Exception {
        // Setup
        // Configure AssistAttachmentService.download(...).
        final AssistAttachmentBo assistAttachmentBo = new AssistAttachmentBo();
        assistAttachmentBo.setFileName("fileName.pdf");
        assistAttachmentBo.setFileData("content".getBytes());
        when(mockAssistAttachmentService.download("relation")).thenReturn(assistAttachmentBo);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/assist_attachment/download")
                .param("relation", "relation")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertTrue(response.getContentAsString().contains("200"));
    }
}
