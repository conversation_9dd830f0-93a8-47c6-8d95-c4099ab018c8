package gov.derate.proof.assist.api;


import gov.derate.proof.assist.req.AssistInfoItemSaveApiRequest;
import gov.derate.proof.assist.req.AssistInfoSaveApiRequest;
import gov.derate.proof.assist.req.AssistRecordSaveApiRequest;
import gov.derate.proof.common.enums.AssistIdentityType;
import gov.license.common.crypto.sca.SMGenerator;
import gov.license.common.tools.jackson.JacksonUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/9/30.
 */

@Rollback
//@Transactional(rollbackFor = Exception.class)
@WebAppConfiguration
//SpringBoot1.4版本之前用的是SpringJUnit4ClassRunner.class
@RunWith(SpringRunner.class)
@SpringBootTest
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = true)  //这里的事务关联到项目的配置文件中的事务控制器（"transactionManager"），同时指定事物自动回滚（defaultRollback= true），以此对数据库不会产生数据污染。
@PowerMockIgnore("javax.management.*")
public class AssistRecordApiControllerTest {

    protected MockMvc mockMvc;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Before
    public void setUp() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    public void listPage() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/assist")
                .param("assist_serial_number", "1111111113333333")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void watiAssistPage() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/user_wait_assist")
                .param("token", "1111111113333333")
                .param("assist_result", "WAIT")
                .param("page_direction", "DESC")
                .param("page_number", "1")
                .param("page_size", "10")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void getGovEasyUserByCode() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/get_gov_easy_user_by_code")
                .param("code", "1111111113333333")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void queryListApiTest() throws Exception {
        long timestamp = System.currentTimeMillis();
        String requestId = UUID.randomUUID().toString();
        System.out.println("requestId=" + requestId);
        System.out.println("timestamp=" + timestamp);
        String appSecurity = "cbd4a59344cfeab1268faf3af4c136b3";
        System.out.println("appSecurity=" + appSecurity);
        String signedValue = requestId + timestamp + appSecurity;
        System.out.println("signedValue=" + signedValue);
        System.out.println("signature=" + SMGenerator.sm3().digestHex(signedValue));
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/api/v1/assist")
                .param("assist_serial_number", "16424086201259ad76")
                .header("x-license-appkey", "123456789")
                .header("x-license-requestid", requestId)
                .header("x-license-timestamp", timestamp)
                .header("x-license-sign", SMGenerator.sm3().digestHex(signedValue))
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

    @Test
    public void createApiTest() throws Exception {
        AssistRecordSaveApiRequest createRequest = new AssistRecordSaveApiRequest();
        createRequest.setItemCode("123123123");
        createRequest.setItemName("测试事项");
        createRequest.setMaterialId(UUID.randomUUID().toString());
        createRequest.setHandleAffairsName("黎远民");
        createRequest.setHandleAffairsIdentityNumber("123");
        createRequest.setHandleAffairsAssistIdentityType(AssistIdentityType.IDENTITY);
        createRequest.setToAssistCreditCode("123");
        createRequest.setToAssistOrgName("测试部门");

        createRequest.setFromAssistCreditCode("123");
        createRequest.setFromAssistOrgName("测试部门");
        createRequest.setFromAssistUserName("黎远民");
        createRequest.setBusinessSerialNumber("111111222222");
//        createRequest.setHistoryAssistSerialNumber("");
//        createRequest.setHistoryAssistSerialNumber("164240834987782b19");
//        createRequest.setHistoryAssistSerialNumber("164240834987782b19,164240847488794802");
        createRequest.setHistoryAssistSerialNumber("164240834987782b19,164240847488794802,1642408529481ea921");
//        createRequest.setHistoryAssistSerialNumber("164240834987782b19,164240847488794802,1642408529481ea921,16424086201259ad76");
        List<AssistInfoSaveApiRequest> assistInfoList = new ArrayList<>();
        AssistInfoSaveApiRequest info1 = new AssistInfoSaveApiRequest();
        info1.setBottom("bottom1");
        List<AssistInfoItemSaveApiRequest> itemList = new ArrayList<>();
        AssistInfoItemSaveApiRequest infoItem1 = new AssistInfoItemSaveApiRequest();
        infoItem1.setKey("ke1");
        infoItem1.setValue("value1");
        infoItem1.setCols(1);

        AssistInfoItemSaveApiRequest infoItem2 = new AssistInfoItemSaveApiRequest();
        infoItem2.setKey("ke1");
        infoItem2.setValue("value1");
        infoItem2.setCols(1);

        itemList.add(infoItem1);
        itemList.add(infoItem2);

        info1.setItemList(itemList);

        assistInfoList.add(info1);

        createRequest.setAssistDataList(assistInfoList);

        String strJson = JacksonUtil.toJsonStr(createRequest);
        System.out.println(strJson);
        long timestamp = System.currentTimeMillis();
        String requestId = UUID.randomUUID().toString();
        System.out.println("requestId=" + requestId);
        System.out.println("timestamp=" + timestamp);
        String appSecurity = "cbd4a59344cfeab1268faf3af4c136b3";
        System.out.println("appSecurity=" + appSecurity);
        String signedValue = requestId + timestamp + appSecurity;
        System.out.println("signedValue=" + signedValue);
        System.out.println("signature=" + SMGenerator.sm3().digestHex(signedValue));
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/api/v1/assist")
                .accept(MediaType.parseMediaType("application/json;charset=utf-8"))
                .contentType(MediaType.APPLICATION_JSON)
                .header("x-license-appkey", "123456789")
                .header("x-license-requestid", requestId)
                .header("x-license-timestamp", timestamp)
                .header("x-license-sign", SMGenerator.sm3().digestHex(signedValue))
                .content(strJson)
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }


}
