package gov.derate.proof.exempt.bo;

import gov.derate.proof.assist.dto.AssistAuditDto;
import gov.derate.proof.catalog.dto.AuditRelationStatusDto;
import gov.derate.proof.catalog.enums.AuditRelationStatusEnums;
import gov.derate.proof.common.enums.AssistResultEnum;
import gov.derate.proof.common.utils.Base64EncoderUtils;
import gov.derate.proof.common.utils.PdfUtil;
import gov.derate.proof.common.utils.WordUtil;
import gov.derate.proof.exempt.service.ExemptInvestigationArchiveParam;
import gov.license.common.tools.date.DateUtil;
import org.apache.commons.compress.utils.Lists;

import javax.validation.constraints.NotNull;
import java.io.InputStream;
import java.util.Date;
import java.util.List;

//@SpringBootTest
class ExemptInvestigationArchivistBoTest {
    public static void main(String[] args) {
        new ExemptInvestigationArchivistBoTest().archivingInvestigation();
    }

    //    @Test
    void archivingInvestigation() {
        ExemptInvestigationArchiveParam archiveParam = new ExemptInvestigationArchiveParam();
        String test1 = "test";
        String test = test1;
        archiveParam.setOperatorOrgName(test);
        archiveParam.setOperatorName(test);
        archiveParam.setPlatformName(test);
        archiveParam.setAssistNumber(test);
        String identityType = test1;
        archiveParam.setBizOrg(test);
        archiveParam.setIdentityNumber(test);
        archiveParam.setIdentityType(identityType);
        archiveParam.setItemName(test);
        archiveParam.setItemMaterialName(test);
        archiveParam.setAssistComment(test);
        archiveParam.setInitialOrg(test);
        archiveParam.setInitialUserName(test);
        archiveParam.setContentNumber(test);
        archiveParam.setInitialDate(test);
        archiveParam.setAssistOrg(test);
        archiveParam.setAssistUserName(test);
        archiveParam.setAssistTime(test);
        archiveParam.setAssistResult(test);
        archiveParam.setAuditSuggestion("test2");
        archiveParam.setAssistAttachment(test);
        archiveParam.setArchiveDate(DateUtil.now());
        List<AuditRelationStatusDto<List<AssistAuditDto>>> list = Lists.newArrayList();
        AuditRelationStatusDto level1 = buildAuditStatusDto(test1, 1);
        AuditRelationStatusDto level2 = buildAuditStatusDto(test1, 2);
        list.add(level1);
        list.add(level2);
        archiveParam.setAuditData(list);

        // 1. 获取模版
        // 2. 对数据进行填值，生成doc文件。
        InputStream inputStream = WordUtil.ftl2WordInputStream("exemptInvestigationArchivistTemplate.ftl", archiveParam);
        // 3. doc文件转pdf
        byte[] pdfData = PdfUtil.word2pdf(inputStream);
        System.out.println(Base64EncoderUtils.transformBase64(pdfData));
    }

    @NotNull
    private static AuditRelationStatusDto buildAuditStatusDto(String test1, int level) {
        AuditRelationStatusDto auditRelationStatusDto = new AuditRelationStatusDto();
        auditRelationStatusDto.setAuditLevel(level);
        auditRelationStatusDto.setAuditStatus(AuditRelationStatusEnums.AUDITED);
//        auditRelationStatusDto.setVisitor();
        List<AssistAuditDto> list2 = Lists.newArrayList();
        AssistAuditDto auditDto = new AssistAuditDto();
        auditDto.setProcessPlatform("pc");
        auditDto.setToUserName(test1);
        auditDto.setToUserAccount(test1);
        auditDto.setAssistFromUser(test1);
        auditDto.setAssistFromUserAccount(test1);
        auditDto.setAssistTime(new Date());
        auditDto.setAuditTime(new Date());
        auditDto.setAssistAuditResult(AssistResultEnum.SUCCESS);
        auditDto.setAuditSuggestion(test1);
        auditDto.setAssistAttachmentId(test1);
        auditDto.setAssistAttachmentName(test1);
        auditDto.setAssistCode(test1);
        auditDto.setItemCode(test1);
        auditDto.setItemMaterialId(test1);
        auditDto.setProofCatalogCode(test1);
        auditDto.setCatalogReplaceWayId(test1);
        auditDto.setNeedAudit(true);
        auditDto.setAuditRecord(true);
        auditDto.setAuditLevel(level);
        auditDto.setAuditOrgCode(test1);
        auditDto.setAuditOrgName(test1);
        auditDto.setAuditDivisionCode(test1);
        auditDto.setAuditDivisionName(test1);
        auditDto.setOnlyKey(test1);

        AssistAuditDto auditDto2 = new AssistAuditDto();
        auditDto2.setProcessPlatform("pc");
        auditDto2.setToUserName(test1);
        auditDto2.setToUserAccount(test1);
        auditDto2.setAssistFromUser(test1);
        auditDto2.setAssistFromUserAccount(test1);
        auditDto2.setAssistTime(new Date());
        auditDto2.setAuditTime(new Date());
        auditDto2.setAssistAuditResult(AssistResultEnum.SUCCESS);
        auditDto2.setAuditSuggestion(test1);
        auditDto2.setAssistAttachmentId(test1);
        auditDto2.setAssistAttachmentName(test1);
        auditDto2.setAssistCode(test1);
        auditDto2.setItemCode(test1);
        auditDto2.setItemMaterialId(test1);
        auditDto2.setProofCatalogCode(test1);
        auditDto2.setCatalogReplaceWayId(test1);
        auditDto2.setNeedAudit(true);
        auditDto2.setAuditRecord(true);
        auditDto2.setAuditLevel(level);
        auditDto2.setAuditOrgCode(test1);
        auditDto2.setAuditOrgName(test1);
        auditDto2.setAuditDivisionCode(test1);
        auditDto2.setAuditDivisionName(test1);
        auditDto2.setOnlyKey(test1);

        list2.add(auditDto);
        list2.add(auditDto2);
        auditRelationStatusDto.setSubAuditObj(list2);
        return auditRelationStatusDto;
    }
}