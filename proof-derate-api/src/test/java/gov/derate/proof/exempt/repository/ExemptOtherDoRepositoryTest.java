package gov.derate.proof.exempt.repository;

import gov.derate.proof.base.BaseTestRepository;
import org.apache.commons.collections4.CollectionUtils;
import gov.derate.proof.exempt.entity.ExemptOtherDo;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ExemptOtherDoRepositoryTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/28；
 */
class ExemptOtherDoRepositoryTest extends BaseTestRepository {
    @Autowired
    private ExemptOtherDoRepository exemptOtherDoRepository;
    /**
     * 初始化数据
     *
     * @return 保存后数据
     */
    public  ExemptOtherDo initData() {
        ExemptOtherDo entity = new ExemptOtherDo();
        entity.setOtherClearDescription("setOtherClearDescription");
        entity.setVerificationAndInspection(true);
        entity.setAttachmentId("setAttachmentId");
        entity.setFileData(new byte[0]);
        entity.setAttachmentName("setAttachmentName");
        entity.setProofListId("setProofListId");
        entity.setItemCode("setItemCode");
        entity.setSerialNumber("setSerialNumber");
        return exemptOtherDoRepository.save(entity);
    }
    @Test
    void findAllBySerialNumberIn() {
        ExemptOtherDo exemptLicenseDo = initData();
        List<ExemptOtherDo> setSerialNumber = exemptOtherDoRepository.findAllBySerialNumberIn(Collections.singleton("setSerialNumber"));
        Assert.assertFalse(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void deleteAllBySerialNumber() {
        ExemptOtherDo exemptLicenseDo = initData();
        exemptOtherDoRepository.deleteAllBySerialNumber("setSerialNumber");
        List<ExemptOtherDo> setSerialNumber = exemptOtherDoRepository.findAllBySerialNumberIn(Collections.singleton("setSerialNumber"));
        Assert.assertTrue(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void findByIdAndAttachmentId() {
        ExemptOtherDo exemptLicenseDo = initData();
        ExemptOtherDo setAttachmentId = exemptOtherDoRepository.findByIdAndAttachmentId(exemptLicenseDo.getId(), "setAttachmentId");
        Assert.assertEquals(exemptLicenseDo, setAttachmentId);
    }
}