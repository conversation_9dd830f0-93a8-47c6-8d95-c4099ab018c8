package gov.derate.proof.exempt.api;

import gov.derate.proof.base.ControllerTestBaseTemplate;
import gov.derate.proof.common.enums.ExemptDownFileReplaceWayType;
import gov.derate.proof.common.enums.VerificationAndInspectionEnum;
import gov.derate.proof.common.vo.SelectItemVo;
import gov.derate.proof.exempt.bo.*;
import gov.derate.proof.exempt.entity.ExemptDoNotCleanDo;
import gov.derate.proof.exempt.resp.ExemptCertificatesManagerPageVo;
import gov.derate.proof.exempt.service.ExemptCertificatesService;
import org.junit.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

public class ExemptCertificatesManagerWebApiTest extends ControllerTestBaseTemplate {


    @MockBean
    private ExemptCertificatesService mockExemptCertificatesService;

    @Test
    public void testManagerPage() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.managerPage(...).
        final ExemptCertificatesManagerPageVo exemptCertificatesManagerPageVo = new ExemptCertificatesManagerPageVo();
        exemptCertificatesManagerPageVo.setSerialNumber("serialNumber");
        exemptCertificatesManagerPageVo.setItemName("itemName");
        exemptCertificatesManagerPageVo.setHandleAffairsNameOrBizOrgName("handleAffairsNameOrBizOrgName");
        exemptCertificatesManagerPageVo.setIdentityNumberOrOrgCode("identityNumberOrOrgCode");
        exemptCertificatesManagerPageVo.setProcessTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        exemptCertificatesManagerPageVo.setBizProgress(0);
        exemptCertificatesManagerPageVo.setProcessResult(VerificationAndInspectionEnum.SUCCESS);
        final Page<ExemptCertificatesManagerPageVo> exemptCertificatesManagerPageVos = new PageImpl<>(Arrays.asList(exemptCertificatesManagerPageVo));
//        when(mockExemptCertificatesService.managerPage(any(ExemptCertificatesManagerPageQuery.class), any(Pageable.class))).thenReturn(exemptCertificatesManagerPageVos);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/manager_page")
                .header("JWT-AUTHORIZATION","eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkaXZpc2lvbkNvZGUiOiI0NDEzMDAiLCJpc3MiOiJ6c29mdC1qd3QtaXNzIiwidXNlclR5cGUiOiJkZWZhdWx0IiwidXNlck5hbWUiOiJ0ZXN0X3h0Z2x5Iiwib3JnVWlkIjoiZTQzNmRkYWMtZDFkMy00MzAyLWEwNTMtZDMzNTAyOGJjNTgwIiwiZXhwIjoxNjU2MzI3MDQyLCJ1c2VySWQiOiIxYTdlNDIxYi1jYWVlLTQ0ZjktYWNiOC1mMWFjNGFlZmM0NzMiLCJpYXQiOjE2NTYzMjU1NDIsImp0aSI6ImY4MGYyZTM1LTQwOWItNDJlZi1iMjQwLWNlOWFhNjg5ZTRhZSIsInJlbmV3RXhwaXJlc0F0IjoxNjU3MTg5ODQyfQ.ZAeyUs_6S5wUqNk2V0nKxg93tSJxPn2w7Y9zHmQ5xTQ")
                .param("serial_number", "serial_number")
                .param("handle_affairs_name_or_biz_org_name", "handle_affairs_name_or_biz_org_name")
                .param("identity_number_or_org_code", "identity_number_or_org_code")
                .param("process_result", VerificationAndInspectionEnum.SUCCESS.name(), VerificationAndInspectionEnum.FAILURE.name(), VerificationAndInspectionEnum.TEMPORARY_STORAGE.name())
                .param("page_number", "0")
                .param("page_size", "0")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testManagerPage_ExemptCertificatesServiceReturnsNoItems() throws Exception {
        // Setup
//        when(mockExemptCertificatesService.managerPage(any(ExemptCertificatesManagerPageQuery.class), any(Pageable.class))).thenReturn(new PageImpl<>(Collections.emptyList()));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/manager_page")
                .param("serial_number", "serial_number")
                .param("handle_affairs_name_or_biz_org_name", "handle_affairs_name_or_biz_org_name")
                .param("identity_number_or_org_code", "identity_number_or_org_code")
                .param("process_result", "process_result")
                .param("page_number", "0")
                .param("page_size", "0")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testManagerViewDetail() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.managerViewDetail(...).
        final ExemptCertificatesDetailBo detailBo = new ExemptCertificatesDetailBo();
        detailBo.setAcceptCondition("acceptCondition");
        final ExemptCertificatesProofListBo proofListBo = new ExemptCertificatesProofListBo();
        proofListBo.setMaterialName("materialName");
        final ExemptClerkCommitmentDetailBo exemptClerkCommitmentDetailBo = new ExemptClerkCommitmentDetailBo();
        exemptClerkCommitmentDetailBo.setSerialNumber("serialNumber");
        exemptClerkCommitmentDetailBo.setProofListId("proofListId");
        exemptClerkCommitmentDetailBo.setVerificationAndInspection(false);
        exemptClerkCommitmentDetailBo.setCommitAttachmentId("commitAttachmentId");
        exemptClerkCommitmentDetailBo.setFileData("content".getBytes());
        exemptClerkCommitmentDetailBo.setCommitAttachmentName("commitAttachmentName");
        exemptClerkCommitmentDetailBo.setItemCode("itemCode");
        exemptClerkCommitmentDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setCommitmentDetailList(Arrays.asList(exemptClerkCommitmentDetailBo));
        final ExemptDataSharedDetailBo exemptDataSharedDetailBo = new ExemptDataSharedDetailBo();
        exemptDataSharedDetailBo.setProofListId("proofListId");
        exemptDataSharedDetailBo.setVerificationAndInspection(false);
        exemptDataSharedDetailBo.setItemCode("itemCode");
        exemptDataSharedDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDataSharedDetailBo.setSerialNumber("serialNumber");
        proofListBo.setDataSharedDetailList(Arrays.asList(exemptDataSharedDetailBo));
        final ExemptDeptSurveyDetailBo exemptDeptSurveyDetailBo = new ExemptDeptSurveyDetailBo();
        exemptDeptSurveyDetailBo.setSerialNumber("serialNumber");
        exemptDeptSurveyDetailBo.setProofListId("proofListId");
        exemptDeptSurveyDetailBo.setDeptName("deptName");
        exemptDeptSurveyDetailBo.setDeptCode("deptCode");
        exemptDeptSurveyDetailBo.setVerificationAndInspection(false);
        exemptDeptSurveyDetailBo.setItemCode("itemCode");
        exemptDeptSurveyDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setDeptSurveyDetailList(Arrays.asList(exemptDeptSurveyDetailBo));
        final ExemptInvestigationDetailBo exemptInvestigationDetailBo = new ExemptInvestigationDetailBo();
        exemptInvestigationDetailBo.setProofListId("proofListId");
        exemptInvestigationDetailBo.setVerificationAndInspection(false);
        exemptInvestigationDetailBo.setAssistSerialNumber("assistSerialNumber");
        exemptInvestigationDetailBo.setItemCode("itemCode");
        exemptInvestigationDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptInvestigationDetailBo.setSerialNumber("serialNumber");
        proofListBo.setInvestigationDetailList(Arrays.asList(exemptInvestigationDetailBo));
        final ExemptLicenseManageDetailBo exemptLicenseManageDetailBo = new ExemptLicenseManageDetailBo();
        exemptLicenseManageDetailBo.setProofListId("proofListId");
        exemptLicenseManageDetailBo.setVerificationAndInspection(false);
        exemptLicenseManageDetailBo.setAuthCode("authCode");
        exemptLicenseManageDetailBo.setCatalogCode("catalogCode");
        exemptLicenseManageDetailBo.setLicenseName("licenseName");
        exemptLicenseManageDetailBo.setItemCode("itemCode");
        exemptLicenseManageDetailBo.setAttachmentId("attachmentId");
        exemptLicenseManageDetailBo.setFileData("content".getBytes());
        exemptLicenseManageDetailBo.setAttachmentName("attachmentName");
        exemptLicenseManageDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setLicenseDetailList(Arrays.asList(exemptLicenseManageDetailBo));
        final ExemptOtherDetailBo exemptOtherDetailBo = new ExemptOtherDetailBo();
        exemptOtherDetailBo.setProofListId("proofListId");
        exemptOtherDetailBo.setVerificationAndInspection(false);
        exemptOtherDetailBo.setItemCode("itemCode");
        exemptOtherDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptOtherDetailBo.setSerialNumber("serialNumber");
        exemptOtherDetailBo.setAttachmentId("attachmentId");
        exemptOtherDetailBo.setFileData("content".getBytes());
        exemptOtherDetailBo.setAttachmentName("attachmentName");
        proofListBo.setOtherDetailList(Arrays.asList(exemptOtherDetailBo));
        final ExemptDirectlyCancelDetailBo exemptDirectlyCancelDetailBo = new ExemptDirectlyCancelDetailBo();
        exemptDirectlyCancelDetailBo.setProofListId("proofListId");
        exemptDirectlyCancelDetailBo.setVerificationAndInspection(false);
        exemptDirectlyCancelDetailBo.setItemCode("itemCode");
        exemptDirectlyCancelDetailBo.setSerialNumber("serialNumber");
        exemptDirectlyCancelDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDirectlyCancelDetailBo.setAttachmentId("attachmentId");
        exemptDirectlyCancelDetailBo.setFileData("content".getBytes());
        exemptDirectlyCancelDetailBo.setAttachmentName("attachmentName");
        proofListBo.setDirectlyCancelDetailList(Arrays.asList(exemptDirectlyCancelDetailBo));
        final ExemptDoNotCleanDetailBo exemptDoNotCleanDetailBo = new ExemptDoNotCleanDetailBo();
        exemptDoNotCleanDetailBo.setProofListId("proofListId");
        exemptDoNotCleanDetailBo.setVerificationAndInspection(false);
        exemptDoNotCleanDetailBo.setItemCode("itemCode");
        exemptDoNotCleanDetailBo.setSerialNumber("serialNumber");
        exemptDoNotCleanDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDoNotCleanDetailBo.setAttachmentId("attachmentId");
        exemptDoNotCleanDetailBo.setFileData("content".getBytes());
        proofListBo.setDoNotCleanDetailList(Arrays.asList(exemptDoNotCleanDetailBo));
        detailBo.setProofListBoList(Arrays.asList(proofListBo));
        when(mockExemptCertificatesService.managerViewDetail(eq("serialNumber"))).thenReturn(detailBo);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/manager_detail/{serial_number}", "serialNumber")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testManagerViewDetail_ExemptCertificatesServiceThrowsInstantiationException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.managerViewDetail(eq("serialNumber"))).thenThrow(InstantiationException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/manager_detail/{serial_number}", "serialNumber")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testManagerViewDetail_ExemptCertificatesServiceThrowsIllegalAccessException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.managerViewDetail(eq("serialNumber"))).thenThrow(IllegalAccessException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/manager_detail/{serial_number}", "serialNumber")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testLicenseAttachmentArchiving() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.licenseAttachmentArchiving(...).
        final ExemptDoNotCleanDo exemptDoNotCleanDo = new ExemptDoNotCleanDo();
        exemptDoNotCleanDo.setProofListId("proofListId");
        exemptDoNotCleanDo.setVerificationAndInspection(false);
        exemptDoNotCleanDo.setItemCode("itemCode");
        exemptDoNotCleanDo.setSerialNumber("serialNumber");
        exemptDoNotCleanDo.setAttachmentId("attachmentId");
        exemptDoNotCleanDo.setFileData("content".getBytes());
        exemptDoNotCleanDo.setAttachmentName("attachmentName");
        final ExemptAttachmentBo exemptAttachmentBo = new ExemptAttachmentBo(exemptDoNotCleanDo);
        when(mockExemptCertificatesService.licenseAttachmentArchiving("replaceWayId", "serialNumber", "licenseCode")).thenReturn(exemptAttachmentBo);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/manager/license/attachment_archiving/{serial_number}", "serialNumber")
                .param("license_code", "license_code")
                .param("replace_way_id", "replace_way_id")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockExemptCertificatesService).licenseAttachmentArchiving("replaceWayId", "serialNumber", "licenseCode");
    }

    @Test
    public void testLicenseView() throws Exception {
        // Setup
        when(mockExemptCertificatesService.licenseView("authCode", "serialNumber", "replaceWayId")).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/manager/license/view/{serial_number}", "serialNumber")
                .param("auth_code", "auth_code")
                .param("replace_way_id", "replace_way_id")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testLicenseView_ThrowsUnsupportedEncodingException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.licenseView("authCode", "serialNumber", "replaceWayId")).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/manager/license/view/{serial_number}", "serialNumber")
                .param("auth_code", "auth_code")
                .param("replace_way_id", "replace_way_id")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testUploadFile() throws Exception {
        // Setup
        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(multipart("/exempt/v1/exempt_certificates/manager/upload_file")
                .file(new MockMultipartFile("file", "originalFilename", MediaType.APPLICATION_JSON_VALUE, "content".getBytes()))
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testDownloadExemptAttachment() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.downloadExemptAttachment(...).
        final ExemptDoNotCleanDo exemptDoNotCleanDo = new ExemptDoNotCleanDo();
        exemptDoNotCleanDo.setProofListId("proofListId");
        exemptDoNotCleanDo.setVerificationAndInspection(false);
        exemptDoNotCleanDo.setItemCode("itemCode");
        exemptDoNotCleanDo.setSerialNumber("serialNumber");
        exemptDoNotCleanDo.setAttachmentId("attachmentId");
        exemptDoNotCleanDo.setFileData("content".getBytes());
        exemptDoNotCleanDo.setAttachmentName("attachmentName");
        final ExemptAttachmentBo exemptAttachmentBo = new ExemptAttachmentBo(exemptDoNotCleanDo);
        when(mockExemptCertificatesService.downloadExemptAttachment(ExemptDownFileReplaceWayType.LICENSE_ATTACHMENT_ARCHIVING, "fileId", "replaceWayId")).thenReturn(exemptAttachmentBo);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/manager/attachment/download")
                .param("replace_way_id", "replace_way_id")
                .param("exempt_down_file_replace_way_type", "exempt_down_file_replace_way_type")
                .param("file_id", "file_id")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testInvestigationAssist() throws Exception {
        // Setup
        when(mockExemptCertificatesService.investigationAssist("proofListId", "serialNumber", "orgCode", "orgName", "fromDemand", "replaceWayId")).thenReturn("result");

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager/investigation/assist/{serial_number}", "serialNumber")
                .param("org_code", "org_code")
                .param("org_name", "org_name")
                .param("from_demand", "from_demand")
                .param("proof_list_id", "proof_list_id")
                .param("replace_way_id", "replace_way_id")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testInvestigationAssist_ExemptCertificatesServiceThrowsIllegalAccessException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.investigationAssist("proofListId", "serialNumber", "orgCode", "orgName", "fromDemand", "replaceWayId")).thenThrow(IllegalAccessException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager/investigation/assist/{serial_number}", "serialNumber")
                .param("org_code", "org_code")
                .param("org_name", "org_name")
                .param("from_demand", "from_demand")
                .param("proof_list_id", "proof_list_id")
                .param("replace_way_id", "replace_way_id")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testInvestigationAssist_ExemptCertificatesServiceThrowsInstantiationException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.investigationAssist("proofListId", "serialNumber", "orgCode", "orgName", "fromDemand", "replaceWayId")).thenThrow(InstantiationException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager/investigation/assist/{serial_number}", "serialNumber")
                .param("org_code", "org_code")
                .param("org_name", "org_name")
                .param("from_demand", "from_demand")
                .param("proof_list_id", "proof_list_id")
                .param("replace_way_id", "replace_way_id")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testInvestigationAssist_ExemptCertificatesServiceThrowsIOException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.investigationAssist("proofListId", "serialNumber", "orgCode", "orgName", "fromDemand", "replaceWayId")).thenThrow(IOException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager/investigation/assist/{serial_number}", "serialNumber")
                .param("org_code", "org_code")
                .param("org_name", "org_name")
                .param("from_demand", "from_demand")
                .param("proof_list_id", "proof_list_id")
                .param("replace_way_id", "replace_way_id")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testGetInvestigationAssistOrg() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.getInvestigationAssistOrg(...).
        final List<SelectItemVo> selectItemVos = Arrays.asList(new SelectItemVo("label", "value"));
        when(mockExemptCertificatesService.getInvestigationAssistOrg("replaceWayId")).thenReturn(selectItemVos);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/manager/investigation/assist/org")
                .param("replace_way_id", "replace_way_id")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testGetInvestigationAssistOrg_ExemptCertificatesServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockExemptCertificatesService.getInvestigationAssistOrg("replaceWayId")).thenReturn(Collections.emptyList());

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/manager/investigation/assist/org")
                .param("replace_way_id", "replace_way_id")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Override
    public void beforeTest() {

    }

    @Override
    public void afterTest() {

    }
}
