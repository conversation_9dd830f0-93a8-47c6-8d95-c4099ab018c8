package gov.derate.proof.exempt.repository;

import gov.derate.proof.base.BaseTestRepository;
import org.apache.commons.collections4.CollectionUtils;
import gov.derate.proof.exempt.entity.ExemptDoNotCleanDo;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ExemptDoNotCleanDoRepositoryTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/28；
 */
class ExemptDoNotCleanDoRepositoryTest extends BaseTestRepository {
    @Autowired
    private ExemptDoNotCleanDoRepository exemptDoNotCleanDoRepository;
    /**
     * 初始化数据
     *
     * @return 保存后数据
     */
    public ExemptDoNotCleanDo initData() {
        ExemptDoNotCleanDo entity = new ExemptDoNotCleanDo();
        entity.setAttachmentId("setAttachmentId");
        entity.setFileData(new byte[0]);
        entity.setAttachmentName("setAttachmentName");
        entity.setProofListId("setProofListId");
        entity.setVerificationAndInspection(true);
        entity.setItemCode("setItemCode");
        entity.setSerialNumber("setSerialNumber");
        return exemptDoNotCleanDoRepository.save(entity);
    }
    @Test
    void findAllBySerialNumberIn() {
        ExemptDoNotCleanDo exemptDoNotCleanDo = initData();
        List<ExemptDoNotCleanDo> setSerialNumber = exemptDoNotCleanDoRepository.findAllBySerialNumberIn(Collections.singleton("setSerialNumber"));
        Assert.assertFalse(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void deleteAllBySerialNumber() {
        ExemptDoNotCleanDo exemptDoNotCleanDo = initData();
        exemptDoNotCleanDoRepository.deleteAllBySerialNumber("setSerialNumber");
        List<ExemptDoNotCleanDo> setSerialNumber = exemptDoNotCleanDoRepository.findAllBySerialNumberIn(Collections.singleton("setSerialNumber"));
        Assert.assertTrue(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void findByIdAndAttachmentId() {
        ExemptDoNotCleanDo exemptDoNotCleanDo = initData();
        ExemptDoNotCleanDo setAttachmentId = exemptDoNotCleanDoRepository.findByIdAndAttachmentId(exemptDoNotCleanDo.getId(), "setAttachmentId");
        Assert.assertEquals(exemptDoNotCleanDo,setAttachmentId);


    }
}