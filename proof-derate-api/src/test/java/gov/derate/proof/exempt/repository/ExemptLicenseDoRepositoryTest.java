package gov.derate.proof.exempt.repository;

import gov.derate.proof.base.BaseTestRepository;
import org.apache.commons.collections4.CollectionUtils;
import gov.derate.proof.exempt.entity.ExemptLicenseDo;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ExemptLicenseDoRepositoryTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/28；
 */
class ExemptLicenseDoRepositoryTest extends BaseTestRepository {
    @Autowired
    private ExemptLicenseDoRepository exemptLicenseDoRepository;

    /**
     * 初始化数据
     *
     * @return 保存后数据
     */
    public ExemptLicenseDo initData() {
        ExemptLicenseDo entity = new ExemptLicenseDo();
        entity.setVerificationAndInspection(true);
        entity.setAuthCode("setAuthCode");
        entity.setAttachmentId("setAttachmentId");
        entity.setFileData(new byte[0]);
        entity.setAttachmentName("setAttachmentName");
        entity.setLicenseCount(1);
        entity.setValidLicenseErrorMsg("错误");

        entity.setCatalogCode("setCatalogCode");
        entity.setLicenseName("setLicenseName");
        entity.setProofListId("setProofListId");
        entity.setItemCode("setItemCode");
        entity.setSerialNumber("setSerialNumber");
        return exemptLicenseDoRepository.save(entity);
    }

    @Test
    void findAllBySerialNumberIn() {
        ExemptLicenseDo exemptLicenseDo = initData();
        List<ExemptLicenseDo> setSerialNumber = exemptLicenseDoRepository.findAllBySerialNumberIn(Collections.singleton("setSerialNumber"));
        Assert.assertFalse(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void deleteAllBySerialNumber() {
        ExemptLicenseDo exemptLicenseDo = initData();
        exemptLicenseDoRepository.deleteAllBySerialNumber("setSerialNumber");
        List<ExemptLicenseDo> setSerialNumber = exemptLicenseDoRepository.findAllBySerialNumberIn(Collections.singleton("setSerialNumber"));
        Assert.assertTrue(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void findByIdAndAttachmentId() {
        ExemptLicenseDo exemptLicenseDo = initData();
        ExemptLicenseDo setAttachmentId = exemptLicenseDoRepository.findByIdAndAttachmentId(exemptLicenseDo.getId(), "setAttachmentId");
        Assert.assertEquals(exemptLicenseDo, setAttachmentId);
    }
}