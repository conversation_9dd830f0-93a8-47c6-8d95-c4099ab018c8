package gov.derate.proof.exempt.repository;

import com.google.common.collect.Lists;
import gov.derate.proof.base.BaseTestRepository;
import gov.derate.proof.exempt.entity.ExemptDataSharedDo;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <p>
 * ExemptDataSharedDoRepositoryTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/27
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/27；
 */
class ExemptDataSharedDoRepositoryTest extends BaseTestRepository {
    @Autowired
    private ExemptDataSharedDoRepository exemptDataSharedDoRepository;

    /**
     * 初始化数据
     *
     * @return 保存后数据
     */
    public ExemptDataSharedDo initData() {
        ExemptDataSharedDo entity = new ExemptDataSharedDo();
        entity.setProofListId("setProofListId");
        entity.setVerificationAndInspection(true);
        entity.setItemCode("setItemCode");
        entity.setSerialNumber("setSerialNumber");
        return exemptDataSharedDoRepository.save(entity);
    }

    /**
     * 测试查询方法
     */
    @Test
    void findAllBySerialNumberIn() {
        initData();
        List<ExemptDataSharedDo> setSerialNumber = exemptDataSharedDoRepository.findAllBySerialNumberIn(Lists.newArrayList("setSerialNumber"));
        Assert.assertTrue(CollectionUtils.isNotEmpty(setSerialNumber));
    }

    /**
     * 测试删除方法
     */
    @Test
    void deleteAllBySerialNumber() {
        initData();
        exemptDataSharedDoRepository.deleteAllBySerialNumber("setSerialNumber");
        List<ExemptDataSharedDo> setSerialNumber = exemptDataSharedDoRepository.findAllBySerialNumberIn(Lists.newArrayList("setSerialNumber"));
        Assert.assertTrue(CollectionUtils.isEmpty(setSerialNumber));

    }
}