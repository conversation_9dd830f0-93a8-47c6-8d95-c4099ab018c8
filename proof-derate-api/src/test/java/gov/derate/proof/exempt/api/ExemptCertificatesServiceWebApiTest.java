package gov.derate.proof.exempt.api;

import gov.derate.proof.base.ControllerTestBaseTemplate;
import gov.derate.proof.common.bo.user.*;
import gov.derate.proof.common.enums.ExemptIdentityTypeEnum;
import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.exempt.bo.*;
import gov.derate.proof.exempt.req.OneKeyValidateRequest;
import gov.derate.proof.exempt.resp.ExemptCentificatesServicePageResponse;
import gov.derate.proof.exempt.resp.OneKeyValidateContextResponse;
import gov.derate.proof.exempt.service.ExemptCertificatesService;
import gov.derate.proof.list.service.ProofListProcedureService;
import gov.derate.proof.list.vo.ProofListProcedureVo;
import org.junit.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.GregorianCalendar;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

public class ExemptCertificatesServiceWebApiTest extends ControllerTestBaseTemplate {


    @MockBean
    private ExemptCertificatesService mockExemptCertificatesService;
    @MockBean
    private ProofListProcedureService mockProofListProcedureService;
    /*@MockBean
    private CommonApiFacadeService mockAuthApiCommonService;*/

    @Test
    public void testServiceItemPage() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.serviceItemPage(...).
        final ExemptCentificatesServicePageResponse exemptCentificatesServicePageResponse = new ExemptCentificatesServicePageResponse();
        exemptCentificatesServicePageResponse.setItemName("itemName");
        exemptCentificatesServicePageResponse.setItemCode("itemCode");
        exemptCentificatesServicePageResponse.setImplOrgName("implOrgName");
        exemptCentificatesServicePageResponse.setCreditCode("creditCode");
        final Page<ExemptCentificatesServicePageResponse> exemptCentificatesServicePageResponses = new PageImpl<>(Arrays.asList(exemptCentificatesServicePageResponse));
        when(mockExemptCertificatesService.serviceItemPage(eq("itemName"), eq("itemOrgName"), any(), any(Pageable.class))).thenReturn(exemptCentificatesServicePageResponses);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/service_item_page")
                .param("item_name", "item_name")
                .param("item_org_name", "item_org_name")
                .param("page_number", "0")
                .param("page_size", "0")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testServiceItemPage_ExemptCertificatesServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockExemptCertificatesService.serviceItemPage(eq("itemName"), eq("itemOrgName"), any(), any(Pageable.class))).thenReturn(new PageImpl<>(Collections.emptyList()));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/service_item_page")
                .param("item_name", "item_name")
                .param("item_org_name", "item_org_name")
                .param("page_number", "0")
                .param("page_size", "0")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("[]", response.getContentAsString());
    }

    @Test
    public void testServiceItemPage_ExemptCertificatesServiceThrowsInstantiationException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.serviceItemPage(eq("itemName"), eq("itemOrgName"), any(), any(Pageable.class))).thenThrow(InstantiationException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/service_item_page")
                .param("item_name", "item_name")
                .param("item_org_name", "item_org_name")
                .param("page_number", "0")
                .param("page_size", "0")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testServiceItemPage_ExemptCertificatesServiceThrowsIllegalAccessException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.serviceItemPage(eq("itemName"), eq("itemOrgName"), any(), any(Pageable.class))).thenThrow(IllegalAccessException.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(get("/exempt/v1/exempt_certificates/service_item_page")
                .param("item_name", "item_name")
                .param("item_org_name", "item_org_name")
                .param("page_number", "0")
                .param("page_size", "0")
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testOneKeyValidate() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.oneKeyValidate(...).
        final ExemptCertificatesDetailBo detailBo = new ExemptCertificatesDetailBo();
        detailBo.setAcceptCondition("acceptCondition");
        final ExemptCertificatesProofListBo proofListBo = new ExemptCertificatesProofListBo();
        proofListBo.setMaterialName("materialName");
        final ExemptClerkCommitmentDetailBo exemptClerkCommitmentDetailBo = new ExemptClerkCommitmentDetailBo();
        exemptClerkCommitmentDetailBo.setSerialNumber("serialNumber");
        exemptClerkCommitmentDetailBo.setProofListId("proofListId");
        exemptClerkCommitmentDetailBo.setVerificationAndInspection(false);
        exemptClerkCommitmentDetailBo.setCommitAttachmentId("commitAttachmentId");
        exemptClerkCommitmentDetailBo.setFileData("content".getBytes());
        exemptClerkCommitmentDetailBo.setCommitAttachmentName("commitAttachmentName");
        exemptClerkCommitmentDetailBo.setItemCode("itemCode");
        exemptClerkCommitmentDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setCommitmentDetailList(Arrays.asList(exemptClerkCommitmentDetailBo));
        final ExemptDataSharedDetailBo exemptDataSharedDetailBo = new ExemptDataSharedDetailBo();
        exemptDataSharedDetailBo.setProofListId("proofListId");
        exemptDataSharedDetailBo.setVerificationAndInspection(false);
        exemptDataSharedDetailBo.setItemCode("itemCode");
        exemptDataSharedDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDataSharedDetailBo.setSerialNumber("serialNumber");
        proofListBo.setDataSharedDetailList(Arrays.asList(exemptDataSharedDetailBo));
        final ExemptDeptSurveyDetailBo exemptDeptSurveyDetailBo = new ExemptDeptSurveyDetailBo();
        exemptDeptSurveyDetailBo.setSerialNumber("serialNumber");
        exemptDeptSurveyDetailBo.setProofListId("proofListId");
        exemptDeptSurveyDetailBo.setDeptName("deptName");
        exemptDeptSurveyDetailBo.setDeptCode("deptCode");
        exemptDeptSurveyDetailBo.setVerificationAndInspection(false);
        exemptDeptSurveyDetailBo.setItemCode("itemCode");
        exemptDeptSurveyDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setDeptSurveyDetailList(Arrays.asList(exemptDeptSurveyDetailBo));
        final ExemptInvestigationDetailBo exemptInvestigationDetailBo = new ExemptInvestigationDetailBo();
        exemptInvestigationDetailBo.setProofListId("proofListId");
        exemptInvestigationDetailBo.setVerificationAndInspection(false);
        exemptInvestigationDetailBo.setAssistSerialNumber("assistSerialNumber");
        exemptInvestigationDetailBo.setItemCode("itemCode");
        exemptInvestigationDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptInvestigationDetailBo.setSerialNumber("serialNumber");
        proofListBo.setInvestigationDetailList(Arrays.asList(exemptInvestigationDetailBo));
        final ExemptLicenseManageDetailBo exemptLicenseManageDetailBo = new ExemptLicenseManageDetailBo();
        exemptLicenseManageDetailBo.setProofListId("proofListId");
        exemptLicenseManageDetailBo.setVerificationAndInspection(false);
        exemptLicenseManageDetailBo.setAuthCode("authCode");
        exemptLicenseManageDetailBo.setCatalogCode("catalogCode");
        exemptLicenseManageDetailBo.setLicenseName("licenseName");
        exemptLicenseManageDetailBo.setItemCode("itemCode");
        exemptLicenseManageDetailBo.setAttachmentId("attachmentId");
        exemptLicenseManageDetailBo.setFileData("content".getBytes());
        exemptLicenseManageDetailBo.setAttachmentName("attachmentName");
        exemptLicenseManageDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setLicenseDetailList(Arrays.asList(exemptLicenseManageDetailBo));
        final ExemptOtherDetailBo exemptOtherDetailBo = new ExemptOtherDetailBo();
        exemptOtherDetailBo.setProofListId("proofListId");
        exemptOtherDetailBo.setVerificationAndInspection(false);
        exemptOtherDetailBo.setItemCode("itemCode");
        exemptOtherDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptOtherDetailBo.setSerialNumber("serialNumber");
        exemptOtherDetailBo.setAttachmentId("attachmentId");
        exemptOtherDetailBo.setFileData("content".getBytes());
        exemptOtherDetailBo.setAttachmentName("attachmentName");
        proofListBo.setOtherDetailList(Arrays.asList(exemptOtherDetailBo));
        final ExemptDirectlyCancelDetailBo exemptDirectlyCancelDetailBo = new ExemptDirectlyCancelDetailBo();
        exemptDirectlyCancelDetailBo.setProofListId("proofListId");
        exemptDirectlyCancelDetailBo.setVerificationAndInspection(false);
        exemptDirectlyCancelDetailBo.setItemCode("itemCode");
        exemptDirectlyCancelDetailBo.setSerialNumber("serialNumber");
        exemptDirectlyCancelDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDirectlyCancelDetailBo.setAttachmentId("attachmentId");
        exemptDirectlyCancelDetailBo.setFileData("content".getBytes());
        exemptDirectlyCancelDetailBo.setAttachmentName("attachmentName");
        proofListBo.setDirectlyCancelDetailList(Arrays.asList(exemptDirectlyCancelDetailBo));
        final ExemptDoNotCleanDetailBo exemptDoNotCleanDetailBo = new ExemptDoNotCleanDetailBo();
        exemptDoNotCleanDetailBo.setProofListId("proofListId");
        exemptDoNotCleanDetailBo.setVerificationAndInspection(false);
        exemptDoNotCleanDetailBo.setItemCode("itemCode");
        exemptDoNotCleanDetailBo.setSerialNumber("serialNumber");
        exemptDoNotCleanDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDoNotCleanDetailBo.setAttachmentId("attachmentId");
        exemptDoNotCleanDetailBo.setFileData("content".getBytes());
        proofListBo.setDoNotCleanDetailList(Arrays.asList(exemptDoNotCleanDetailBo));
        detailBo.setProofListBoList(Arrays.asList(proofListBo));
        when(mockExemptCertificatesService.oneKeyValidate(any(OneKeyValidateRequest.class))).thenReturn(detailBo);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/one_key_validate")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testOneKeyValidate_ExemptCertificatesServiceThrowsException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.oneKeyValidate(any(OneKeyValidateRequest.class))).thenThrow(Exception.class);

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/one_key_validate")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testServiceHoldUp() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.saveByPageDetailBo(...).
        final ExemptCertificatesDetailBo detailBo = new ExemptCertificatesDetailBo();
        detailBo.setAcceptCondition("acceptCondition");
        final ExemptCertificatesProofListBo proofListBo = new ExemptCertificatesProofListBo();
        proofListBo.setMaterialName("materialName");
        final ExemptClerkCommitmentDetailBo exemptClerkCommitmentDetailBo = new ExemptClerkCommitmentDetailBo();
        exemptClerkCommitmentDetailBo.setSerialNumber("serialNumber");
        exemptClerkCommitmentDetailBo.setProofListId("proofListId");
        exemptClerkCommitmentDetailBo.setVerificationAndInspection(false);
        exemptClerkCommitmentDetailBo.setCommitAttachmentId("commitAttachmentId");
        exemptClerkCommitmentDetailBo.setFileData("content".getBytes());
        exemptClerkCommitmentDetailBo.setCommitAttachmentName("commitAttachmentName");
        exemptClerkCommitmentDetailBo.setItemCode("itemCode");
        exemptClerkCommitmentDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setCommitmentDetailList(Arrays.asList(exemptClerkCommitmentDetailBo));
        final ExemptDataSharedDetailBo exemptDataSharedDetailBo = new ExemptDataSharedDetailBo();
        exemptDataSharedDetailBo.setProofListId("proofListId");
        exemptDataSharedDetailBo.setVerificationAndInspection(false);
        exemptDataSharedDetailBo.setItemCode("itemCode");
        exemptDataSharedDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDataSharedDetailBo.setSerialNumber("serialNumber");
        proofListBo.setDataSharedDetailList(Arrays.asList(exemptDataSharedDetailBo));
        final ExemptDeptSurveyDetailBo exemptDeptSurveyDetailBo = new ExemptDeptSurveyDetailBo();
        exemptDeptSurveyDetailBo.setSerialNumber("serialNumber");
        exemptDeptSurveyDetailBo.setProofListId("proofListId");
        exemptDeptSurveyDetailBo.setDeptName("deptName");
        exemptDeptSurveyDetailBo.setDeptCode("deptCode");
        exemptDeptSurveyDetailBo.setVerificationAndInspection(false);
        exemptDeptSurveyDetailBo.setItemCode("itemCode");
        exemptDeptSurveyDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setDeptSurveyDetailList(Arrays.asList(exemptDeptSurveyDetailBo));
        final ExemptInvestigationDetailBo exemptInvestigationDetailBo = new ExemptInvestigationDetailBo();
        exemptInvestigationDetailBo.setProofListId("proofListId");
        exemptInvestigationDetailBo.setVerificationAndInspection(false);
        exemptInvestigationDetailBo.setAssistSerialNumber("assistSerialNumber");
        exemptInvestigationDetailBo.setItemCode("itemCode");
        exemptInvestigationDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptInvestigationDetailBo.setSerialNumber("serialNumber");
        proofListBo.setInvestigationDetailList(Arrays.asList(exemptInvestigationDetailBo));
        final ExemptLicenseManageDetailBo exemptLicenseManageDetailBo = new ExemptLicenseManageDetailBo();
        exemptLicenseManageDetailBo.setProofListId("proofListId");
        exemptLicenseManageDetailBo.setVerificationAndInspection(false);
        exemptLicenseManageDetailBo.setAuthCode("authCode");
        exemptLicenseManageDetailBo.setCatalogCode("catalogCode");
        exemptLicenseManageDetailBo.setLicenseName("licenseName");
        exemptLicenseManageDetailBo.setItemCode("itemCode");
        exemptLicenseManageDetailBo.setAttachmentId("attachmentId");
        exemptLicenseManageDetailBo.setFileData("content".getBytes());
        exemptLicenseManageDetailBo.setAttachmentName("attachmentName");
        exemptLicenseManageDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setLicenseDetailList(Arrays.asList(exemptLicenseManageDetailBo));
        final ExemptOtherDetailBo exemptOtherDetailBo = new ExemptOtherDetailBo();
        exemptOtherDetailBo.setProofListId("proofListId");
        exemptOtherDetailBo.setVerificationAndInspection(false);
        exemptOtherDetailBo.setItemCode("itemCode");
        exemptOtherDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptOtherDetailBo.setSerialNumber("serialNumber");
        exemptOtherDetailBo.setAttachmentId("attachmentId");
        exemptOtherDetailBo.setFileData("content".getBytes());
        exemptOtherDetailBo.setAttachmentName("attachmentName");
        proofListBo.setOtherDetailList(Arrays.asList(exemptOtherDetailBo));
        final ExemptDirectlyCancelDetailBo exemptDirectlyCancelDetailBo = new ExemptDirectlyCancelDetailBo();
        exemptDirectlyCancelDetailBo.setProofListId("proofListId");
        exemptDirectlyCancelDetailBo.setVerificationAndInspection(false);
        exemptDirectlyCancelDetailBo.setItemCode("itemCode");
        exemptDirectlyCancelDetailBo.setSerialNumber("serialNumber");
        exemptDirectlyCancelDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDirectlyCancelDetailBo.setAttachmentId("attachmentId");
        exemptDirectlyCancelDetailBo.setFileData("content".getBytes());
        exemptDirectlyCancelDetailBo.setAttachmentName("attachmentName");
        proofListBo.setDirectlyCancelDetailList(Arrays.asList(exemptDirectlyCancelDetailBo));
        final ExemptDoNotCleanDetailBo exemptDoNotCleanDetailBo = new ExemptDoNotCleanDetailBo();
        exemptDoNotCleanDetailBo.setProofListId("proofListId");
        exemptDoNotCleanDetailBo.setVerificationAndInspection(false);
        exemptDoNotCleanDetailBo.setItemCode("itemCode");
        exemptDoNotCleanDetailBo.setSerialNumber("serialNumber");
        exemptDoNotCleanDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDoNotCleanDetailBo.setAttachmentId("attachmentId");
        exemptDoNotCleanDetailBo.setFileData("content".getBytes());
        proofListBo.setDoNotCleanDetailList(Arrays.asList(exemptDoNotCleanDetailBo));
        detailBo.setProofListBoList(Arrays.asList(proofListBo));
        when(mockExemptCertificatesService.saveByPageDetailBo(any(ExemptCertificatesDetailBo.class))).thenReturn(detailBo);

        // Configure AuthApiCommonService.getCurrentUser(...).
        //final CurrentUserBo currentUserBo = new CurrentUserBo();
        final CurrentUserAccountBo userAccount = new CurrentUserAccountBo();
        userAccount.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setId("id");
        userAccount.setAccount("account");
        userAccount.setName("name");
        userAccount.setUserType("userType");
        userAccount.setDivisionCode("divisionCode");
        userAccount.setAccountCertificated("accountCertificated");
        /*currentUserBo.setUserAccount(userAccount);
        final CurrentUserInfoBo userInfo = new CurrentUserInfoBo();
        userInfo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setOrgId("orgId");
        userInfo.setOrgCode("orgCode");
        userInfo.setOrgName("orgName");
        userInfo.setTyshxxdm("tyshxxdm");
        userInfo.setDivisionCode("divisionCode");
        userInfo.setPhone("phone");
        userInfo.setMobile("mobile");
        userInfo.setIdentityNumber("identityNumber");
        currentUserBo.setUserInfo(userInfo);
        final CurrentUserRoleBo currentUserRoleBo = new CurrentUserRoleBo();
        currentUserRoleBo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setName("name");
        currentUserRoleBo.setCode("code");
        currentUserRoleBo.setRoleType("roleType");
        currentUserBo.setRoles(Arrays.asList(currentUserRoleBo));
        final CurrentUserRolePermissionVoBo currentUserRolePermissionVoBo = new CurrentUserRolePermissionVoBo();
        currentUserRolePermissionVoBo.setTarget("target");
        currentUserRolePermissionVoBo.setAction("action");
        currentUserRolePermissionVoBo.setRecipient("recipient");
        currentUserRolePermissionVoBo.setDataScope("dataScope");
        currentUserBo.setRolePermissionVos(Arrays.asList(currentUserRolePermissionVoBo));
        when(mockAuthApiCommonService.getCurrentUser()).thenReturn(currentUserBo);*/

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager_service/hold_up")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockProofListProcedureService).generateProcedure(any(ProofListProcedureVo.class), eq(ItemProofStatusEnum.WAIT_FOR_CLEAN), eq("operation"));
    }

    @Test
    public void testServiceHoldUp_ExemptCertificatesServiceThrowsInstantiationException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.saveByPageDetailBo(any(ExemptCertificatesDetailBo.class))).thenThrow(InstantiationException.class);

        // Configure AuthApiCommonService.getCurrentUser(...).
        //final CurrentUserBo currentUserBo = new CurrentUserBo();
        final CurrentUserAccountBo userAccount = new CurrentUserAccountBo();
        userAccount.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setId("id");
        userAccount.setAccount("account");
        userAccount.setName("name");
        userAccount.setUserType("userType");
        userAccount.setDivisionCode("divisionCode");
        userAccount.setAccountCertificated("accountCertificated");
        /*currentUserBo.setUserAccount(userAccount);
        final CurrentUserInfoBo userInfo = new CurrentUserInfoBo();
        userInfo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setOrgId("orgId");
        userInfo.setOrgCode("orgCode");
        userInfo.setOrgName("orgName");
        userInfo.setTyshxxdm("tyshxxdm");
        userInfo.setDivisionCode("divisionCode");
        userInfo.setPhone("phone");
        userInfo.setMobile("mobile");
        userInfo.setIdentityNumber("identityNumber");
        currentUserBo.setUserInfo(userInfo);
        final CurrentUserRoleBo currentUserRoleBo = new CurrentUserRoleBo();
        currentUserRoleBo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setName("name");
        currentUserRoleBo.setCode("code");
        currentUserRoleBo.setRoleType("roleType");
        currentUserBo.setRoles(Arrays.asList(currentUserRoleBo));
        final CurrentUserRolePermissionVoBo currentUserRolePermissionVoBo = new CurrentUserRolePermissionVoBo();
        currentUserRolePermissionVoBo.setTarget("target");
        currentUserRolePermissionVoBo.setAction("action");
        currentUserRolePermissionVoBo.setRecipient("recipient");
        currentUserRolePermissionVoBo.setDataScope("dataScope");
        currentUserBo.setRolePermissionVos(Arrays.asList(currentUserRolePermissionVoBo));
        when(mockAuthApiCommonService.getCurrentUser()).thenReturn(currentUserBo);*/

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager_service/hold_up")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockProofListProcedureService).generateProcedure(any(ProofListProcedureVo.class), eq(ItemProofStatusEnum.WAIT_FOR_CLEAN), eq("operation"));
    }

    @Test
    public void testServiceHoldUp_ExemptCertificatesServiceThrowsIllegalAccessException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.saveByPageDetailBo(any(ExemptCertificatesDetailBo.class))).thenThrow(IllegalAccessException.class);

        // Configure AuthApiCommonService.getCurrentUser(...).
        //final CurrentUserBo currentUserBo = new CurrentUserBo();
        final CurrentUserAccountBo userAccount = new CurrentUserAccountBo();
        userAccount.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setId("id");
        userAccount.setAccount("account");
        userAccount.setName("name");
        userAccount.setUserType("userType");
        userAccount.setDivisionCode("divisionCode");
        userAccount.setAccountCertificated("accountCertificated");
        /*currentUserBo.setUserAccount(userAccount);
        final CurrentUserInfoBo userInfo = new CurrentUserInfoBo();
        userInfo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setOrgId("orgId");
        userInfo.setOrgCode("orgCode");
        userInfo.setOrgName("orgName");
        userInfo.setTyshxxdm("tyshxxdm");
        userInfo.setDivisionCode("divisionCode");
        userInfo.setPhone("phone");
        userInfo.setMobile("mobile");
        userInfo.setIdentityNumber("identityNumber");
        currentUserBo.setUserInfo(userInfo);
        final CurrentUserRoleBo currentUserRoleBo = new CurrentUserRoleBo();
        currentUserRoleBo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setName("name");
        currentUserRoleBo.setCode("code");
        currentUserRoleBo.setRoleType("roleType");
        currentUserBo.setRoles(Arrays.asList(currentUserRoleBo));
        final CurrentUserRolePermissionVoBo currentUserRolePermissionVoBo = new CurrentUserRolePermissionVoBo();
        currentUserRolePermissionVoBo.setTarget("target");
        currentUserRolePermissionVoBo.setAction("action");
        currentUserRolePermissionVoBo.setRecipient("recipient");
        currentUserRolePermissionVoBo.setDataScope("dataScope");
        currentUserBo.setRolePermissionVos(Arrays.asList(currentUserRolePermissionVoBo));
        when(mockAuthApiCommonService.getCurrentUser()).thenReturn(currentUserBo);*/

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager_service/hold_up")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockProofListProcedureService).generateProcedure(any(ProofListProcedureVo.class), eq(ItemProofStatusEnum.WAIT_FOR_CLEAN), eq("operation"));
    }

    @Test
    public void testServiceHoldUp_ProofListProcedureServiceThrowsIllegalAccessException() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.saveByPageDetailBo(...).
        final ExemptCertificatesDetailBo detailBo = new ExemptCertificatesDetailBo();
        detailBo.setAcceptCondition("acceptCondition");
        final ExemptCertificatesProofListBo proofListBo = new ExemptCertificatesProofListBo();
        proofListBo.setMaterialName("materialName");
        final ExemptClerkCommitmentDetailBo exemptClerkCommitmentDetailBo = new ExemptClerkCommitmentDetailBo();
        exemptClerkCommitmentDetailBo.setSerialNumber("serialNumber");
        exemptClerkCommitmentDetailBo.setProofListId("proofListId");
        exemptClerkCommitmentDetailBo.setVerificationAndInspection(false);
        exemptClerkCommitmentDetailBo.setCommitAttachmentId("commitAttachmentId");
        exemptClerkCommitmentDetailBo.setFileData("content".getBytes());
        exemptClerkCommitmentDetailBo.setCommitAttachmentName("commitAttachmentName");
        exemptClerkCommitmentDetailBo.setItemCode("itemCode");
        exemptClerkCommitmentDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setCommitmentDetailList(Arrays.asList(exemptClerkCommitmentDetailBo));
        final ExemptDataSharedDetailBo exemptDataSharedDetailBo = new ExemptDataSharedDetailBo();
        exemptDataSharedDetailBo.setProofListId("proofListId");
        exemptDataSharedDetailBo.setVerificationAndInspection(false);
        exemptDataSharedDetailBo.setItemCode("itemCode");
        exemptDataSharedDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDataSharedDetailBo.setSerialNumber("serialNumber");
        proofListBo.setDataSharedDetailList(Arrays.asList(exemptDataSharedDetailBo));
        final ExemptDeptSurveyDetailBo exemptDeptSurveyDetailBo = new ExemptDeptSurveyDetailBo();
        exemptDeptSurveyDetailBo.setSerialNumber("serialNumber");
        exemptDeptSurveyDetailBo.setProofListId("proofListId");
        exemptDeptSurveyDetailBo.setDeptName("deptName");
        exemptDeptSurveyDetailBo.setDeptCode("deptCode");
        exemptDeptSurveyDetailBo.setVerificationAndInspection(false);
        exemptDeptSurveyDetailBo.setItemCode("itemCode");
        exemptDeptSurveyDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setDeptSurveyDetailList(Arrays.asList(exemptDeptSurveyDetailBo));
        final ExemptInvestigationDetailBo exemptInvestigationDetailBo = new ExemptInvestigationDetailBo();
        exemptInvestigationDetailBo.setProofListId("proofListId");
        exemptInvestigationDetailBo.setVerificationAndInspection(false);
        exemptInvestigationDetailBo.setAssistSerialNumber("assistSerialNumber");
        exemptInvestigationDetailBo.setItemCode("itemCode");
        exemptInvestigationDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptInvestigationDetailBo.setSerialNumber("serialNumber");
        proofListBo.setInvestigationDetailList(Arrays.asList(exemptInvestigationDetailBo));
        final ExemptLicenseManageDetailBo exemptLicenseManageDetailBo = new ExemptLicenseManageDetailBo();
        exemptLicenseManageDetailBo.setProofListId("proofListId");
        exemptLicenseManageDetailBo.setVerificationAndInspection(false);
        exemptLicenseManageDetailBo.setAuthCode("authCode");
        exemptLicenseManageDetailBo.setCatalogCode("catalogCode");
        exemptLicenseManageDetailBo.setLicenseName("licenseName");
        exemptLicenseManageDetailBo.setItemCode("itemCode");
        exemptLicenseManageDetailBo.setAttachmentId("attachmentId");
        exemptLicenseManageDetailBo.setFileData("content".getBytes());
        exemptLicenseManageDetailBo.setAttachmentName("attachmentName");
        exemptLicenseManageDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setLicenseDetailList(Arrays.asList(exemptLicenseManageDetailBo));
        final ExemptOtherDetailBo exemptOtherDetailBo = new ExemptOtherDetailBo();
        exemptOtherDetailBo.setProofListId("proofListId");
        exemptOtherDetailBo.setVerificationAndInspection(false);
        exemptOtherDetailBo.setItemCode("itemCode");
        exemptOtherDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptOtherDetailBo.setSerialNumber("serialNumber");
        exemptOtherDetailBo.setAttachmentId("attachmentId");
        exemptOtherDetailBo.setFileData("content".getBytes());
        exemptOtherDetailBo.setAttachmentName("attachmentName");
        proofListBo.setOtherDetailList(Arrays.asList(exemptOtherDetailBo));
        final ExemptDirectlyCancelDetailBo exemptDirectlyCancelDetailBo = new ExemptDirectlyCancelDetailBo();
        exemptDirectlyCancelDetailBo.setProofListId("proofListId");
        exemptDirectlyCancelDetailBo.setVerificationAndInspection(false);
        exemptDirectlyCancelDetailBo.setItemCode("itemCode");
        exemptDirectlyCancelDetailBo.setSerialNumber("serialNumber");
        exemptDirectlyCancelDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDirectlyCancelDetailBo.setAttachmentId("attachmentId");
        exemptDirectlyCancelDetailBo.setFileData("content".getBytes());
        exemptDirectlyCancelDetailBo.setAttachmentName("attachmentName");
        proofListBo.setDirectlyCancelDetailList(Arrays.asList(exemptDirectlyCancelDetailBo));
        final ExemptDoNotCleanDetailBo exemptDoNotCleanDetailBo = new ExemptDoNotCleanDetailBo();
        exemptDoNotCleanDetailBo.setProofListId("proofListId");
        exemptDoNotCleanDetailBo.setVerificationAndInspection(false);
        exemptDoNotCleanDetailBo.setItemCode("itemCode");
        exemptDoNotCleanDetailBo.setSerialNumber("serialNumber");
        exemptDoNotCleanDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDoNotCleanDetailBo.setAttachmentId("attachmentId");
        exemptDoNotCleanDetailBo.setFileData("content".getBytes());
        proofListBo.setDoNotCleanDetailList(Arrays.asList(exemptDoNotCleanDetailBo));
        detailBo.setProofListBoList(Arrays.asList(proofListBo));
        when(mockExemptCertificatesService.saveByPageDetailBo(any(ExemptCertificatesDetailBo.class))).thenReturn(detailBo);

        // Configure AuthApiCommonService.getCurrentUser(...).
        //final CurrentUserBo currentUserBo = new CurrentUserBo();
        final CurrentUserAccountBo userAccount = new CurrentUserAccountBo();
        userAccount.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setId("id");
        userAccount.setAccount("account");
        userAccount.setName("name");
        userAccount.setUserType("userType");
        userAccount.setDivisionCode("divisionCode");
        userAccount.setAccountCertificated("accountCertificated");
        /*currentUserBo.setUserAccount(userAccount);
        final CurrentUserInfoBo userInfo = new CurrentUserInfoBo();
        userInfo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setOrgId("orgId");
        userInfo.setOrgCode("orgCode");
        userInfo.setOrgName("orgName");
        userInfo.setTyshxxdm("tyshxxdm");
        userInfo.setDivisionCode("divisionCode");
        userInfo.setPhone("phone");
        userInfo.setMobile("mobile");
        userInfo.setIdentityNumber("identityNumber");
        currentUserBo.setUserInfo(userInfo);
        final CurrentUserRoleBo currentUserRoleBo = new CurrentUserRoleBo();
        currentUserRoleBo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setName("name");
        currentUserRoleBo.setCode("code");
        currentUserRoleBo.setRoleType("roleType");
        currentUserBo.setRoles(Arrays.asList(currentUserRoleBo));
        final CurrentUserRolePermissionVoBo currentUserRolePermissionVoBo = new CurrentUserRolePermissionVoBo();
        currentUserRolePermissionVoBo.setTarget("target");
        currentUserRolePermissionVoBo.setAction("action");
        currentUserRolePermissionVoBo.setRecipient("recipient");
        currentUserRolePermissionVoBo.setDataScope("dataScope");
        currentUserBo.setRolePermissionVos(Arrays.asList(currentUserRolePermissionVoBo));
        when(mockAuthApiCommonService.getCurrentUser()).thenReturn(currentUserBo);*/

        doThrow(IllegalAccessException.class).when(mockProofListProcedureService).generateProcedure(any(ProofListProcedureVo.class), eq(ItemProofStatusEnum.WAIT_FOR_CLEAN), eq("operation"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager_service/hold_up")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testServiceHoldUp_ProofListProcedureServiceThrowsInstantiationException() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.saveByPageDetailBo(...).
        final ExemptCertificatesDetailBo detailBo = new ExemptCertificatesDetailBo();
        detailBo.setAcceptCondition("acceptCondition");
        final ExemptCertificatesProofListBo proofListBo = new ExemptCertificatesProofListBo();
        proofListBo.setMaterialName("materialName");
        final ExemptClerkCommitmentDetailBo exemptClerkCommitmentDetailBo = new ExemptClerkCommitmentDetailBo();
        exemptClerkCommitmentDetailBo.setSerialNumber("serialNumber");
        exemptClerkCommitmentDetailBo.setProofListId("proofListId");
        exemptClerkCommitmentDetailBo.setVerificationAndInspection(false);
        exemptClerkCommitmentDetailBo.setCommitAttachmentId("commitAttachmentId");
        exemptClerkCommitmentDetailBo.setFileData("content".getBytes());
        exemptClerkCommitmentDetailBo.setCommitAttachmentName("commitAttachmentName");
        exemptClerkCommitmentDetailBo.setItemCode("itemCode");
        exemptClerkCommitmentDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setCommitmentDetailList(Arrays.asList(exemptClerkCommitmentDetailBo));
        final ExemptDataSharedDetailBo exemptDataSharedDetailBo = new ExemptDataSharedDetailBo();
        exemptDataSharedDetailBo.setProofListId("proofListId");
        exemptDataSharedDetailBo.setVerificationAndInspection(false);
        exemptDataSharedDetailBo.setItemCode("itemCode");
        exemptDataSharedDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDataSharedDetailBo.setSerialNumber("serialNumber");
        proofListBo.setDataSharedDetailList(Arrays.asList(exemptDataSharedDetailBo));
        final ExemptDeptSurveyDetailBo exemptDeptSurveyDetailBo = new ExemptDeptSurveyDetailBo();
        exemptDeptSurveyDetailBo.setSerialNumber("serialNumber");
        exemptDeptSurveyDetailBo.setProofListId("proofListId");
        exemptDeptSurveyDetailBo.setDeptName("deptName");
        exemptDeptSurveyDetailBo.setDeptCode("deptCode");
        exemptDeptSurveyDetailBo.setVerificationAndInspection(false);
        exemptDeptSurveyDetailBo.setItemCode("itemCode");
        exemptDeptSurveyDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setDeptSurveyDetailList(Arrays.asList(exemptDeptSurveyDetailBo));
        final ExemptInvestigationDetailBo exemptInvestigationDetailBo = new ExemptInvestigationDetailBo();
        exemptInvestigationDetailBo.setProofListId("proofListId");
        exemptInvestigationDetailBo.setVerificationAndInspection(false);
        exemptInvestigationDetailBo.setAssistSerialNumber("assistSerialNumber");
        exemptInvestigationDetailBo.setItemCode("itemCode");
        exemptInvestigationDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptInvestigationDetailBo.setSerialNumber("serialNumber");
        proofListBo.setInvestigationDetailList(Arrays.asList(exemptInvestigationDetailBo));
        final ExemptLicenseManageDetailBo exemptLicenseManageDetailBo = new ExemptLicenseManageDetailBo();
        exemptLicenseManageDetailBo.setProofListId("proofListId");
        exemptLicenseManageDetailBo.setVerificationAndInspection(false);
        exemptLicenseManageDetailBo.setAuthCode("authCode");
        exemptLicenseManageDetailBo.setCatalogCode("catalogCode");
        exemptLicenseManageDetailBo.setLicenseName("licenseName");
        exemptLicenseManageDetailBo.setItemCode("itemCode");
        exemptLicenseManageDetailBo.setAttachmentId("attachmentId");
        exemptLicenseManageDetailBo.setFileData("content".getBytes());
        exemptLicenseManageDetailBo.setAttachmentName("attachmentName");
        exemptLicenseManageDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setLicenseDetailList(Arrays.asList(exemptLicenseManageDetailBo));
        final ExemptOtherDetailBo exemptOtherDetailBo = new ExemptOtherDetailBo();
        exemptOtherDetailBo.setProofListId("proofListId");
        exemptOtherDetailBo.setVerificationAndInspection(false);
        exemptOtherDetailBo.setItemCode("itemCode");
        exemptOtherDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptOtherDetailBo.setSerialNumber("serialNumber");
        exemptOtherDetailBo.setAttachmentId("attachmentId");
        exemptOtherDetailBo.setFileData("content".getBytes());
        exemptOtherDetailBo.setAttachmentName("attachmentName");
        proofListBo.setOtherDetailList(Arrays.asList(exemptOtherDetailBo));
        final ExemptDirectlyCancelDetailBo exemptDirectlyCancelDetailBo = new ExemptDirectlyCancelDetailBo();
        exemptDirectlyCancelDetailBo.setProofListId("proofListId");
        exemptDirectlyCancelDetailBo.setVerificationAndInspection(false);
        exemptDirectlyCancelDetailBo.setItemCode("itemCode");
        exemptDirectlyCancelDetailBo.setSerialNumber("serialNumber");
        exemptDirectlyCancelDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDirectlyCancelDetailBo.setAttachmentId("attachmentId");
        exemptDirectlyCancelDetailBo.setFileData("content".getBytes());
        exemptDirectlyCancelDetailBo.setAttachmentName("attachmentName");
        proofListBo.setDirectlyCancelDetailList(Arrays.asList(exemptDirectlyCancelDetailBo));
        final ExemptDoNotCleanDetailBo exemptDoNotCleanDetailBo = new ExemptDoNotCleanDetailBo();
        exemptDoNotCleanDetailBo.setProofListId("proofListId");
        exemptDoNotCleanDetailBo.setVerificationAndInspection(false);
        exemptDoNotCleanDetailBo.setItemCode("itemCode");
        exemptDoNotCleanDetailBo.setSerialNumber("serialNumber");
        exemptDoNotCleanDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDoNotCleanDetailBo.setAttachmentId("attachmentId");
        exemptDoNotCleanDetailBo.setFileData("content".getBytes());
        proofListBo.setDoNotCleanDetailList(Arrays.asList(exemptDoNotCleanDetailBo));
        detailBo.setProofListBoList(Arrays.asList(proofListBo));
        when(mockExemptCertificatesService.saveByPageDetailBo(any(ExemptCertificatesDetailBo.class))).thenReturn(detailBo);

        // Configure AuthApiCommonService.getCurrentUser(...).
        //final CurrentUserBo currentUserBo = new CurrentUserBo();
        final CurrentUserAccountBo userAccount = new CurrentUserAccountBo();
        userAccount.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setId("id");
        userAccount.setAccount("account");
        userAccount.setName("name");
        userAccount.setUserType("userType");
        userAccount.setDivisionCode("divisionCode");
        userAccount.setAccountCertificated("accountCertificated");
        /*currentUserBo.setUserAccount(userAccount);
        final CurrentUserInfoBo userInfo = new CurrentUserInfoBo();
        userInfo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setOrgId("orgId");
        userInfo.setOrgCode("orgCode");
        userInfo.setOrgName("orgName");
        userInfo.setTyshxxdm("tyshxxdm");
        userInfo.setDivisionCode("divisionCode");
        userInfo.setPhone("phone");
        userInfo.setMobile("mobile");
        userInfo.setIdentityNumber("identityNumber");
        currentUserBo.setUserInfo(userInfo);
        final CurrentUserRoleBo currentUserRoleBo = new CurrentUserRoleBo();
        currentUserRoleBo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setName("name");
        currentUserRoleBo.setCode("code");
        currentUserRoleBo.setRoleType("roleType");
        currentUserBo.setRoles(Arrays.asList(currentUserRoleBo));
        final CurrentUserRolePermissionVoBo currentUserRolePermissionVoBo = new CurrentUserRolePermissionVoBo();
        currentUserRolePermissionVoBo.setTarget("target");
        currentUserRolePermissionVoBo.setAction("action");
        currentUserRolePermissionVoBo.setRecipient("recipient");
        currentUserRolePermissionVoBo.setDataScope("dataScope");
        currentUserBo.setRolePermissionVos(Arrays.asList(currentUserRolePermissionVoBo));
        when(mockAuthApiCommonService.getCurrentUser()).thenReturn(currentUserBo);*/

        doThrow(InstantiationException.class).when(mockProofListProcedureService).generateProcedure(any(ProofListProcedureVo.class), eq(ItemProofStatusEnum.WAIT_FOR_CLEAN), eq("operation"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager_service/hold_up")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
    }

    @Test
    public void testServiceHandle() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.saveByPageDetailBo(...).
        final ExemptCertificatesDetailBo detailBo = new ExemptCertificatesDetailBo();
        detailBo.setAcceptCondition("acceptCondition");
        final ExemptCertificatesProofListBo proofListBo = new ExemptCertificatesProofListBo();
        proofListBo.setMaterialName("materialName");
        final ExemptClerkCommitmentDetailBo exemptClerkCommitmentDetailBo = new ExemptClerkCommitmentDetailBo();
        exemptClerkCommitmentDetailBo.setSerialNumber("serialNumber");
        exemptClerkCommitmentDetailBo.setProofListId("proofListId");
        exemptClerkCommitmentDetailBo.setVerificationAndInspection(false);
        exemptClerkCommitmentDetailBo.setCommitAttachmentId("commitAttachmentId");
        exemptClerkCommitmentDetailBo.setFileData("content".getBytes());
        exemptClerkCommitmentDetailBo.setCommitAttachmentName("commitAttachmentName");
        exemptClerkCommitmentDetailBo.setItemCode("itemCode");
        exemptClerkCommitmentDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setCommitmentDetailList(Arrays.asList(exemptClerkCommitmentDetailBo));
        final ExemptDataSharedDetailBo exemptDataSharedDetailBo = new ExemptDataSharedDetailBo();
        exemptDataSharedDetailBo.setProofListId("proofListId");
        exemptDataSharedDetailBo.setVerificationAndInspection(false);
        exemptDataSharedDetailBo.setItemCode("itemCode");
        exemptDataSharedDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDataSharedDetailBo.setSerialNumber("serialNumber");
        proofListBo.setDataSharedDetailList(Arrays.asList(exemptDataSharedDetailBo));
        final ExemptDeptSurveyDetailBo exemptDeptSurveyDetailBo = new ExemptDeptSurveyDetailBo();
        exemptDeptSurveyDetailBo.setSerialNumber("serialNumber");
        exemptDeptSurveyDetailBo.setProofListId("proofListId");
        exemptDeptSurveyDetailBo.setDeptName("deptName");
        exemptDeptSurveyDetailBo.setDeptCode("deptCode");
        exemptDeptSurveyDetailBo.setVerificationAndInspection(false);
        exemptDeptSurveyDetailBo.setItemCode("itemCode");
        exemptDeptSurveyDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setDeptSurveyDetailList(Arrays.asList(exemptDeptSurveyDetailBo));
        final ExemptInvestigationDetailBo exemptInvestigationDetailBo = new ExemptInvestigationDetailBo();
        exemptInvestigationDetailBo.setProofListId("proofListId");
        exemptInvestigationDetailBo.setVerificationAndInspection(false);
        exemptInvestigationDetailBo.setAssistSerialNumber("assistSerialNumber");
        exemptInvestigationDetailBo.setItemCode("itemCode");
        exemptInvestigationDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptInvestigationDetailBo.setSerialNumber("serialNumber");
        proofListBo.setInvestigationDetailList(Arrays.asList(exemptInvestigationDetailBo));
        final ExemptLicenseManageDetailBo exemptLicenseManageDetailBo = new ExemptLicenseManageDetailBo();
        exemptLicenseManageDetailBo.setProofListId("proofListId");
        exemptLicenseManageDetailBo.setVerificationAndInspection(false);
        exemptLicenseManageDetailBo.setAuthCode("authCode");
        exemptLicenseManageDetailBo.setCatalogCode("catalogCode");
        exemptLicenseManageDetailBo.setLicenseName("licenseName");
        exemptLicenseManageDetailBo.setItemCode("itemCode");
        exemptLicenseManageDetailBo.setAttachmentId("attachmentId");
        exemptLicenseManageDetailBo.setFileData("content".getBytes());
        exemptLicenseManageDetailBo.setAttachmentName("attachmentName");
        exemptLicenseManageDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setLicenseDetailList(Arrays.asList(exemptLicenseManageDetailBo));
        final ExemptOtherDetailBo exemptOtherDetailBo = new ExemptOtherDetailBo();
        exemptOtherDetailBo.setProofListId("proofListId");
        exemptOtherDetailBo.setVerificationAndInspection(false);
        exemptOtherDetailBo.setItemCode("itemCode");
        exemptOtherDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptOtherDetailBo.setSerialNumber("serialNumber");
        exemptOtherDetailBo.setAttachmentId("attachmentId");
        exemptOtherDetailBo.setFileData("content".getBytes());
        exemptOtherDetailBo.setAttachmentName("attachmentName");
        proofListBo.setOtherDetailList(Arrays.asList(exemptOtherDetailBo));
        final ExemptDirectlyCancelDetailBo exemptDirectlyCancelDetailBo = new ExemptDirectlyCancelDetailBo();
        exemptDirectlyCancelDetailBo.setProofListId("proofListId");
        exemptDirectlyCancelDetailBo.setVerificationAndInspection(false);
        exemptDirectlyCancelDetailBo.setItemCode("itemCode");
        exemptDirectlyCancelDetailBo.setSerialNumber("serialNumber");
        exemptDirectlyCancelDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDirectlyCancelDetailBo.setAttachmentId("attachmentId");
        exemptDirectlyCancelDetailBo.setFileData("content".getBytes());
        exemptDirectlyCancelDetailBo.setAttachmentName("attachmentName");
        proofListBo.setDirectlyCancelDetailList(Arrays.asList(exemptDirectlyCancelDetailBo));
        final ExemptDoNotCleanDetailBo exemptDoNotCleanDetailBo = new ExemptDoNotCleanDetailBo();
        exemptDoNotCleanDetailBo.setProofListId("proofListId");
        exemptDoNotCleanDetailBo.setVerificationAndInspection(false);
        exemptDoNotCleanDetailBo.setItemCode("itemCode");
        exemptDoNotCleanDetailBo.setSerialNumber("serialNumber");
        exemptDoNotCleanDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDoNotCleanDetailBo.setAttachmentId("attachmentId");
        exemptDoNotCleanDetailBo.setFileData("content".getBytes());
        proofListBo.setDoNotCleanDetailList(Arrays.asList(exemptDoNotCleanDetailBo));
        detailBo.setProofListBoList(Arrays.asList(proofListBo));
        when(mockExemptCertificatesService.saveByPageDetailBo(any(ExemptCertificatesDetailBo.class))).thenReturn(detailBo);

        // Configure AuthApiCommonService.getCurrentUser(...).
        //final CurrentUserBo currentUserBo = new CurrentUserBo();
        final CurrentUserAccountBo userAccount = new CurrentUserAccountBo();
        userAccount.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setId("id");
        userAccount.setAccount("account");
        userAccount.setName("name");
        userAccount.setUserType("userType");
        userAccount.setDivisionCode("divisionCode");
        userAccount.setAccountCertificated("accountCertificated");
        /*currentUserBo.setUserAccount(userAccount);
        final CurrentUserInfoBo userInfo = new CurrentUserInfoBo();
        userInfo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setOrgId("orgId");
        userInfo.setOrgCode("orgCode");
        userInfo.setOrgName("orgName");
        userInfo.setTyshxxdm("tyshxxdm");
        userInfo.setDivisionCode("divisionCode");
        userInfo.setPhone("phone");
        userInfo.setMobile("mobile");
        userInfo.setIdentityNumber("identityNumber");
        currentUserBo.setUserInfo(userInfo);
        final CurrentUserRoleBo currentUserRoleBo = new CurrentUserRoleBo();
        currentUserRoleBo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setName("name");
        currentUserRoleBo.setCode("code");
        currentUserRoleBo.setRoleType("roleType");
        currentUserBo.setRoles(Arrays.asList(currentUserRoleBo));
        final CurrentUserRolePermissionVoBo currentUserRolePermissionVoBo = new CurrentUserRolePermissionVoBo();
        currentUserRolePermissionVoBo.setTarget("target");
        currentUserRolePermissionVoBo.setAction("action");
        currentUserRolePermissionVoBo.setRecipient("recipient");
        currentUserRolePermissionVoBo.setDataScope("dataScope");
        currentUserBo.setRolePermissionVos(Arrays.asList(currentUserRolePermissionVoBo));
        when(mockAuthApiCommonService.getCurrentUser()).thenReturn(currentUserBo);*/

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager_service/handle")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockExemptCertificatesService).saveByPageDetailBo(any(ExemptCertificatesDetailBo.class));
        verify(mockProofListProcedureService).generateProcedure(any(ProofListProcedureVo.class), eq(ItemProofStatusEnum.WAIT_FOR_CLEAN), eq("operation"));
    }

    @Test
    public void testServiceHandle_ExemptCertificatesServiceThrowsInstantiationException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.saveByPageDetailBo(any(ExemptCertificatesDetailBo.class))).thenThrow(InstantiationException.class);

        // Configure AuthApiCommonService.getCurrentUser(...).
        //final CurrentUserBo currentUserBo = new CurrentUserBo();
        final CurrentUserAccountBo userAccount = new CurrentUserAccountBo();
        userAccount.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setId("id");
        userAccount.setAccount("account");
        userAccount.setName("name");
        userAccount.setUserType("userType");
        userAccount.setDivisionCode("divisionCode");
        userAccount.setAccountCertificated("accountCertificated");
        /*currentUserBo.setUserAccount(userAccount);
        final CurrentUserInfoBo userInfo = new CurrentUserInfoBo();
        userInfo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setOrgId("orgId");
        userInfo.setOrgCode("orgCode");
        userInfo.setOrgName("orgName");
        userInfo.setTyshxxdm("tyshxxdm");
        userInfo.setDivisionCode("divisionCode");
        userInfo.setPhone("phone");
        userInfo.setMobile("mobile");
        userInfo.setIdentityNumber("identityNumber");
        currentUserBo.setUserInfo(userInfo);
        final CurrentUserRoleBo currentUserRoleBo = new CurrentUserRoleBo();
        currentUserRoleBo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setName("name");
        currentUserRoleBo.setCode("code");
        currentUserRoleBo.setRoleType("roleType");
        currentUserBo.setRoles(Arrays.asList(currentUserRoleBo));
        final CurrentUserRolePermissionVoBo currentUserRolePermissionVoBo = new CurrentUserRolePermissionVoBo();
        currentUserRolePermissionVoBo.setTarget("target");
        currentUserRolePermissionVoBo.setAction("action");
        currentUserRolePermissionVoBo.setRecipient("recipient");
        currentUserRolePermissionVoBo.setDataScope("dataScope");
        currentUserBo.setRolePermissionVos(Arrays.asList(currentUserRolePermissionVoBo));
        when(mockAuthApiCommonService.getCurrentUser()).thenReturn(currentUserBo);*/

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager_service/handle")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockProofListProcedureService).generateProcedure(any(ProofListProcedureVo.class), eq(ItemProofStatusEnum.WAIT_FOR_CLEAN), eq("operation"));
    }

    @Test
    public void testServiceHandle_ExemptCertificatesServiceThrowsIllegalAccessException() throws Exception {
        // Setup
        when(mockExemptCertificatesService.saveByPageDetailBo(any(ExemptCertificatesDetailBo.class))).thenThrow(IllegalAccessException.class);

        // Configure AuthApiCommonService.getCurrentUser(...).
        //final CurrentUserBo currentUserBo = new CurrentUserBo();
        final CurrentUserAccountBo userAccount = new CurrentUserAccountBo();
        userAccount.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setId("id");
        userAccount.setAccount("account");
        userAccount.setName("name");
        userAccount.setUserType("userType");
        userAccount.setDivisionCode("divisionCode");
        userAccount.setAccountCertificated("accountCertificated");
        /*currentUserBo.setUserAccount(userAccount);
        final CurrentUserInfoBo userInfo = new CurrentUserInfoBo();
        userInfo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setOrgId("orgId");
        userInfo.setOrgCode("orgCode");
        userInfo.setOrgName("orgName");
        userInfo.setTyshxxdm("tyshxxdm");
        userInfo.setDivisionCode("divisionCode");
        userInfo.setPhone("phone");
        userInfo.setMobile("mobile");
        userInfo.setIdentityNumber("identityNumber");
        currentUserBo.setUserInfo(userInfo);
        final CurrentUserRoleBo currentUserRoleBo = new CurrentUserRoleBo();
        currentUserRoleBo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setName("name");
        currentUserRoleBo.setCode("code");
        currentUserRoleBo.setRoleType("roleType");
        currentUserBo.setRoles(Arrays.asList(currentUserRoleBo));
        final CurrentUserRolePermissionVoBo currentUserRolePermissionVoBo = new CurrentUserRolePermissionVoBo();
        currentUserRolePermissionVoBo.setTarget("target");
        currentUserRolePermissionVoBo.setAction("action");
        currentUserRolePermissionVoBo.setRecipient("recipient");
        currentUserRolePermissionVoBo.setDataScope("dataScope");
        currentUserBo.setRolePermissionVos(Arrays.asList(currentUserRolePermissionVoBo));
        when(mockAuthApiCommonService.getCurrentUser()).thenReturn(currentUserBo);*/

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager_service/handle")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockProofListProcedureService).generateProcedure(any(ProofListProcedureVo.class), eq(ItemProofStatusEnum.WAIT_FOR_CLEAN), eq("operation"));
    }

    @Test
    public void testServiceHandle_ProofListProcedureServiceThrowsIllegalAccessException() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.saveByPageDetailBo(...).
        final ExemptCertificatesDetailBo detailBo = new ExemptCertificatesDetailBo();
        detailBo.setAcceptCondition("acceptCondition");
        final ExemptCertificatesProofListBo proofListBo = new ExemptCertificatesProofListBo();
        proofListBo.setMaterialName("materialName");
        final ExemptClerkCommitmentDetailBo exemptClerkCommitmentDetailBo = new ExemptClerkCommitmentDetailBo();
        exemptClerkCommitmentDetailBo.setSerialNumber("serialNumber");
        exemptClerkCommitmentDetailBo.setProofListId("proofListId");
        exemptClerkCommitmentDetailBo.setVerificationAndInspection(false);
        exemptClerkCommitmentDetailBo.setCommitAttachmentId("commitAttachmentId");
        exemptClerkCommitmentDetailBo.setFileData("content".getBytes());
        exemptClerkCommitmentDetailBo.setCommitAttachmentName("commitAttachmentName");
        exemptClerkCommitmentDetailBo.setItemCode("itemCode");
        exemptClerkCommitmentDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setCommitmentDetailList(Arrays.asList(exemptClerkCommitmentDetailBo));
        final ExemptDataSharedDetailBo exemptDataSharedDetailBo = new ExemptDataSharedDetailBo();
        exemptDataSharedDetailBo.setProofListId("proofListId");
        exemptDataSharedDetailBo.setVerificationAndInspection(false);
        exemptDataSharedDetailBo.setItemCode("itemCode");
        exemptDataSharedDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDataSharedDetailBo.setSerialNumber("serialNumber");
        proofListBo.setDataSharedDetailList(Arrays.asList(exemptDataSharedDetailBo));
        final ExemptDeptSurveyDetailBo exemptDeptSurveyDetailBo = new ExemptDeptSurveyDetailBo();
        exemptDeptSurveyDetailBo.setSerialNumber("serialNumber");
        exemptDeptSurveyDetailBo.setProofListId("proofListId");
        exemptDeptSurveyDetailBo.setDeptName("deptName");
        exemptDeptSurveyDetailBo.setDeptCode("deptCode");
        exemptDeptSurveyDetailBo.setVerificationAndInspection(false);
        exemptDeptSurveyDetailBo.setItemCode("itemCode");
        exemptDeptSurveyDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setDeptSurveyDetailList(Arrays.asList(exemptDeptSurveyDetailBo));
        final ExemptInvestigationDetailBo exemptInvestigationDetailBo = new ExemptInvestigationDetailBo();
        exemptInvestigationDetailBo.setProofListId("proofListId");
        exemptInvestigationDetailBo.setVerificationAndInspection(false);
        exemptInvestigationDetailBo.setAssistSerialNumber("assistSerialNumber");
        exemptInvestigationDetailBo.setItemCode("itemCode");
        exemptInvestigationDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptInvestigationDetailBo.setSerialNumber("serialNumber");
        proofListBo.setInvestigationDetailList(Arrays.asList(exemptInvestigationDetailBo));
        final ExemptLicenseManageDetailBo exemptLicenseManageDetailBo = new ExemptLicenseManageDetailBo();
        exemptLicenseManageDetailBo.setProofListId("proofListId");
        exemptLicenseManageDetailBo.setVerificationAndInspection(false);
        exemptLicenseManageDetailBo.setAuthCode("authCode");
        exemptLicenseManageDetailBo.setCatalogCode("catalogCode");
        exemptLicenseManageDetailBo.setLicenseName("licenseName");
        exemptLicenseManageDetailBo.setItemCode("itemCode");
        exemptLicenseManageDetailBo.setAttachmentId("attachmentId");
        exemptLicenseManageDetailBo.setFileData("content".getBytes());
        exemptLicenseManageDetailBo.setAttachmentName("attachmentName");
        exemptLicenseManageDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setLicenseDetailList(Arrays.asList(exemptLicenseManageDetailBo));
        final ExemptOtherDetailBo exemptOtherDetailBo = new ExemptOtherDetailBo();
        exemptOtherDetailBo.setProofListId("proofListId");
        exemptOtherDetailBo.setVerificationAndInspection(false);
        exemptOtherDetailBo.setItemCode("itemCode");
        exemptOtherDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptOtherDetailBo.setSerialNumber("serialNumber");
        exemptOtherDetailBo.setAttachmentId("attachmentId");
        exemptOtherDetailBo.setFileData("content".getBytes());
        exemptOtherDetailBo.setAttachmentName("attachmentName");
        proofListBo.setOtherDetailList(Arrays.asList(exemptOtherDetailBo));
        final ExemptDirectlyCancelDetailBo exemptDirectlyCancelDetailBo = new ExemptDirectlyCancelDetailBo();
        exemptDirectlyCancelDetailBo.setProofListId("proofListId");
        exemptDirectlyCancelDetailBo.setVerificationAndInspection(false);
        exemptDirectlyCancelDetailBo.setItemCode("itemCode");
        exemptDirectlyCancelDetailBo.setSerialNumber("serialNumber");
        exemptDirectlyCancelDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDirectlyCancelDetailBo.setAttachmentId("attachmentId");
        exemptDirectlyCancelDetailBo.setFileData("content".getBytes());
        exemptDirectlyCancelDetailBo.setAttachmentName("attachmentName");
        proofListBo.setDirectlyCancelDetailList(Arrays.asList(exemptDirectlyCancelDetailBo));
        final ExemptDoNotCleanDetailBo exemptDoNotCleanDetailBo = new ExemptDoNotCleanDetailBo();
        exemptDoNotCleanDetailBo.setProofListId("proofListId");
        exemptDoNotCleanDetailBo.setVerificationAndInspection(false);
        exemptDoNotCleanDetailBo.setItemCode("itemCode");
        exemptDoNotCleanDetailBo.setSerialNumber("serialNumber");
        exemptDoNotCleanDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDoNotCleanDetailBo.setAttachmentId("attachmentId");
        exemptDoNotCleanDetailBo.setFileData("content".getBytes());
        proofListBo.setDoNotCleanDetailList(Arrays.asList(exemptDoNotCleanDetailBo));
        detailBo.setProofListBoList(Arrays.asList(proofListBo));
        when(mockExemptCertificatesService.saveByPageDetailBo(any(ExemptCertificatesDetailBo.class))).thenReturn(detailBo);

        // Configure AuthApiCommonService.getCurrentUser(...).
        // final CurrentUserBo currentUserBo = new CurrentUserBo();
        final CurrentUserAccountBo userAccount = new CurrentUserAccountBo();
        userAccount.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setId("id");
        userAccount.setAccount("account");
        userAccount.setName("name");
        userAccount.setUserType("userType");
        userAccount.setDivisionCode("divisionCode");
        userAccount.setAccountCertificated("accountCertificated");
        /*currentUserBo.setUserAccount(userAccount);
        final CurrentUserInfoBo userInfo = new CurrentUserInfoBo();
        userInfo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setOrgId("orgId");
        userInfo.setOrgCode("orgCode");
        userInfo.setOrgName("orgName");
        userInfo.setTyshxxdm("tyshxxdm");
        userInfo.setDivisionCode("divisionCode");
        userInfo.setPhone("phone");
        userInfo.setMobile("mobile");
        userInfo.setIdentityNumber("identityNumber");
        currentUserBo.setUserInfo(userInfo);
        final CurrentUserRoleBo currentUserRoleBo = new CurrentUserRoleBo();
        currentUserRoleBo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setName("name");
        currentUserRoleBo.setCode("code");
        currentUserRoleBo.setRoleType("roleType");
        currentUserBo.setRoles(Arrays.asList(currentUserRoleBo));
        final CurrentUserRolePermissionVoBo currentUserRolePermissionVoBo = new CurrentUserRolePermissionVoBo();
        currentUserRolePermissionVoBo.setTarget("target");
        currentUserRolePermissionVoBo.setAction("action");
        currentUserRolePermissionVoBo.setRecipient("recipient");
        currentUserRolePermissionVoBo.setDataScope("dataScope");
        currentUserBo.setRolePermissionVos(Arrays.asList(currentUserRolePermissionVoBo));
        when(mockAuthApiCommonService.getCurrentUser()).thenReturn(currentUserBo);*/

        doThrow(IllegalAccessException.class).when(mockProofListProcedureService).generateProcedure(any(ProofListProcedureVo.class), eq(ItemProofStatusEnum.WAIT_FOR_CLEAN), eq("operation"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager_service/handle")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockExemptCertificatesService).saveByPageDetailBo(any(ExemptCertificatesDetailBo.class));
    }

    @Test
    public void testServiceHandle_ProofListProcedureServiceThrowsInstantiationException() throws Exception {
        // Setup
        // Configure ExemptCertificatesService.saveByPageDetailBo(...).
        final ExemptCertificatesDetailBo detailBo = new ExemptCertificatesDetailBo();
        detailBo.setAcceptCondition("acceptCondition");
        final ExemptCertificatesProofListBo proofListBo = new ExemptCertificatesProofListBo();
        proofListBo.setMaterialName("materialName");
        final ExemptClerkCommitmentDetailBo exemptClerkCommitmentDetailBo = new ExemptClerkCommitmentDetailBo();
        exemptClerkCommitmentDetailBo.setSerialNumber("serialNumber");
        exemptClerkCommitmentDetailBo.setProofListId("proofListId");
        exemptClerkCommitmentDetailBo.setVerificationAndInspection(false);
        exemptClerkCommitmentDetailBo.setCommitAttachmentId("commitAttachmentId");
        exemptClerkCommitmentDetailBo.setFileData("content".getBytes());
        exemptClerkCommitmentDetailBo.setCommitAttachmentName("commitAttachmentName");
        exemptClerkCommitmentDetailBo.setItemCode("itemCode");
        exemptClerkCommitmentDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setCommitmentDetailList(Arrays.asList(exemptClerkCommitmentDetailBo));
        final ExemptDataSharedDetailBo exemptDataSharedDetailBo = new ExemptDataSharedDetailBo();
        exemptDataSharedDetailBo.setProofListId("proofListId");
        exemptDataSharedDetailBo.setVerificationAndInspection(false);
        exemptDataSharedDetailBo.setItemCode("itemCode");
        exemptDataSharedDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDataSharedDetailBo.setSerialNumber("serialNumber");
        proofListBo.setDataSharedDetailList(Arrays.asList(exemptDataSharedDetailBo));
        final ExemptDeptSurveyDetailBo exemptDeptSurveyDetailBo = new ExemptDeptSurveyDetailBo();
        exemptDeptSurveyDetailBo.setSerialNumber("serialNumber");
        exemptDeptSurveyDetailBo.setProofListId("proofListId");
        exemptDeptSurveyDetailBo.setDeptName("deptName");
        exemptDeptSurveyDetailBo.setDeptCode("deptCode");
        exemptDeptSurveyDetailBo.setVerificationAndInspection(false);
        exemptDeptSurveyDetailBo.setItemCode("itemCode");
        exemptDeptSurveyDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setDeptSurveyDetailList(Arrays.asList(exemptDeptSurveyDetailBo));
        final ExemptInvestigationDetailBo exemptInvestigationDetailBo = new ExemptInvestigationDetailBo();
        exemptInvestigationDetailBo.setProofListId("proofListId");
        exemptInvestigationDetailBo.setVerificationAndInspection(false);
        exemptInvestigationDetailBo.setAssistSerialNumber("assistSerialNumber");
        exemptInvestigationDetailBo.setItemCode("itemCode");
        exemptInvestigationDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptInvestigationDetailBo.setSerialNumber("serialNumber");
        proofListBo.setInvestigationDetailList(Arrays.asList(exemptInvestigationDetailBo));
        final ExemptLicenseManageDetailBo exemptLicenseManageDetailBo = new ExemptLicenseManageDetailBo();
        exemptLicenseManageDetailBo.setProofListId("proofListId");
        exemptLicenseManageDetailBo.setVerificationAndInspection(false);
        exemptLicenseManageDetailBo.setAuthCode("authCode");
        exemptLicenseManageDetailBo.setCatalogCode("catalogCode");
        exemptLicenseManageDetailBo.setLicenseName("licenseName");
        exemptLicenseManageDetailBo.setItemCode("itemCode");
        exemptLicenseManageDetailBo.setAttachmentId("attachmentId");
        exemptLicenseManageDetailBo.setFileData("content".getBytes());
        exemptLicenseManageDetailBo.setAttachmentName("attachmentName");
        exemptLicenseManageDetailBo.setReplaceCancelWay("replaceCancelWay");
        proofListBo.setLicenseDetailList(Arrays.asList(exemptLicenseManageDetailBo));
        final ExemptOtherDetailBo exemptOtherDetailBo = new ExemptOtherDetailBo();
        exemptOtherDetailBo.setProofListId("proofListId");
        exemptOtherDetailBo.setVerificationAndInspection(false);
        exemptOtherDetailBo.setItemCode("itemCode");
        exemptOtherDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptOtherDetailBo.setSerialNumber("serialNumber");
        exemptOtherDetailBo.setAttachmentId("attachmentId");
        exemptOtherDetailBo.setFileData("content".getBytes());
        exemptOtherDetailBo.setAttachmentName("attachmentName");
        proofListBo.setOtherDetailList(Arrays.asList(exemptOtherDetailBo));
        final ExemptDirectlyCancelDetailBo exemptDirectlyCancelDetailBo = new ExemptDirectlyCancelDetailBo();
        exemptDirectlyCancelDetailBo.setProofListId("proofListId");
        exemptDirectlyCancelDetailBo.setVerificationAndInspection(false);
        exemptDirectlyCancelDetailBo.setItemCode("itemCode");
        exemptDirectlyCancelDetailBo.setSerialNumber("serialNumber");
        exemptDirectlyCancelDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDirectlyCancelDetailBo.setAttachmentId("attachmentId");
        exemptDirectlyCancelDetailBo.setFileData("content".getBytes());
        exemptDirectlyCancelDetailBo.setAttachmentName("attachmentName");
        proofListBo.setDirectlyCancelDetailList(Arrays.asList(exemptDirectlyCancelDetailBo));
        final ExemptDoNotCleanDetailBo exemptDoNotCleanDetailBo = new ExemptDoNotCleanDetailBo();
        exemptDoNotCleanDetailBo.setProofListId("proofListId");
        exemptDoNotCleanDetailBo.setVerificationAndInspection(false);
        exemptDoNotCleanDetailBo.setItemCode("itemCode");
        exemptDoNotCleanDetailBo.setSerialNumber("serialNumber");
        exemptDoNotCleanDetailBo.setReplaceCancelWay("replaceCancelWay");
        exemptDoNotCleanDetailBo.setAttachmentId("attachmentId");
        exemptDoNotCleanDetailBo.setFileData("content".getBytes());
        proofListBo.setDoNotCleanDetailList(Arrays.asList(exemptDoNotCleanDetailBo));
        detailBo.setProofListBoList(Arrays.asList(proofListBo));
        when(mockExemptCertificatesService.saveByPageDetailBo(any(ExemptCertificatesDetailBo.class))).thenReturn(detailBo);

        // Configure AuthApiCommonService.getCurrentUser(...).
        // final CurrentUserBo currentUserBo = new CurrentUserBo();
        final CurrentUserAccountBo userAccount = new CurrentUserAccountBo();
        userAccount.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userAccount.setId("id");
        userAccount.setAccount("account");
        userAccount.setName("name");
        userAccount.setUserType("userType");
        userAccount.setDivisionCode("divisionCode");
        userAccount.setAccountCertificated("accountCertificated");
        /*currentUserBo.setUserAccount(userAccount);
        final CurrentUserInfoBo userInfo = new CurrentUserInfoBo();
        userInfo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        userInfo.setOrgId("orgId");
        userInfo.setOrgCode("orgCode");
        userInfo.setOrgName("orgName");
        userInfo.setTyshxxdm("tyshxxdm");
        userInfo.setDivisionCode("divisionCode");
        userInfo.setPhone("phone");
        userInfo.setMobile("mobile");
        userInfo.setIdentityNumber("identityNumber");
        currentUserBo.setUserInfo(userInfo);
        final CurrentUserRoleBo currentUserRoleBo = new CurrentUserRoleBo();
        currentUserRoleBo.setCreatedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setLastModifiedDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        currentUserRoleBo.setName("name");
        currentUserRoleBo.setCode("code");
        currentUserRoleBo.setRoleType("roleType");
        currentUserBo.setRoles(Arrays.asList(currentUserRoleBo));
        final CurrentUserRolePermissionVoBo currentUserRolePermissionVoBo = new CurrentUserRolePermissionVoBo();
        currentUserRolePermissionVoBo.setTarget("target");
        currentUserRolePermissionVoBo.setAction("action");
        currentUserRolePermissionVoBo.setRecipient("recipient");
        currentUserRolePermissionVoBo.setDataScope("dataScope");
        currentUserBo.setRolePermissionVos(Arrays.asList(currentUserRolePermissionVoBo));
        when(mockAuthApiCommonService.getCurrentUser()).thenReturn(currentUserBo);*/

        doThrow(InstantiationException.class).when(mockProofListProcedureService).generateProcedure(any(ProofListProcedureVo.class), eq(ItemProofStatusEnum.WAIT_FOR_CLEAN), eq("operation"));

        // Run the test
        final MockHttpServletResponse response = mockMvc.perform(post("/exempt/v1/exempt_certificates/manager_service/handle")
                .content("content").contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        // Verify the results
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), response.getStatus());
        assertEquals("expectedResponse", response.getContentAsString());
        verify(mockExemptCertificatesService).saveByPageDetailBo(any(ExemptCertificatesDetailBo.class));
    }

    @Override
    public void beforeTest() {

    }

    @Override
    public void afterTest() {

    }
}
