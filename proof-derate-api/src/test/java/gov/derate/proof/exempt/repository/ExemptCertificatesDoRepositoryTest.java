package gov.derate.proof.exempt.repository;

import gov.derate.proof.base.BaseTestRepository;
import gov.derate.proof.common.enums.ExemptCertificatesAffairsTypeEnum;
import gov.derate.proof.common.enums.ExemptIdentityTypeEnum;
import gov.derate.proof.common.enums.VerificationAndInspectionEnum;
import gov.derate.proof.exempt.entity.ExemptCertificatesDo;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <p>
 * ExemptCertificatesDoRepositoryTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/27
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/27；
 */
class ExemptCertificatesDoRepositoryTest extends BaseTestRepository {
    @Autowired
    private ExemptCertificatesDoRepository exemptCertificatesDoRepository;

    /**
     * 初始化数据
     *
     * @return 保存后数据
     */
    public ExemptCertificatesDo initData() {
        ExemptCertificatesDo entity = new ExemptCertificatesDo();
        entity.setSerialNumber("setSerialNumber");
        entity.setHandleAffairsName("setHandleAffairsName");
        entity.setIdentityType(ExemptIdentityTypeEnum.IDENTITY);
        entity.setIdentityNumber("setIdentityNumber");
        entity.setBizOrgName("setBizOrgName");
        entity.setBizOrgIdentityType(ExemptIdentityTypeEnum.OTHER_ORG_IDENTITY_LICENSE);
        entity.setBizOrgIdentityNum("setBizOrgIdentityNum");
        entity.setExemptCertificatesType(ExemptCertificatesAffairsTypeEnum.NATURAL_PERSON);
        entity.setItemCode("setItemCode");
        entity.setToUserName("setToUserName");
        entity.setToUserAccount("setToUserAccount");
        entity.setToUserOrg("setToUserOrg");
        entity.setToUserOrgCode("setToUserOrgCode");
        entity.setProcessResult(VerificationAndInspectionEnum.SUCCESS);
        entity.setProcessTime(new Date());
        entity.setProcessComment("setProcessComment");
        entity.setContextCode("setContextCode");
        return exemptCertificatesDoRepository.save(entity);
    }

    @Test
    void findBySerialNumber() {
        initData();
        ExemptCertificatesDo setSerialNumber = exemptCertificatesDoRepository.findBySerialNumber("setSerialNumber");
        Assert.assertNotNull(setSerialNumber);
    }

    @Test
    void deleteAllBySerialNumber() {
        initData();
        exemptCertificatesDoRepository.deleteAllBySerialNumber("setSerialNumber");
        ExemptCertificatesDo setSerialNumber = exemptCertificatesDoRepository.findBySerialNumber("setSerialNumber");
        Assert.assertNull(setSerialNumber);

    }
}