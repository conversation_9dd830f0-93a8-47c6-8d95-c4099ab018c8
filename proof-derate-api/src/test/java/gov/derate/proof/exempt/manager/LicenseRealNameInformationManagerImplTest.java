package gov.derate.proof.exempt.manager;

import gov.derate.proof.exempt.resp.LicenseRealNameInformationManagerResponse;
import gov.derate.proof.exempt.resp.UserSupportInfoResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class LicenseRealNameInformationManagerImplTest  {


    @Autowired
    private LicenseRealNameInformationManager licenseRealNameInformationManager;

    /**
     * 依赖环境的单元测试，测试H5支撑平台
     * @throws Exception Exception
     */
    @Test
    public void testUserSupportInfo() throws Exception {
        // Setup
        // Run the test
        final LicenseRealNameInformationManagerResponse<UserSupportInfoResponse> result = licenseRealNameInformationManager.userSupportInfo("**********");
        // Verify the results
        Assert.assertNotNull(result);
        Assert.assertTrue(result.getMeta().getCode().equals("200"));
    }
}
