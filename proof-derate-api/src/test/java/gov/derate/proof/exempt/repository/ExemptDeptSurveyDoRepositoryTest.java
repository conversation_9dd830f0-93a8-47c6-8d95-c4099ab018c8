package gov.derate.proof.exempt.repository;

import com.google.common.collect.Lists;
import gov.derate.proof.base.BaseTestRepository;
import org.apache.commons.collections4.CollectionUtils;
import gov.derate.proof.exempt.entity.ExemptDeptSurveyDo;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <p>
 * ExemptDeptSurveyDoRepositoryTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/28；
 */
class ExemptDeptSurveyDoRepositoryTest extends BaseTestRepository {
    @Autowired
    private  ExemptDeptSurveyDoRepository exemptDeptSurveyDoRepository;
    /**
     * 初始化数据
     *
     * @return 保存后数据
     */
    public ExemptDeptSurveyDo initData() {
        ExemptDeptSurveyDo entity = new ExemptDeptSurveyDo();
        entity.setDeptCancelDescription("setDeptCancelDescription");
        entity.setDeptName("setDeptName");
        entity.setDeptCode("setDeptCode");
        entity.setProofListId("setProofListId");
        entity.setVerificationAndInspection(true);
        entity.setItemCode("setItemCode");
        entity.setSerialNumber("setSerialNumber");
        return exemptDeptSurveyDoRepository.save(entity);
    }

    @Test
    void findAllBySerialNumberIn() {
        initData();
        List<ExemptDeptSurveyDo> setSerialNumber = exemptDeptSurveyDoRepository.findAllBySerialNumberIn(Lists.newArrayList("setSerialNumber"));
        Assert.assertNotNull(setSerialNumber);
        Assert.assertFalse(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void deleteAllBySerialNumber() {
        initData();
        exemptDeptSurveyDoRepository.deleteAllBySerialNumber("setSerialNumber");
        List<ExemptDeptSurveyDo> setSerialNumber = exemptDeptSurveyDoRepository.findAllBySerialNumberIn(Lists.newArrayList("setSerialNumber"));
        Assert.assertTrue(CollectionUtils.isEmpty(setSerialNumber));
    }
}