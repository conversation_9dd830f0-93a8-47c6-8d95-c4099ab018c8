package gov.derate.proof.exempt.repository;

import com.google.common.collect.Lists;
import gov.derate.proof.base.BaseTestRepository;
import org.apache.commons.collections4.CollectionUtils;
import gov.derate.proof.exempt.entity.ExemptInvestigationDo;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <p>
 * ExemptInvestigationDoRepositoryTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/28；
 */
class ExemptInvestigationDoRepositoryTest extends BaseTestRepository {
    @Autowired
    private ExemptInvestigationDoRepository exemptInvestigationDoRepository;

    /**
     * 初始化数据
     *
     * @return 保存后数据
     */
    public ExemptInvestigationDo initData() {
        ExemptInvestigationDo entity = new ExemptInvestigationDo();
        entity.setAssistSerialNumber("setAssistSerialNumber");
        entity.setProofListId("setProofListId");
        entity.setVerificationAndInspection(true);
        entity.setItemCode("setItemCode");
        entity.setSerialNumber("setSerialNumber");
        return exemptInvestigationDoRepository.save(entity);
    }

    @Test
    void findAllBySerialNumberIn() {
        ExemptInvestigationDo exemptInvestigationDo = initData();
        List<ExemptInvestigationDo> setSerialNumber = exemptInvestigationDoRepository.findAllBySerialNumberIn(Lists.newArrayList("setSerialNumber"));
        Assert.assertFalse(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void deleteAllBySerialNumber() {
        ExemptInvestigationDo exemptInvestigationDo = initData();
        exemptInvestigationDoRepository.deleteAllBySerialNumber("setSerialNumber");
        List<ExemptInvestigationDo> setSerialNumber = exemptInvestigationDoRepository.findAllBySerialNumberIn(Lists.newArrayList("setSerialNumber"));
        Assert.assertTrue(CollectionUtils.isEmpty(setSerialNumber));
    }
}