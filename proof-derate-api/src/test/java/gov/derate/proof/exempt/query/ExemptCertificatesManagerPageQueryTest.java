package gov.derate.proof.exempt.query;

import gov.derate.proof.common.enums.VerificationAndInspectionEnum;
import gov.derate.proof.common.query.OrganizationQuery;
import gov.derate.proof.exempt.entity.ExemptCertificatesDo;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.jpa.domain.Specification;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;

/**
 * <p>
 * query对象单元测试
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/28；
 */
@RunWith(MockitoJUnitRunner.class)
public class ExemptCertificatesManagerPageQueryTest {

    /*@Mock
    private OrganizationQuery mockOrganizationQuery;*/

    private ExemptCertificatesManagerPageQuery exemptCertificatesManagerPageQueryUnderTest;

    @Before
    public void setUp() {
        exemptCertificatesManagerPageQueryUnderTest = new ExemptCertificatesManagerPageQuery("serialNumber", "handleAffairsNameOrBizOrgName", "identityNumberOrOrgCode", Arrays.asList(VerificationAndInspectionEnum.SUCCESS), any());
    }

    /**
     * 测试拼接条件
     */
    @Test
    public void testToSpec() {
        // Setup
        // Run the test
        final Specification<ExemptCertificatesDo> result = exemptCertificatesManagerPageQueryUnderTest.toSpec();
        // Verify the results
        Assert.assertNotNull(result);
    }

}
