package gov.derate.proof.exempt.repository;

import com.google.common.collect.Lists;
import gov.derate.proof.base.BaseTestRepository;
import org.apache.commons.collections4.CollectionUtils;
import gov.derate.proof.exempt.entity.ExemptClerkCommitmentDo;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <p>
 * ExemptClerkCommitmentDoRepositoryTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/27
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/27；
 */
class ExemptClerkCommitmentDoRepositoryTest extends BaseTestRepository {
    @Autowired
    private ExemptClerkCommitmentDoRepository exemptClerkCommitmentDoRepository;
    /**
     * 初始化数据
     * @return 保存后数据
     */
    public ExemptClerkCommitmentDo initData() {
        ExemptClerkCommitmentDo entity = new ExemptClerkCommitmentDo();
        entity.setCommitAttachmentId("setCommitAttachmentId");
        entity.setFileData(new byte[0]);
        entity.setCommitAttachmentName("setCommitAttachmentName");
        entity.setProofListId("setProofListId");
        entity.setVerificationAndInspection(true);
        entity.setItemCode("setItemCode");
        entity.setSerialNumber("setSerialNumber");
        return exemptClerkCommitmentDoRepository.save(entity);
    }
    @Test
    void findAllBySerialNumberIn() {
        initData();
        List<ExemptClerkCommitmentDo> setSerialNumber = exemptClerkCommitmentDoRepository.findAllBySerialNumberIn(Lists.newArrayList("setSerialNumber"));
        Assert.assertFalse(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void deleteAllBySerialNumber() {
        initData();
        exemptClerkCommitmentDoRepository.deleteAllBySerialNumber("setSerialNumber");
        List<ExemptClerkCommitmentDo> setSerialNumber = exemptClerkCommitmentDoRepository.findAllBySerialNumberIn(Lists.newArrayList("setSerialNumber"));
        Assert.assertTrue(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void findByIdAndCommitAttachmentId() {
        ExemptClerkCommitmentDo exemptClerkCommitmentDo = initData();
        ExemptClerkCommitmentDo setCommitAttachmentId = exemptClerkCommitmentDoRepository.findByIdAndCommitAttachmentId(exemptClerkCommitmentDo.getId(), "setCommitAttachmentId");
        Assert.assertEquals(exemptClerkCommitmentDo,setCommitAttachmentId);

    }

}