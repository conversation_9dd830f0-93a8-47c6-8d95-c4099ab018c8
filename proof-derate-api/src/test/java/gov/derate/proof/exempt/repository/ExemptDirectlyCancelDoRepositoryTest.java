package gov.derate.proof.exempt.repository;

import gov.derate.proof.base.BaseTestRepository;
import org.apache.commons.collections4.CollectionUtils;
import gov.derate.proof.exempt.entity.ExemptDirectlyCancelDo;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * ExemptDirectlyCancelDoRepositoryTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/28；
 */
class ExemptDirectlyCancelDoRepositoryTest extends BaseTestRepository {
    @Autowired
    private ExemptDirectlyCancelDoRepository exemptDirectlyCancelDoRepository;

    /**
     * 初始化数据
     *
     * @return 保存后数据
     */
    public ExemptDirectlyCancelDo initData() {
        ExemptDirectlyCancelDo entity = new ExemptDirectlyCancelDo();
        entity.setAttachmentId("setAttachmentId");
        entity.setFileData(new byte[0]);
        entity.setAttachmentName("setAttachmentName");
        entity.setProofListId("setProofListId");
        entity.setVerificationAndInspection(true);
        entity.setItemCode("setItemCode");
        entity.setSerialNumber("setSerialNumber");
        return exemptDirectlyCancelDoRepository.save(entity);
    }

    @Test
    void findAllBySerialNumberIn() {
        initData();
        List<ExemptDirectlyCancelDo> setSerialNumber = exemptDirectlyCancelDoRepository.findAllBySerialNumberIn(Collections.singleton("setSerialNumber"));
        Assert.assertFalse(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void deleteAllBySerialNumber() {
        initData();
        exemptDirectlyCancelDoRepository.deleteAllBySerialNumber("setSerialNumber");
        List<ExemptDirectlyCancelDo> setSerialNumber = exemptDirectlyCancelDoRepository.findAllBySerialNumberIn(Collections.singleton("setSerialNumber"));
        Assert.assertTrue(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void findByIdAndAttachmentId() {
        ExemptDirectlyCancelDo exemptDirectlyCancelDo = initData();
        ExemptDirectlyCancelDo setAttachmentId = exemptDirectlyCancelDoRepository.findByIdAndAttachmentId(exemptDirectlyCancelDo.getId(), "setAttachmentId");
        Assert.assertEquals(exemptDirectlyCancelDo, setAttachmentId);
    }
}