package gov.derate.proof.exempt.repository;

import com.google.common.collect.Lists;
import gov.derate.proof.base.BaseTestRepository;
import org.apache.commons.collections4.CollectionUtils;
import gov.derate.proof.exempt.entity.ExemptLicenseDetailDo;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <p>
 * ExemptLicenseDetailDoRepositoryTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/28；
 */
class ExemptLicenseDetailDoRepositoryTest extends BaseTestRepository {

    @Autowired
    private ExemptLicenseDetailDoRepository exemptLicenseDetailDoRepository;

    /**
     * 初始化数据
     *
     * @return 保存后数据
     */
    public ExemptLicenseDetailDo initData() {
        ExemptLicenseDetailDo entity = new ExemptLicenseDetailDo();
        entity.setCatalogCode("setCatalogCode");
        entity.setLicenseName("setLicenseName");
        entity.setLicenseCode("setLicenseCode");
        entity.setArchivingId("setArchivingId");
        entity.setArchivingFileData("setArchivingFileData");
        entity.setArchivingName("setArchivingName");
        entity.setExemptLicenseId("setExemptLicenseId");
        entity.setProofListId("setProofListId");
        entity.setItemCode("setItemCode");
        entity.setSerialNumber("setSerialNumber");
        return exemptLicenseDetailDoRepository.save(entity);
    }

    @Test
    void findAllBySerialNumberIn() {
        ExemptLicenseDetailDo exemptLicenseDetailDo = initData();
        List<ExemptLicenseDetailDo> setSerialNumber = exemptLicenseDetailDoRepository.findAllBySerialNumberIn(Lists.newArrayList("setSerialNumber"));
        Assert.assertFalse(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void deleteAllBySerialNumber() {
        ExemptLicenseDetailDo exemptLicenseDetailDo = initData();
        exemptLicenseDetailDoRepository.deleteAllBySerialNumber("setSerialNumber");
        List<ExemptLicenseDetailDo> setSerialNumber = exemptLicenseDetailDoRepository.findAllBySerialNumberIn(Lists.newArrayList("setSerialNumber"));
        Assert.assertTrue(CollectionUtils.isEmpty(setSerialNumber));
    }

    @Test
    void findByLicenseCodeAndExemptLicenseId() {

        ExemptLicenseDetailDo exemptLicenseDetailDo = initData();
        exemptLicenseDetailDoRepository.deleteAllBySerialNumber("setSerialNumber");
        ExemptLicenseDetailDo setSerialNumber = exemptLicenseDetailDoRepository.findByLicenseCodeAndExemptLicenseId("setLicenseCode", "setExemptLicenseId");
        Assert.assertEquals(exemptLicenseDetailDo, setSerialNumber);

    }

    @Test
    void findByArchivingIdAndExemptLicenseId() {
        ExemptLicenseDetailDo exemptLicenseDetailDo = initData();
        exemptLicenseDetailDoRepository.deleteAllBySerialNumber("setSerialNumber");
        ExemptLicenseDetailDo setSerialNumber = exemptLicenseDetailDoRepository.findByArchivingIdAndExemptLicenseId("setArchivingId", "setExemptLicenseId");
        Assert.assertEquals(exemptLicenseDetailDo, setSerialNumber);
    }

    @Test
    void findAllByExemptLicenseId() {
        ExemptLicenseDetailDo exemptLicenseDetailDo = initData();
        exemptLicenseDetailDoRepository.deleteAllBySerialNumber("setSerialNumber");
        List<ExemptLicenseDetailDo> setSerialNumber = exemptLicenseDetailDoRepository.findAllByExemptLicenseId("setExemptLicenseId");
        Assert.assertFalse(CollectionUtils.isEmpty(setSerialNumber));
    }
}