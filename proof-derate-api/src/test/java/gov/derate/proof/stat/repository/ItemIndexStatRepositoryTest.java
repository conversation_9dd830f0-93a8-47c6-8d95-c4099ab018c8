package gov.derate.proof.stat.repository;

import com.google.common.collect.Lists;
import gov.derate.proof.common.AbstractDaoTestTemplate;
import gov.derate.proof.stat.entity.ItemIndexStatDo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <p>
 * ItemIndexStatRepositoryTest
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/5/16
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2023/5/16；
 */
public class ItemIndexStatRepositoryTest extends AbstractDaoTestTemplate {
    @Autowired
    private ItemIndexStatRepository itemIndexStatRepository;

    @Test
//    @Sql({"deleteItemIndexStat.sql"})
    void countItemTotalNumTest() throws Exception {
        templateMethodJsonCompare(()->null,(preSetData)->{
            itemIndexStatRepository.deleteAll();
            ItemIndexStatDo entity = createData();
            return entity.getItemCount();
        },()->  itemIndexStatRepository.countItemTotalNum());
    }

    @Test
    void countMaterialTotalNumTest() throws Exception {
        templateMethodJsonCompare(()->null,(preSetData)->{
            itemIndexStatRepository.deleteAll();
            ItemIndexStatDo entity = createData();
            return entity.getMaterialCount();
        },()->  itemIndexStatRepository.countMaterialTotalNum());
    }

    @Test
    void countCleanItemTest() throws Exception {
        templateMethodJsonCompare(()->null,(preSetData)->{
            itemIndexStatRepository.deleteAll();
            ItemIndexStatDo entity = createData();
            return entity.getItemCleanCount();
        },()->  itemIndexStatRepository.countCleanItem());
    }

    @Test
    void countCleanMaterialTest() throws Exception {
        templateMethodJsonCompare(()->null,(preSetData)->{
            itemIndexStatRepository.deleteAll();
            ItemIndexStatDo entity = createData();
            return entity.getItemCleanMaterialCount();
        },()->  itemIndexStatRepository.countCleanMaterial());
    }

    @Test
    void countNotCompleteCleanItemTest() throws Exception {
        templateMethodJsonCompare(()->null,(preSetData)->{
            itemIndexStatRepository.deleteAll();
            ItemIndexStatDo entity = createData();
            return entity.getItemWaitForCleanCount()+entity.getItemCardingUnconfirmedCount()+entity.getItemCardingConfirmedCount();
        },()->  itemIndexStatRepository.countNotCompleteCleanItem());
    }

    @Test
    void countNotCompleteCleanMaterialTest() throws Exception {
        templateMethodJsonCompare(()->null,(preSetData)->{
            itemIndexStatRepository.deleteAll();
            ItemIndexStatDo entity = createData();
            return entity.getItemWaitForCleanMaterialCount()+entity.getMaterialCardingUnconfirmedCount()+entity.getMaterialCardingConfirmedCount();
        },()->  itemIndexStatRepository.countNotCompleteCleanMaterial());
    }

    @Override
    public ItemIndexStatDo createData() {
        ItemIndexStatDo entity = buildItemIndexStatDo();
        itemIndexStatRepository.save(entity);
        return entity;
    }

    private ItemIndexStatDo buildItemIndexStatDo() {
        ItemIndexStatDo entity = new ItemIndexStatDo();
        entity.setItemCount(1L);
        entity.setItemCleanCount(2L);
        entity.setItemCleanMaterialCount(3L);
        entity.setItemWaitForCleanCount(4L);
        entity.setItemWaitForCleanMaterialCount(5L);
        entity.setOrgCode("1234567");
        entity.setDivisionCode("440000");
        entity.setItemCardingUnconfirmedCount(6L);
        entity.setMaterialCardingUnconfirmedCount(7L);
        entity.setItemCardingConfirmedCount(8L);
        entity.setMaterialCardingConfirmedCount(9L);
        entity.setMaterialCount(10L);
        return entity;
    }

    @Override
    public List<Object> createListData() {
        ItemIndexStatDo entity = buildItemIndexStatDo();
        return Lists.newArrayList(entity);
    }
}