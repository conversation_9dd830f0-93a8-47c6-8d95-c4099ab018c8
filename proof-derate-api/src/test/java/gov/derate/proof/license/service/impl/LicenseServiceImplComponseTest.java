package gov.derate.proof.license.service.impl;

import gov.derate.proof.base.BaseTestCompomse;
import org.apache.commons.collections4.CollectionUtils;
import gov.derate.proof.license.service.LicenseService;
import gov.derate.proof.license.vo.LicenseItemSelectModel;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Objects;

public class LicenseServiceImplComponseTest extends BaseTestCompomse {

    @Autowired
    private LicenseService licenseService;

    @Test
    public void testQueryLicenseItemByName() throws Exception {
        // Setup
        // Run the test
        final List<LicenseItemSelectModel> result = licenseService.queryLicenseItemByName("证");

        // Verify the results
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }


    @Test
    public void testQueryPageByCatalogName() throws Exception {
        // Setup
        // Run the test
        final Page<LicenseItemSelectModel> result = licenseService.queryPageByCatalogName(1, 10, "证");

        // Verify the results
        // Verify the results
        Assert.assertTrue(!Objects.nonNull(result));
    }

}
