package gov.derate.proof.common;

import gov.license.common.tools.jackson.JacksonUtil;
import org.junit.Assert;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
/**
 * <p>
 * 抽象DAO测试模板类。
 *
 * 可以考虑链式调用，但需要使用工具类，每一次都要new，否则无法解决链式调用成员对象设值问题。并且多个测试用用例公用一个。此时无法采取链式调用。
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/5/15
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2023/5/15；
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Rollback(true)
@Transactional(rollbackFor = Exception.class)
public abstract class AbstractDaoTestTemplate {


    /**
     * dao模板测试
     * 最终结果，通过json比对。
     *
     * @param preSetSaveMethod   预设值准备数据的存库动作方法。由调用者实现。
     * @param mockDataMethod 由preSetMethod对象返回的数据，交由调用者来进行mock数据的实现。
     * @param actualDataMethod  实际执行的方法，交由调用者来进行mock数据的实现。
     * @throws Exception 报错抛出任何异常
     */
    public <T> void templateMethodJsonCompare(Supplier<T> preSetSaveMethod, Function<T, Object> mockDataMethod, Supplier<Object> actualDataMethod) throws Exception {
        T preSetData = preSetSaveMethod.get();
        Object expectData = mockDataMethod.apply(preSetData);
        Object actualData = actualDataMethod.get();
        Assert.assertEquals(JacksonUtil.toJsonStr(expectData), JacksonUtil.toJsonStr(actualData));
    }
    /**
     * 构造单个对象
     *
     * @return 返回指定对象类型的实例
     */
    protected abstract Object createData();

    /**
     * 构造指定类型的List对象
     *
     * @param <T> 指定对象类型
     * @return 返回指定对象类型的实例。
     */
    protected abstract List<Object> createListData();

    /**
     * 测试新增账号和权限的关联关系
     * @throws Exception
     */
   /* @Test
    @Sql({"clean_account_priority_relationship.sql"})
    public void testSave() throws Exception {
        Long accountId = 1L;
        Long priorityId = 1L;
        AccountPriorityRelationshipDO accountPriorityRelationshipDO =
                createAccountPriorityRelationshipDO(accountId, priorityId);
        assertNotNull(accountPriorityRelationshipDO.getId());
        assertThat(accountPriorityRelationshipDO.getId(), greaterThan(0L));
    }*/

    /**
     * 测试根据权限id查询记录数
     * @throws Exception
     */
   /* @Test
    @Sql({"clean_account_priority_relationship.sql"})
    public void testCountByPriorityId() throws Exception {
        Long priorityId = 1L;

        Long accountId1 = 1L;
        createAccountPriorityRelationshipDO(accountId1, priorityId);

        Long accountId2 = 2L;
        createAccountPriorityRelationshipDO(accountId2, priorityId);

        Long resultCount = accountPriorityRelationshipDAO.countByPriorityId(priorityId);

        assertEquals(2L, resultCount.longValue());
    }*/

    /**
     * 测试根据账号id查询账号和权限的关联关系
     * @throws Exception
     */
    /*@Test
    @Sql({"clean_account_priority_relationship.sql"})
    public void testListByAccountId() throws Exception {
        Long accountId = 1L;
        int count = 20;
        Map<Long, AccountPriorityRelationshipDO> relationMap =
                createRelations(accountId, count);

        List<AccountPriorityRelationshipDO> resultRelations =
                accountPriorityRelationshipDAO.listByAccountId(accountId);

        compareRelations(relationMap, resultRelations);
    }*/
    /*@Test
    @Sql({"clean_account_priority_relationship.sql"})
    public void testRemoveByAccountId() throws Exception {
        Long accountId = 1L;
        int count = 20;
        createRelations(accountId, count);

        accountPriorityRelationshipDAO.removeByAccountId(accountId);

        List<AccountPriorityRelationshipDO> resultRelations =
                accountPriorityRelationshipDAO.listByAccountId(accountId);

        assertEquals(0, resultRelations.size());
    }*/
    /**
     * 比较两个关联关系集合
     * @param relationMap
     * @param resultRelations
     * @throws Exception
     */
    /*private void compareRelations(Map<Long, AccountPriorityRelationshipDO> relationMap,
                                  List<AccountPriorityRelationshipDO> resultRelations) throws Exception {
        assertThat(resultRelations.size(), greaterThanOrEqualTo(relationMap.size()));

        for(AccountPriorityRelationshipDO relation : resultRelations) {
            AccountPriorityRelationshipDO targetRelation = relationMap.get(relation.getId());
            if(targetRelation == null) {
                continue;
            }
            assertEquals(targetRelation, relation);
        }
    }*/

    /**
     * 创建账号和权限关联关系的集合
     * @param accountId
     * @return
     * @throws Exception
     */
   /* private Map<Long, AccountPriorityRelationshipDO> createRelations(
            Long accountId, int count) throws Exception {
        Map<Long, AccountPriorityRelationshipDO> relationMap =
                new HashMap<Long, AccountPriorityRelationshipDO>(CollectionSize.DEFAULT);

        for(int i = 0; i < count; i++) {
            AccountPriorityRelationshipDO relation =
                    createAccountPriorityRelationshipDO(accountId, (long)i);
            relationMap.put(relation.getId(), relation);
        }

        return relationMap;
    }*/

    /**
     * 创建账号和权限关系DO对象
     * @return 账号和权限关系DO对象
     * @throws Exception
     */
   /* private AccountPriorityRelationshipDO createAccountPriorityRelationshipDO(
            Long accountId, Long priorityId) throws Exception {
        AccountPriorityRelationshipDO accountPriorityRelationshipDO =
                new AccountPriorityRelationshipDO();
        accountPriorityRelationshipDO.setAccountId(accountId);
        accountPriorityRelationshipDO.setPriorityId(priorityId);
        accountPriorityRelationshipDO.setGmtCreate(dateProvider.getCurrentTime());
        accountPriorityRelationshipDO.setGmtModified(dateProvider.getCurrentTime());

        accountPriorityRelationshipDAO.save(accountPriorityRelationshipDO);

        return accountPriorityRelationshipDO;
    }*/

}
