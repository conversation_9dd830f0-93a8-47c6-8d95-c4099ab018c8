package gov.derate.proof.common.api;

/**
 * <p>
 * ControllerTestTemplateInterface
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/5/15
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2023/5/15；
 */
public interface ControllerTestTemplateInterface {
    /**
     * 构造mock数据，返回最终期望controller比对的设值
     * @param preSetData 预处理的构造数据。
     * @return 期望值对象
     */
    <T>Object mockData(T preSetData);

    /**
     * 测试构造的数据方法，
     * @return  返回的方法
     */
    <T> T preSet();
}
