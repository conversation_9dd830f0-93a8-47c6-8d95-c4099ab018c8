package gov.derate.proof.common;

import gov.license.common.tools.jackson.JacksonUtil;
import org.junit.Assert;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.function.Function;
import java.util.function.Supplier;
/**
 * <p>
 * service测试模板类
 *
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/5/15
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2023/5/15；
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public abstract class AbstractServiceTestTemplate {


    /**
     * service模板测试
     * 最终结果，通过json比对。
     *
     * @param preSetMethod   预设值准备数据的动作方法。由调用者实现。
     * @param mockDataMethod 由preSetMethod对象返回的数据，交由调用者来进行mock数据的实现。
     * @param actualDataMethod  实际执行的方法，交由调用者来进行mock数据的实现。
     * @throws Exception 报错抛出任何异常
     */
    public <T> void templateMethodJsonCompare(Supplier<T> preSetMethod, Function<T, Object> mockDataMethod, Supplier<Object> actualDataMethod) throws Exception {
        T preSetData = preSetMethod.get();
        Object expectData = mockDataMethod.apply(preSetData);
        Object actualData = actualDataMethod.get();
        Assert.assertEquals(JacksonUtil.toJsonStr(expectData), JacksonUtil.toJsonStr(actualData));
    }

    /**
     * 权限管理模块的service组件
     */
   /* @Autowired
    private PriorityService priorityService;*/
    /**
     * 测试查询根权限
     * @throws Exception
     */
    /*@Test
    public void testListRootPriorities() throws Exception {
        Long parentId = null;
        List<PriorityDO> rootPriorityDOs = createMockPriorityDOs(parentId);
        when(priorityDAO.listRootPriorities()).thenReturn(rootPriorityDOs);
        Map<Long, PriorityDTO> rootPriorityDTOMap = convertPriorityDOs2Map(rootPriorityDOs);

        List<PriorityDTO> resultRootPriorityDTOs = priorityService.listRootPriorities();

        comparePriorityDTOs(rootPriorityDTOMap, resultRootPriorityDTOs);
    }*/
    /**
     * 将权限DO集合转换为权限DTO集合
     * @param priorityDOs 权限DO集合
     * @return 权限DTO集合
     */
  /*  private Map<Long, PriorityDTO> convertPriorityDOs2Map(List<PriorityDO> priorityDOs) {
        Map<Long, PriorityDTO> priorityDTOMap = new HashMap<Long, PriorityDTO>(priorityDOs.size());
        for(PriorityDO priorityDO : priorityDOs) {
            priorityDTOMap.put(priorityDO.getId(), convertPriorityDO2DTO(priorityDO));
        }
        return priorityDTOMap;
    }*/
    /**
     * 比较两个权限DTO集合
     * @param priorityDTOMap 权限DTO集合1
     * @param priorityDTOs 权限DTO集合2
     */
   /* private void comparePriorityDTOs(Map<Long, PriorityDTO> priorityDTOMap,
                                     List<PriorityDTO> priorityDTOs) {
        assertEquals(priorityDTOMap.size(), priorityDTOs.size());

        for(PriorityDTO resultPriorityDTO : priorityDTOs) {
            PriorityDTO rootPrioiryDTO = priorityDTOMap.get(resultPriorityDTO.getId());
            assertEquals(rootPrioiryDTO, resultPriorityDTO);
        }
    }*/
    /**
     * 将权限DO对象转换为权限DTO对象
     * @param priorityDO 权限DO对象
     * @return 权限DTO对象
     */
    /*private PriorityDTO convertPriorityDO2DTO(PriorityDO priorityDO) {
        PriorityDTO priorityDTO = new PriorityDTO();
        priorityDTO.setCode(priorityDO.getCode());
        priorityDTO.setGmtCreate(priorityDO.getGmtCreate());
        priorityDTO.setGmtModified(priorityDO.getGmtModified());
        priorityDTO.setId(priorityDO.getId());
        priorityDTO.setParentId(priorityDO.getParentId());
        priorityDTO.setPriorityComment(priorityDO.getPriorityComment());
        priorityDTO.setPriorityType(priorityDO.getPriorityType());
        priorityDTO.setUrl(priorityDO.getUrl());
        return priorityDTO;
    }*/


}
