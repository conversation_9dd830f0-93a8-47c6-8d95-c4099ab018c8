package gov.derate.proof.common;

import com.fasterxml.jackson.core.type.TypeReference;
import gov.derate.proof.common.api.ControllerTestTemplateInterface;
import gov.license.common.api.resp.ResponseResult;
import gov.license.common.tools.jackson.JacksonUtil;
import org.junit.Assert;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.ResultActions;

import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;

/**
 * <p>
 * controller测试模板类。
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/5/15
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2023/5/15；
 */
@RunWith(SpringRunner.class)
public abstract class AbstractControllerTestTemplate implements ControllerTestTemplateInterface {
    /**
     * mvc测试模拟类
     */
    @Autowired
    protected MockMvc mvc;

    /**
     * 测试controller的模板
     * <p>
     * 继承类需要复写preSet方法和mockData方法，来进行mock数据和期望数据的设值。
     * 该方法，最后会比对接口请求的json是否和期望的json字符串对应
     *
     * @param requestBuilder test的url请求构造
     * @param resultActions  接收到构造的mvcBuilder后的请求头、请求体等设值
     * @throws Exception 报错抛出任何异常
     */
    public void templateMethodJson(RequestBuilder requestBuilder, Consumer<ResultActions> resultActions) throws Exception {
        Object preSetData = preSet();
        Object expectData = mockData(preSetData);
        ResultActions perform = mvcUrlAndOtherSetting(requestBuilder, resultActions);
        perform.andExpect(content().json(JacksonUtil.toJsonStr(expectData)));
    }

    /**
     * 测试ResponseResult测试情况
     *
     * @param requestBuilder test的url请求构造
     * @param resultActions  接收到构造的mvcBuilder后的请求头、请求体等设值
     * @throws Exception 报错抛出任何异常
     */
    public void templateMethodResponseResult(RequestBuilder requestBuilder, Consumer<ResultActions> resultActions) throws Exception {
        Object preSetData = preSet();
        Object expectData = mockData(preSetData);
        ResultActions perform = mvcUrlAndOtherSetting(requestBuilder, resultActions);
        compareByResponseResult(expectData, perform);
    }

    /**
     * 测试ResponseResult测试情况
     *
     * @param requestBuilder test的url请求构造
     * @param resultActions  接收到构造的mvcBuilder后的请求头、请求体等设值
     * @param preSetMethod   预设值准备数据的动作方法。由调用者实现。
     * @param mockDataMethod 由preSetMethod对象返回的数据，交由调用者来进行mock数据的实现。
     * @throws Exception 报错抛出任何异常
     */
    public<T> void templateMethodResponseResult(RequestBuilder requestBuilder, Consumer<ResultActions> resultActions, Supplier<T> preSetMethod, Function<T, Object> mockDataMethod) throws Exception {
        T preSetData = preSetMethod.get();
        Object expectData = mockDataMethod.apply(preSetData);
        ResultActions perform = mvcUrlAndOtherSetting(requestBuilder, resultActions);
        compareByResponseResult(expectData, perform);
    }

    private ResultActions mvcUrlAndOtherSetting(RequestBuilder requestBuilder, Consumer<ResultActions> resultActions) throws Exception {
        ResultActions perform = mvc.perform(requestBuilder);
        resultActions.accept(perform);
        return perform;
    }

    /**
     * 测试ResponseResult测试情况
     *
     * @param requestBuilder test的url请求构造
     * @param resultActions  接收到构造的mvcBuilder后的请求头、请求体等设值
     * @param expectData     期望data
     * @throws Exception 报错抛出任何异常
     */
    public <T> void templateMethodResponseResult(RequestBuilder requestBuilder, Consumer<ResultActions> resultActions, T expectData) throws Exception {
        ResultActions perform = mvcUrlAndOtherSetting(requestBuilder, resultActions);
        compareByResponseResult(expectData, perform);
    }

    private<T> void compareByResponseResult(Object expectData, ResultActions perform) throws java.io.IOException {
        MockHttpServletResponse response = perform.andReturn().getResponse();
        ResponseResult responseHelper = JacksonUtil.toBean(response.getContentAsString(), new TypeReference<ResponseResult>() {
        });
        Object data = responseHelper.getData();
        Assert.assertEquals(JacksonUtil.toJsonStr(expectData), JacksonUtil.toJsonStr(data));
    }

}
