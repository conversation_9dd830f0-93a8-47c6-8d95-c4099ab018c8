package gov.derate.proof.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.util.StreamUtils;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.file.Files;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <p>
 * JUnit5 Test Class.java.java
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2024/2/7
 * </p>
 *
 * <AUTHOR>
 */
public class PdfUtilTest {

    @Test
    void word2pdf() {
        try (InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("./test.docx")) {
            byte[] bytes = PdfUtil.word2pdf(in);
            String pdfBase64 = Base64EncoderUtils.transformBase64(bytes);
            System.out.println(pdfBase64);
            Assert.assertTrue(StringUtils.isNotBlank(pdfBase64));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 图片转pdf
     */
    @Test
    void pic2pdf() {
        try (InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream("./test.jpeg")) {
            byte[] bytes = PdfUtil.pic2pdf(in);
            String pdfBase64 = Base64EncoderUtils.transformBase64(bytes);
            System.out.println(pdfBase64);
            Assert.assertTrue(StringUtils.isNotBlank(pdfBase64));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}