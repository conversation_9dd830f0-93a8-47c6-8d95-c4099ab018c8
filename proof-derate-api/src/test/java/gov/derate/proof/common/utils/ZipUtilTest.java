package gov.derate.proof.common.utils;


import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.util.FileSystemUtils;
import org.springframework.util.StreamUtils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 测试zip工具类。
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2024/2/18
 * </p>
 *
 * <AUTHOR>
 */
public class ZipUtilTest {

    public static final String SRC_MAIN_JAVA = "./src/main/java";
    public static final String POM_XML = "./pom.xml";
    public static final String POM_XML_FILE_NAME = "pom.xml";

    @Test
    public void zip2FileTest() throws IOException {
        File file = ZipUtil.zip2File(SRC_MAIN_JAVA, "./", "local2.zip");
        Assert.assertTrue(file.exists());
    }

    @Test
    public void zip2ByteTestTest() throws IOException {
        byte[] bytes = ZipUtil.zip2Byte(SRC_MAIN_JAVA);
        Assert.assertTrue(bytes.length > 0);
    }

    @Test
    public void zip2Base64Test() throws IOException {
        String base64 = ZipUtil.zip2Base64(SRC_MAIN_JAVA);
        Assert.assertTrue(StringUtils.isNotBlank(base64));
    }

    @Test
    public void zip2ByteTest() throws IOException {
        byte[] byteArray = loadPathFile(POM_XML);
        byte[] bytes = ZipUtil.zip2Byte(byteArray, POM_XML_FILE_NAME);
        Assert.assertTrue(bytes.length > 0);
    }

    private static byte[] loadClassPathFile(String fileNamePath) throws IOException {
        InputStream in = Thread.currentThread().getContextClassLoader().getResourceAsStream(fileNamePath);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        StreamUtils.copy(in, out);
        return out.toByteArray();
    }
    private static byte[] loadPathFile(String fileNamePath) throws IOException {
        InputStream in = Files.newInputStream(new File(fileNamePath).toPath());
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        StreamUtils.copy(in, out);
        return out.toByteArray();
    }

    @Test
    public void zip2ByteTest2() throws IOException {
        byte[] byteArray = loadPathFile(POM_XML);
        ZipUtil.ZipByteFileAndFileName zipByteFileAndFileName = ZipUtil.buildZipFile(byteArray, POM_XML_FILE_NAME);
        List<ZipUtil.ZipByteFileAndFileName> dataList = new ArrayList<>();
        dataList.add(zipByteFileAndFileName);
        byte[] bytes = ZipUtil.zip2Byte(dataList);
        Assert.assertTrue(bytes.length > 0);
    }

    @Test
    public void zip2Base64Test2() throws IOException {
        byte[] byteArray = loadPathFile(POM_XML);
        String base64 = ZipUtil.zip2Base64(byteArray, POM_XML_FILE_NAME);
        Assert.assertTrue(StringUtils.isNotBlank(base64));
    }

    @Test
    public void zip2Base64Test3() throws IOException {
        byte[] file1 = loadPathFile(POM_XML);
        ZipUtil.ZipByteFileAndFileName zipData1 = ZipUtil.buildZipFile(file1, "test.xml");
        ZipUtil.ZipByteFileAndFileName zipData2 = ZipUtil.buildZipFile(file1, "test2.xml");
        List<ZipUtil.ZipByteFileAndFileName> zipDataList = new ArrayList<>();
        zipDataList.add(zipData1);
        zipDataList.add(zipData2);
        String base64 = ZipUtil.zip2Base64(zipDataList);
        Assert.assertTrue(StringUtils.isNotBlank(base64));
    }

    @Test
    public void unzipByFilePathTest() throws IOException {
        // 经过测试用例，先压缩数据目录
        try {
            zip2FileTest();
            File file = ZipUtil.unzipByFilePath("./local2.zip", "./testLocal2");
            Assert.assertTrue(Objects.requireNonNull(file.listFiles()).length >= 1);
        } finally {
            Files.deleteIfExists(Paths.get("./local2.zip"));
            FileSystemUtils.deleteRecursively(new File("./testLocal2"));
        }
    }

    @Test
    public void unzipByByteTest() throws IOException {
        // 经过测试用例，先压缩数据目录
        try {
            zip2FileTest();
            File file = new File("./local2.zip");
            try (FileInputStream in = new FileInputStream(file);
                 ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                StreamUtils.copy(in, out);
                File file1 = ZipUtil.unzipByByte(out.toByteArray(), "./testLocal2");
                Assert.assertTrue(Objects.requireNonNull(file1.listFiles()).length >= 1);
            }
        } finally {
            Files.deleteIfExists(Paths.get("./local2.zip"));
            FileSystemUtils.deleteRecursively(new File("./testLocal2"));
        }
    }

    @Test
    public void unzipByByteTest2() throws IOException {
        // 经过测试用例，先压缩数据目录
        try {
            zip2FileTest();
            File file = new File("./local2.zip");
            try (FileInputStream in = new FileInputStream(file);
                 ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                StreamUtils.copy(in, out);
                List<ZipUtil.ZipByteFileAndFileName> zipByteFileAndFileNames = ZipUtil.unzipByByte(out.toByteArray());
                Assert.assertTrue(zipByteFileAndFileNames.size() >= 1);
            }
        } finally {
            Files.deleteIfExists(Paths.get("./local2.zip"));
            FileSystemUtils.deleteRecursively(new File("./testLocal2"));

        }
    }

    @Test
    public void unzipTest() throws IOException {
        // 经过测试用例，先压缩数据目录
        try {
            zip2FileTest();
            File file = new File("./local2.zip");
            try (FileInputStream in = new FileInputStream(file);) {
                File unzip = ZipUtil.unzip(in, "./testLocal2");
                Assert.assertTrue(Objects.requireNonNull(unzip.listFiles()).length >= 1);
            }
        } finally {
            Files.deleteIfExists(Paths.get("./local2.zip"));
            FileSystemUtils.deleteRecursively(new File("./testLocal2"));

        }
    }

    @Test
    public void unzipByInputStreamTest() throws IOException {
        try {
            zip2FileTest();
            File file = new File("./local2.zip");
            try (FileInputStream in = new FileInputStream(file);) {
                List<ZipUtil.ZipByteFileAndFileName> zipByteFileAndFileNames = ZipUtil.unzipByInputStream(in);
                Assert.assertTrue(zipByteFileAndFileNames.size() >= 1);
            }
        } finally {
            Files.deleteIfExists(Paths.get("./local2.zip"));
            FileSystemUtils.deleteRecursively(new File("./testLocal2"));
        }
    }


}