package gov.derate.proof.preliminary.drafts.service.impl;

import gov.derate.proof.preliminary.drafts.entity.PreliminaryDraftsDo;
import gov.derate.proof.preliminary.drafts.repository.PreliminaryDraftsRepository;
import gov.derate.proof.preliminary.drafts.service.PreliminaryDraftsService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2021/9/13.
 */
@RunWith(SpringRunner.class)
@PowerMockIgnore("javax.management.*")
public class PreliminaryDraftsServiceImplTest {

    /**
     * 注入实例
     */
    @InjectMocks
    private PreliminaryDraftsServiceImpl preliminaryDraftsServiceImpl;

    @Mock
    private PreliminaryDraftsRepository preliminaryDraftsRepository;

    @Test
    public void getViewDataByProofListIdeTest() {
        PreliminaryDraftsService preliminaryDraftsService = Mockito.mock(PreliminaryDraftsService.class);
        PreliminaryDraftsDo preliminaryDrafts = new PreliminaryDraftsDo();
        PowerMockito.when(preliminaryDraftsService.findByProofListId(Mockito.anyString())).thenReturn(preliminaryDrafts);

        PreliminaryDraftsDo preliminaryDraftsTest = preliminaryDraftsServiceImpl.getViewDataByProofListIde("1");

        Assert.assertNull(preliminaryDraftsTest);
    }

    @Test
    public void findByProofListIdTest() {
        PowerMockito.when(preliminaryDraftsRepository.findByProofListId(Mockito.anyString())).thenReturn(null);

        PreliminaryDraftsDo preliminaryDrafts = preliminaryDraftsServiceImpl.findByProofListId("1");

        Assert.assertNull(preliminaryDrafts);
    }
}
