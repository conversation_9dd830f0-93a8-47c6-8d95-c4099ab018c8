package gov.derate.proof.preliminary.drafts.repository;

import gov.derate.proof.preliminary.drafts.entity.PreliminaryDraftsDo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2021/9/13.
 */
@RunWith(SpringRunner.class)
@WebAppConfiguration
@PowerMockIgnore("javax.management.*")
@Rollback
@Transactional(rollbackFor = Exception.class)
@SpringBootTest
public class PreliminaryDraftsRepositoryTest {
    @Autowired
    private PreliminaryDraftsRepository preliminaryDraftsRepository;

    @Test
    public void findByProofListIdTest() {
        PreliminaryDraftsDo preliminaryDrafts = preliminaryDraftsRepository.findByProofListId("1");
        Assert.assertNull(preliminaryDrafts);
    }
}
