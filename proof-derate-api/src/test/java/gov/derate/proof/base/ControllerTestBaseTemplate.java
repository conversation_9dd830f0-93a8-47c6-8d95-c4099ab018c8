package gov.derate.proof.base;

import org.junit.After;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;


@RunWith(SpringRunner.class)
@SpringBootTest
public abstract class ControllerTestBaseTemplate {

    @Autowired
    public WebApplicationContext wac;

    public MockMvc mockMvc;
    public MockHttpSession session;

    @Before
    public void beforeClass(){
        mockMvc = MockMvcBuilders.webAppContextSetup(wac).build(); //初始化MockMvc对象
        session = new MockHttpSession();
        beforeTest();
    }

    @After
    public void afterClass(){
        afterTest();
    }

    public abstract void beforeTest();
    public abstract void afterTest();

}
