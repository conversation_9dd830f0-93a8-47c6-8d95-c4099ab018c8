package gov.derate.proof.base;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * repository 基础测试继承
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2016-11-23
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2021/10/19；
 */
@RunWith(SpringRunner.class)
@Rollback(true)
@Transactional(rollbackFor = Exception.class)
@SpringBootTest
public class BaseTestRepository {
}
