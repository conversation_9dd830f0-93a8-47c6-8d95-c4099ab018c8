package gov.derate.proof.catalog.service.impl;

import gov.derate.proof.catalog.bo.ProofCatalogCreateBo;
import gov.derate.proof.catalog.entity.ProofCatalogDo;
import gov.derate.proof.catalog.repository.ProofCatalogRepository;
import gov.derate.proof.catalog.req.ProofCatalogArtificialRelationRequest;
import gov.derate.proof.catalog.req.ProofCatalogDataSharedRelationRequest;
import gov.derate.proof.catalog.req.ProofCatalogLicenseRelationRequest;
import gov.derate.proof.catalog.service.ProofCatalogArtificialRelationService;
import gov.derate.proof.catalog.service.ProofCatalogDataSharedRelationService;
import gov.derate.proof.catalog.service.ProofCatalogLicenseRelationService;
import gov.derate.proof.common.utils.UserUtils;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/13.
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.management.*")
@PrepareForTest({UserUtils.class})
public class ProofCatalogServiceImplTest {
    /**
     * 注入实例
     */
    @InjectMocks
    private ProofCatalogServiceImpl proofCatalogServiceImpl;

    @Mock
    private ProofCatalogArtificialRelationService artificialRelationService;

    @Mock
    private ProofCatalogDataSharedRelationService dataSharedRelationService;

    @Mock
    private ProofCatalogLicenseRelationService licenseRelationService;
    @Mock
    private ProofCatalogRepository proofCatalogRepository;

    @Test
    public void saveProofCatalogTest() throws IOException, IllegalAccessException, InstantiationException {

        //打桩
        PowerMockito.mockStatic(UserUtils.class);
        PowerMockito.when(UserUtils.getUserId()).thenReturn("11");
        ProofCatalogDo proofCatalogMock = Mockito.mock(ProofCatalogDo.class);
        PowerMockito.doNothing().when(proofCatalogMock).prePersist();

        PowerMockito.when(proofCatalogRepository.save(Mockito.any())).thenReturn(null);
        ProofCatalogDo proofCatalog = new ProofCatalogDo();
        PowerMockito.when(proofCatalogRepository.findByName(Mockito.anyString())).thenReturn(proofCatalog);
        PowerMockito.doNothing().when(artificialRelationService).saveAll(Mockito.anyList());
        PowerMockito.doNothing().when(dataSharedRelationService).saveAll(Mockito.anyList());
        PowerMockito.doNothing().when(licenseRelationService).saveAll(Mockito.anyList());

        //实例测试
        ProofCatalogCreateBo ProofCatalogAllRelationRequest = new ProofCatalogCreateBo();
        List<ProofCatalogArtificialRelationRequest> artificialRelationList = Lists.newArrayList();
        List<ProofCatalogDataSharedRelationRequest> dataSharedRelation = Lists.newArrayList();
        List<ProofCatalogLicenseRelationRequest> licenseRelation = Lists.newArrayList();
//        ProofCatalogAllRelationRequest.setArtificialRelationList(artificialRelationList);
//        ProofCatalogAllRelationRequest.setDataSharedRelation(dataSharedRelation);
//        ProofCatalogAllRelationRequest.setLicenseRelation(licenseRelation);
        proofCatalogServiceImpl.saveProofCatalog(ProofCatalogAllRelationRequest);

        //检验
        Mockito.verify(artificialRelationService, Mockito.times(1)).saveAll(Mockito.any());
        Mockito.verify(dataSharedRelationService, Mockito.times(1)).saveAll(Mockito.any());
        Mockito.verify(licenseRelationService, Mockito.times(1)).saveAll(Mockito.any());
    }

}
