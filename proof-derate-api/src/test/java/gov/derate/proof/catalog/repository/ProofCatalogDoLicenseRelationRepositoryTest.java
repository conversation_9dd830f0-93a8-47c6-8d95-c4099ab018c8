package gov.derate.proof.catalog.repository;

import gov.derate.proof.catalog.entity.ProofCatalogLicenseRelationDo;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 证明目录与证照关系仓库测试类。
 *
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/5/9
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2023/5/9；
 */
@RunWith(SpringRunner.class)
@Rollback(true)
@Transactional(rollbackFor = Exception.class)
@SpringBootTest
public class ProofCatalogDoLicenseRelationRepositoryTest {
    @Autowired
    private ProofCatalogLicenseRelationRepository proofCatalogLicenseRelationRepository;

    /**
     * 保存数据
     *
     * @return 保存后的数据
     */
    public ProofCatalogLicenseRelationDo saveOne() {
        ProofCatalogLicenseRelationDo entity = new ProofCatalogLicenseRelationDo();
        entity.setProofCatalogId("123");
        entity.setLicenseName("123");
        entity.setLicenseCode("123");
        entity.setDeptName("123");
        entity.setId("1");
        proofCatalogLicenseRelationRepository.save(entity);
        return entity;
    }

}
