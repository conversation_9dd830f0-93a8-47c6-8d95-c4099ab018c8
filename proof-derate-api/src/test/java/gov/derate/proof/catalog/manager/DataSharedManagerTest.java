package gov.derate.proof.catalog.manager;

import gov.derate.proof.catalog.entity.DataSubjectInfoDto;
import gov.derate.proof.catalog.req.ResourceCatalogRequest;
import gov.derate.proof.dictionary.bo.DictionaryBo;
import gov.derate.proof.dictionary.entity.DictionaryDicTypeConstant;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.domain.Page;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;

import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DataSharedManagerTest {


    @InjectMocks
    private DataSharedManager dataSharedManagerUnderTest;

    @Test
    public void testGetDataSubjectListSearch() throws Exception {
        settingTokenMock();
        // Setup
        final ResourceCatalogRequest query = new ResourceCatalogRequest();
        query.setPageEnabled("true");
        query.setPageIndex(0);
        query.setPageSize(10);
        query.setDataSubjectName("");
        query.setExTableName("");
        query.setOrgCode("");
        query.setOrgName("");
        query.setProjectName("");
        query.setTag("");

        // Configure DictionaryService.findByTypeNameAndDicType(...).
        final DictionaryBo dictionaryBo = new DictionaryBo();
        dictionaryBo.setDicName("dicName");
        dictionaryBo.setDicType(DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name());
        dictionaryBo.setTypeCode("http://192.168.10.58:7706/data-gateway-api/data_subject/list_search");
        dictionaryBo.setTypeName("themeUrl");
        dictionaryBo.setDicSort(new BigDecimal("0.00"));
//        when(mockDictionaryService.findByTypeNameAndDicType("themeUrl", DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name())).thenReturn(dictionaryBo);

        // Run the test
        final Page<DataSubjectInfoDto> result = dataSharedManagerUnderTest.getResourceCatalog(query);

        // Verify the results
        assertTrue(result.hasContent());
    }

    @Test
    public void testGetDataSubjectListSearch2() throws Exception {
        settingTokenMock();
        // Setup
        final ResourceCatalogRequest query = new ResourceCatalogRequest();
        query.setPageEnabled("true");
        query.setPageIndex(0);
        query.setPageSize(10);
        query.setDataSubjectName("惠州市离婚登记信息");
        query.setExTableName("");
        query.setOrgCode("");
        query.setOrgName("");
        query.setProjectName("");
        query.setTag("");

        // Configure DictionaryService.findByTypeNameAndDicType(...).
        final DictionaryBo dictionaryBo = new DictionaryBo();
        dictionaryBo.setDicName("dicName");
        dictionaryBo.setDicType(DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name());
        dictionaryBo.setTypeCode("http://192.168.10.58:7706/data-gateway-api/data_subject/list_search");
        dictionaryBo.setTypeName("themeUrl");
        dictionaryBo.setDicSort(new BigDecimal("0.00"));
//        when(mockDictionaryService.findByTypeNameAndDicType("themeUrl", DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name())).thenReturn(dictionaryBo);

        // Run the test
        final Page<DataSubjectInfoDto> result = dataSharedManagerUnderTest.getResourceCatalog(query);

        // Verify the results
        assertTrue(result.hasContent());
    }


    @Test
    public void testGetDataSubjectListSearch_ThrowsException() {
        // Setup
        final ResourceCatalogRequest query = new ResourceCatalogRequest();
        query.setPageEnabled("true");
        query.setPageIndex(0);
        query.setPageSize(10);
        query.setDataSubjectName("");
        query.setExTableName("");
        query.setOrgCode("");
        query.setOrgName("");
        query.setProjectName("");
        query.setTag("");

        // Configure DictionaryService.findByTypeNameAndDicType(...).
        final DictionaryBo dictionaryBo = new DictionaryBo();
        dictionaryBo.setDicName("dicName");
        dictionaryBo.setDicType(DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name());
        dictionaryBo.setTypeCode("http://192.168.10.58:7706/data-gateway-api/data_subject/list_search");
        dictionaryBo.setTypeName("themeUrl");
        dictionaryBo.setDicSort(new BigDecimal("0.00"));
//        when(mockDictionaryService.findByTypeNameAndDicType("themeUrl", DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name())).thenReturn(null);

        // Run the test
        assertThrows(Exception.class, () -> dataSharedManagerUnderTest.getResourceCatalog(query));
    }

    @Test
    public void testGetToken() throws Exception {
        // Setup
        // Configure DictionaryService.findByTypeNameAndDicType(...).
        final String result = getToken();

        // Verify the results
        assertTrue(StringUtils.isNotBlank(result));
    }

    /**
     * 获取惠州loginToken
     *
     * @return
     * @throws Exception
     */
    private String getToken() throws Exception {
        settingTokenMock();

        // Run the test
        return dataSharedManagerUnderTest.getToken();
    }

    private void settingTokenMock() {
        final DictionaryBo loginUrl = new DictionaryBo();
        loginUrl.setDicName("dicName");
        loginUrl.setDicType(DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name());
        loginUrl.setTypeCode("http://192.168.10.58:7706/data-gateway-api/security/login");
        loginUrl.setTypeName("loginUrl");
        final DictionaryBo appKey = new DictionaryBo();
        appKey.setDicName("dicName");
        appKey.setDicType(DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name());
        appKey.setTypeCode("hz_minzheng_dzzz");
        appKey.setTypeName("appKey");
        final DictionaryBo appSecret = new DictionaryBo();
        appSecret.setDicName("dicName");
        appSecret.setDicType(DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name());
        appSecret.setTypeCode("hz_minzheng_dzzz");
        appSecret.setTypeName("appSecret");
        final DictionaryBo account = new DictionaryBo();
        account.setDicName("dicName");
        account.setDicType(DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name());
        account.setTypeCode("hz_minzheng_dzzz");
        account.setTypeName("account");
        final DictionaryBo password = new DictionaryBo();
        password.setDicName("dicName");
        password.setDicType(DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name());
        password.setTypeCode("hz_minzheng_dzzz");
        password.setTypeName("password");
        loginUrl.setDicSort(new BigDecimal("0.00"));

//        when(mockDictionaryService.findByTypeNameAndDicType("loginUrl", DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name())).thenReturn(loginUrl);
//        when(mockDictionaryService.findByTypeNameAndDicType("appKey", DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name())).thenReturn(appKey);
//        when(mockDictionaryService.findByTypeNameAndDicType("appSecret", DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name())).thenReturn(appSecret);
//        when(mockDictionaryService.findByTypeNameAndDicType("account", DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name())).thenReturn(account);
//        when(mockDictionaryService.findByTypeNameAndDicType("password", DictionaryDicTypeConstant.HZ_DATA_SHARED_SYSTEM.name())).thenReturn(password);
    }

    @Test
    public void testGetToken_ThrowsException() {
        // Setup
        // Configure DictionaryService.findByTypeNameAndDicType(...).
//        when(mockDictionaryService.findByTypeNameAndDicType("typeName", "dicType")).thenReturn(null);

        // Run the test
        assertThrows(Exception.class, () -> dataSharedManagerUnderTest.getToken());
    }
}
