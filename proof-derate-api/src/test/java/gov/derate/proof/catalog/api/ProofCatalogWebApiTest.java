package gov.derate.proof.catalog.api;


import gov.derate.proof.catalog.req.ProofCatalogAllRelationRequest;
import gov.derate.proof.catalog.req.ProofCatalogRequest;
import gov.license.common.tools.jackson.JacksonUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

/**
 * <AUTHOR>
 * @date 2021/9/30.
 */

@Rollback
@Transactional(rollbackFor = Exception.class)
@WebAppConfiguration
//SpringBoot1.4版本之前用的是SpringJUnit4ClassRunner.class
@RunWith(SpringRunner.class)
@SpringBootTest
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = true)  //这里的事务关联到项目的配置文件中的事务控制器（"transactionManager"），同时指定事物自动回滚（defaultRollback= true），以此对数据库不会产生数据污染。
@PowerMockIgnore("javax.management.*")
public class ProofCatalogWebApiTest {

    protected MockMvc mockMvc;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Before
    public void setUp() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    public void listPage() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/proof_catalog/page")
                .param("page_direction", "DESC")
                .param("page_number", "1")
                .param("page_size", "10")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void listTest() throws Exception {

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/proof_catalog/list")

        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void createTest() throws Exception {
        ProofCatalogAllRelationRequest proofCatalogAllRelationVo = new ProofCatalogAllRelationRequest();
        proofCatalogAllRelationVo.setId("1");
        String strJson = JacksonUtil.toJsonStr(proofCatalogAllRelationVo);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/proof_catalog/create")
                        .accept(MediaType.parseMediaType("application/json;charset=utf-8"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(strJson)
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

    @Test
    public void editTest() throws Exception {

        ProofCatalogRequest editVo = new ProofCatalogRequest();
        editVo.setId("1");
        String strJson = JacksonUtil.toJsonStr(editVo);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/proof_catalog/edit/123")
                        .accept(MediaType.parseMediaType("application/json;charset=utf-8"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(strJson)
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);

    }

    @Test
    public void deleteTest() throws Exception {
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/proof_catalog/delete/1")

        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();
        int Status = mvcResult.getResponse().getStatus();
        Assert.assertEquals(200, Status);
    }

}
