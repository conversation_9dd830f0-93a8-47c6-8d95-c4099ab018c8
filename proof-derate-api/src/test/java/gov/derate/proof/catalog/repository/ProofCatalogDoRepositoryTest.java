package gov.derate.proof.catalog.repository;

import gov.derate.proof.catalog.entity.ProofCatalogDo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2021/9/13.
 */
@RunWith(SpringRunner.class)
@WebAppConfiguration
@PowerMockIgnore("javax.management.*")
@Rollback
@Transactional(rollbackFor = Exception.class)
@SpringBootTest
public class ProofCatalogDoRepositoryTest {
    @Autowired
    private ProofCatalogRepository proofCatalogRepository;

    @Test
    public void findByNameTest() {
        ProofCatalogDo proofCatalogDo = proofCatalogRepository.findByName("1");
        Assert.assertNull(proofCatalogDo);
    }
}
