version: '3.0'
services:
  java-test:
    image: 192.168.10.205/hub/jdk8:1.3
    container_name: proof-derate-api-test
    working_dir: /home/<USER>/project/
    volumes:
      - ./target:/home/<USER>/project
      - ./target/logs:/home/<USER>/project/logs
      - ./target/config:/home/<USER>/project/config
      - ./target/config:/home/<USER>/project/platform_path
    network_mode: bridge
    ports:
      - 8080:8080
      #- 5005:5005 # debug端口
    environment:
      - TZ=Asia/Shanghai
      - platform_path=/home/<USER>/project/platform_path
    command: java -jar ./proof-derate-api.jar
#    debug的command 命令
#    command:  java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005 -jar ./executor-stat-ops.jar
