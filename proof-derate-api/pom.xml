<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>proof-derate-parent</artifactId>
        <groupId>gov.derate.proof</groupId>
        <version>V1.3032.0_20250724</version>
    </parent>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <license.watermark.api.version>1.0.0.2</license.watermark.api.version>
        <license.word.pdf.version>1.0.0.0</license.word.pdf.version>
        <open.feign.version>2.2.9.RELEASE</open.feign.version>
    </properties>
    <artifactId>proof-derate-api</artifactId>
    <dependencies>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.12</version>
        </dependency>
        <!--证照水印-->
        <dependency>
            <groupId>gov.license.watermark</groupId>
            <artifactId>license-watermark-api</artifactId>
            <version>${license.watermark.api.version}</version>
        </dependency>
        <dependency>
            <groupId>gov.license</groupId>
            <artifactId>license-word2pdf</artifactId>
            <version>${license.word.pdf.version}</version>
        </dependency>
        <!--pdf,itext7-->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext7-core</artifactId>
            <version>7.2.1</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>gov.derate.proof</groupId>
            <artifactId>proof-derate-api-common</artifactId>
            <version>${parent.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--数字广东-粤省事加密包-->
        <dependency>
            <groupId>com.dg.dpd</groupId>
            <artifactId>dspace</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>3.0.4</version>
        </dependency>
        <!-- springboot freemarker -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>logback-core</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.digital</groupId>
            <artifactId>dspace-encrypiton</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.2</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>${open.feign.version}</version>
        </dependency>
        <dependency>
            <groupId>gov.license</groupId>
            <artifactId>license-derate-api-sdk</artifactId>
            <version>0.0.0.1-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>proof-derate-api</finalName>
        <!-- 控制资源文件的拷贝 -->
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <!--根据不同业务系统启动类，指定入口类 -->
                            <mainClass>gov.ProofDerateApplication</mainClass>
                            <!--是否在MANIFEST.MF文件的Class-Path指定加载的具体版本第三方依赖jar,这里统一配置为true -->
                            <addClasspath>true</addClasspath>
                            <!--所加载的第三方依赖包具体在业务系统主程序当前目录的哪个位置，这里统一放在主程序所在同级目录的上两层目录下 -->
                            <classpathPrefix>./lib/</classpathPrefix>
                            <!--不包括时间后缀的jar包。去除名称-->
                            <useUniqueVersions>false</useUniqueVersions>
                        </manifest>
                        <!-- (配置文件外置目录) -->
                        <manifestEntries>
                            <Class-Path>./config/</Class-Path>
                        </manifestEntries>
                    </archive>
                    <excludes>
                        <!--排除配置文件，由外部resources下的配置文件来配置 -->
                        <exclude>*</exclude>
                        <exclude>fonts/*</exclude>
                        <exclude>template/*</exclude>
                        <exclude>mock/*</exclude>
                        <exclude>license/*</exclude>
                    </excludes>
                    <outputDirectory>${project.build.directory}</outputDirectory>
                </configuration>
            </plugin>
            <!-- 分离lib -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!-- 依赖包输出目录 -->
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <!--是否只copy当前pom文件的依赖jar包-->
                            <excludeTransitive>false</excludeTransitive>
                            <!--是否忽略版本号-->
                            <stripVersion>false</stripVersion>
                            <!-- 依赖包的作用域 -->
                            <!--<includeScope>runtime</includeScope>-->
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <!--允许启动时进行配置赋值-->
                <filtering>false</filtering>
                <targetPath>${project.build.directory}/classes</targetPath>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <!--允许启动时进行配置赋值-->
                <filtering>false</filtering>
                <targetPath>${project.build.directory}/config</targetPath>
            </resource>
        </resources>
    </build>
    <profiles>
        <profile>
            <id>licc-clu</id>
            <dependencies>
                <dependency>
                    <groupId>gov.license</groupId>
                    <artifactId>licc-func-clu-api</artifactId>
                    <version>${licc-func.version}</version>
                    <exclusions>
                        <exclusion>
                            <artifactId>log4j-jul</artifactId>
                            <groupId>org.apache.logging.log4j</groupId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.checkerframework</groupId>
                            <artifactId>checker-qual</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>com.google.errorprone</groupId>
                            <artifactId>error_prone_annotations</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
            </dependencies>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>licc-api</id>
            <dependencies>
                <dependency>
                    <groupId>gov.license</groupId>
                    <artifactId>licc-func-core</artifactId>
                    <version>${licc-func.version}</version>
                    <exclusions>
                        <exclusion>
                            <artifactId>log4j-jul</artifactId>
                            <groupId>org.apache.logging.log4j</groupId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.checkerframework</groupId>
                            <artifactId>checker-qual</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>com.google.errorprone</groupId>
                            <artifactId>error_prone_annotations</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
</project>
