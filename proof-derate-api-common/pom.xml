<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>gov.derate.proof</groupId>
        <artifactId>proof-derate-parent</artifactId>
        <version>V1.3032.0_20250724</version>
    </parent>
    <artifactId>proof-derate-api-common</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <!-- 导出Excel依赖包 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>bcprov-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- apache POI end -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
        </dependency>
        <!-- bouncycastle -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.66</version>
        </dependency>
        <!--   apache httpClient begin     -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore-nio</artifactId>
        </dependency>
        <!--   apache httpClient end     -->
    </dependencies>
    <profiles>
        <profile>
            <id>licc-clu</id>
            <dependencies>
                <dependency>
                    <groupId>gov.license</groupId>
                    <artifactId>licc-func-clu-api</artifactId>
                    <version>${licc-func.version}</version>
                    <exclusions>
                        <exclusion>
                            <artifactId>log4j-jul</artifactId>
                            <groupId>org.apache.logging.log4j</groupId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.checkerframework</groupId>
                            <artifactId>checker-qual</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>com.google.errorprone</groupId>
                            <artifactId>error_prone_annotations</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
            </dependencies>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>licc-api</id>
            <dependencies>
                <dependency>
                    <groupId>gov.license</groupId>
                    <artifactId>licc-func-core</artifactId>
                    <version>${licc-func.version}</version>
                    <exclusions>
                        <exclusion>
                            <artifactId>log4j-jul</artifactId>
                            <groupId>org.apache.logging.log4j</groupId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.checkerframework</groupId>
                            <artifactId>checker-qual</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>com.google.errorprone</groupId>
                            <artifactId>error_prone_annotations</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
</project>
