package gov.derate.proof.dictionary.entity;

/**
 * <p>
 * 系统字段表支持类型
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/29
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/29；
 */
public enum DictionaryDicTypeConstant {
    /**
     * 协查-假期范围
     */
    ASSIST_HOLIDAY_RANGE("协查-假期范围"),
    /**
     * 协查-补班范围
     */
    ASSIST_HOLIDAY_WORK_RANGE("协查-补班范围"),
    /**
     * 协查-非工作时间范围
     */
    ASSIST_WORK_HOUR_RANGE("协查-非工作时间范围"),
    /**
     * 证明目录序列号
     */
    CATALOG_SER_NUM("证明目录序列号"),
    /**
     * 惠州数据共享系统
     */
    HZ_DATA_SHARED_SYSTEM("惠州数据共享系统"),
    /**
     * 证明目录数据共享系统
     */
    PROOF_CATALOG_DATA_SHARED_SYSTEM("证明目录数据共享系统"),
    /**
     * 大屏接口
     */
    MONITOR_URL("大屏接口"),
    /**
     * 电子证照
     */
    LICENSE("电子证照"),
    /**
     * 电子证明
     */
    LICENSE_PROOF("电子证明"),
    /**
     * 功能配置
     */
    FUNC_CONFIG("功能配置"),
    /**
     * 广东省一网共享配置
     */
    GD_PROVINCE_SHARE("广东省一网共享配置"),

    /**
     * 粤证易配置
     */
    GOV_EASY("粤证易配置"),
    /**
     * 汕尾地区配置
     */
    SHAN_WEI_CONFIG("汕尾地区配置"),
    REAL_NAME_INFO_SERVICE("实名信息服务"),
    YSS("粤省事配置"),
    YST("粤商通配置"),
    JX_GOV_SERVICE("江西政务服务"),
    JX_DATA_SHARED_SYS("江西数据共享交换平台"),



    ;

    private final String names;

    DictionaryDicTypeConstant(String names) {
        this.names = names;
    }

    public String getNames() {
        return names;
    }

    @Override
    public String toString() {
        return "DictionaryDicTypeConstant{" +
                "names='" + names + '\'' +
                '}';
    }
}
