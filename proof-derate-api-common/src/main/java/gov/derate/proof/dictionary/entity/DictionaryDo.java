package gov.derate.proof.dictionary.entity;

import gov.derate.proof.common.entity.AbstractDomainEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <p>
 * 字典表实体
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-10-22
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:yxh;Date:2021-10-22;
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "tbl_dictionary")
public class DictionaryDo extends AbstractDomainEntity {
    /**
     * 字典名称
     */
    @Column(name = "DIC_NAME", length = 200, nullable = false)
    private String dicName;

    /**
     * 字典类型
     */
    @Column(name = "DIC_TYPE", length = 100, nullable = false)
    private String dicType;

    /**
     * 类别编码
     */
    @Column(name = "TYPE_CODE", length = 100, nullable = false)
    private String typeCode;

    /**
     * 类别名称
     */
    @Column(name = "TYPE_NAME", length = 200, nullable = false)
    private String typeName;

    /**
     * 排序值
     */
    @Column(name = "DIC_SORT")
    private BigDecimal dicSort;

    public String getDicName() {
        return dicName;
    }

    public void setDicName(String dicName) {
        this.dicName = dicName;
    }

    public String getDicType() {
        return dicType;
    }

    public void setDicType(String dicType) {
        this.dicType = dicType;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public BigDecimal getDicSort() {
        return dicSort;
    }

    public void setDicSort(BigDecimal dicSort) {
        this.dicSort = dicSort;
    }

    @Override
    public String toString() {
        return "DictionaryDo{" +
                "dicName='" + dicName + '\'' +
                ", dicType='" + dicType + '\'' +
                ", typeCode='" + typeCode + '\'' +
                ", typeName='" + typeName + '\'' +
                ", dicSort=" + dicSort +
                '}';
    }
}
