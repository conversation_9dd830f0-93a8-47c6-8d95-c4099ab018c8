package gov.derate.proof.dictionary.entity;

/**
 * <p>
 * 系统字段表支持类型
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/29
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/29；
 */
public enum DictionaryTypeItemConstant {
    THIRD_PARTY_MOCK_RESPONSE("数据共享模拟响应开关", DictionaryDicTypeConstant.FUNC_CONFIG),
    GD_PROVINCE_SHARE_CATALOG_URL("省一网共享目录URL", DictionaryDicTypeConstant.GD_PROVINCE_SHARE),
    GD_PROVINCE_SHARE_CATALOG_SERVICE_ID("省一网共享目录serviceId", DictionaryDicTypeConstant.GD_PROVINCE_SHARE),
    GD_PROVINCE_SHARE_CATALOG_PASS_ID("省一网共享目录passId", DictionaryDicTypeConstant.GD_PROVINCE_SHARE),
    GD_PROVINCE_SHARE_CATALOG_PAAS_TOKEN("省一网共享目录paasToken", DictionaryDicTypeConstant.GD_PROVINCE_SHARE),
    GD_PROVINCE_GOVZZ_APP_KEY("省一网共享目录govzzAppKey", DictionaryDicTypeConstant.GD_PROVINCE_SHARE),
    GD_PROVINCE_GOVZZ_APP_SECRET("省一网共享目录govzzAppSecret", DictionaryDicTypeConstant.GD_PROVINCE_SHARE),
    GD_PROVINCE_GOVZZ_TOKEN_URL("省一网共享目录govzzTokenUrl地址", DictionaryDicTypeConstant.GD_PROVINCE_SHARE),
    GD_PROVINCE_GOVZZ_TOKEN_SERVICE_ID("省一网共享目录govzzTokenServiceId", DictionaryDicTypeConstant.GD_PROVINCE_SHARE),
    IS_REAL_STAT("是否启动实时统计开启", DictionaryDicTypeConstant.FUNC_CONFIG),
    STAT_LT_DIST("是否统计大于等于区县的统计数据", DictionaryDicTypeConstant.FUNC_CONFIG),
    ASSIST_NOTICE_WAY("协查发送消息方式配置 govEasy 粤证易，sms短信", DictionaryDicTypeConstant.FUNC_CONFIG),
    ASSIST_NOTICE_WAY_LOGIC("协查办理，粤证易走汕尾跳转逻辑和其他地区的跳转逻辑  shanwei代表走汕尾的跳转逻辑，weixin则走微信程序的跳转逻辑。", DictionaryDicTypeConstant.FUNC_CONFIG),
    SERVICE_ITEM_FILTER_ITEM_TYPE("事项类型过滤  多个用逗号拼接，01:行政许可 02:行政处罚 03:行政强制 04:行政征收 05:行政给付 06:行政检查 07:行政确认08:行政奖励 09:行政裁决 20:其他行政权力 21:公共服务", DictionaryDicTypeConstant.FUNC_CONFIG),
    LICENSE_USE_UNION_LICENSE_ITEM_SWITCH("电子证照刷新-组合目录新通道开关", DictionaryDicTypeConstant.FUNC_CONFIG),
    ASSIST_TOOLS_ASSIST_REDIRECT_URL("协查发送消息-协查工具-跳转地址", DictionaryDicTypeConstant.FUNC_CONFIG),
    PROOF_CATALOG_LOGIC("证明目录-执行逻辑选择，LICENSE=电子证照，LICENSE_PROOF=电子证明，PROVINCE_CATALOG=省目录", DictionaryDicTypeConstant.FUNC_CONFIG),
    ASSIST_RECORD_CALL_BACK_SIGN_INFO("协查管理-回调功能签名标识", DictionaryDicTypeConstant.FUNC_CONFIG),
    ASSIST_RECORD_CALL_BACK_URL("协查管理-回调接口url", DictionaryDicTypeConstant.FUNC_CONFIG),
    SYNC_ITEM_USE_MATERIAL_BIZ_ID("同步是否使用业务材料id", DictionaryDicTypeConstant.FUNC_CONFIG),
    ITEM_SYSTEM_BIZ_SELECT("事项系统采用业务，可选选项HZ,JX,DEFAULT", DictionaryDicTypeConstant.FUNC_CONFIG),
    ASSIST_VALIDATE_SWITCH("校验粤证易跳转到协查工具的校验开关,true为开，false为关闭", DictionaryDicTypeConstant.FUNC_CONFIG),


    GOV_EASY_SECRET("粤证易密钥", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_EXPIRE("粤证易token过期时间", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_RENEW_EXPIRE("粤证易续签过期时间(单位秒)，默认10天", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_DEFAULT_USER("粤证易-临时id", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_AGENT_ID("粤证易-AgentID", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_AGENT_SECRET("粤证易-AgentSecret", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_AGENT_CORP_ID("粤证易-AgentCorpID", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_AGENT_PAAS_ID("粤证易-AgentPaaSID", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_AGENT_TOKEN("粤证易-AgentToken", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_BASE_URL("粤证易-访问接口基础URL", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_SEND_TEXT_TITLE("粤证易-消息文本title，避免中文乱码，使用base64，进行编码中文", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_SEND_TEXT_CONTEXT("粤证易-消息文本内容，避免中文乱码，使用base64，进行编码中文", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_SEND_TEXT_REDIRECT_URL("粤证易发送消息，跳转地址【微信地址】 digit.loginType=weixin 生效", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_GET_USER_BY_USER_ID_URL("粤证易-根据用户userId获取用户信息Url POST请求", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_GET_USER_BY_USER_CODE_URL("粤证易-根据粤证易获取到点击用户的code后，获取UserId GET请求 %s是待业务填充字符", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_ASSIST_TOKEN_URL("粤证易-获取token链接 GET请求 ,token5分钟内有效", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_SEND_MSG_URL("粤证易-发送消息 POST请求 %s是待业务填充字符", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_GET_USER_BY_PERSON_AUTH_CODE_URL("粤证易-根据用户个人授权码获取用户信息Url", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_USER_PERSON_AUTH_URL("粤证易-用户个人授权Url", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_USER_PERSON_AUTH_CALL_BACK_URL("粤证易-用户个人授权回调Url", DictionaryDicTypeConstant.GOV_EASY),
    GOV_EASY_PAGE_BASE_URL("粤证易-页面访问的page-base-Url 前端页面地址生产环境是https://xtbg.gdzwfw.gov.cn，预发布测试环境https://xtbg.digitalgd.com.cn", DictionaryDicTypeConstant.GOV_EASY),

    LICENSE_APP_ACCOUNT("电子证照-接入系统账号", DictionaryDicTypeConstant.LICENSE),
    LICENSE_APP_PASSWORD("电子证照-接入系统密码", DictionaryDicTypeConstant.LICENSE),
    LICENSE_APP_KEY("电子证照-接入系统appKey", DictionaryDicTypeConstant.LICENSE),
    LICENSE_APP_SECRET("电子证照-接入系统密钥", DictionaryDicTypeConstant.LICENSE),
    LICENSE_HOLDER_AUTH_SHOW_CONTEXT_CODE_URL("电子证照，根据业务信息换取上下文编码url", DictionaryDicTypeConstant.LICENSE),
    LICENSE_HOLDER_AUTH_SHOW_AUTH_CODE_URL("电子证照，获取用证码和证照摘要", DictionaryDicTypeConstant.LICENSE),
    LICENSE_GET_TOKEN_AUDIT_URL("电子证照，获取受理审批证照访问令牌", DictionaryDicTypeConstant.LICENSE),
    LICENSE_VIEW_LICENSE_URL("电子证照，查看电子证照页面", DictionaryDicTypeConstant.LICENSE),
    LICENSE_ATTACHMENT_ARCHIVING_URL("电子证照，归档url", DictionaryDicTypeConstant.LICENSE),
    LICENSE_APP_URL("电子证照接口url", DictionaryDicTypeConstant.LICENSE),
    LICENSE_BIZ_URL("电子证照-biz页面url", DictionaryDicTypeConstant.LICENSE),
    LICENSE_BIZ_VIEW_URL("电子证照-biz系统基本码查看url", DictionaryDicTypeConstant.LICENSE),
    LICENSE_GET_LICENSE_AUDIT_URL("电子证照-提取受理审批证照数据接口url", DictionaryDicTypeConstant.LICENSE),
    LICENSE_HOLDER_URL("电子证照，持有人授权用证url", DictionaryDicTypeConstant.LICENSE),
    LICENSE_ARCHIVE_URL("电子证照，证照归档url", DictionaryDicTypeConstant.LICENSE),
    LICENSE_GET_LICENSE_URL("电子证照，获取证照数据url", DictionaryDicTypeConstant.LICENSE),
    LICENSE_HOLDER_CAS_NO("电子证照-持有人授权用证-实名参数", DictionaryDicTypeConstant.LICENSE),
    LICENSE_SEAL_INFO_URL("电子证照-查询组别印章接口接口url", DictionaryDicTypeConstant.LICENSE),
    LICENSE_ISSUE_URL("电子证照-签发接口url", DictionaryDicTypeConstant.LICENSE),


    LICENSE_PROOF_APP_ACCOUNT("电子证明-电子证明接入账号", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_APP_PASSWORD("电子证照-电子证明接入密码", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_APP_KEY("电子证照-接入系统appKey", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_APP_SECRET("电子证照-接入系统密钥", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_APP_URL("电子证照-电子证明接入系统URL配置", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_AUTH_URL("电子证明-用证授权接口", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_TOKEN_URL("电子证明-用证token用证接口", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_BIZ_URL("电子证明-biz页面接口", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_BIZ_VIEW_HOLDER_URL("license-biz页面接口-查看电子证照页面", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_LOGIN_URL("license-biz页面接口-电子证明管理系统跳转登录地址", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_BIZ_VIEW_URL("电子证明-biz系统基本码查看url", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_ARCHIVE_URL("电子证明-归档电子证明URL", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_ISSUE_SERV_LIST_URL("电子证明-查询部门开通目录URL", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_RECORD_CALL_BACK_SIGN_INFO("电子证明-证明开具回调功能签名标识", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_RECORD_CALL_BACK_URL("电子证明-证明开具回调接口url", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_ISSUE_ASSIST_RESULT_MAPPING("电子证明-协查结果证明开具映射，默认SUCCESS:通过,FAIL:不通过", DictionaryDicTypeConstant.LICENSE_PROOF),

    LICENSE_PROOF_SEAL_INFO_URL("电子证明-查询组别印章接口接口url", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_ISSUE_URL("电子证明-签发接口url", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_HOLDER_ID_TYPE_CONVERT_RULE("电子证明-持有人身份证类型转换关系", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_LEGAL_HOLDER_ID_TYPE_CONVERT_RULE("电子证明-协查单法人持有人身份证类型转换关系", DictionaryDicTypeConstant.LICENSE_PROOF),
    LICENSE_PROOF_AUDIT_CALL_ISSUE_REGION_RULE("电子证明-协查审核调用issue接口可用地区规则", DictionaryDicTypeConstant.LICENSE_PROOF),

    SHAN_WEI_CONFIG_BASE_URL("汕尾配置-善美码基础url", DictionaryDicTypeConstant.SHAN_WEI_CONFIG),
    SHAN_WEB_CONFIG_SEND_TEXT_REDIRECT_URL("粤证易发送消息，跳转地址【5部地址】  digit.loginType=shanwei 生效", DictionaryDicTypeConstant.SHAN_WEI_CONFIG),
    SHAN_WEI_CONFIG_ASSIST_REDIRECT_URL_SUF("无证明协查工具-跳转地址 粤证易发送消息，跳转地址【5部地址】 digit.loginType=shanwei 跳转地址尾缀", DictionaryDicTypeConstant.SHAN_WEI_CONFIG),
    SHAN_WEI_GOV_EASY_CONFIG_GET_USER_URL("汕尾逻辑-粤证易-获取5部的用户信息的appkey和secretURL", DictionaryDicTypeConstant.SHAN_WEI_CONFIG),
    SHAN_WEI_GOV_EASY_CONFIG_GET_USER_APP_KEY("粤证易-获取5部的用户信息的appkey", DictionaryDicTypeConstant.SHAN_WEI_CONFIG),
    SHAN_WEI_GOV_EASY_CONFIG_GET_USER_SECRET("粤证易-获取5部的用户信息的secret", DictionaryDicTypeConstant.SHAN_WEI_CONFIG),

    REAL_NAME_INFO_SERVICE_USER_INFO_URL("实名信息用户获取接口", DictionaryDicTypeConstant.REAL_NAME_INFO_SERVICE),
    REAL_NAME_INFO_SERVICE_APP_KEY("实名信息appKey", DictionaryDicTypeConstant.REAL_NAME_INFO_SERVICE),
    REAL_NAME_INFO_SERVICE_APP_SECRET("实名信息appSecret", DictionaryDicTypeConstant.REAL_NAME_INFO_SERVICE),
    REAL_NAME_INFO_SERVICE_CAS("实名信息跳过参数", DictionaryDicTypeConstant.REAL_NAME_INFO_SERVICE),
    REAL_NAME_INFO_SERVICE_CONTENT_CODE("事由编码", DictionaryDicTypeConstant.REAL_NAME_INFO_SERVICE),
    REAL_NAME_INFO_SERVICE_CONTENT_NAME("事由名称", DictionaryDicTypeConstant.REAL_NAME_INFO_SERVICE),

    YSS_DECODE_URL("粤省事码解码接口",DictionaryDicTypeConstant.YSS),
    YSS_SYSTEM_ID("粤省事-系统id",DictionaryDicTypeConstant.YSS),
    YSS_PAAS_ID("粤省事-系统PAAS_ID",DictionaryDicTypeConstant.YSS),
    YSS_PAAS_TOKEN("粤省事-系统PAAS_Token",DictionaryDicTypeConstant.YSS),
    YSS_SERVICE_ID("粤省事-系统serviceId",DictionaryDicTypeConstant.YSS),
    YSS_PRIVATE_KEY("粤省事-加密密钥",DictionaryDicTypeConstant.YSS),
    YSS_DETAIL_URL("粤省事-授权信息查询接口",DictionaryDicTypeConstant.YSS),
    YSS_FILE_URL("粤省事-证照PDF获取接口",DictionaryDicTypeConstant.YSS),
    YSS_DATA_ITEM_URL("粤省事-数据项获取接口",DictionaryDicTypeConstant.YSS),
    YSS_PUBLIC_KEY("粤省事-加密公钥",DictionaryDicTypeConstant.YSS),

    YST_PRIVATE_KEY("粤商通-加密密钥", DictionaryDicTypeConstant.YST),
    YST_PAAS_ID("粤商通-passId", DictionaryDicTypeConstant.YST),
    YST_PASS_TOKEN("粤商通-passToken", DictionaryDicTypeConstant.YST),
    YST_SYSTEM_ID("粤商通-系统id", DictionaryDicTypeConstant.YST),
    YST_CREATE_AUTH_URL("粤商通-扫粤商码下单", DictionaryDicTypeConstant.YST),
    YST_AUTH_RESULT_URL("粤商通-取数令牌获取接口", DictionaryDicTypeConstant.YST),
    YST_FILE_URL("粤商通-证照PDF获取接口", DictionaryDicTypeConstant.YST),
    YST_DATA_ITEM_URL("粤商通-数据项获取接口", DictionaryDicTypeConstant.YST),
    YST_AUTH_INFO_URL("粤商通-授权信息查询接口", DictionaryDicTypeConstant.YST),

    JX_GOV_SERVICE_DECODE_PUB_KEY("江西政务服务码-解码pubKey", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_DECODE_PRI_KEY("江西政务服务码-解密密钥", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_PUB_KEY("江西政务服务码-解密公钥", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_DECODE_URL("江西政务服务码-政务服务码解码接口URL", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_TOKEN_URL("江西政务服务码-获取tokenUrl", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_GRANT_TYPE("江西政务服务码-默认值client_credentials", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_ORG_GRANT_TYPE("江西政务服务码-企业默认值client_credentials", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_CLIENT_SECRET("江西政务服务码-应用授权密钥，由江西省政务服务码平台分配", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_ORG_CLIENT_SECRET("江西政务服务码-企业应用授权密钥，由江西省政务服务码平台分配", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_CLIENT_ID("江西政务服务码-应用授权标识，由江西省政务服务码平台分配", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_ORG_CLIENT_ID("江西政务服务码-企业应用授权标识，由江西省政务服务码平台分配", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_DECODE_DIVISION_CODE("江西政务服务码-解码接口行政区划代码", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_DECODE_CREDIT_CODE("江西政务服务码-解码接口统一社会信用代码", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_DECODE_ORG_SIMP_NAME("江西政务服务码-解码接口部门简称", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_DECODE_ORG_NAME("江西政务服务码-解码接口部门全称", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_DECODE_SCENE_CODE("江西政务服务码-解码接口-自然人业务场景唯一标识", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_DECODE_ORG_SCENE_CODE("江西政务服务码-解码接口-企业业务场景唯一标识", DictionaryDicTypeConstant.JX_GOV_SERVICE),
    JX_GOV_SERVICE_SKIP_VALID_DECODE("江西政务服务码-解码接口-跳过验签配置", DictionaryDicTypeConstant.JX_GOV_SERVICE),

    JX_DATA_SHARED_GRANT_TYPE("江西数据共享-授权方式", DictionaryDicTypeConstant.JX_DATA_SHARED_SYS),
    JX_DATA_SHARED_CLIENT_SECRET("江西数据共享-客户端secret", DictionaryDicTypeConstant.JX_DATA_SHARED_SYS),
    JX_DATA_SHARED_CLIENT_ID("江西数据共享-客户端id", DictionaryDicTypeConstant.JX_DATA_SHARED_SYS),
    JX_DATA_SHARED_LOGIN_URL("江西数据共享-登录接口URL", DictionaryDicTypeConstant.JX_DATA_SHARED_SYS),
    ;
    /**
     * 字典子项
     */
    private final String desc;
    /**
     * 字典所属dictType
     */
    private final DictionaryDicTypeConstant dictType;

    DictionaryTypeItemConstant(String desc, DictionaryDicTypeConstant dictType) {
        this.desc = desc;
        this.dictType = dictType;
    }

    public String getDesc() {
        return desc;
    }

    public DictionaryDicTypeConstant getDictType() {
        return dictType;
    }

    @Override
    public String toString() {
        return "DictionaryTypeItemConstant{" +
                "names='" + desc + '\'' +
                ", dictType=" + dictType +
                '}';
    }
}
