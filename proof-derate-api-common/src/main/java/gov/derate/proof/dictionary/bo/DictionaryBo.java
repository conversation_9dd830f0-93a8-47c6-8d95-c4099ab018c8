package gov.derate.proof.dictionary.bo;


import gov.derate.proof.common.bo.BaseBo;

import java.math.BigDecimal;

/**
 * <p>
 * 字典表实体
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-10-22
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:yxh;Date:2021-10-22;
 */
public class DictionaryBo extends BaseBo {
    /**
     * 字典名称
     */
    private String dicName;

    /**
     * 字典类型
     */
    private String dicType;

    /**
     * 类别编码
     */
    private String typeCode;

    /**
     * 类别名称
     */
    private String typeName;

    /**
     * 排序值
     */
    private BigDecimal dicSort;

    public String getDicName() {
        return dicName;
    }

    public void setDicName(String dicName) {
        this.dicName = dicName;
    }

    public String getDicType() {
        return dicType;
    }

    public void setDicType(String dicType) {
        this.dicType = dicType;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public BigDecimal getDicSort() {
        return dicSort;
    }

    public void setDicSort(BigDecimal dicSort) {
        this.dicSort = dicSort;
    }

    @Override
    public String toString() {
        return "DictionaryBo{" +
                "dicName='" + dicName + '\'' +
                ", dicType='" + dicType + '\'' +
                ", typeCode='" + typeCode + '\'' +
                ", typeName='" + typeName + '\'' +
                ", dicSort=" + dicSort +
                '}';
    }
}
