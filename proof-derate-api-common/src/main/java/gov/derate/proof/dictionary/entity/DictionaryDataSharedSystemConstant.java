package gov.derate.proof.dictionary.entity;

/**
 * <p>
 * 系统字段表支持类型
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/29
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/29；
 */
public enum DictionaryDataSharedSystemConstant {
    /**
     * 惠州数据共享系统
     */
    HZ_DATA_SHARED_SYSTEM("惠州数据共享系统"),
    ;
    /**
     * 系统名称
     */
    private String systemName;
    DictionaryDataSharedSystemConstant(String systemName) {
        this.systemName = systemName;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }
}
