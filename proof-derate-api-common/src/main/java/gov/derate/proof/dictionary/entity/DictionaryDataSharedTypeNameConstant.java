package gov.derate.proof.dictionary.entity;

/**
 * <p>
 * 系统字段表支持类型
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/29
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/29；
 */
public enum DictionaryDataSharedTypeNameConstant {
    /**
     * 主题详情url
     */
    THEME_DETAIL_URL("themeDetailUrl"),
    /**
     * 登录url
     */
    LOGIN_URL("loginUrl"),
    /**
     * 主题获取url
     */
    THEME_URL("themeUrl"),
    /**
     * 登录appKey
     */
    APP_KEY("appKey"),
    /**
     * 登录appKey
     */
    APP_SECRET("appSecret"),
    /**
     * 登录appKey
     */
    ACCOUNT("account"),
    /**
     * 登录appKey
     */
    PASSWORD("password"),
    ;
    /**
     * 系统名称
     */
    private String value;
    DictionaryDataSharedTypeNameConstant(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
