package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import gov.derate.proof.catalog.convert.DataSharedDataTypeEnumJsonDeserialize;

import java.io.Serializable;

/**
 * <p>
 * 数据共享配置-数据项bo类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/28；
 */
public class ProofCatalogDataSharedConfigDataItemDto implements Serializable {
    /**
     * 序号
     */
    @JsonProperty("order_num")
    private Integer orderNum;
    /**
     * 显示名称
     */
    @JsonProperty("display_name")
    private String displayName;
    /**
     * 数据项名称
     */
    @JsonProperty("data_item_name")
    private String dataItemName;
    /**
     * 数据类型名称
     */
    @JsonProperty("data_type_name")
    private String dataTypeName;
    /**
     * 数据类型枚举类
     */
    @JsonProperty("data_type")
    @JsonDeserialize(using = DataSharedDataTypeEnumJsonDeserialize.class)
    private DataSharedDataTypeEnum dataType;
    /**
     * 值域映射
     */
    @JsonProperty("data_range_mapping")
    private String dataRangeMapping;
    /**
     * 是否显示
     */
    @JsonProperty("DISPLAY")
    private Boolean display;

    public ProofCatalogDataSharedConfigDataItemDto() {
        this.display = true;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDataItemName() {
        return dataItemName;
    }

    public void setDataItemName(String dataItemName) {
        this.dataItemName = dataItemName;
    }

    public String getDataTypeName() {
        return dataTypeName;
    }

    public void setDataTypeName(String dataTypeName) {
        this.dataTypeName = dataTypeName;
    }

    public DataSharedDataTypeEnum getDataType() {
        return dataType;
    }

    public void setDataType(DataSharedDataTypeEnum dataType) {
        this.dataType = dataType;
    }

    public String getDataRangeMapping() {
        return dataRangeMapping;
    }

    public void setDataRangeMapping(String dataRangeMapping) {
        this.dataRangeMapping = dataRangeMapping;
    }

    public Boolean getDisplay() {
        return display;
    }

    public void setDisplay(Boolean display) {
        this.display = display;
    }

    @Override
    public String toString() {
        return "ProofCatalogDataSharedConfigDataItemBo{" +
                "orderNum=" + orderNum +
                ", displayName='" + displayName + '\'' +
                ", dataItemName='" + dataItemName + '\'' +
                ", dataTypeName='" + dataTypeName + '\'' +
                ", dataType=" + dataType +
                ", dataRangeMapping='" + dataRangeMapping + '\'' +
                ", display=" + display +
                '}';
    }
}
