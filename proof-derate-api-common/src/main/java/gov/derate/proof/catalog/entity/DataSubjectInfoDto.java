package gov.derate.proof.catalog.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * DataSubjectInfoDto
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/29
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/29；
 */
public class DataSubjectInfoDto implements Serializable {
    /**
     * 唯一标识
     */
    @JsonProperty("id")
    private String id;
    /**
     * 中文名称
     */
    @JsonProperty("name")
    private String name;
    /**
     * 库表资源表名列表（以，分隔开）
     */
    @JsonProperty("name_en")
    private String nameEn;
    /**
     * 资源标识符
     */
    @JsonProperty("resource_id")
    private String resourceId;
    /**
     * 版本号
     */
    @JsonProperty("version")
    private String version;
    /**
     * 资源状态（草案、试用、标准、废置）
     */
    @JsonProperty("status")
    private String status;
    /**
     * 管理方8
     */
    @JsonProperty("admin_org_name")
    private String adminOrgName;
    /**
     * 管理方组织机构代码
     */
    @JsonProperty("admin_org_code")
    private String adminOrgCode;
    /**
     * 资源摘要
     */
    @JsonProperty("description")
    private String description;
    /**
     * 使用限制
     */
    @JsonProperty("use_restriction")
    private String useRestriction;
    /**
     *
     */
    @JsonProperty("data_amount")
    private Long dataAmount;
    /**
     * 共享方式（强制共享、条件共享、不予共
     */
    @JsonProperty("share_constraint_type")
    private String shareConstraintType;
    /**
     * 不予共享的原因
     */
    @JsonProperty("not_share_reason")
    private String notShareReason;
    /**
     * 标签（多个以“，”分隔开）
     */
    @JsonProperty("last_provided_date")
    private Date lastProvidedDate;
    /**
     * 标签（多个以“，”分隔开）
     */
    @JsonProperty("tags")
    private String tags;
    /**
     * 提供方（多个以“、”分隔开）
     */
    @JsonProperty("provide_org_name")
    private String provideOrgName;
    /**
     * 所属专项（多个以“、”分隔开）
     */
    @JsonProperty("project_name")
    private String projectName;
    /**
     * 来源信息系统
     */
    @JsonProperty("system_info_name")
    private String systemInfoName;
    /**
     * 来源权责事项（多个以“，”分隔开）
     */
    @JsonProperty("authority_list_name")
    private String authorityListName;
    /**
     * 数据项列表
     */
    @JsonProperty("data_item_infos")
    private java.util.List<DataItemInfoDto> dataItemInfos;
    /**
     * 提供频度
     */
    @JsonProperty("provider_frequency")
    private String providerFrequency;
    /**
     * 信息资源分类
     */
    @JsonProperty("type")
    private String type;
    /**
     * 是否开放
     */
    @JsonProperty("is_open")
    private Boolean isOpen;
    /**
     * 不纳入开放目录原因
     */
    @JsonProperty("is_open_note")
    private String isOpenNote;
    /**
     * 管理方式：计算机[COMPUTER]；手工
     */
    @JsonProperty("manage_mode")
    private String manageMode;
    /**
     * 信息资源格式：数据库[DATABASE]；扫描件[SCANNING]；图形图像[IMAGE]；电子文档[E-DOCUMENT]；流媒体[STREAM]；电子证照[LICENSE]；其他[OTHER]
     */
    @JsonProperty("medium")
    private String medium;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAdminOrgName() {
        return adminOrgName;
    }

    public void setAdminOrgName(String adminOrgName) {
        this.adminOrgName = adminOrgName;
    }

    public String getAdminOrgCode() {
        return adminOrgCode;
    }

    public void setAdminOrgCode(String adminOrgCode) {
        this.adminOrgCode = adminOrgCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUseRestriction() {
        return useRestriction;
    }

    public void setUseRestriction(String useRestriction) {
        this.useRestriction = useRestriction;
    }

    public Long getDataAmount() {
        return dataAmount;
    }

    public void setDataAmount(Long dataAmount) {
        this.dataAmount = dataAmount;
    }

    public String getShareConstraintType() {
        return shareConstraintType;
    }

    public void setShareConstraintType(String shareConstraintType) {
        this.shareConstraintType = shareConstraintType;
    }

    public String getNotShareReason() {
        return notShareReason;
    }

    public void setNotShareReason(String notShareReason) {
        this.notShareReason = notShareReason;
    }

    public Date getLastProvidedDate() {
        return lastProvidedDate;
    }

    public void setLastProvidedDate(Date lastProvidedDate) {
        this.lastProvidedDate = lastProvidedDate;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getProvideOrgName() {
        return provideOrgName;
    }

    public void setProvideOrgName(String provideOrgName) {
        this.provideOrgName = provideOrgName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getSystemInfoName() {
        return systemInfoName;
    }

    public void setSystemInfoName(String systemInfoName) {
        this.systemInfoName = systemInfoName;
    }

    public String getAuthorityListName() {
        return authorityListName;
    }

    public void setAuthorityListName(String authorityListName) {
        this.authorityListName = authorityListName;
    }

    public List<DataItemInfoDto> getDataItemInfos() {
        return dataItemInfos;
    }

    public void setDataItemInfos(List<DataItemInfoDto> dataItemInfos) {
        this.dataItemInfos = dataItemInfos;
    }

    public String getProviderFrequency() {
        return providerFrequency;
    }

    public void setProviderFrequency(String providerFrequency) {
        this.providerFrequency = providerFrequency;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isOpen() {
        return isOpen;
    }

    public void setOpen(Boolean open) {
        isOpen = open;
    }

    public String getIsOpenNote() {
        return isOpenNote;
    }

    public void setIsOpenNote(String isOpenNote) {
        this.isOpenNote = isOpenNote;
    }

    public String getManageMode() {
        return manageMode;
    }

    public void setManageMode(String manageMode) {
        this.manageMode = manageMode;
    }

    public String getMedium() {
        return medium;
    }

    public void setMedium(String medium) {
        this.medium = medium;
    }

    @Override
    public String toString() {
        return "DataSubjectInfoDto{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", nameEn='" + nameEn + '\'' +
                ", resourceId='" + resourceId + '\'' +
                ", version='" + version + '\'' +
                ", status='" + status + '\'' +
                ", adminOrgName='" + adminOrgName + '\'' +
                ", adminOrgCode='" + adminOrgCode + '\'' +
                ", description='" + description + '\'' +
                ", useRestriction='" + useRestriction + '\'' +
                ", dataAmount=" + dataAmount +
                ", shareConstraintType='" + shareConstraintType + '\'' +
                ", notShareReason='" + notShareReason + '\'' +
                ", lastProvidedDate=" + lastProvidedDate +
                ", tags='" + tags + '\'' +
                ", provideOrgName='" + provideOrgName + '\'' +
                ", projectName='" + projectName + '\'' +
                ", systemInfoName='" + systemInfoName + '\'' +
                ", authorityListName='" + authorityListName + '\'' +
                ", dataItemInfos=" + dataItemInfos +
                ", providerFrequency='" + providerFrequency + '\'' +
                ", type='" + type + '\'' +
                ", isOpen=" + isOpen +
                ", isOpenNote='" + isOpenNote + '\'' +
                ", manageMode='" + manageMode + '\'' +
                ", medium='" + medium + '\'' +
                ", dataItemInfos=" + dataItemInfos +
                '}';
    }
}
