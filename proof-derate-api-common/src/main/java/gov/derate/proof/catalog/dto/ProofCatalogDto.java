package gov.derate.proof.catalog.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.enums.ProofProvideTypeEnum;
import gov.license.common.api.dto.BaseDto;

/**
 * <p>
 * 证明目录
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-02
 * </p>
 *
 * <AUTHOR>
 */
public class ProofCatalogDto extends BaseDto {

    /**
     * 证明目录名称
     */
    @JsonProperty("NAME")
    private String name;
    /**
     * 证明目录编码
     */
    @JsonProperty("CODE")
    private String code;
    /**
     * 证明开具单位类型
     */
    @JsonProperty("unit_type")
    private ProofProvideTypeEnum unitType;
    /**
     * 所属行业部门编码
     */
    @JsonProperty("industry_dept_code")
    private String industryDeptCode;
    /**
     * 所属行业部门名称
     */
    @JsonProperty("industry_dept_name")
    private String industryDeptName;
    /**
     * 水印id
     */
    @JsonProperty("water_mark_id")
    private String waterMarkId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public ProofProvideTypeEnum getUnitType() {
        return unitType;
    }

    public void setUnitType(ProofProvideTypeEnum unitType) {
        this.unitType = unitType;
    }

    public String getIndustryDeptCode() {
        return industryDeptCode;
    }

    public void setIndustryDeptCode(String industryDeptCode) {
        this.industryDeptCode = industryDeptCode;
    }

    public String getIndustryDeptName() {
        return industryDeptName;
    }

    public void setIndustryDeptName(String industryDeptName) {
        this.industryDeptName = industryDeptName;
    }

    public String getWaterMarkId() {
        return waterMarkId;
    }

    public void setWaterMarkId(String waterMarkId) {
        this.waterMarkId = waterMarkId;
    }

    @Override
    public String toString() {
        return "ProofCatalogDto{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", unitType=" + unitType +
                ", industryDeptCode='" + industryDeptCode + '\'' +
                ", industryDeptName='" + industryDeptName + '\'' +
                ", waterMarkId='" + waterMarkId + '\'' +
                '}';
    }
}
