package gov.derate.proof.catalog.entity;

import gov.derate.proof.common.entity.AbstractDomainEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <p>
 * 证明目录关联的部门自行调查表
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/8/10 14:42
 * </p>
 *
 * <AUTHOR>
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_R_PROOF_CATALOG_DEPT")
public class ProofCatalogDeptSurveyDo extends AbstractDomainEntity {
    /**
     * 证明目录ID
     */
    @Column(name = "PROOF_CATALOG_ID", length = 50, nullable = false)
    private String proofCatalogId;
    /**
     * 证明目录code
     */
    @Column(name = "PROOF_CATALOG_CODE", length = 50, nullable = false)
    private String proofCatalogCode;

    /**
     * 部门名称
     */
    @Column(name = "DEPT_NAME", length = 200, nullable = true)
    private String deptName;

    /**
     * 部门代码
     */
    @Column(name = "DEPT_CODE", length = 100, nullable = true)
    private String deptCode;

    /**
     * 自行调查说明 （部门自行调查）
     */
    @Column(name = "DEPT_CANCEL_DESCRIPTION", length = 500, nullable = false)
    private String deptCancelDescription;

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptCancelDescription() {
        return deptCancelDescription;
    }

    public void setDeptCancelDescription(String deptCancelDescription) {
        this.deptCancelDescription = deptCancelDescription;
    }

    public String getProofCatalogCode() {
        return proofCatalogCode;
    }

    public void setProofCatalogCode(String proofCatalogCode) {
        this.proofCatalogCode = proofCatalogCode;
    }

    @Override
    public String toString() {
        return "ProofCatalogDeptSurveyDo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogCode='" + proofCatalogCode + '\'' +
                ", deptName='" + deptName + '\'' +
                ", deptCode='" + deptCode + '\'' +
                ", deptCancelDescription='" + deptCancelDescription + '\'' +
                '}';
    }
}
