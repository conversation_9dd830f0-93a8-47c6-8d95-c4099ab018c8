package gov.derate.proof.catalog.bo;


import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.bo.BaseBo;
import gov.derate.proof.common.enums.ProofClearTypeEnum;

import java.util.List;

/**
 * <p>
 * 证明材料、事项、证明目录数据
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-10-22
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:yxh;Date:2021-10-22;
 */
public class CatalogApiBo extends BaseBo {
    /**
     * 材料名称
     */
    @JsonProperty("material_name")
    private String materialName;
    /**
     * 证明清理类型:0替代取消、1直接取消
     */
    @JsonProperty("proof_clear_type")
    private ProofClearTypeEnum proofClearType;
    /**
     * 替代取消方式 ： TURN_LICENSE_OR_OTHER_LICENSE_WAY, 转化为电子证照/其他证件
     * HANDLE_AFFAIRS_PROMISE, 办事人承诺
     * DATA_SHARING, 数据共享（证明开具部门）
     * ARTIFICIAL_INVESTIGATION, 人工协查（证明开具部门）
     * DEPARTMENT_INVESTIGATION, 部门自行调查
     * OTHER_WAY 其它
     */
    @JsonProperty("replace_cancel_way")
    private String replaceCancelWay;
    /**
     * 所属证明目录ID
     */
    @JsonProperty("proof_catalog_id")
    private String proofCatalogId;

    /**
     * 事项编码
     */
    @JsonProperty("item_code")
    private String itemCode;
    /**
     * 事项名称
     */
    @JsonProperty("item_name")
    private String itemName;
    /**
     * 材料id
     */
    @JsonProperty("material_id")
    private String materialId;
    /**
     * 证明目录名称
     */
    @JsonProperty(value = "name")
    private String name;

    /**
     * 证明目录编码
     */
    @JsonProperty(value = "code")
    private String code;
    /**
     * 电子证照列表
     */
    @JsonProperty(value = "license_relation")
    private List<ProofCatalogLicenseRelationDto> catalogLicense;
    /**
     * 承诺书
     */
    @JsonProperty(value = "clerk_commit_relation")
    private List<ProofCatalogClerkCommitmentDto> catalogClerkCommitment;

    /**
     * 数据共享列表
     */
    @JsonProperty(value = "data_shared_relation")
    private List<ProofCatalogDataSharedRelationDto> dataSharedRelation;

    /**
     * 人工协查列表
     */
    @JsonProperty(value = "artificial_relation")
    private List<ProofCatalogArtificialRelationDto> artificialRelationList;
    /**
     * 电子证明列表
     */
    @JsonProperty(value = "license_item_relation")
    private List<ProofCatalogLicenseItemRelationDto> licenseItemRelationList;

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public ProofClearTypeEnum getProofClearType() {
        return proofClearType;
    }

    public void setProofClearType(ProofClearTypeEnum proofClearType) {
        this.proofClearType = proofClearType;
    }

    public String getReplaceCancelWay() {
        return replaceCancelWay;
    }

    public void setReplaceCancelWay(String replaceCancelWay) {
        this.replaceCancelWay = replaceCancelWay;
    }

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getMaterialId() {
        return materialId;
    }

    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<ProofCatalogLicenseRelationDto> getCatalogLicense() {
        return catalogLicense;
    }

    public void setCatalogLicense(List<ProofCatalogLicenseRelationDto> catalogLicense) {
        this.catalogLicense = catalogLicense;
    }

    public List<ProofCatalogClerkCommitmentDto> getCatalogClerkCommitment() {
        return catalogClerkCommitment;
    }

    public void setCatalogClerkCommitment(List<ProofCatalogClerkCommitmentDto> catalogClerkCommitment) {
        this.catalogClerkCommitment = catalogClerkCommitment;
    }

    public List<ProofCatalogDataSharedRelationDto> getDataSharedRelation() {
        return dataSharedRelation;
    }

    public void setDataSharedRelation(List<ProofCatalogDataSharedRelationDto> dataSharedRelation) {
        this.dataSharedRelation = dataSharedRelation;
    }

    public List<ProofCatalogArtificialRelationDto> getArtificialRelationList() {
        return artificialRelationList;
    }

    public void setArtificialRelationList(List<ProofCatalogArtificialRelationDto> artificialRelationList) {
        this.artificialRelationList = artificialRelationList;
    }

    public List<ProofCatalogLicenseItemRelationDto> getLicenseItemRelationList() {
        return licenseItemRelationList;
    }

    public void setLicenseItemRelationList(List<ProofCatalogLicenseItemRelationDto> licenseItemRelationList) {
        this.licenseItemRelationList = licenseItemRelationList;
    }

    @Override
    public String toString() {
        return "CatalogApiBo{" +
                "materialName='" + materialName + '\'' +
                ", proofClearType=" + proofClearType +
                ", replaceCancelWay='" + replaceCancelWay + '\'' +
                ", proofCatalogId='" + proofCatalogId + '\'' +
                ", itemCode='" + itemCode + '\'' +
                ", itemName='" + itemName + '\'' +
                ", materialId='" + materialId + '\'' +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", catalogLicense=" + catalogLicense +
                ", catalogClerkCommitment=" + catalogClerkCommitment +
                ", dataSharedRelation=" + dataSharedRelation +
                ", artificialRelationList=" + artificialRelationList +
                ", licenseItemRelationList=" + licenseItemRelationList +
                '}';
    }
}
