package gov.derate.proof.catalog.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.catalog.enums.AuditRelationStatusEnums;
import gov.derate.proof.common.visitor.Visitor;

/**
 * 审核状态dto
 *
 * <AUTHOR>
 * @date 2024年7月29日.
 */
public class AuditRelationStatusDto<T> {
    private static final long serialVersionUID = 7964418155449687452L;
    /**
     * 审核对象
     */
    @JsonProperty("audit_level")
    private Integer auditLevel;
    /**
     * 审核状态
     */
    @JsonProperty("audit_status")
    private AuditRelationStatusEnums auditStatus;

    /**
     * 增强逻辑接口
     */
    @JsonIgnore
    private Visitor visitor;

    /**
     * 属于该审核对象下的审核对象
     */
    @JsonProperty("sub_audit_obj")
    protected T subAuditObj;

    /**
     * 层级名称
     */
    @JsonProperty("level_desc")
    private String levelDesc;

    public Integer getAuditLevel() {
        return auditLevel;
    }

    public void setAuditLevel(Integer auditLevel) {
        this.auditLevel = auditLevel;
    }

    public AuditRelationStatusEnums getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(AuditRelationStatusEnums auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Visitor getVisitor() {
        return visitor;
    }

    public void setVisitor(Visitor visitor) {
        this.visitor = visitor;
    }


    public T getSubAuditObj() {
        return subAuditObj;
    }

    public void setSubAuditObj(T subAuditObj) {
        this.subAuditObj = subAuditObj;
    }

    public String getLevelDesc() {
        return levelDesc;
    }

    public void setLevelDesc(String levelDesc) {
        this.levelDesc = levelDesc;
    }

    @Override
    public String toString() {
        return "AuditRelationStatusDto{" +
                "auditLevel=" + auditLevel +
                ", auditStatus=" + auditStatus +
                ", visitor=" + visitor +
                ", subAuditObj=" + subAuditObj +
                ", levelDesc='" + levelDesc + '\'' +
                '}';
    }
}
