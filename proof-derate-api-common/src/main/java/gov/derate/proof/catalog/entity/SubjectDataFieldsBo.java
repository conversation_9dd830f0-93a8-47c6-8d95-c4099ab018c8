package gov.derate.proof.catalog.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import gov.derate.proof.catalog.bo.DataSharedDataTypeEnum;
import gov.derate.proof.catalog.bo.ProofCatalogDataSharedConfigDataItemDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * SubjectDataDto
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/29
 * </p>
 *
 * <AUTHOR>
 */
public class SubjectDataFieldsBo implements Serializable {
    /**
     * 证照结构化数据。显示值
     */
    @JsonProperty("display_name")
    private String displayName;
    /**
     * 证照结构化数据。value或者转换值
     */
    @JsonProperty("display_value")
    private String displayValue;

    public SubjectDataFieldsBo() {
    }

    public SubjectDataFieldsBo(String displayName, String displayValue) {
        this.displayName = displayName;
        this.displayValue = displayValue;
    }

    /**
     * 构造显示对象
     *
     * @param displayName      显示名称
     * @param displayValue     设置显示数据
     * @param configDataItemBo 配置对象，对显示数据进行映射或处理
     */
    public SubjectDataFieldsBo(String displayName, String displayValue, ProofCatalogDataSharedConfigDataItemDto configDataItemBo) {
        this.displayName = displayName;
        settingDisplayValue(displayValue, configDataItemBo);
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayValue() {
        return displayValue;
    }

    public void setDisplayValue(String displayValue) {
        this.displayValue = displayValue;
    }

    @Override
    public String toString() {
        return "SubjectDataFieldsBo{" +
                "displayName='" + displayName + '\'' +
                ", displayValue='" + displayValue + '\'' +
                '}';
    }

    /**
     * 设值转换
     *
     * @param dataItemValue    接口值
     * @param configDataItemBo 映射数据配置
     * @return 设值成功=true
     */
    public boolean settingDisplayValue(String dataItemValue, ProofCatalogDataSharedConfigDataItemDto configDataItemBo) {
        if (configDataItemBo.getDataType() == DataSharedDataTypeEnum.ENUM) {
            // 值域映射处理
            String dataRangeMapping = configDataItemBo.getDataRangeMapping();
            if (StringUtils.isBlank(dataRangeMapping)) {
                setDisplayValue(dataItemValue);
            } else {
                List<String> splitValue = Lists.newArrayList(dataRangeMapping.split(";"));
                for (String splitData : splitValue) {
                    if (splitData.contains(dataItemValue)) {
                        List<String> splitDataList = Lists.newArrayList(splitData.split(":"));
                        if (CollectionUtils.isEmpty(splitDataList)) {
                            return false;
                        }
                        if (CollectionUtils.isNotEmpty(splitDataList) && splitDataList.size() >= 2 && StringUtils.equals(dataItemValue, splitDataList.get(0))) {
                            String displayValue = splitDataList.get(1);
                            setDisplayValue(displayValue);
                        }
                    }
                }
            }
        } else {
            setDisplayValue(dataItemValue);
        }
        return true;
    }
}
