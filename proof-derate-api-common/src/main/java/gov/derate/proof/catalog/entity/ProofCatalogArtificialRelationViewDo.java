package gov.derate.proof.catalog.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.enums.ProofProvideTypeEnum;
import gov.license.common.api.entity.BaseDo;
import org.hibernate.annotations.Subselect;
import org.springframework.data.annotation.Immutable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;

/**
 * <p>
 * 证明目录-人工协查关系视图表
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2024年6月11日
 * </p>
 *
 * <AUTHOR>
 */
@Entity
@Immutable
@Subselect(value = "select " +
        "o.id ," +
        "o.CREATOR_ID ," +
        "o.CREATION_TIME ," +
        "o.LAST_MODIFICATOR_ID ," +
        "o.LAST_MODIFICATION_TIME ," +
        "o.NAME ," +
        "o.CODE ," +
        "o.UNIT_TYPE ," +
        "o.INDUSTRY_DEPT_CODE ," +
        "o.INDUSTRY_DEPT_NAME ," +
        "o.ACCOUNT_NAME ," +
        "o.WATER_MARK_ID ," +
        "p.INVESTIGATION_DEPT_NAME , " +
        "p.INVESTIGATION_DEPT_CODE , " +
        "p.NOTE , " +
        "p.DIVISION_CODE , " +
        "p.DIVISION_NAME , " +
        "p.ASSIST_TIME_LIMIT , " +
        "p.ASSIST_TIME_HOUR , " +
        "p.ASSIST_TIME_MINUTE,  " +
        "p.AUDIT_R_TEMP_KEY,  " +
        "q.LICENSE_CODE  " +
        " from TBL_PROOF_CATALOG o left join TBL_R_PROOF_CATALOG_ARTIFICIAL p on o.id = p.PROOF_CATALOG_ID LEFT JOIN TBL_R_PROOF_CATALOG_LICENSE_ITEM q on o.id = q.PROOF_CATALOG_ID ")
public class ProofCatalogArtificialRelationViewDo extends BaseDo {

    /**
     * 证明目录名称
     */
    @Column(name = "NAME")
    @JsonProperty("name")
    private String name;
    /**
     * 证明目录编码
     */
    @Column(name = "CODE")
    @JsonProperty("code")
    private String code;
    /**
     * 证明开具单位类型
     */
    @Column(name = "UNIT_TYPE")
    @JsonProperty("unit_type")
    private ProofProvideTypeEnum unitType;
    /**
     * 所属行业部门编码
     */
    @Column(name = "INDUSTRY_DEPT_CODE")
    @JsonProperty("industry_dept_code")
    private String industryDeptCode;
    /**
     * 所属行业部门名称
     */
    @Column(name = "INDUSTRY_DEPT_NAME")
    @JsonProperty("industry_dept_name")
    private String industryDeptName;
    /**
     * 账号名称
     */
    @Column(name = "ACCOUNT_NAME")
    @JsonProperty("account_name")
    private String accountName;

    /**
     * 水印id
     */
    @Column(name = "WATER_MARK_ID")
    @JsonProperty("water_mark_id")
    private String waterMarkId;

    /**
     * 协查部门名称
     */
    @Column(name = "INVESTIGATION_DEPT_NAME")
    private String investigationDeptName;
    /**
     * 协查部门编码
     */
    @Column(name = "INVESTIGATION_DEPT_CODE")
    private String investigationDeptCode;
    /**
     * 人工协查说明
     */
    @Column(name = "NOTE")
    private String note;
    /**
     * 实施区域代码
     */
    @Column(name = "DIVISION_CODE")
    private String divisionCode;
    /**
     * 实施区域名称
     */
    @Column(name = "DIVISION_NAME")
    private String divisionName;
    /**
     * 协查时限，单位分钟
     */
    @Column(name = "ASSIST_TIME_LIMIT")
    private Integer assistTimeLimit;

    /**
     * 协查时限，单位小时
     */
    @Column(name = "ASSIST_TIME_HOUR")
    private Integer assistTimeHour;
    /**
     * 协查时限，单位分钟
     */
    @Column(name = "ASSIST_TIME_MINUTE")
    private Integer assistTimeMinute;
    /**
     * 审核关系对象唯一键
     */
    @Column(name = "AUDIT_R_TEMP_KEY", nullable = true)
    @Lob
    private String auditRelationTemplateKey;

    /**
     * 证明目录-电子证明-证照目录码
     */
    @Column(name = "LICENSE_CODE")
    private String licenseCode;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public ProofProvideTypeEnum getUnitType() {
        return unitType;
    }

    public void setUnitType(ProofProvideTypeEnum unitType) {
        this.unitType = unitType;
    }

    public String getIndustryDeptCode() {
        return industryDeptCode;
    }

    public void setIndustryDeptCode(String industryDeptCode) {
        this.industryDeptCode = industryDeptCode;
    }

    public String getIndustryDeptName() {
        return industryDeptName;
    }

    public void setIndustryDeptName(String industryDeptName) {
        this.industryDeptName = industryDeptName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getWaterMarkId() {
        return waterMarkId;
    }

    public void setWaterMarkId(String waterMarkId) {
        this.waterMarkId = waterMarkId;
    }

    public String getInvestigationDeptName() {
        return investigationDeptName;
    }

    public void setInvestigationDeptName(String investigationDeptName) {
        this.investigationDeptName = investigationDeptName;
    }

    public String getInvestigationDeptCode() {
        return investigationDeptCode;
    }

    public void setInvestigationDeptCode(String investigationDeptCode) {
        this.investigationDeptCode = investigationDeptCode;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName;
    }

    public Integer getAssistTimeLimit() {
        return assistTimeLimit;
    }

    public void setAssistTimeLimit(Integer assistTimeLimit) {
        this.assistTimeLimit = assistTimeLimit;
    }

    public Integer getAssistTimeHour() {
        return assistTimeHour;
    }

    public void setAssistTimeHour(Integer assistTimeHour) {
        this.assistTimeHour = assistTimeHour;
    }

    public Integer getAssistTimeMinute() {
        return assistTimeMinute;
    }

    public void setAssistTimeMinute(Integer assistTimeMinute) {
        this.assistTimeMinute = assistTimeMinute;
    }

    public String getAuditRelationTemplateKey() {
        return auditRelationTemplateKey;
    }

    public void setAuditRelationTemplateKey(String auditRelationTemplateKey) {
        this.auditRelationTemplateKey = auditRelationTemplateKey;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    @Override
    public String toString() {
        return "ProofCatalogArtificialRelationViewDo{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", unitType=" + unitType +
                ", industryDeptCode='" + industryDeptCode + '\'' +
                ", industryDeptName='" + industryDeptName + '\'' +
                ", accountName='" + accountName + '\'' +
                ", waterMarkId='" + waterMarkId + '\'' +
                ", investigationDeptName='" + investigationDeptName + '\'' +
                ", investigationDeptCode='" + investigationDeptCode + '\'' +
                ", note='" + note + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", divisionName='" + divisionName + '\'' +
                ", assistTimeLimit=" + assistTimeLimit +
                ", assistTimeHour=" + assistTimeHour +
                ", assistTimeMinute=" + assistTimeMinute +
                ", auditRelationTemplateKey='" + auditRelationTemplateKey + '\'' +
                ", licenseCode='" + licenseCode + '\'' +
                '}';
    }
}
