package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.bo.BaseBo;
import gov.derate.proof.common.utils.UserUtils;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 证明目录更变日志表
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021年11月18日
 * </p>
 *
 * <AUTHOR>
 */
public class ProofCatalogChangeLogBo extends BaseBo {
    /**
     * 证明目录ID
     */
    @JsonProperty("proof_catalog_id")
    private String proofCatalogId;
    /**
     * 修改前
     */
    @JsonProperty("change_prefix")
    private String changePrefix;
    /**
     * 修改后
     */
    @JsonProperty("change_post")
    private String changePost;
    /**
     * 操作名称
     * 例如新建目录，编辑目录
     */
    @JsonProperty("operation_name")
    private String operationName;

    /**
     * 账号
     */
    @JsonProperty("account")
    private String account;
    /**
     * 账号名称
     */
    @JsonProperty("account_name")
    private String accountName;

    /**
     * 构造业务数据的存放list
     */
    @JsonIgnore
    private List<ProofCatalogChangeLogBo> changeLogList;

    /**
     * 构造编辑日志对象
     *
     * @param proofCatalogId       proofCatalogId
     * @param lastModificationTime lastModificationTime
     * @param operationName        operationName
     * @return Bo类
     */
    public ProofCatalogChangeLogBo buildCatalogChangeLog(String proofCatalogId, long lastModificationTime, String operationName) {
        ProofCatalogChangeLogBo logBo = new ProofCatalogChangeLogBo();
        logBo.setOperationName(operationName);
        logBo.setProofCatalogId(proofCatalogId);
        logBo.setCreationTime(new Date(lastModificationTime));
        logBo.setLastModificationTime(new Date(lastModificationTime));
        logBo.setAccount(UserUtils.getUserName());
        logBo.setAccountName(UserUtils.getUserName());
        return logBo;
    }

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getChangePrefix() {
        return changePrefix;
    }

    public void setChangePrefix(String changePrefix) {
        this.changePrefix = changePrefix;
    }

    public String getChangePost() {
        return changePost;
    }

    public void setChangePost(String changePost) {
        this.changePost = changePost;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public List<ProofCatalogChangeLogBo> getChangeLogList() {
        return changeLogList;
    }

    public void setChangeLogList(List<ProofCatalogChangeLogBo> changeLogList) {
        this.changeLogList = changeLogList;
    }

    @Override
    public String toString() {
        return "ProofCatalogChangeLogBo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", changePrefix='" + changePrefix + '\'' +
                ", changePost='" + changePost + '\'' +
                ", operationName='" + operationName + '\'' +
                ", account='" + account + '\'' +
                ", accountName='" + accountName + '\'' +
                ", changeLogList=" + changeLogList +
                '}';
    }
}
