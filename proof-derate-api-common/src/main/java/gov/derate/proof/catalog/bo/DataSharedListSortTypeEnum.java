package gov.derate.proof.catalog.bo;

/**
 * <p>
 * 主题显示排序类型
 * 默认排序、自定义排序
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/10/11
 * </p>
 *
 * <AUTHOR>
 */
public enum DataSharedListSortTypeEnum {
    /**
     *
     * 默认排序
     */
    DEFAULT_SORT("默认排序",0),
    /**
     *
     * 自定义排序
     */
    CUSTOM_SORT("自定义排序",1),
    ;

    DataSharedListSortTypeEnum(String cn, int index) {
        this.cn = cn;
        this.index = index;
    }

    /**
     * 排序中文名称
     */
    private final String cn;
    /**
     * 排序下标
     */
    private final int index;
    public String getCn() {
        return cn;
    }

    public int getIndex() {
        return index;
    }
}
