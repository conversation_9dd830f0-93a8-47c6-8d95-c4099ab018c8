package gov.derate.proof.catalog.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import gov.derate.proof.catalog.bo.DataSharedDataTypeEnum;
import gov.derate.proof.catalog.convert.DataSharedDataTypeEnumJsonDeserialize;

import java.io.Serializable;

/**
 * <p>
 * DataItemInfoDto
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/29
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/29；
 */
public class DataItemInfoDto implements Serializable {
    /**
     * 中文名称9
     */
    @JsonProperty("name")
    private String name;
    /**
     * 英文缩写
     */
    @JsonProperty("name_en")
    private String nameEn;
    /**
     * 序号
     */
    @JsonProperty("ordinal")
    private String ordinal;
    /**
     * 数据类型（字符型、日期型、时间戳、整数型、浮点型、布尔型、枚举型、数字型、文本型、二进制）
     */
    @JsonProperty("data_type")
    @JsonDeserialize(using = DataSharedDataTypeEnumJsonDeserialize.class)
    private DataSharedDataTypeEnum dataType;
    /**
     * 长度
     */
    @JsonProperty("length")
    private Integer length;
    /**
     * 精度
     */
    @JsonProperty("precision")
    private Integer precision;
    /**
     * 信息语义
     */
    @JsonProperty("description")
    private String description;
    /**
     * 是否可空
     */
    @JsonProperty("is_allow_null")
    private Boolean allowNull;
    /**
     * 是否业务主键数据项
     */
    @JsonProperty("is_biz_key")
    private Boolean bizKey;
    /**
     * 是否业务日期数据项
     */
    @JsonProperty("is_biz_date")
    private Boolean bizDate;
    /**
     * 是否开放
     */
    @JsonProperty("is_open")
    private Boolean open;
    /**
     * 不开放原因
     */
    @JsonProperty("not_open_reason")
    private String notOpenReason;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(String ordinal) {
        this.ordinal = ordinal;
    }

    public DataSharedDataTypeEnum getDataType() {
        return dataType;
    }

    public void setDataType(DataSharedDataTypeEnum dataType) {
        this.dataType = dataType;
    }

    public int getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    public int getPrecision() {
        return precision;
    }

    public void setPrecision(Integer precision) {
        this.precision = precision;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getAllowNull() {
        return allowNull;
    }

    public void setAllowNull(Boolean allowNull) {
        this.allowNull = allowNull;
    }

    public Boolean getBizKey() {
        return bizKey;
    }

    public void setBizKey(Boolean bizKey) {
        this.bizKey = bizKey;
    }

    public Boolean getBizDate() {
        return bizDate;
    }

    public void setBizDate(Boolean bizDate) {
        this.bizDate = bizDate;
    }

    public Boolean getOpen() {
        return open;
    }

    public void setOpen(Boolean open) {
        this.open = open;
    }

    public String getNotOpenReason() {
        return notOpenReason;
    }

    public void setNotOpenReason(String notOpenReason) {
        this.notOpenReason = notOpenReason;
    }

    @Override
    public String toString() {
        return "DataItemInfoDto{" +
                "name='" + name + '\'' +
                ", nameEn='" + nameEn + '\'' +
                ", ordinal='" + ordinal + '\'' +
                ", dataType='" + dataType + '\'' +
                ", length=" + length +
                ", precision=" + precision +
                ", description='" + description + '\'' +
                ", allowNull=" + allowNull +
                ", bizKey=" + bizKey +
                ", bizDate=" + bizDate +
                ", open=" + open +
                ", notOpenReason='" + notOpenReason + '\'' +
                '}';
    }
}
