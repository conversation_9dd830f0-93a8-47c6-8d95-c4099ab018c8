package gov.derate.proof.catalog.enums;

/**
 * 证明目录-人工协查方式-电子证明开具方式
 * Company: Zsoft
 * CreateDate:2024/10/24
 *
 * <AUTHOR>
 */
public enum IssueProofLicenseWay {
    SYSTEM_GENERATE(0, "系统生成"),
    MANUALLY_GENERATED(1, "人工开具"),

    ;
    private final int index;
    private final String desc;

    IssueProofLicenseWay(int index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    public int getIndex() {
        return index;
    }

    public String getDesc() {
        return desc;
    }
}
