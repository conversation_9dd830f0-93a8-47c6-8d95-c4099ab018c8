package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.bo.BaseBo;

/**
 * <p>
 * 证明目录与清单关系表
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-02
 * </p>
 *
 * <AUTHOR>
 */
public class ProofCatalogItemRelationBo extends BaseBo {

    /**
     * 证明目录ID
     */
    @JsonProperty("proof_catalog_id")
    private String proofCatalogId;

    /**
     * 证明目录ID
     */
    @JsonProperty("proof_list_id")
    private String proofListId;

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getProofListId() {
        return proofListId;
    }

    public void setProofListId(String proofListId) {
        this.proofListId = proofListId;
    }

    @Override
    public String toString() {
        return "ProofCatalogItemRelationBo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofListId='" + proofListId + '\'' +
                '}';
    }
}
