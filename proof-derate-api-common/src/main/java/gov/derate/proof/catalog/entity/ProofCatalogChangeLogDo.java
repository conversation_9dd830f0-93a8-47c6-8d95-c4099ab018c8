package gov.derate.proof.catalog.entity;

import gov.derate.proof.common.entity.AbstractDomainEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <p>
 * 证明目录更变日志表
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-02
 * </p>
 *
 * <AUTHOR>
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_CATALOG_CHANGE_LOG")
public class ProofCatalogChangeLogDo extends AbstractDomainEntity {
    /**
     * 证明目录ID
     */
    @Column(name = "PROOF_CATALOG_ID", length = 50, nullable = false)
    private String proofCatalogId;
    /**
     * 修改前
     */
    @Column(name = "CHANGE_PREFIX", length = 100, nullable = true)
    private String changePrefix;
    /**
     * 修改后
     */
    @Column(name = "CHANGE_POST", length = 100, nullable = true)
    private String changePost;
    /**
     * 操作名称
     * 例如新建目录，编辑目录
     */
    @Column(name = "OPERATION_NAME", length = 100, nullable = false)
    private String operationName;

    /**
     * 账号
     */
    @Column(name = "ACCOUNT", length = 100, nullable = false)
    private String account;
    /**
     * 账号名称
     */
    @Column(name = "ACCOUNT_NAME", length = 200, nullable = false)
    private String accountName;

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getChangePrefix() {
        return changePrefix;
    }

    public void setChangePrefix(String changePrefix) {
        this.changePrefix = changePrefix;
    }

    public String getChangePost() {
        return changePost;
    }

    public void setChangePost(String changePost) {
        this.changePost = changePost;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    @Override
    public String toString() {
        return "ProofCatalogChangeLogDo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", changePrefix='" + changePrefix + '\'' +
                ", changePost='" + changePost + '\'' +
                ", operationName='" + operationName + '\'' +
                ", account='" + account + '\'' +
                ", accountName='" + accountName + '\'' +
                '}';
    }
}
