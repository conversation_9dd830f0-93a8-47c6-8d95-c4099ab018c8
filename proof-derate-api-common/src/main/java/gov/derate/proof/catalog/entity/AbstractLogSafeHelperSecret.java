package gov.derate.proof.catalog.entity;

/**
 * <p>
 * 抽象日志加密父类
 * 定义获取sm4的secret，让子类复现读取方式
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/7/24
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2023/7/24；
 */
public abstract class AbstractLogSafeHelperSecret {
    /**
     * 获取sm4的secret,由子类实现获取secret的方式
     *
     * @return secret
     */
    public abstract String getSm4Secret();
}
