package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.bo.BaseBo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 证明目录更变日志表
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021年11月18日
 * </p>
 *
 * <AUTHOR>
 */
public class ProofCatalogChangeLogViewBo extends BaseBo {
    /**
     * 证明目录ID
     */
    @JsonProperty("proof_catalog_id")
    private String proofCatalogId;
    /**
     * 操作名称
     * 例如新建目录，编辑目录
     */
    @JsonProperty("operation_name")
    private String operationName;

    /**
     * 账号
     */
    @JsonProperty("account")
    private String account;
    /**
     * 账号名称
     */
    @JsonProperty("account_name")
    private String accountName;
    /**
     * 同一个证明目录中，proofCatalogId && lastModifyTime相等，则归集到同一个list中。
     */
    @JsonProperty("change_prefix_and_post_list")
    List<Map<String, String>> changePrefixAndPostList;

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public List<Map<String, String>> getChangePrefixAndPostList() {
        return changePrefixAndPostList;
    }

    public void setChangePrefixAndPostList(List<Map<String, String>> changePrefixAndPostList) {
        this.changePrefixAndPostList = changePrefixAndPostList;
    }

    @Override
    public String toString() {
        return "ProofCatalogChangeLogViewBo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", operationName='" + operationName + '\'' +
                ", account='" + account + '\'' +
                ", accountName='" + accountName + '\'' +
                ", changePrefixAndPostList=" + changePrefixAndPostList +
                '}';
    }
}
