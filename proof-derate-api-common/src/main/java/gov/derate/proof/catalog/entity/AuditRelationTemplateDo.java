package gov.derate.proof.catalog.entity;

import gov.derate.proof.common.entity.AbstractDomainEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

/**
 * 多层审核关系表
 *
 * <AUTHOR>
 * @date 2024年7月29日.
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_R_AUDIT_TEMPLATE")
public class AuditRelationTemplateDo extends AbstractDomainEntity {
    private static final long serialVersionUID = 7964418155449687452L;

    /**
     * 审核层级
     */
    @Column(name = "AUDIT_LEVEL", length = 2, nullable = false)
    private Integer auditLevel;
    /**
     * 审核部门编码
     */
    @Column(name = "AUDIT_ORG_CODE", length = 50, nullable = false)
    private String auditOrgCode;
    /**
     * 审核部门名称
     */
    @Column(name = "AUDIT_ORG_NAME", length = 100, nullable = false)
    private String auditOrgName;
    /**
     * 审核部门行政区划代码
     */
    @Column(name = "AUDIT_DIVI_CODE", length = 100, nullable = false)
    private String auditDivisionCode;

    /**
     * 审核部门行政区划名称
     */
    @Column(name = "AUDIT_DIVI_NAME", length = 100, nullable = false)
    private String auditDivisionName;

    /**
     * 唯一键
     */
    @Column(name = "ONLY_KEY", nullable = false)
    @Lob
    private String onlyKey;

    /**
     * 层级名称
     */
    @Column(name = "LEVEL_DESC", length = 50, nullable = true)
    private String levelDesc;

    public Integer getAuditLevel() {
        return auditLevel;
    }

    public void setAuditLevel(Integer auditLevel) {
        this.auditLevel = auditLevel;
    }

    public String getAuditOrgCode() {
        return auditOrgCode;
    }

    public void setAuditOrgCode(String auditOrgCode) {
        this.auditOrgCode = auditOrgCode;
    }

    public String getAuditOrgName() {
        return auditOrgName;
    }

    public void setAuditOrgName(String auditOrgName) {
        this.auditOrgName = auditOrgName;
    }

    public String getAuditDivisionCode() {
        return auditDivisionCode;
    }

    public void setAuditDivisionCode(String auditDivisionCode) {
        this.auditDivisionCode = auditDivisionCode;
    }

    public String getAuditDivisionName() {
        return auditDivisionName;
    }

    public void setAuditDivisionName(String auditDivisionName) {
        this.auditDivisionName = auditDivisionName;
    }

    public String getOnlyKey() {
        return onlyKey;
    }

    public void setOnlyKey(String onlyKey) {
        this.onlyKey = onlyKey;
    }

    public String getLevelDesc() {
        return levelDesc;
    }

    public void setLevelDesc(String levelDesc) {
        this.levelDesc = levelDesc;
    }

    @Override
    public String toString() {
        return "AuditRelationTemplateDo{" +
                "auditLevel=" + auditLevel +
                ", auditOrgCode='" + auditOrgCode + '\'' +
                ", auditOrgName='" + auditOrgName + '\'' +
                ", auditDivisionCode='" + auditDivisionCode + '\'' +
                ", auditDivisionName='" + auditDivisionName + '\'' +
                ", onlyKey='" + onlyKey + '\'' +
                ", levelDesc='" + levelDesc + '\'' +
                '}';
    }
}
