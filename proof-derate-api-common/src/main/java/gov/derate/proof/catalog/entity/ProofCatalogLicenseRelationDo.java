package gov.derate.proof.catalog.entity;

import gov.derate.proof.common.entity.AbstractDomainEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <p>
 * 证明目录关联的电子证照
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/8/2 15:24
 * </p>
 *
 * <AUTHOR>
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_R_PROOF_CATALOG_LICENSE")
public class ProofCatalogLicenseRelationDo extends AbstractDomainEntity {
    /**
     * 证明目录ID
     */
    @Column(name = "PROOF_CATALOG_ID", length = 50, nullable = false)
    private String proofCatalogId;
    /**
     * 证明目录code
     */
    @Column(name = "PROOF_CATALOG_CODE", length = 50, nullable = false)
    private String proofCatalogCode;
    /**
     * 证照名称
     */
    @Column(name = "LICENSE_NAME", length = 200, nullable = false)
    private String licenseName;
    /**
     * 证照目录码
     */
    @Column(name = "LICENSE_CODE", length = 100, nullable = false)
    private String licenseCode;
    /**
     * 行业部门名称
     */
    @Column(name = "DEPT_NAME", length = 200, nullable = false)
    private String deptName;

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getLicenseName() {
        return licenseName;
    }

    public void setLicenseName(String licenseName) {
        this.licenseName = licenseName;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getProofCatalogCode() {
        return proofCatalogCode;
    }

    public void setProofCatalogCode(String proofCatalogCode) {
        this.proofCatalogCode = proofCatalogCode;
    }

    @Override
    public String toString() {
        return "ProofCatalogLicenseRelationDo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogCode='" + proofCatalogCode + '\'' +
                ", licenseName='" + licenseName + '\'' +
                ", licenseCode='" + licenseCode + '\'' +
                ", deptName='" + deptName + '\'' +
                '}';
    }
}
