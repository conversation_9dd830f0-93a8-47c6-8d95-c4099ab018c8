package gov.derate.proof.catalog.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import gov.license.common.api.entity.BaseDo;

/**
 * 多层审核关系表
 *
 * <AUTHOR>
 * @date 2024年7月29日.
 */
public class AuditRelationTemplateDto extends BaseDo {
    private static final long serialVersionUID = 7964418155449687452L;
    /**
     * 审核层级
     */
    @JsonProperty("audit_level")
    private Integer auditLevel = 1;
    /**
     * 审核部门
     */
    @JsonProperty("audit_org_code")
    private String auditOrgCode;
    /**
     * 审核部门名称
     */
    @JsonProperty("audit_org_name")
    private String auditOrgName;
    /**
     * 审核行政区划
     */
    @JsonProperty("audit_divi_code")
    private String auditDivisionCode;
    /**
     * 审核行政区划名称
     */
    @JsonProperty("audit_divi_name")
    private String auditDivisionName;
    /**
     * 唯一键
     */
    @JsonProperty("only_key")
    @JsonIgnore
    private String onlyKey;

    /**
     * 层级名称
     */
    @JsonProperty("level_desc")
    private String levelDesc;

    public Integer getAuditLevel() {
        return auditLevel;
    }

    public void setAuditLevel(Integer auditLevel) {
        this.auditLevel = auditLevel;
    }

    public String getAuditOrgCode() {
        return auditOrgCode;
    }

    public void setAuditOrgCode(String auditOrgCode) {
        this.auditOrgCode = auditOrgCode;
    }

    public String getAuditOrgName() {
        return auditOrgName;
    }

    public void setAuditOrgName(String auditOrgName) {
        this.auditOrgName = auditOrgName;
    }

    public String getAuditDivisionCode() {
        return auditDivisionCode;
    }

    public void setAuditDivisionCode(String auditDivisionCode) {
        this.auditDivisionCode = auditDivisionCode;
    }

    public String getAuditDivisionName() {
        return auditDivisionName;
    }

    public void setAuditDivisionName(String auditDivisionName) {
        this.auditDivisionName = auditDivisionName;
    }

    public String getOnlyKey() {
        return onlyKey;
    }

    public void setOnlyKey(String onlyKey) {
        this.onlyKey = onlyKey;
    }

    public String getLevelDesc() {
        return levelDesc;
    }

    public void setLevelDesc(String levelDesc) {
        this.levelDesc = levelDesc;
    }

    @Override
    public String toString() {
        return "AuditRelationTemplateDto{" +
                "auditLevel=" + auditLevel +
                ", auditOrgCode='" + auditOrgCode + '\'' +
                ", auditOrgName='" + auditOrgName + '\'' +
                ", auditDivisionCode='" + auditDivisionCode + '\'' +
                ", auditDivisionName='" + auditDivisionName + '\'' +
                ", onlyKey='" + onlyKey + '\'' +
                ", levelDesc='" + levelDesc + '\'' +
                '}';
    }
}
