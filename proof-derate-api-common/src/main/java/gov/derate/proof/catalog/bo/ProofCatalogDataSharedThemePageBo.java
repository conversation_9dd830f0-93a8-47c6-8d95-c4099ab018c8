package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.bo.BaseBo;

/**
 * <p>
 * 数据主题分页
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/11/15
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2021/11/15；
 */
public class ProofCatalogDataSharedThemePageBo extends BaseBo {
    /**
     * 数据主题名称
     */
    @JsonProperty("data_shared_theme_name")
    private String dataSharedThemeName;
    /**
     * 数据主题编码
     */
    @JsonProperty("data_shared_theme_code")
    private String dataSharedThemeCode;
    /**
     * 数据主题部门
     */
    @JsonProperty("data_shared_org")
    private String dataSharedOrg;
    /**
     * 来源信息系统
     */
    @JsonProperty("source_information_system")
    private String sourceInformationSystem;

    public String getDataSharedThemeName() {
        return dataSharedThemeName;
    }

    public void setDataSharedThemeName(String dataSharedThemeName) {
        this.dataSharedThemeName = dataSharedThemeName;
    }

    public String getDataSharedThemeCode() {
        return dataSharedThemeCode;
    }

    public void setDataSharedThemeCode(String dataSharedThemeCode) {
        this.dataSharedThemeCode = dataSharedThemeCode;
    }

    public String getDataSharedOrg() {
        return dataSharedOrg;
    }

    public void setDataSharedOrg(String dataSharedOrg) {
        this.dataSharedOrg = dataSharedOrg;
    }

    public String getSourceInformationSystem() {
        return sourceInformationSystem;
    }

    public void setSourceInformationSystem(String sourceInformationSystem) {
        this.sourceInformationSystem = sourceInformationSystem;
    }

    @Override
    public String toString() {
        return "ProofCatalogDataSharedThemePageBo{" +
                "dataSharedThemeName='" + dataSharedThemeName + '\'' +
                ", dataSharedThemeCode='" + dataSharedThemeCode + '\'' +
                ", dataSharedOrg='" + dataSharedOrg + '\'' +
                ", sourceInformationSystem='" + sourceInformationSystem + '\'' +
                '}';
    }
}
