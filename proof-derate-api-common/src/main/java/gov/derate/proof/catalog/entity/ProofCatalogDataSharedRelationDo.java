package gov.derate.proof.catalog.entity;

import gov.derate.proof.common.entity.AbstractDomainEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

/**
 * <p>
 * 证明目录关联的数据共享
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/8/2 15:11
 * </p>
 *
 * <AUTHOR>
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_R_PROOF_CATALOG_SHARED")
public class ProofCatalogDataSharedRelationDo extends AbstractDomainEntity {
    /**
     * 证明目录ID
     */
    @Column(name = "PROOF_CATALOG_ID", length = 50, nullable = false)
    private String proofCatalogId;
    /**
     * 证明目录code
     */
    @Column(name = "CATALOG_CODE", length = 50, nullable = true)
    private String catalogCode;
    /**
     * 协查部门名称
     */
    @Deprecated
    @Column(name = "INVESTIGATION_DEPT_NAME", length = 200, nullable = true)
    private String investigationDeptName;
    /**
     * 协查部门编码
     */
    @Deprecated
    @Column(name = "INVESTIGATION_DEPT_CODE", length = 100, nullable = true)
    private String investigationDeptCode;
    /**
     * 接口功能说明
     */
    @Deprecated
    @Column(name = "NOTE", length = 300, nullable = true)
    private String note;
    /**
     * 数据参数
     */
    @Deprecated
    @Column(name = "INVESTIGATION_PARAMETER", length = 500, nullable = true)
    private String investigationParameter;
    /**
     * 实施区域代码
     */
    @Column(name = "DIVISION_CODE", length = 100, nullable = true)
    @Deprecated
    private String divisionCode;
    /**
     * 实施区域名称
     */
    @Deprecated
    @Column(name = "DIVISION_NAME", length = 200, nullable = true)
    private String divisionName;

    /**
     * 数据共享系统
     */
    @Column(name = "SYSTEM_CODE", length = 100, nullable = true)
    @Deprecated
    private String systemCode;
    /**
     * 数据共享系统
     */
    @Column(name = "SYSTEM_NAME", length = 200, nullable = true)
    @Deprecated
    private String systemName;

    /**
     * 主题名称
     */
    @Column(name = "DATA_THEME_NAME", length = 200, nullable = true)
    @Deprecated
    private String dataThemeName;
    /**
     * 主题编码
     */
    @Column(name = "DATA_THEME_CODE", length = 50, nullable = true)
    @Deprecated
    private String dataThemeCode;
    /**
     * 数据主题状态，启用，禁用
     */
    @Column(name = "DATA_THEME_STATUS", length = 50, nullable = true)
    @Deprecated
    private Integer dataThemeStatus;

    /**
     * 接口访问地址
     */
    @Column(name = "SYSTEM_API_URL", length = 300, nullable = true)
    @Deprecated
    private String apiUrl;

    /**
     * 配置json
     * 解析类： ProofCatalogDataSharedConfigBo
     */
    @Lob
    @Column(name = "CONFIG_JSON", nullable = true)
    @Deprecated
    private String configJson;
    /**
     * 数据主题配置id
     */
    @Column(name = "DATA_SHARED_CONFIG_ID",length = 50, nullable = true)
    private String dataSharedConfigId;

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getCatalogCode() {
        return catalogCode;
    }

    public void setCatalogCode(String catalogCode) {
        this.catalogCode = catalogCode;
    }

    public String getInvestigationDeptName() {
        return investigationDeptName;
    }

    public void setInvestigationDeptName(String investigationDeptName) {
        this.investigationDeptName = investigationDeptName;
    }

    public String getInvestigationDeptCode() {
        return investigationDeptCode;
    }

    public void setInvestigationDeptCode(String investigationDeptCode) {
        this.investigationDeptCode = investigationDeptCode;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getInvestigationParameter() {
        return investigationParameter;
    }

    public void setInvestigationParameter(String investigationParameter) {
        this.investigationParameter = investigationParameter;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName;
    }

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getDataThemeName() {
        return dataThemeName;
    }

    public void setDataThemeName(String dataThemeName) {
        this.dataThemeName = dataThemeName;
    }

    public String getDataThemeCode() {
        return dataThemeCode;
    }

    public void setDataThemeCode(String dataThemeCode) {
        this.dataThemeCode = dataThemeCode;
    }

    public Integer getDataThemeStatus() {
        return dataThemeStatus;
    }

    public void setDataThemeStatus(Integer dataThemeStatus) {
        this.dataThemeStatus = dataThemeStatus;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getConfigJson() {
        return configJson;
    }

    public void setConfigJson(String configJson) {
        this.configJson = configJson;
    }

    public String getDataSharedConfigId() {
        return dataSharedConfigId;
    }

    public void setDataSharedConfigId(String dataSharedConfigId) {
        this.dataSharedConfigId = dataSharedConfigId;
    }

    @Override
    public String toString() {
        return "ProofCatalogDataSharedRelationDo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", catalogCode='" + catalogCode + '\'' +
                ", investigationDeptName='" + investigationDeptName + '\'' +
                ", investigationDeptCode='" + investigationDeptCode + '\'' +
                ", note='" + note + '\'' +
                ", investigationParameter='" + investigationParameter + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", divisionName='" + divisionName + '\'' +
                ", systemCode='" + systemCode + '\'' +
                ", systemName='" + systemName + '\'' +
                ", dataThemeName='" + dataThemeName + '\'' +
                ", dataThemeCode='" + dataThemeCode + '\'' +
                ", dataThemeStatus=" + dataThemeStatus +
                ", apiUrl='" + apiUrl + '\'' +
                ", configJson='" + configJson + '\'' +
                ", dataSharedConfigId='" + dataSharedConfigId + '\'' +
                '}';
    }
}
