package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.bo.BaseBo;
import gov.derate.proof.common.enums.ProofProvideTypeEnum;

/**
 * <p>
 * ProofCatalogPageBo
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/11/15
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2021/11/15；
 */
public class ProofCatalogPageBo extends BaseBo {
    /**
     * 证明目录名称
     */
    @JsonProperty("name")
    private String name;
    /**
     * 证明目录编码
     */
    @JsonProperty("code")
    private String code;
    /**
     * 证明开具单位类型
     */
    @JsonProperty("unit_type")
    private ProofProvideTypeEnum unitType;
    /**
     * 所属行业部门编码
     */
    @JsonProperty("industry_dept_code")
    private String industryDeptCode;
    /**
     * 所属行业部门名称
     */
    @JsonProperty("industry_dept_name")
    private String industryDeptName;
    /**
     * 水印id
     */
    @JsonProperty("water_mark_id")
    private String waterMarkId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public ProofProvideTypeEnum getUnitType() {
        return unitType;
    }

    public void setUnitType(ProofProvideTypeEnum unitType) {
        this.unitType = unitType;
    }

    public String getIndustryDeptCode() {
        return industryDeptCode;
    }

    public void setIndustryDeptCode(String industryDeptCode) {
        this.industryDeptCode = industryDeptCode;
    }

    public String getIndustryDeptName() {
        return industryDeptName;
    }

    public void setIndustryDeptName(String industryDeptName) {
        this.industryDeptName = industryDeptName;
    }

    public String getWaterMarkId() {
        return waterMarkId;
    }

    public void setWaterMarkId(String waterMarkId) {
        this.waterMarkId = waterMarkId;
    }

    @Override
    public String toString() {
        return "ProofCatalogPageBo{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", unitType=" + unitType +
                ", industryDeptCode='" + industryDeptCode + '\'' +
                ", industryDeptName='" + industryDeptName + '\'' +
                ", waterMarkId='" + waterMarkId + '\'' +
                '}';
    }
}
