package gov.derate.proof.catalog.bo;

import gov.derate.proof.catalog.dto.AuditRelationStatusDto;
import gov.derate.proof.catalog.dto.AuditRelationTemplateDto;
import gov.derate.proof.catalog.enums.AuditRelationStatusEnums;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 多层审核关系表
 *
 * <AUTHOR>
 * @date 2024年7月29日.
 */
public class AuditRelationTemplateBo extends AuditRelationTemplateDto {
    private static final long serialVersionUID = 7964418155449687452L;

    /**
     * 获取按审核层级排序得结构
     *
     * @param relationDtoList 审核层级列表
     * @return 排序后得数据
     */
    public static List<? extends AuditRelationTemplateDto> getRelationDtoSortList(List<? extends AuditRelationTemplateDto> relationDtoList) {
        if (CollectionUtils.isNotEmpty(relationDtoList)) {
            relationDtoList.sort(Comparator.comparing(AuditRelationTemplateDto::getAuditLevel));
        }
        return relationDtoList;
    }

    /**
     * 构造审核层级的审核状态
     *
     * @param relationDtoList 审核层级列表
     * @return 生成的审核状态
     */
    public static List<AuditRelationStatusDto<List<AuditRelationTemplateDto>>> buildAuditStatusList(List<AuditRelationTemplateDto> relationDtoList, AuditRelationStatusEnums statusEnums) {
        List<? extends AuditRelationTemplateDto> auditList = getRelationDtoSortList(relationDtoList);
        Set<Integer> auditLevelSet = auditList.stream().map(AuditRelationTemplateDto::getAuditLevel).collect(Collectors.toSet());
        return auditLevelSet.stream().map(item -> {
            List<AuditRelationTemplateDto> collect = auditList.stream().filter(auditObj -> item.equals(auditObj.getAuditLevel())).collect(Collectors.toList());
            AuditRelationStatusDto<List<AuditRelationTemplateDto>> status = new AuditRelationStatusDto<>();
            status.setAuditLevel(item);
            status.setAuditStatus(statusEnums);
//            status.setVisitor();
            status.setSubAuditObj(collect);
            return status;
        }).collect(Collectors.toList());
    }

}
