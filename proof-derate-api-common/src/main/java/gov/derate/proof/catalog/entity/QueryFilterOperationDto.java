package gov.derate.proof.catalog.entity;


import org.springframework.util.Assert;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * QueryFilterDto
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/29
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/29；
 */
public enum QueryFilterOperationDto implements Serializable {
    /**
     * like 操作符
     */
    LIKE("含有"),
    /**
     * 之间 操作符
     */
    BETWEEN("之间"),
    /**
     *
     */
    EQUAL("等于"),
    CUSTOM("自定义"),
    ;
    /**
     * 操作渲染名称
     */
    private final String displayName;

    public String getDisplayName() {
        return displayName;
    }

    QueryFilterOperationDto(String displayName) {
        this.displayName = displayName;
    }

    /**
     * 根据枚举对接口对象继续宁设值
     *
     * @param qf          数据对象同步设值对象
     * @param firstValue  值1
     * @param secondValue 值2
     * @return 设值成功返回1，否则返回2
     */
    public boolean settingQueryFilterDto(@NotNull QueryFilterDto qf, String firstValue, String secondValue) {
        Assert.notNull(qf, "QueryFilterDto can't be null");
        switch (this) {
            case EQUAL:
                qf.setFirstValue(firstValue);
                return true;
            case LIKE:
                qf.setFirstValue(firstValue);
                return true;
            case BETWEEN:
                qf.setFirstValue(firstValue);
                qf.setSecondValue(secondValue);
                return true;
            default:
                return false;
        }
    }
}
