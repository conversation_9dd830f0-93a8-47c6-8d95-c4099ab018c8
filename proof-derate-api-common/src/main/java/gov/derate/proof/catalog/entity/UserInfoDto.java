package gov.derate.proof.catalog.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * UserInfoDto
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/29
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/29；
 */
public class UserInfoDto implements Serializable {
    /**
     * 账户名称。标识属性。
     */
    @JsonProperty("account")
    private String account;
    /**
     * 用户姓名
     */
    @JsonProperty("name")
    private String name;
    /**
     * 身份证件号码
     */
    @JsonProperty("identity_num")
    private String identityNum;
    /**
     * 行政区划名称。只读属性。
     */
    @JsonProperty("division")
    private String division;
    /**
     * 行政区划代码。只读属性。
     */
    @JsonProperty("division_code")
    private String divisionCode;
    /**
     * 扩展属性。
     */
    @JsonProperty("extend_props")
    private String extendProps;
    /**
     * 创建者
     */
    @JsonProperty("creator")
    private String creator;
    /**
     *
     */
    @JsonProperty("creation_time")
    private Date creationTime;
    /**
     * 最后修改者
     */
    @JsonProperty("last_modificator")
    private String lastModificator;
    /**
     * 最后修改时间
     */
    @JsonProperty("last_modification_time")
    private Date lastModificationTime;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdentityNum() {
        return identityNum;
    }

    public void setIdentityNum(String identityNum) {
        this.identityNum = identityNum;
    }

    public String getDivision() {
        return division;
    }

    public void setDivision(String division) {
        this.division = division;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public String getExtendProps() {
        return extendProps;
    }

    public void setExtendProps(String extendProps) {
        this.extendProps = extendProps;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public String getLastModificator() {
        return lastModificator;
    }

    public void setLastModificator(String lastModificator) {
        this.lastModificator = lastModificator;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    @Override
    public String toString() {
        return "UserInfoDto{" +
                "account='" + account + '\'' +
                ", name='" + name + '\'' +
                ", identityNum='" + identityNum + '\'' +
                ", division='" + division + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", extendProps='" + extendProps + '\'' +
                ", creator='" + creator + '\'' +
                ", creationTime=" + creationTime +
                ", lastModificator='" + lastModificator + '\'' +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
