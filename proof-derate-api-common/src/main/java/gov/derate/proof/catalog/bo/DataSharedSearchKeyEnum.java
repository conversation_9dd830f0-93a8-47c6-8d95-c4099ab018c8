package gov.derate.proof.catalog.bo;


/**
 * <p>
 * 证照数据项数据类型
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022年8月2日
 * </p>
 *
 * <AUTHOR>
 */
public enum DataSharedSearchKeyEnum {
    /**
     * 办事人姓名
     */
    HANDLE_AFFAIRS_NAME("handleAffairsName", "办事人姓名"),
    /**
     * 办事人证件类型
     */
    EXEMPT_IDENTITY_TYPE("identityType", "办事人证件类型"),
    /**
     * 办事人证件号码
     */
    EXEMPT_IDENTITY_NUMBER("identityNumber", "办事人证件号码"),
    /**
     * 办事单位名称
     */
    BIZ_ORG_NAME("bizOrgName", "办事单位名称"),
    /**
     * 办事单位证件类型
     */
    BIZ_ORG_IDENTITY_TYPE("bizOrgIdentityType", "办事单位证件类型"),
    /**
     * 办事单位证件号码
     */
    BIZ_ORG_IDENTITY_NUM("bizOrgIdentityNum", "办事单位证件号码"),
    /**
     * 事项编码
     */
    ITEM_CODE("itemCode", "事项编码"),
    /**
     * 事项名称
     */
    ITEM_NAME("itemName", "事项名称"),
    /**
     * 实施机构
     */
    ITEM_ORG("implOrgName", "实施机构"),
    /**
     * 实施机构统一社会信用代码
     */
    ITEM_CREDIT_CODE("creditCode", "实施机构统一社会信用代码"),
    /**
     * 事项行政区划
     */
    ITEM_DIVISION_CODE("divisionCode", "事项行政区划"),
    /**
     * 自定义
     */
    CUSTOM("", "自定义"),
    ;

    /**
     * 对应映射免证办字段
     * ExemptCertificatesDo表字段
     */
    private final String fieldName;
    /**
     * 描述显示名称
     */
    private final String displayName;

    public String getFieldName() {
        return fieldName;
    }

    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String toString() {
        return "DataSharedSearchKeyEnum{" +
                "fieldName='" + fieldName + '\'' +
                ", displayName='" + displayName + '\'' +
                '}';
    }

    DataSharedSearchKeyEnum(String fieldName, String displayName) {
        this.fieldName = fieldName;
        this.displayName = displayName;
    }
}
