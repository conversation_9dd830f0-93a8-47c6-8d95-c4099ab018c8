package gov.derate.proof.catalog.entity;

import com.google.common.collect.Lists;
import gov.derate.proof.catalog.dto.ProofCatalogLicenseItemImplItemInfoDto;
import gov.derate.proof.catalog.enums.IssueProofLicenseWay;
import gov.derate.proof.common.entity.AbstractDomainEntity;
import gov.license.common.tools.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.List;

/**
 * <p>
 * 证明目录关联的人工协查
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/8/2 15:11
 * </p>
 *
 * <AUTHOR>
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_R_PROOF_CATALOG_ARTIFICIAL")
public class ProofCatalogArtificialRelationDo extends AbstractDomainEntity {
    /**
     * catalog replace way constant
     */
    public static final String ARTIFICIAL = "artificial";
    /**
     * 证明目录ID
     */
    @Column(name = "PROOF_CATALOG_ID", length = 50, nullable = false)
    private String proofCatalogId;
    /**
     * 证明目录code
     */
    @Column(name = "PROOF_CATALOG_CODE", length = 50, nullable = false)
    private String proofCatalogCode;
    /**
     * 协查部门名称
     */
    @Column(name = "INVESTIGATION_DEPT_NAME", length = 200, nullable = true)
    private String investigationDeptName;
    /**
     * 协查部门编码
     */
    @Column(name = "INVESTIGATION_DEPT_CODE", length = 100, nullable = false)
    private String investigationDeptCode;
    /**
     * 人工协查说明
     */
    @Column(name = "NOTE", length = 2000, nullable = false)
    private String note;
    /**
     * 实施区域代码
     */
    @Column(name = "DIVISION_CODE", length = 100, nullable = false)
    private String divisionCode;
    /**
     * 实施区域名称
     */
    @Column(name = "DIVISION_NAME", length = 200, nullable = false)
    private String divisionName;

    /**
     * 审核关系对象唯一键
     */
    @Column(name = "AUDIT_R_TEMP_KEY", nullable = true)
    @Lob
    private String auditRelationTemplateKey;
    /**
     * 协查时限，单位分钟
     */
    @Column(name = "ASSIST_TIME_LIMIT", length = 10, nullable = true)
    private Integer assistTimeLimit;

    /**
     * 协查时限，单位小时
     */
    @Column(name = "ASSIST_TIME_HOUR", length = 10, nullable = true)
    private Integer assistTimeHour;
    /**
     * 协查时限，单位分钟
     */
    @Column(name = "ASSIST_TIME_MINUTE", length = 10, nullable = true)
    private Integer assistTimeMinute;

    /**
     * 是否开具电子证明
     */
    @Column(name = "ISSUE_P_LICENSE", nullable = false)
    private Boolean issueProofLicense;
    /**
     * 电子证明开具方式;系统生成，人工开具
     */
    @Column(name = "ISSUE_P_LICENSE_WAY", length = 50, nullable = true)
    @Enumerated(EnumType.STRING)
    private IssueProofLicenseWay issueProofLicenseWay;

    /**
     * 实施目录信息
     * 解析对象 List<ProofCatalogLicenseItemImplItemInfo>
     */
    @Column(name = "IMPL_ITEM_INFO_JSON", nullable = true)
    @Lob
    private String implementItemInfoJson;

    /**
     * 证照名称
     */
    @Column(name = "LICENSE_NAME", length = 200, nullable = false)
    private String licenseName;

    /**
     * 证照目录码
     */
    @Column(name = "LICENSE_CODE", length = 100, nullable = false)
    private String licenseCode;

    /**
     * 审核关系对象唯一键
     *
     * @return 唯一键
     */
    public String buildAuditRelationTemplateKey() {
        return ARTIFICIAL + this.proofCatalogCode;
    }

    /**
     * 构造目录信息对象
     *
     * @return 实施目录信息对象
     */
    public List<ProofCatalogLicenseItemImplItemInfoDto> buildByImplItemInfoJson() {
        if (StringUtils.isNotBlank(this.implementItemInfoJson)) {
            return JacksonUtil.toList(this.implementItemInfoJson, ProofCatalogLicenseItemImplItemInfoDto.class);
        }
        return Lists.newArrayList();
    }

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getInvestigationDeptName() {
        return investigationDeptName;
    }

    public void setInvestigationDeptName(String investigationDeptName) {
        this.investigationDeptName = investigationDeptName;
    }

    public String getInvestigationDeptCode() {
        return investigationDeptCode;
    }

    public void setInvestigationDeptCode(String investigationDeptCode) {
        this.investigationDeptCode = investigationDeptCode;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName;
    }

    public Integer getAssistTimeLimit() {
        return assistTimeLimit;
    }

    public void setAssistTimeLimit(Integer assistTimeLimit) {
        this.assistTimeLimit = assistTimeLimit;
    }

    public Integer getAssistTimeHour() {
        return assistTimeHour;
    }

    public void setAssistTimeHour(Integer assistTimeHour) {
        this.assistTimeHour = assistTimeHour;
    }

    public Integer getAssistTimeMinute() {
        return assistTimeMinute;
    }

    public void setAssistTimeMinute(Integer assistTimeMinute) {
        this.assistTimeMinute = assistTimeMinute;
    }

    public String getProofCatalogCode() {
        return proofCatalogCode;
    }

    public void setProofCatalogCode(String proofCatalogCode) {
        this.proofCatalogCode = proofCatalogCode;
    }

    public String getAuditRelationTemplateKey() {
        return auditRelationTemplateKey;
    }

    public void setAuditRelationTemplateKey(String auditRelationTemplateKey) {
        this.auditRelationTemplateKey = auditRelationTemplateKey;
    }

    public Boolean getIssueProofLicense() {
        return issueProofLicense;
    }

    public void setIssueProofLicense(Boolean issueProofLicense) {
        this.issueProofLicense = issueProofLicense;
    }

    public IssueProofLicenseWay getIssueProofLicenseWay() {
        return issueProofLicenseWay;
    }

    public void setIssueProofLicenseWay(IssueProofLicenseWay issueProofLicenseWay) {
        this.issueProofLicenseWay = issueProofLicenseWay;
    }

    public String getImplementItemInfoJson() {
        return implementItemInfoJson;
    }

    public void setImplementItemInfoJson(String implementItemInfoJson) {
        this.implementItemInfoJson = implementItemInfoJson;
    }

    public String getLicenseName() {
        return licenseName;
    }

    public void setLicenseName(String licenseName) {
        this.licenseName = licenseName;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    @Override
    public String toString() {
        return "ProofCatalogArtificialRelationDo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogCode='" + proofCatalogCode + '\'' +
                ", investigationDeptName='" + investigationDeptName + '\'' +
                ", investigationDeptCode='" + investigationDeptCode + '\'' +
                ", note='" + note + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", divisionName='" + divisionName + '\'' +
                ", auditRelationTemplateKey='" + auditRelationTemplateKey + '\'' +
                ", assistTimeLimit=" + assistTimeLimit +
                ", assistTimeHour=" + assistTimeHour +
                ", assistTimeMinute=" + assistTimeMinute +
                ", issueProofLicense=" + issueProofLicense +
                ", issueProofLicenseWay=" + issueProofLicenseWay +
                ", implementItemInfoJson='" + implementItemInfoJson + '\'' +
                ", licenseName='" + licenseName + '\'' +
                ", licenseCode='" + licenseCode + '\'' +
                '}';
    }
}
