package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.bo.BaseBo;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 证明目录关联的其它表
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021年11月16日
 * </p>
 *
 * <AUTHOR>
 */
public class ProofCatalogOtherRelationDto extends BaseBo {
    /**
     * 证明目录id
     */
    @JsonProperty("proof_catalog_id")
    private String proofCatalogId;
    /**
     * 证明目录code
     */
    @JsonProperty("proof_catalog_code")
    private String proofCatalogCode;

    /**
     * 其它说明（其他替代取消方式的说明）
     */
    @JsonProperty("other_clear_description")
    @NotBlank(message = "其他-其他说明不能为空")
    private String otherClearDescription;

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getOtherClearDescription() {
        return otherClearDescription;
    }

    public void setOtherClearDescription(String otherClearDescription) {
        this.otherClearDescription = otherClearDescription;
    }

    public String getProofCatalogCode() {
        return proofCatalogCode;
    }

    public void setProofCatalogCode(String proofCatalogCode) {
        this.proofCatalogCode = proofCatalogCode;
    }

    @Override
    public String toString() {
        return "ProofCatalogOtherRelationBo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogCode='" + proofCatalogCode + '\'' +
                ", otherClearDescription='" + otherClearDescription + '\'' +
                '}';
    }
}
