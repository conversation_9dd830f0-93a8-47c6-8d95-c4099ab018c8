package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.bo.BaseBo;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 * 证明目录关联的部门自行调查表
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/8/10 14:42
 * </p>
 *
 * <AUTHOR>
 */
public class ProofCatalogDeptSurveyDto extends BaseBo {
    /**
     * 证明目录id
     */
    @JsonProperty("proof_catalog_id")
    private String proofCatalogId;
    /**
     * 证明目录code
     */
    @JsonProperty("proof_catalog_code")
    private String proofCatalogCode;

    /**
     * 部门名称
     */
    @JsonProperty("dept_name")
    private String deptName;

    /**
     * 部门代码
     */
    @JsonProperty("dept_code")
    private String deptCode;

    /**
     * 自行调查说明 （部门自行调查）
     */
    @NotBlank(message = "自行协查-自行协查说明不能为ongoing")
    @JsonProperty("dept_cancel_description")
    private String deptCancelDescription;

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptCancelDescription() {
        return deptCancelDescription;
    }

    public void setDeptCancelDescription(String deptCancelDescription) {
        this.deptCancelDescription = deptCancelDescription;
    }

    public String getProofCatalogCode() {
        return proofCatalogCode;
    }

    public void setProofCatalogCode(String proofCatalogCode) {
        this.proofCatalogCode = proofCatalogCode;
    }

    @Override
    public String toString() {
        return "ProofCatalogDeptSurveyBo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogCode='" + proofCatalogCode + '\'' +
                ", deptName='" + deptName + '\'' +
                ", deptCode='" + deptCode + '\'' +
                ", deptCancelDescription='" + deptCancelDescription + '\'' +
                '}';
    }
}
