package gov.derate.proof.catalog.entity;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import gov.license.common.crypto.sca.SM4;
import gov.license.common.crypto.sca.SMGenerator;

/**
 * <p>
 * 日志加密类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/7/24
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2023/7/24；
 */
@Component
public class LogSafeHelperImpl extends AbstractLogSafeHelperSecret implements LogSafeHelper, InitializingBean {
    private static final Logger LOGGER = LoggerFactory.getLogger(LogSafeHelperImpl.class);
    /**
     * spring配置获取
     */
    @Autowired
    private Environment environment;

    /**
     * sm4 工具类
     */
    private SM4 sm4;
    /**
     * 秘钥
     */
    private String secret;
    /**
     * 手机号格式正则
     */
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 身份证号码格式正则
     */
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)");

    /**
     * 全部脱敏
     */
    private static final String ALL_MASK = "******";

    /**
     * 邮箱正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile("(^[^@]{2})[^@]*(@.*)$");

    /**
     * 初始化bean后加载的方法
     *
     * @throws Exception 异常抛出
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        String sm4Secret = getSm4Secret();
        this.secret = sm4Secret;
        if (StringUtils.isNotBlank(sm4Secret)) {
            this.sm4 = SMGenerator.sm4(sm4Secret.getBytes());
        }
    }

    @Override
    public String getSm4Secret() {
        return environment.getProperty("platform.secret");
    }

    /**
     * sm4加密
     *
     * @param content 原文字符串
     * @return sm4加密后字符串
     */
    @Override
    public String sign(String content) {
        if (Objects.isNull(this.sm4)) {
            LOGGER.warn("sm4 init error,return content");
            return content;
        }
        return this.sm4.encryptHex(content);
    }

    /**
     * sm4解密
     *
     * @param content 加密字符串
     * @return 解密后的字符串
     */
    @Override
    public String design(String content) {
        if (Objects.isNull(this.sm4)) {
            LOGGER.warn("sm4 init error,return content");
            return content;
        }
        return this.sm4.decryptStr(content);
    }

    /**
     * 手机号脱敏
     *
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    @Override
    public String maskPhone(String phone) {
        if (org.apache.commons.lang3.StringUtils.isBlank(phone)) {
            return phone;
        }
        Matcher m = PHONE_PATTERN.matcher(phone);
        if (!m.matches()) {
            return ALL_MASK;
        }
        return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    /**
     * 身份证号脱敏
     *
     * @param idCard 身份证号
     * @return 脱敏后的身份证号
     */
    @Override
    public String maskIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return idCard;
        }
        Matcher m = ID_CARD_PATTERN.matcher(idCard);
        if (!m.matches()) {
            return ALL_MASK;
        }
        return idCard.length() == 15 ? idCard.replaceAll("(?<=\\w{4})\\w(?=\\w{4})", "*") :
                idCard.replaceAll("(?<=\\w{4})\\w(?=\\w{3})", "*");
    }

    /**
     * 名称脱敏
     *
     * @param name 名称
     * @return 脱敏后的名称
     */
    @Override
    public String maskName(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        if (name.length() <= 2) {
            return StringUtils.overlay(name, "*", 1, name.length());
        }
        String middleStars = StringUtils.repeat("*", name.length() - 2);
        return name.charAt(0) + middleStars + name.charAt(name.length() - 1);
    }

    /**
     * 邮箱脱敏
     *
     * @param email 邮箱脱敏
     * @return 脱敏后的邮箱
     */
    @Override
    public String maskEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return email;
        }

        Matcher m = EMAIL_PATTERN.matcher(email);
        if (!m.find()) {
            return ALL_MASK;
        }
        return m.group(1) + StringUtils.repeat("*", email.indexOf("@") - 2) + m.group(2);
    }

    /**
     * 内容脱敏
     * 保留第一位/最后一位 中间内容均脱敏
     *
     * @param content 原文
     * @return 脱敏后的内容
     */
    @Override
    public String maskExceptFirstAndLast(String content) {
        if (content.length() <= 2) {
            return content.charAt(0) + "*";
        } else {
            String middleMask = StringUtils.repeat("*", content.length() - 2);
            return content.charAt(0) + middleMask + content.substring(content.length() - 1);
        }
    }


}
