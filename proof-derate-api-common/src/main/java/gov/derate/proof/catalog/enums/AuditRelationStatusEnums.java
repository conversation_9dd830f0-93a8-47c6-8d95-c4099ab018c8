package gov.derate.proof.catalog.enums;

/**
 * 审核状态枚举类
 * <p>
 * Company: Zsoft
 * CreateDate:2024/7/29
 *
 * <AUTHOR>
 */
public enum AuditRelationStatusEnums {
    /**
     * 未审核
     */
    NOT_AUDIT("未审核"),
    /**
     * 待审核
     */
    WAIT_FOR_AUDIT("待审核"),
    /**
     * 审核中
     */
    AUDITING("审核中"),
    /**
     * 审核完成
     */
    AUDITED("审核完成"),
    ;
    private final String desc;

    AuditRelationStatusEnums(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
