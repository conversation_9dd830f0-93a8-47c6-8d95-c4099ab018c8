package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;

import gov.derate.proof.common.bo.BaseBo;

/**
 * <p>
 * 证明目录水印List
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/11/15
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2021/11/15；
 */
public class ProofCatalogWaterMarkListBo extends BaseBo {
    /**
     * 证明目录名称
     */
    @JsonProperty("name")
    private String name;
    /**
     * 证明目录编码
     */
    @JsonProperty("code")
    private String code;
    /**
     * 水印id
     */
    @JsonProperty("water_mark_id")
    private String waterMarkId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getWaterMarkId() {
        return waterMarkId;
    }

    public void setWaterMarkId(String waterMarkId) {
        this.waterMarkId = waterMarkId;
    }

    @Override
    public String toString() {
        return "ProofCatalogWaterMarkListBo{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", waterMarkId='" + waterMarkId + '\'' +
                '}';
    }
}
