package gov.derate.proof.catalog.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.entity.AbstractDomainEntity;
import gov.derate.proof.common.enums.ProofProvideTypeEnum;
import gov.derate.proof.common.enums.convert.ProofProvideTypeEnumConvert;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <p>
 * 证明目录
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-02
 * </p>
 *
 * <AUTHOR>
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_PROOF_CATALOG")
public class ProofCatalogDo extends AbstractDomainEntity {

    /**
     * 证明目录名称
     */
    @Column(name = "NAME", length = 200, nullable = false)
    @JsonProperty("name")
    private String name;
    /**
     * 证明目录编码
     */
    @Column(name = "CODE", length = 100, nullable = false)
    @JsonProperty("code")
    private String code;
    /**
     * 证明开具单位类型
     */
    @Column(name = "UNIT_TYPE", length = 2, nullable = true)
    @JsonProperty("unit_type")
    @Convert(converter = ProofProvideTypeEnumConvert.class)
    private ProofProvideTypeEnum unitType;
    /**
     * 所属行业部门编码
     */
    @Column(name = "INDUSTRY_DEPT_CODE", length = 100, nullable = true)
    @JsonProperty("industry_dept_code")
    private String industryDeptCode;
    /**
     * 所属行业部门名称
     */
    @Column(name = "INDUSTRY_DEPT_NAME", length = 200, nullable = true)
    @JsonProperty("industry_dept_name")
    private String industryDeptName;
    /**
     * 账号名称
     */
    @Column(name = "ACCOUNT_NAME", length = 100, nullable = true)
    @JsonProperty("account_name")
    private String accountName;

    /**
     * 水印id
     */
    @Column(name = "WATER_MARK_ID", length = 50, nullable = true)
    @JsonProperty("water_mark_id")
    private String waterMarkId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public ProofProvideTypeEnum getUnitType() {
        return unitType;
    }

    public void setUnitType(ProofProvideTypeEnum unitType) {
        this.unitType = unitType;
    }

    public String getIndustryDeptCode() {
        return industryDeptCode;
    }

    public void setIndustryDeptCode(String industryDeptCode) {
        this.industryDeptCode = industryDeptCode;
    }

    public String getIndustryDeptName() {
        return industryDeptName;
    }

    public void setIndustryDeptName(String industryDeptName) {
        this.industryDeptName = industryDeptName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getWaterMarkId() {
        return waterMarkId;
    }

    public void setWaterMarkId(String waterMarkId) {
        this.waterMarkId = waterMarkId;
    }

    @Override
    public String toString() {
        return "ProofCatalogDo{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", unitType=" + unitType +
                ", industryDeptCode='" + industryDeptCode + '\'' +
                ", industryDeptName='" + industryDeptName + '\'' +
                ", accountName='" + accountName + '\'' +
                ", waterMarkId='" + waterMarkId + '\'' +
                '}';
    }
}
