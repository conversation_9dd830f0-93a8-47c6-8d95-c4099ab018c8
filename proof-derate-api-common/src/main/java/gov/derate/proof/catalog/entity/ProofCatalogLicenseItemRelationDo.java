package gov.derate.proof.catalog.entity;

import com.google.common.collect.Lists;
import gov.derate.proof.catalog.dto.ProofCatalogLicenseItemImplItemInfoDto;
import gov.derate.proof.common.entity.AbstractDomainEntity;
import gov.license.common.tools.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.util.List;

/**
 * 证明目录关联的电子证明
 *
 * <AUTHOR>
 * @date 2023/03/22.
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_R_PROOF_CATALOG_LICENSE_ITEM")
public class ProofCatalogLicenseItemRelationDo extends AbstractDomainEntity {
    private static final long serialVersionUID = 7964418155449687452L;

    /**
     * 证明目录ID
     */
    @Column(name = "PROOF_CATALOG_ID", length = 50, nullable = false)
    private String proofCatalogId;
    /**
     * 证明目录code
     */
    @Column(name = "PROOF_CATALOG_CODE", length = 50, nullable = false)
    private String proofCatalogCode;
    /**
     * 证照名称
     */
    @Column(name = "LICENSE_NAME", length = 200, nullable = false)
    private String licenseName;

    /**
     * 证照目录码
     */
    @Column(name = "LICENSE_CODE", length = 100, nullable = false)
    private String licenseCode;

    /**
     * 行业部门名称
     */
    @Column(name = "DEPT_NAME", length = 200, nullable = true)
    @Deprecated
    private String deptName;
    /**
     * 实施目录信息
     * 解析对象 List<ProofCatalogLicenseItemImplItemInfo>
     */
    @Column(name = "IMPL_ITEM_INFO_JSON", nullable = true)
    @Lob
    private String implementItemInfoJson;
    /**
     * 协查时限，单位小时
     */
    @Column(name = "ASSIST_TIME_HOUR", length = 10, nullable = true)
    private Integer assistTimeHour;

    /**
     * 协查时限，单位分钟
     */
    @Column(name = "ASSIST_TIME_MINUTE", length = 10, nullable = true)
    private Integer assistTimeMinute;

    /**
     * 协查说明
     */
    @Column(name = "ASSIST_DESC", length = 500, nullable = true)
    private String assistDesc;

    /**
     * 构造目录信息对象
     *
     * @return 实施目录信息对象
     */
    public List<ProofCatalogLicenseItemImplItemInfoDto> buildByImplItemInfoJson() {
        if (StringUtils.isNotBlank(this.implementItemInfoJson)) {
            return JacksonUtil.toList(this.implementItemInfoJson, ProofCatalogLicenseItemImplItemInfoDto.class);
        }
        return Lists.newArrayList();
    }

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getLicenseName() {
        return licenseName;
    }

    public void setLicenseName(String licenseName) {
        this.licenseName = licenseName;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getImplementItemInfoJson() {
        return implementItemInfoJson;
    }

    public void setImplementItemInfoJson(String implementItemInfoJson) {
        this.implementItemInfoJson = implementItemInfoJson;
    }

    public String getProofCatalogCode() {
        return proofCatalogCode;
    }

    public void setProofCatalogCode(String proofCatalogCode) {
        this.proofCatalogCode = proofCatalogCode;
    }

    public Integer getAssistTimeHour() {
        return assistTimeHour;
    }

    public void setAssistTimeHour(Integer assistTimeHour) {
        this.assistTimeHour = assistTimeHour;
    }

    public Integer getAssistTimeMinute() {
        return assistTimeMinute;
    }

    public void setAssistTimeMinute(Integer assistTimeMinute) {
        this.assistTimeMinute = assistTimeMinute;
    }

    public String getAssistDesc() {
        return assistDesc;
    }

    public void setAssistDesc(String assistDesc) {
        this.assistDesc = assistDesc;
    }

    @Override
    public String toString() {
        return "ProofCatalogLicenseItemRelationDo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogCode='" + proofCatalogCode + '\'' +
                ", licenseName='" + licenseName + '\'' +
                ", licenseCode='" + licenseCode + '\'' +
                ", deptName='" + deptName + '\'' +
                ", implementItemInfoJson='" + implementItemInfoJson + '\'' +
                ", assistTimeHour=" + assistTimeHour +
                ", assistTimeMinute=" + assistTimeMinute +
                ", assistDesc='" + assistDesc + '\'' +
                '}';
    }
}
