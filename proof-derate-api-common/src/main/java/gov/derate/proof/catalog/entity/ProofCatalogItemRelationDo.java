package gov.derate.proof.catalog.entity;

import gov.derate.proof.common.entity.AbstractDomainEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <p>
 * 证明目录与清单关系表
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-02
 * </p>
 *
 * <AUTHOR>
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_R_CATALOG_LIST")
public class ProofCatalogItemRelationDo extends AbstractDomainEntity {

    /**
     * 证明目录ID
     */
    @Column(name = "PROOF_CATALOG_ID", length = 50, nullable = false)
    private String proofCatalogId;

    /**
     * 证明目录ID
     */
    @Column(name = "PROOF_LIST_ID", length = 50, nullable = false)
    private String proofListId;

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getProofListId() {
        return proofListId;
    }

    public void setProofListId(String proofListId) {
        this.proofListId = proofListId;
    }

    @Override
    public String toString() {
        return "ProofCatalogItemRelationDo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofListId='" + proofListId + '\'' +
                '}';
    }
}
