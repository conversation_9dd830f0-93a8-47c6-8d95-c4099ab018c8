package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.bo.BaseBo;
import gov.derate.proof.common.enums.ProofProvideTypeEnum;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 创建证明目录Bo对象
 * <p>
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/8/2 16:55
 * </p>
 *
 * <AUTHOR>
 */
public class ProofCatalogCreateBo extends BaseBo {

    private static final long serialVersionUID = 1L;
    /**
     * 证明目录id
     */
    @JsonProperty("id")
    private String id;
    /**
     * 证明目录名称
     */
    @NotBlank(message = "证明目录名称不能为空")
    @JsonProperty("name")
    private String name;
    /**
     * 证明目录编码
     */
    @JsonProperty("code")
    private String code;
    /**
     * 证明开具单位类型
     */
    @NotNull(message = "证明开具单位类型不能为空")
    @JsonProperty("unit_type")
    private ProofProvideTypeEnum unitType;
    /**
     * 所属行业部门编码
     */
    @JsonProperty("industry_dept_code")
    private String industryDeptCode;
    /**
     * 所属行业部门名称
     */
    @JsonProperty("industry_dept_name")
    private String industryDeptName;

    /**
     * 证明目录关联的人工协查
     */
    @JsonProperty("proof_catalog_artificial_list")
    private List<ProofCatalogArtificialRelationDto> proofCatalogArtificialList;
    /**
     * 证明目录关联的告知承诺
     */
    @JsonProperty("proof_catalog_clerk_commitment_list")
    private List<ProofCatalogClerkCommitmentDto> proofCatalogClerkCommitmentList;
    /**
     * 证明目录关联的数据共享
     */
    @JsonProperty("proof_catalog_data_shared_list")
    private List<ProofCatalogDataSharedRelationDto> proofCatalogDataSharedList;
    /**
     * 证明目录关联的部门自行调查表
     */
    @JsonProperty("proof_catalog_dept_survey_list")
    private List<ProofCatalogDeptSurveyDto> proofCatalogDeptSurveyList;
    /**
     * 证明目录与清单关系表
     */
    @JsonProperty("proof_catalog_item_relation_list")
    private List<ProofCatalogItemRelationBo> proofCatalogItemRelationList;
    /**
     * 证明目录关联的电子证照
     */
    @JsonProperty("proof_catalog_license_relation_list")
    private List<ProofCatalogLicenseRelationDto> proofCatalogLicenseRelationList;
    /**
     * 证明目录关联的其它表
     */
    @JsonProperty("proof_catalog_other_relation_list")
    private List<ProofCatalogOtherRelationDto> proofCatalogOtherRelationList;

    /**
     * 证明目录关联的电子证明
     */
    @JsonProperty("proof_catalog_license_item_relation")
    private List<ProofCatalogLicenseItemRelationDto> proofCatalogLicenseItemRelation;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public ProofProvideTypeEnum getUnitType() {
        return unitType;
    }

    public void setUnitType(ProofProvideTypeEnum unitType) {
        this.unitType = unitType;
    }

    public String getIndustryDeptCode() {
        return industryDeptCode;
    }

    public void setIndustryDeptCode(String industryDeptCode) {
        this.industryDeptCode = industryDeptCode;
    }

    public String getIndustryDeptName() {
        return industryDeptName;
    }

    public void setIndustryDeptName(String industryDeptName) {
        this.industryDeptName = industryDeptName;
    }

    public List<ProofCatalogArtificialRelationDto> getProofCatalogArtificialList() {
        return proofCatalogArtificialList;
    }

    public void setProofCatalogArtificialList(List<ProofCatalogArtificialRelationDto> proofCatalogArtificialList) {
        this.proofCatalogArtificialList = proofCatalogArtificialList;
    }

    public List<ProofCatalogClerkCommitmentDto> getProofCatalogClerkCommitmentList() {
        return proofCatalogClerkCommitmentList;
    }

    public void setProofCatalogClerkCommitmentList(List<ProofCatalogClerkCommitmentDto> proofCatalogClerkCommitmentList) {
        this.proofCatalogClerkCommitmentList = proofCatalogClerkCommitmentList;
    }

    public List<ProofCatalogDataSharedRelationDto> getProofCatalogDataSharedList() {
        return proofCatalogDataSharedList;
    }

    public void setProofCatalogDataSharedList(List<ProofCatalogDataSharedRelationDto> proofCatalogDataSharedList) {
        this.proofCatalogDataSharedList = proofCatalogDataSharedList;
    }

    public List<ProofCatalogDeptSurveyDto> getProofCatalogDeptSurveyList() {
        return proofCatalogDeptSurveyList;
    }

    public void setProofCatalogDeptSurveyList(List<ProofCatalogDeptSurveyDto> proofCatalogDeptSurveyList) {
        this.proofCatalogDeptSurveyList = proofCatalogDeptSurveyList;
    }

    public List<ProofCatalogItemRelationBo> getProofCatalogItemRelationList() {
        return proofCatalogItemRelationList;
    }

    public void setProofCatalogItemRelationList(List<ProofCatalogItemRelationBo> proofCatalogItemRelationList) {
        this.proofCatalogItemRelationList = proofCatalogItemRelationList;
    }

    public List<ProofCatalogLicenseRelationDto> getProofCatalogLicenseRelationList() {
        return proofCatalogLicenseRelationList;
    }

    public void setProofCatalogLicenseRelationList(List<ProofCatalogLicenseRelationDto> proofCatalogLicenseRelationList) {
        this.proofCatalogLicenseRelationList = proofCatalogLicenseRelationList;
    }

    public List<ProofCatalogOtherRelationDto> getProofCatalogOtherRelationList() {
        return proofCatalogOtherRelationList;
    }

    public void setProofCatalogOtherRelationList(List<ProofCatalogOtherRelationDto> proofCatalogOtherRelationList) {
        this.proofCatalogOtherRelationList = proofCatalogOtherRelationList;
    }

    public List<ProofCatalogLicenseItemRelationDto> getProofCatalogLicenseItemRelation() {
        return proofCatalogLicenseItemRelation;
    }

    public void setProofCatalogLicenseItemRelation(List<ProofCatalogLicenseItemRelationDto> proofCatalogLicenseItemRelation) {
        this.proofCatalogLicenseItemRelation = proofCatalogLicenseItemRelation;
    }

    @Override
    public String toString() {
        return "ProofCatalogCreateBo{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", unitType=" + unitType +
                ", industryDeptCode='" + industryDeptCode + '\'' +
                ", industryDeptName='" + industryDeptName + '\'' +
                ", proofCatalogArtificialList=" + proofCatalogArtificialList +
                ", proofCatalogClerkCommitmentList=" + proofCatalogClerkCommitmentList +
                ", proofCatalogDataSharedList=" + proofCatalogDataSharedList +
                ", proofCatalogDeptSurveyList=" + proofCatalogDeptSurveyList +
                ", proofCatalogItemRelationList=" + proofCatalogItemRelationList +
                ", proofCatalogLicenseRelationList=" + proofCatalogLicenseRelationList +
                ", proofCatalogOtherRelationList=" + proofCatalogOtherRelationList +
                ", proofCatalogLicenseItemRelation=" + proofCatalogLicenseItemRelation +
                '}';
    }
}
