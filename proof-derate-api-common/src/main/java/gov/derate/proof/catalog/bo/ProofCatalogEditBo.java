package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import gov.derate.proof.common.bo.BaseBo;
import gov.derate.proof.common.enums.ProofProvideTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 创建证明目录Bo对象
 * <p>
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/8/2 16:55
 * </p>
 *
 * <AUTHOR>
 */
public class ProofCatalogEditBo extends BaseBo {

    /**
     *
     */
    private static final long serialVersionUID = 1L;
    /**
     * 证明目录id
     */
    @JsonProperty("id")
    private String id;
    /**
     * 证明目录名称
     */
    @NotBlank(message = "证明目录名称不能为空")
    @JsonProperty("name")
    private String name;
    /**
     * 证明目录编码
     */
    @JsonProperty("code")
    private String code;
    /**
     * 证明开具单位类型
     */
    @NotNull(message = "证明开具单位类型不能为空")
    @JsonProperty("unit_type")
    private ProofProvideTypeEnum unitType;
    /**
     * 所属行业部门编码
     */
    @JsonProperty("industry_dept_code")
    private String industryDeptCode;
    /**
     * 所属行业部门名称
     */
    @JsonProperty("industry_dept_name")
    private String industryDeptName;

    /**
     * 证明目录关联的人工协查
     */
    @JsonProperty("proof_catalog_artificial_list")
    private List<ProofCatalogArtificialRelationDto> proofCatalogArtificialList;
    /**
     * 证明目录关联的告知承诺
     */
    @JsonProperty("proof_catalog_clerk_commitment_list")
    private List<ProofCatalogClerkCommitmentDto> proofCatalogClerkCommitmentList;
    /**
     * 证明目录关联的数据共享
     */
    @JsonProperty("proof_catalog_data_shared_list")
    private List<ProofCatalogDataSharedRelationDto> proofCatalogDataSharedList;
    /**
     * 证明目录关联的部门自行调查表
     */
    @JsonProperty("proof_catalog_dept_survey_list")
    private List<ProofCatalogDeptSurveyDto> proofCatalogDeptSurveyList;
    /**
     * 证明目录与清单关系表
     */
    @JsonProperty("proof_catalog_item_relation_list")
    private List<ProofCatalogItemRelationBo> proofCatalogItemRelationList;
    /**
     * 证明目录关联的电子证照
     */
    @JsonProperty("proof_catalog_license_relation_list")
    private List<ProofCatalogLicenseRelationDto> proofCatalogLicenseRelationList;
    /**
     * 证明目录关联的其它表
     */
    @JsonProperty("proof_catalog_other_relation_list")
    private List<ProofCatalogOtherRelationDto> proofCatalogOtherRelationList;

    /**
     * 证明目录关联的电子证明
     */
    @JsonProperty("proof_catalog_license_item_relation")
    private List<ProofCatalogLicenseItemRelationDto> proofCatalogLicenseItemRelation;

    @JsonIgnore
    private List<ProofCatalogChangeLogBo> changeLogBoList;

    @JsonIgnore
    private ProofCatalogEditBo beforeEdit;

    /**
     * 证明目录关联的人工协查
     */
    @JsonIgnore
    private List<ProofCatalogArtificialRelationDto> beforeProofCatalogArtificialList;
    /**
     * 证明目录关联的告知承诺
     */
    @JsonIgnore
    private List<ProofCatalogClerkCommitmentDto> beforeProofCatalogClerkCommitmentList;
    /**
     * 证明目录关联的数据共享
     */
    @JsonIgnore
    private List<ProofCatalogDataSharedRelationDto> beforeProofCatalogDataSharedList;
    /**
     * 证明目录关联的部门自行调查表
     */
    @JsonIgnore
    private List<ProofCatalogDeptSurveyDto> beforeProofCatalogDeptSurveyList;
    /**
     * 证明目录关联的电子证照
     */
    @JsonIgnore
    private List<ProofCatalogLicenseRelationDto> beforeProofCatalogLicenseRelationList;
    /**
     * 证明目录关联的其它表
     */
    @JsonIgnore
    private List<ProofCatalogOtherRelationDto> beforeProofCatalogOtherRelationList;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public ProofProvideTypeEnum getUnitType() {
        return unitType;
    }

    public void setUnitType(ProofProvideTypeEnum unitType) {
        this.unitType = unitType;
    }

    public String getIndustryDeptCode() {
        return industryDeptCode;
    }

    public void setIndustryDeptCode(String industryDeptCode) {
        this.industryDeptCode = industryDeptCode;
    }

    public String getIndustryDeptName() {
        return industryDeptName;
    }

    public void setIndustryDeptName(String industryDeptName) {
        this.industryDeptName = industryDeptName;
    }

    public List<ProofCatalogArtificialRelationDto> getProofCatalogArtificialList() {
        return proofCatalogArtificialList;
    }

    public void setProofCatalogArtificialList(List<ProofCatalogArtificialRelationDto> proofCatalogArtificialList) {
        this.proofCatalogArtificialList = proofCatalogArtificialList;
    }

    public List<ProofCatalogClerkCommitmentDto> getProofCatalogClerkCommitmentList() {
        return proofCatalogClerkCommitmentList;
    }

    public void setProofCatalogClerkCommitmentList(List<ProofCatalogClerkCommitmentDto> proofCatalogClerkCommitmentList) {
        this.proofCatalogClerkCommitmentList = proofCatalogClerkCommitmentList;
    }

    public List<ProofCatalogDataSharedRelationDto> getProofCatalogDataSharedList() {
        return proofCatalogDataSharedList;
    }

    public void setProofCatalogDataSharedList(List<ProofCatalogDataSharedRelationDto> proofCatalogDataSharedList) {
        this.proofCatalogDataSharedList = proofCatalogDataSharedList;
    }

    public List<ProofCatalogDeptSurveyDto> getProofCatalogDeptSurveyList() {
        return proofCatalogDeptSurveyList;
    }

    public void setProofCatalogDeptSurveyList(List<ProofCatalogDeptSurveyDto> proofCatalogDeptSurveyList) {
        this.proofCatalogDeptSurveyList = proofCatalogDeptSurveyList;
    }

    public List<ProofCatalogItemRelationBo> getProofCatalogItemRelationList() {
        return proofCatalogItemRelationList;
    }

    public void setProofCatalogItemRelationList(List<ProofCatalogItemRelationBo> proofCatalogItemRelationList) {
        this.proofCatalogItemRelationList = proofCatalogItemRelationList;
    }

    public List<ProofCatalogLicenseRelationDto> getProofCatalogLicenseRelationList() {
        return proofCatalogLicenseRelationList;
    }

    public void setProofCatalogLicenseRelationList(List<ProofCatalogLicenseRelationDto> proofCatalogLicenseRelationList) {
        this.proofCatalogLicenseRelationList = proofCatalogLicenseRelationList;
    }

    public List<ProofCatalogOtherRelationDto> getProofCatalogOtherRelationList() {
        return proofCatalogOtherRelationList;
    }

    public void setProofCatalogOtherRelationList(List<ProofCatalogOtherRelationDto> proofCatalogOtherRelationList) {
        this.proofCatalogOtherRelationList = proofCatalogOtherRelationList;
    }

    public List<ProofCatalogLicenseItemRelationDto> getProofCatalogLicenseItemRelation() {
        return proofCatalogLicenseItemRelation;
    }

    public void setProofCatalogLicenseItemRelation(List<ProofCatalogLicenseItemRelationDto> proofCatalogLicenseItemRelation) {
        this.proofCatalogLicenseItemRelation = proofCatalogLicenseItemRelation;
    }

    public List<ProofCatalogChangeLogBo> getChangeLogBoList() {
        return changeLogBoList;
    }

    public void setChangeLogBoList(List<ProofCatalogChangeLogBo> changeLogBoList) {
        this.changeLogBoList = changeLogBoList;
    }

    public ProofCatalogEditBo getBeforeEdit() {
        return beforeEdit;
    }

    public void setBeforeEdit(ProofCatalogEditBo beforeEdit) {
        this.beforeEdit = beforeEdit;
    }

    public List<ProofCatalogArtificialRelationDto> getBeforeProofCatalogArtificialList() {
        return beforeProofCatalogArtificialList;
    }

    public void setBeforeProofCatalogArtificialList(List<ProofCatalogArtificialRelationDto> beforeProofCatalogArtificialList) {
        this.beforeProofCatalogArtificialList = beforeProofCatalogArtificialList;
    }

    public List<ProofCatalogClerkCommitmentDto> getBeforeProofCatalogClerkCommitmentList() {
        return beforeProofCatalogClerkCommitmentList;
    }

    public void setBeforeProofCatalogClerkCommitmentList(List<ProofCatalogClerkCommitmentDto> beforeProofCatalogClerkCommitmentList) {
        this.beforeProofCatalogClerkCommitmentList = beforeProofCatalogClerkCommitmentList;
    }

    public List<ProofCatalogDataSharedRelationDto> getBeforeProofCatalogDataSharedList() {
        return beforeProofCatalogDataSharedList;
    }

    public void setBeforeProofCatalogDataSharedList(List<ProofCatalogDataSharedRelationDto> beforeProofCatalogDataSharedList) {
        this.beforeProofCatalogDataSharedList = beforeProofCatalogDataSharedList;
    }

    public List<ProofCatalogDeptSurveyDto> getBeforeProofCatalogDeptSurveyList() {
        return beforeProofCatalogDeptSurveyList;
    }

    public void setBeforeProofCatalogDeptSurveyList(List<ProofCatalogDeptSurveyDto> beforeProofCatalogDeptSurveyList) {
        this.beforeProofCatalogDeptSurveyList = beforeProofCatalogDeptSurveyList;
    }

    public List<ProofCatalogLicenseRelationDto> getBeforeProofCatalogLicenseRelationList() {
        return beforeProofCatalogLicenseRelationList;
    }

    public void setBeforeProofCatalogLicenseRelationList(List<ProofCatalogLicenseRelationDto> beforeProofCatalogLicenseRelationList) {
        this.beforeProofCatalogLicenseRelationList = beforeProofCatalogLicenseRelationList;
    }

    public List<ProofCatalogOtherRelationDto> getBeforeProofCatalogOtherRelationList() {
        return beforeProofCatalogOtherRelationList;
    }

    public void setBeforeProofCatalogOtherRelationList(List<ProofCatalogOtherRelationDto> beforeProofCatalogOtherRelationList) {
        this.beforeProofCatalogOtherRelationList = beforeProofCatalogOtherRelationList;
    }

    @Override
    public String toString() {
        return "ProofCatalogEditBo{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", unitType=" + unitType +
                ", industryDeptCode='" + industryDeptCode + '\'' +
                ", industryDeptName='" + industryDeptName + '\'' +
                ", proofCatalogArtificialList=" + proofCatalogArtificialList +
                ", proofCatalogClerkCommitmentList=" + proofCatalogClerkCommitmentList +
                ", proofCatalogDataSharedList=" + proofCatalogDataSharedList +
                ", proofCatalogDeptSurveyList=" + proofCatalogDeptSurveyList +
                ", proofCatalogItemRelationList=" + proofCatalogItemRelationList +
                ", proofCatalogLicenseRelationList=" + proofCatalogLicenseRelationList +
                ", proofCatalogOtherRelationList=" + proofCatalogOtherRelationList +
                ", proofCatalogLicenseItemRelation=" + proofCatalogLicenseItemRelation +
                ", changeLogBoList=" + changeLogBoList +
                ", beforeEdit=" + beforeEdit +
                ", beforeProofCatalogArtificialList=" + beforeProofCatalogArtificialList +
                ", beforeProofCatalogClerkCommitmentList=" + beforeProofCatalogClerkCommitmentList +
                ", beforeProofCatalogDataSharedList=" + beforeProofCatalogDataSharedList +
                ", beforeProofCatalogDeptSurveyList=" + beforeProofCatalogDeptSurveyList +
                ", beforeProofCatalogLicenseRelationList=" + beforeProofCatalogLicenseRelationList +
                ", beforeProofCatalogOtherRelationList=" + beforeProofCatalogOtherRelationList +
                '}';
    }

    /**
     * 比对更变数据是否不一样。
     *
     * @param name                 更变前数据
     * @param lastModificationTime 更变记录时间
     * @return 有更变，返回true，否则返回false
     */
    @JsonIgnore
    public boolean hasDiffName(String name, long lastModificationTime) {
        initChangeLogBoList();
        if (!StringUtils.equals(this.name, name)) {
            String changeLogOperation = "编辑证明目录";
            ProofCatalogChangeLogBo changeLogBo = new ProofCatalogChangeLogBo();
            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "证明目录名称：", name, this.name);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 初始化更变日志
     */
    @JsonIgnore
    private void initChangeLogBoList() {
        if (CollectionUtils.isEmpty(changeLogBoList)) {
            changeLogBoList = Lists.newArrayList();
        }
    }

    /**
     * 比对更变数据是否不一样。
     *
     * @param proofProvideType     更变前数据
     * @param lastModificationTime 更变记录时间
     * @return 有更变，返回true，否则返回false
     */
    @JsonIgnore
    public boolean hasDiffProofProvideType(ProofProvideTypeEnum proofProvideType, long lastModificationTime) {
        initChangeLogBoList();
        if (this.unitType != proofProvideType) {
            String changeLogOperation = "编辑证明目录";
            ProofCatalogChangeLogBo changeLogBo = new ProofCatalogChangeLogBo();
            ProofCatalogChangeLogBo logDo = changeLogBo.buildCatalogChangeLog(this.id, lastModificationTime, changeLogOperation);
            logDo.setChangePrefix("证明开具单位类型：" + proofProvideType);
            logDo.setChangePost("证明开具单位类型：" + this.unitType);
            changeLogBoList.add(logDo);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 比对更变数据是否不一样。
     *
     * @param industryDeptCode     更变前数据
     * @param lastModificationTime 更变记录时间
     * @return 有更变，返回true，否则返回false
     */
    @JsonIgnore
    public boolean hasDiffIndustryDeptCode(String industryDeptCode, long lastModificationTime) {
        initChangeLogBoList();
        if (!StringUtils.equals(this.industryDeptCode, industryDeptCode)) {
            String changeLogOperation = "编辑证明目录";
            ProofCatalogChangeLogBo changeLogBo = new ProofCatalogChangeLogBo();
            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "所属行业部门编码：", industryDeptCode, this.industryDeptCode);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 比对更变数据是否不一样。
     *
     * @param industryDeptName     更变前数据
     * @param lastModificationTime 更变记录时间
     * @return 有更变，返回true，否则返回false
     */
    @JsonIgnore
    public boolean hasDiffIndustryDeptName(String industryDeptName, long lastModificationTime) {
        initChangeLogBoList();
        if (!StringUtils.equals(this.industryDeptName, industryDeptName)) {
            String changeLogOperation = "编辑证明目录";
            ProofCatalogChangeLogBo changeLogBo = new ProofCatalogChangeLogBo();
            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "所属行业部门名称：", industryDeptName, this.industryDeptName);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 比对更变数据是否不一样。
     *
     * @param proofCatalogArtificialList 更变前数据
     * @param lastModificationTime       更变记录时间
     * @return 有更变，返回true，否则返回false
     */
    @JsonIgnore
    public void hasDiffProofCatalogArtificialList(List<ProofCatalogArtificialRelationDto> proofCatalogArtificialList, long lastModificationTime) {
        initChangeLogBoList();
        String changeLogOperation = "编辑证明目录";

        ProofCatalogChangeLogBo changeLogBo = new ProofCatalogChangeLogBo();
        if (CollectionUtils.isEmpty(proofCatalogArtificialList) && CollectionUtils.isNotEmpty(this.proofCatalogArtificialList)) {
            // 证明目录关联的人工协查 编辑全部为新增。
            this.proofCatalogArtificialList.forEach(after -> {
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-部门名称：", null, after.getInvestigationDeptName());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-部门编码：", null, after.getInvestigationDeptCode());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-说明：", null, after.getNote());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-实施区域代码：", null, after.getDivisionCode());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-实施区域名称：", null, after.getDivisionName());
            });
            return;
        }
        if (CollectionUtils.isNotEmpty(proofCatalogArtificialList) && CollectionUtils.isNotEmpty(this.proofCatalogArtificialList)) {
            // 更变前后都有数据，则区分两种情况，
            // 1. 只要更变后的List的Id，不存在更变前的Id，作为新增处理
            Map<String, ProofCatalogArtificialRelationDto> beforeIdAndBoMap = proofCatalogArtificialList.stream().filter(Objects::nonNull).collect(Collectors.toMap(BaseBo::getId, (v) -> v, (before, after) -> after));
            this.proofCatalogArtificialList.stream()
                    .filter(after -> beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        ProofCatalogArtificialRelationDto before = beforeIdAndBoMap.get(after.getId());
                        if (!StringUtils.equals(after.getInvestigationDeptName(), before.getInvestigationDeptName())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-部门名称：", before.getInvestigationDeptCode(), after.getInvestigationDeptName());
                        }
                        if (!StringUtils.equals(after.getInvestigationDeptCode(), before.getInvestigationDeptCode())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-部门编码：", before.getInvestigationDeptCode(), after.getInvestigationDeptCode());
                        }
                        if (!StringUtils.equals(after.getNote(), before.getNote())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-说明：", before.getNote(), after.getNote());
                        }
                        if (!StringUtils.equals(after.getDivisionCode(), before.getDivisionCode())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-实施区域代码：", before.getDivisionCode(), after.getDivisionCode());
                        }
                        if (!StringUtils.equals(after.getDivisionName(), before.getDivisionName())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-实施区域名称：", before.getDivisionName(), after.getDivisionName());
                        }
                        if (Objects.nonNull(after.getAssistTimeHour()) && after.getAssistTimeHour().equals(before.getAssistTimeHour())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-协查时限小时：", before.getAssistTimeHour(), after.getAssistTimeHour());
                        }
                        if (Objects.nonNull(after.getAssistTimeMinute()) && after.getAssistTimeMinute().equals(before.getAssistTimeMinute())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-协查时限分钟：", before.getAssistTimeMinute(), after.getAssistTimeMinute());
                        }
                    });
            // 2. 除开1的，比对id相同的数据，是否一致，不一致则进行记录。
            this.proofCatalogArtificialList.stream()
                    .filter(after -> !beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-部门名称：", null, after.getInvestigationDeptName());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-部门编码：", null, after.getInvestigationDeptCode());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-说明：", null, after.getNote());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-实施区域代码：", null, after.getDivisionCode());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-实施区域名称：", null, after.getDivisionName());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-协查时限小时：", null, after.getAssistTimeHour());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "人工协查-协查时限分钟：", null, after.getAssistTimeMinute());
                    });
        }
    }

    /**
     * 构造更变记录，并且添加到保存数组中。
     *
     * @param lastModificationTime 统一的更新时间
     * @param changeLogOperation   操作描述，新增描述或者修改描述
     * @param changeLogBo          bo实例
     * @param operationDescribe    更变描述，例如 更变描述：+更变前的值
     * @param beforeValue          更变前的值
     * @param afterValue           更变后的值
     */
    private void appendChangeLog(long lastModificationTime, String changeLogOperation, ProofCatalogChangeLogBo changeLogBo, String operationDescribe, Object beforeValue, Object afterValue) {
        ProofCatalogChangeLogBo logDo = changeLogBo.buildCatalogChangeLog(this.id, lastModificationTime, changeLogOperation);
        logDo.setChangePrefix(operationDescribe + (Objects.isNull(beforeValue) ? "" : beforeValue));
        logDo.setChangePost(operationDescribe + afterValue);
        changeLogBoList.add(logDo);
    }

    /**
     * 比对更变数据是否不一样。
     *
     * @param proofCatalogClerkCommitmentList 更变前数据
     * @param lastModificationTime            更变记录时间
     */
    @JsonIgnore
    public void hasDiffProofCatalogClerkCommitmentList(List<ProofCatalogClerkCommitmentDto> proofCatalogClerkCommitmentList, long lastModificationTime) {
        initChangeLogBoList();
        String changeLogOperation = "编辑证明目录";

        ProofCatalogChangeLogBo changeLogBo = new ProofCatalogChangeLogBo();
        if (CollectionUtils.isEmpty(proofCatalogClerkCommitmentList) && CollectionUtils.isNotEmpty(this.proofCatalogClerkCommitmentList)) {
            // 证明目录关联的人工协查 编辑全部为新增。
            this.proofCatalogClerkCommitmentList.forEach(after -> {
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "告知承诺-承诺书说明：", null, after.getCommitBookDescription());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "告知承诺-承诺书模板附件ID：", null, after.getCommitAttachmentId());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "告知承诺-承诺书模板文件名称：", null, after.getCommitAttachmentName());
            });
            return;
        }
        if (CollectionUtils.isNotEmpty(proofCatalogClerkCommitmentList) && CollectionUtils.isNotEmpty(this.proofCatalogClerkCommitmentList)) {
            // 更变前后都有数据，则区分两种情况，
            // 1. 只要更变后的List的Id，不存在更变前的Id，作为新增处理
            Map<String, ProofCatalogClerkCommitmentDto> beforeIdAndBoMap = proofCatalogClerkCommitmentList.stream().filter(Objects::nonNull).collect(Collectors.toMap(BaseBo::getId, (v) -> v, (before, after) -> after));
            this.proofCatalogClerkCommitmentList.stream()
                    .filter(after -> beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        ProofCatalogClerkCommitmentDto before = beforeIdAndBoMap.get(after.getId());
                        if (!StringUtils.equals(after.getCommitBookDescription(), before.getCommitBookDescription())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "告知承诺-承诺书说明：", before.getCommitBookDescription(), after.getCommitBookDescription());
                        }
                        if (!StringUtils.equals(after.getCommitAttachmentId(), before.getCommitAttachmentId())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "告知承诺-承诺书模板附件ID：", before.getCommitAttachmentId(), after.getCommitAttachmentId());
                        }
                        if (!StringUtils.equals(after.getCommitAttachmentName(), before.getCommitAttachmentName())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "告知承诺-承诺书模板文件名称：", before.getCommitAttachmentName(), after.getCommitAttachmentName());
                        }
                    });
            // 2. 除开1的，比对id相同的数据，是否一致，不一致则进行记录。
            this.proofCatalogClerkCommitmentList.stream()
                    .filter(after -> !beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "告知承诺-承诺书说明：", null, after.getCommitBookDescription());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "告知承诺-承诺书模板附件ID：", null, after.getCommitAttachmentId());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "告知承诺-承诺书模板文件名称：", null, after.getCommitAttachmentName());
                    });
        }
    }

    /**
     * 比对更变数据是否不一样。
     *
     * @param proofCatalogDataSharedList 更变前数据
     * @param lastModificationTime       更变记录时间
     */
    @JsonIgnore
    public void hasDiffProofCatalogDataSharedList(List<ProofCatalogDataSharedRelationDto> proofCatalogDataSharedList, long lastModificationTime) {
        initChangeLogBoList();
        String changeLogOperation = "编辑证明目录";

        ProofCatalogChangeLogBo changeLogBo = new ProofCatalogChangeLogBo();
        if (CollectionUtils.isEmpty(proofCatalogDataSharedList) && CollectionUtils.isNotEmpty(this.proofCatalogDataSharedList)) {
            // 证明目录关联的人工协查 编辑全部为新增。
            this.proofCatalogDataSharedList.forEach(after -> {
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-协查部门名称：", null, after.getInvestigationDeptName());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-协查部门编码：", null, after.getInvestigationDeptCode());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-数据协查说明：", null, after.getNote());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-数据参数：", null, after.getInvestigationParameter());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-实施区域代码：", null, after.getDivisionCode());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-实施区域名称：", null, after.getDivisionName());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-实施区域名称：", null, after.getSystemCode());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-实施区域名称：", null, after.getSystemName());
            });
            return;
        }
        if (CollectionUtils.isNotEmpty(proofCatalogDataSharedList) && CollectionUtils.isNotEmpty(this.proofCatalogDataSharedList)) {
            // 更变前后都有数据，则区分两种情况，
            // 1. 只要更变后的List的Id，不存在更变前的Id，作为新增处理
            Map<String, ProofCatalogDataSharedRelationDto> beforeIdAndBoMap = proofCatalogDataSharedList.stream().filter(Objects::nonNull).collect(Collectors.toMap(BaseBo::getId, (v) -> v, (before, after) -> after));
            this.proofCatalogDataSharedList.stream()
                    .filter(after -> beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        ProofCatalogDataSharedRelationDto before = beforeIdAndBoMap.get(after.getId());
                        if (!StringUtils.equals(after.getInvestigationDeptName(), before.getInvestigationDeptName())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-协查部门名称：", before.getInvestigationDeptName(), after.getInvestigationDeptName());
                        }
                        if (!StringUtils.equals(after.getInvestigationDeptCode(), before.getInvestigationDeptCode())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-协查部门编码：", before.getInvestigationDeptCode(), after.getInvestigationDeptCode());
                        }
                        if (!StringUtils.equals(after.getNote(), before.getNote())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-数据协查说明：", before.getNote(), after.getNote());
                        }
                        if (!StringUtils.equals(after.getInvestigationParameter(), before.getInvestigationParameter())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-数据参数：", before.getInvestigationParameter(), after.getInvestigationParameter());
                        }
                        if (!StringUtils.equals(after.getDivisionCode(), before.getDivisionCode())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-实施区域代码：", before.getDivisionCode(), after.getDivisionCode());
                        }
                        if (!StringUtils.equals(after.getDivisionName(), before.getDivisionName())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-实施区域名称：", before.getDivisionName(), after.getDivisionName());
                        }
                    });
            // 2. 除开1的，比对id相同的数据，是否一致，不一致则进行记录。
            this.proofCatalogDataSharedList.stream()
                    .filter(after -> !beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-协查部门名称：", null, after.getInvestigationDeptName());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-协查部门编码：", null, after.getInvestigationDeptCode());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-数据协查说明：", null, after.getNote());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-数据参数：", null, after.getInvestigationParameter());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-实施区域代码：", null, after.getDivisionCode());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "数据共享-实施区域名称：", null, after.getDivisionName());
                    });
        }
    }

    /**
     * 比对更变数据是否不一样。
     *
     * @param proofCatalogDeptSurveyList 更变前数据
     * @param lastModificationTime       更变记录时间
     */
    @JsonIgnore
    public void hasDiffProofCatalogDeptSurveyList(List<ProofCatalogDeptSurveyDto> proofCatalogDeptSurveyList, long lastModificationTime) {
        initChangeLogBoList();
        String changeLogOperation = "编辑证明目录";

        ProofCatalogChangeLogBo changeLogBo = new ProofCatalogChangeLogBo();
        if (CollectionUtils.isEmpty(proofCatalogDeptSurveyList) && CollectionUtils.isNotEmpty(this.proofCatalogDeptSurveyList)) {
            // 证明目录关联的人工协查 编辑全部为新增。
            this.proofCatalogDeptSurveyList.forEach(after -> {
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "部门自行调查-自行调查说明 （部门自行调查）：", null, after.getDeptCancelDescription());
            });
            return;
        }
        if (CollectionUtils.isNotEmpty(proofCatalogDeptSurveyList) && CollectionUtils.isNotEmpty(this.proofCatalogDeptSurveyList)) {
            // 更变前后都有数据，则区分两种情况，
            // 1. 只要更变后的List的Id，不存在更变前的Id，作为新增处理
            Map<String, ProofCatalogDeptSurveyDto> beforeIdAndBoMap = proofCatalogDeptSurveyList.stream().filter(Objects::nonNull).collect(Collectors.toMap(BaseBo::getId, (v) -> v, (before, after) -> after));
            this.proofCatalogDeptSurveyList.stream()
                    .filter(after -> beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        ProofCatalogDeptSurveyDto before = beforeIdAndBoMap.get(after.getId());
                        if (!StringUtils.equals(after.getDeptCancelDescription(), before.getDeptCancelDescription())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "部门自行调查-自行调查说明 （部门自行调查）：", before.getDeptCancelDescription(), after.getDeptCancelDescription());
                        }
                    });
            // 2. 除开1的，比对id相同的数据，是否一致，不一致则进行记录。
            this.proofCatalogDeptSurveyList.stream()
                    .filter(after -> !beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "部门自行调查-自行调查说明 （部门自行调查）：", null, after.getDeptCancelDescription());
                    });
        }
    }

    /**
     * 比对更变数据是否不一样。
     *
     * @param proofCatalogLicenseRelationList 更变前数据
     * @param lastModificationTime            更变记录时间
     */
    @JsonIgnore
    public void hasDiffProofCatalogLicenseRelationList(List<ProofCatalogLicenseRelationDto> proofCatalogLicenseRelationList, long lastModificationTime) {
        initChangeLogBoList();
        String changeLogOperation = "编辑证明目录";

        ProofCatalogChangeLogBo changeLogBo = new ProofCatalogChangeLogBo();
        if (CollectionUtils.isEmpty(proofCatalogLicenseRelationList) && CollectionUtils.isNotEmpty(this.proofCatalogLicenseRelationList)) {
            // 证明目录关联的人工协查 编辑全部为新增。
            this.proofCatalogLicenseRelationList.forEach(after -> {
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证照-证照名称：", null, after.getLicenseName());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证照-证照编码：", null, after.getLicenseCode());
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证照-行业部门名称：", null, after.getDeptName());
            });
            return;
        }
        if (CollectionUtils.isNotEmpty(proofCatalogLicenseRelationList) && CollectionUtils.isNotEmpty(this.proofCatalogLicenseRelationList)) {
            // 更变前后都有数据，则区分两种情况，
            // 1. 只要更变后的List的Id，不存在更变前的Id，作为新增处理
            Map<String, ProofCatalogLicenseRelationDto> beforeIdAndBoMap = proofCatalogLicenseRelationList.stream().filter(Objects::nonNull).collect(Collectors.toMap(BaseBo::getId, (v) -> v, (before, after) -> after));
            this.proofCatalogLicenseRelationList.stream()
                    .filter(after -> beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        ProofCatalogLicenseRelationDto before = beforeIdAndBoMap.get(after.getId());
                        if (!StringUtils.equals(after.getLicenseName(), before.getLicenseName())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证照-证照名称：", null, after.getLicenseName());
                        }
                        if (!StringUtils.equals(after.getLicenseCode(), before.getLicenseCode())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证照-证照编码：", null, after.getLicenseCode());
                        }
                        if (!StringUtils.equals(after.getDeptName(), before.getDeptName())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证照-行业部门名称：", null, after.getDeptName());
                        }
                    });
            // 2. 除开1的，比对id相同的数据，是否一致，不一致则进行记录。
            this.proofCatalogLicenseRelationList.stream()
                    .filter(after -> !beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证照-证照名称：", null, after.getLicenseName());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证照-证照编码：", null, after.getLicenseCode());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证照-行业部门名称：", null, after.getDeptName());
                    });
        }
    }

    /**
     * 比对更变数据是否不一样。
     *
     * @param beforeLicenseItemRelation 变更之前的关联电子证明
     * @param lastModificationTime      更变记录时间
     */
    @JsonIgnore
    public void hasDiffProofCatalogLicenseItemRelation(List<ProofCatalogLicenseItemRelationDto> beforeLicenseItemRelation,
                                                       long lastModificationTime) {
        initChangeLogBoList();
        String changeLogOperation = "编辑证明目录";

        ProofCatalogChangeLogBo changeLogBo = new ProofCatalogChangeLogBo();
        // 更变前无数据
        if (CollectionUtils.isEmpty(beforeLicenseItemRelation) && CollectionUtils.isNotEmpty(proofCatalogLicenseItemRelation)) {
            proofCatalogLicenseItemRelation.stream()
                    .filter(Objects::nonNull)
                    .forEach(item -> {
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证明-证照名称：",
                                null, item.getLicenseName());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证明-证照编码：",
                                null, item.getLicenseCode());
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证明-行业部门名称：",
                                null, item.getDeptName());
                    });
            return;
        }
        // 更变前后都有数据
        if (CollectionUtils.isNotEmpty(beforeLicenseItemRelation) && CollectionUtils.isNotEmpty(proofCatalogLicenseItemRelation)) {
            // 更变前后都有数据，则区分两种情况，
            // 1. 只要更变后的List的Id，不存在更变前的Id，作为新增处理
            Map<String, ProofCatalogLicenseItemRelationDto> beforeIdAndBoMap = beforeLicenseItemRelation.stream().filter(Objects::nonNull).collect(Collectors.toMap(BaseBo::getId, (v) -> v, (before, after) -> after));
            proofCatalogLicenseItemRelation.stream()
                    .filter(after -> beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        ProofCatalogLicenseItemRelationDto before = beforeIdAndBoMap.get(after.getId());
                        if (!StringUtils.equals(before.getLicenseName(), after.getLicenseName())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证明-证照名称：",
                                    before.getLicenseName(), after.getLicenseName());
                        }
                        if (!StringUtils.equals(before.getLicenseCode(), after.getLicenseCode())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证明-证照编码：",
                                    before.getLicenseCode(), after.getLicenseCode());
                        }
                        if (!StringUtils.equals(before.getDeptName(), after.getDeptName())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证明-行业部门名称：",
                                    before.getDeptName(), after.getDeptName());
                        }
                    });
        }
    }

    /**
     * 比对更变数据是否不一样。
     *
     * @param proofCatalogOtherRelationList 更变前数据
     * @param lastModificationTime          更变记录时间
     */
    @JsonIgnore
    public void hasDiffProofCatalogOtherRelationList(List<ProofCatalogOtherRelationDto> proofCatalogOtherRelationList, long lastModificationTime) {
        initChangeLogBoList();
        String changeLogOperation = "编辑证明目录";

        ProofCatalogChangeLogBo changeLogBo = new ProofCatalogChangeLogBo();
        if (CollectionUtils.isEmpty(proofCatalogOtherRelationList) && CollectionUtils.isNotEmpty(this.proofCatalogOtherRelationList)) {
            // 证明目录关联的人工协查 编辑全部为新增。
            this.proofCatalogOtherRelationList.forEach(after -> {
                appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联其它-其它说明：", null, after.getOtherClearDescription());
            });
            return;
        }
        if (CollectionUtils.isNotEmpty(proofCatalogOtherRelationList) && CollectionUtils.isNotEmpty(this.proofCatalogOtherRelationList)) {
            // 更变前后都有数据，则区分两种情况，
            // 1. 只要更变后的List的Id，不存在更变前的Id，作为新增处理
            Map<String, ProofCatalogOtherRelationDto> beforeIdAndBoMap = proofCatalogOtherRelationList.stream().filter(Objects::nonNull).collect(Collectors.toMap(BaseBo::getId, (v) -> v, (before, after) -> after));
            this.proofCatalogOtherRelationList.stream()
                    .filter(after -> beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        ProofCatalogOtherRelationDto before = beforeIdAndBoMap.get(after.getId());
                        if (!StringUtils.equals(after.getOtherClearDescription(), before.getOtherClearDescription())) {
                            appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联电子证照-证照名称：", before.getOtherClearDescription(), after.getOtherClearDescription());
                        }
                    });
            // 2. 除开1的，比对id相同的数据，是否一致，不一致则进行记录。
            this.proofCatalogOtherRelationList.stream()
                    .filter(after -> !beforeIdAndBoMap.containsKey(after.getId()))
                    .forEach(after -> {
                        appendChangeLog(lastModificationTime, changeLogOperation, changeLogBo, "关联其它-其它说明：", null, after.getOtherClearDescription());
                    });
        }
    }
}
