package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.catalog.entity.ProofCatalogDataSharedConfigDo;
import gov.derate.proof.common.bo.BaseBo;
import gov.derate.proof.common.utils.BeanCopyUtils;
import org.apache.commons.collections4.CollectionUtils;
import gov.license.common.tools.jackson.JacksonUtil;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 数据共享配置
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/28；
 */
public class ProofCatalogDataSharedConfigManagerDto extends BaseBo {
    /**
     * 系统名称
     */
    @JsonProperty("system_name")
    private String systemName;
    /**
     * 系统编码
     */
    @JsonProperty("system_code")
    private String systemCode;
    /**
     * 数据主题名称
     */
    @JsonProperty("data_theme_name")
    private String dataThemeName;
    /**
     * 数据主题编码
     */
    @JsonProperty("data_theme_code")
    private String dataThemeCode;
    /**
     * 接口访问地址
     * 例如： http://{api_root}/subject/{subject_name}/list
     */
    @JsonProperty("system_api_url")
    private String apiUrl;
    /**
     * 数据项
     * ProofCatalogDataSharedConfigDataItemBo
     */
    @JsonProperty("data_item_list")
    private List<ProofCatalogDataSharedConfigDataItemDto> dataItemList;
    /**
     * 查询条件
     * ProofCatalogDataSharedConfigSearchConditionBo
     */
    @JsonProperty("search_condition_list")
    private List<ProofCatalogDataSharedConfigSearchConditionDto> searchConditionList;
    /**
     * 状态：启用/禁用
     */
    @JsonProperty("data_theme_status")
    private DataSharedStatusEnum dataThemeStatus;
    /**
     * 主题显示排序类型
     * 默认排序、自定义排序
     */
    @JsonProperty("list_sort_type")
    private DataSharedListSortTypeEnum listSortType;

    /**
     * 排序数据项
     */
    @JsonProperty("list_sort_type_data_item_name")
    private String listSortTypeDataItemName;
    /**
     * 主题显示排序规则
     * 自定义排序该字段才有值
     */
    @JsonProperty("list_sort_rule")
    private DataSharedListSortRuleEnum listSortRule;

    /**
     * 主题查询次数限制
     */
    @JsonProperty("search_limit_count")
    private Integer searchLimitCount;

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public String getDataThemeName() {
        return dataThemeName;
    }

    public void setDataThemeName(String dataThemeName) {
        this.dataThemeName = dataThemeName;
    }

    public String getDataThemeCode() {
        return dataThemeCode;
    }

    public void setDataThemeCode(String dataThemeCode) {
        this.dataThemeCode = dataThemeCode;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public List<ProofCatalogDataSharedConfigDataItemDto> getDataItemList() {
        return dataItemList;
    }

    public void setDataItemList(List<ProofCatalogDataSharedConfigDataItemDto> dataItemList) {
        this.dataItemList = dataItemList;
    }

    public List<ProofCatalogDataSharedConfigSearchConditionDto> getSearchConditionList() {
        return searchConditionList;
    }

    public void setSearchConditionList(List<ProofCatalogDataSharedConfigSearchConditionDto> searchConditionList) {
        this.searchConditionList = searchConditionList;
    }

    public DataSharedStatusEnum getDataThemeStatus() {
        return dataThemeStatus;
    }

    public void setDataThemeStatus(DataSharedStatusEnum dataThemeStatus) {
        this.dataThemeStatus = dataThemeStatus;
    }

    public DataSharedListSortTypeEnum getListSortType() {
        return listSortType;
    }

    public void setListSortType(DataSharedListSortTypeEnum listSortType) {
        this.listSortType = listSortType;
    }

    public String getListSortTypeDataItemName() {
        return listSortTypeDataItemName;
    }

    public void setListSortTypeDataItemName(String listSortTypeDataItemName) {
        this.listSortTypeDataItemName = listSortTypeDataItemName;
    }

    public DataSharedListSortRuleEnum getListSortRule() {
        return listSortRule;
    }

    public void setListSortRule(DataSharedListSortRuleEnum listSortRule) {
        this.listSortRule = listSortRule;
    }

    public Integer getSearchLimitCount() {
        return searchLimitCount;
    }

    public void setSearchLimitCount(Integer searchLimitCount) {
        this.searchLimitCount = searchLimitCount;
    }

    @Override
    public String toString() {
        return "ProofCatalogDataSharedConfigManagerBo{" +
                "systemName='" + systemName + '\'' +
                ", systemCode='" + systemCode + '\'' +
                ", dataThemeName='" + dataThemeName + '\'' +
                ", dataThemeCode='" + dataThemeCode + '\'' +
                ", apiUrl='" + apiUrl + '\'' +
                ", dataItemList=" + dataItemList +
                ", searchConditionList=" + searchConditionList +
                ", dataThemeStatus=" + dataThemeStatus +
                ", listSortType=" + listSortType +
                ", listSortTypeDataItemName='" + listSortTypeDataItemName + '\'' +
                ", listSortRule=" + listSortRule +
                ", searchLimitCount=" + searchLimitCount +
                '}';
    }

    /**
     * 数据转换
     *
     * @param entity entity
     * @return 数据
     */
    public static ProofCatalogDataSharedConfigManagerDto convert(ProofCatalogDataSharedConfigDo entity) {
        ProofCatalogDataSharedConfigManagerDto copy = BeanCopyUtils.copy(entity, ProofCatalogDataSharedConfigManagerDto.class);
        List<ProofCatalogDataSharedConfigDataItemDto> dataItemList = JacksonUtil.toList(entity.getDataItemList(), ProofCatalogDataSharedConfigDataItemDto.class);
        copy.setDataItemList(dataItemList);
        List<ProofCatalogDataSharedConfigSearchConditionDto> searchConditionList = JacksonUtil.toList(entity.getSearchConditionList(), ProofCatalogDataSharedConfigSearchConditionDto.class);
        if (CollectionUtils.isNotEmpty(searchConditionList)) {
            searchConditionList.forEach(item -> item.settingByDataItem(dataItemList));
        }
        copy.setSearchConditionList(searchConditionList);
        return copy;
    }

    /**
     * 转换Do对象
     * @return 数据
     */
    public ProofCatalogDataSharedConfigDo convertDo() {
        ProofCatalogDataSharedConfigDo copy = BeanCopyUtils.copy(this, ProofCatalogDataSharedConfigDo.class);
        if (CollectionUtils.isNotEmpty(this.getDataItemList())) {
            this.getDataItemList().forEach(item -> item.setDataTypeName(Objects.nonNull(item.getDataType()) ? item.getDataType().getNameCn() : null));
        }
        copy.setDataItemList(JacksonUtil.toJsonStr(this.getDataItemList()));
        copy.setSearchConditionList(JacksonUtil.toJsonStr(this.getSearchConditionList()));
        return copy;
    }

}
