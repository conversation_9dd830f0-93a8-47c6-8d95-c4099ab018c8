package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import gov.derate.proof.assist.enums.IssueProofLicenseTypeEnum;
import gov.derate.proof.catalog.dto.AuditRelationTemplateDto;
import gov.derate.proof.catalog.dto.ProofCatalogLicenseItemImplItemInfoDto;
import gov.derate.proof.catalog.enums.IssueProofLicenseWay;
import gov.derate.proof.common.bo.BaseBo;
import gov.license.common.tools.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <p>
 * 证明目录关联的人工协查
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/8/2 15:11
 * </p>
 *
 * <AUTHOR>
 */
public class ProofCatalogArtificialRelationDto extends BaseBo {
    /**
     * catalog replace way constant
     */
    public static final String ARTIFICIAL = "artificial";
    /**
     * 证明目录ID
     */
    @JsonProperty("proof_catalog_id")
    private String proofCatalogId;
    /**
     * 证明目录code
     */
    @JsonProperty("proof_catalog_code")
    private String proofCatalogCode;
    /**
     * 所属部门名称
     */
    @JsonProperty("investigation_dept_name")
    private String investigationDeptName;
    /**
     * 协查部门编码
     */
    @JsonProperty("investigation_dept_code")
    private String investigationDeptCode;
    /**
     * 人工协查说明
     */
    @NotBlank(message = "人工协查-说明不能为空")
    @JsonProperty("note")
    private String note;
    /**
     * 实施区域代码
     */
    @NotBlank(message = "人工协查-实施区域代码不能为空")
    @JsonProperty("division_code")
    private String divisionCode;
    /**
     * 实施区域名称
     */
    @NotBlank(message = "人工协查-实施区域名称不能为空")
    @JsonProperty("division_name")
    private String divisionName;
    /**
     * 协查时限，单位小时
     */
    @NotBlank(message = "人工协查-协查时限不能为空")
    @JsonProperty("assist_time_hour")
    private Integer assistTimeHour;
    /**
     * 协查时限，单位分钟
     */
    @NotBlank(message = "人工协查-协查时限不能为空")
    @JsonProperty("assist_time_minute")
    private Integer assistTimeMinute;
    /**
     * 审核关系对象唯一键
     */
    @JsonProperty("audit_r_temp_key")
    @JsonIgnore
    private String auditRelationTemplateKey;
    /**
     * 审核关系
     */
    @JsonProperty("audit_r_temp_list")
    private List<AuditRelationTemplateDto> auditRelationTemplateList;
    /**
     * 是否开具电子证明
     */
    @JsonProperty("issue_p_license")
    private IssueProofLicenseTypeEnum issueProofLicense;
    /**
     * 电子证明开具方式;系统生成，人工开具
     */
    @JsonProperty("issue_p_license_way")
    private IssueProofLicenseWay issueProofLicenseWay;
    /**
     * 证照名称
     */
    @NotBlank(message = "关联电子证明-证照名称不能为空")
    @JsonProperty("license_name")
    private String licenseName;

    /**
     * 证照编码
     */
    @JsonProperty("license_code")
    @NotBlank(message = "关联电子证明-证照目录编码不能为空")
    private String licenseCode;

    /**
     * 实施目录信息
     * 解析对象 List<ProofCatalogLicenseItemImplItemInfo>
     */
    @JsonProperty("implement_item_info_json")
    @JsonIgnore
    private String implementItemInfoJson;

    @JsonProperty("implement_item_info")
    @JsonIgnore
    private List<ProofCatalogLicenseItemImplItemInfoDto> implementItemInfo;
    /**
     * 配置审核层级数
     */
    @JsonProperty("examine_level")
    private Integer examineLevel;

    /**
     * 构造目录信息对象
     *
     * @return 实施目录信息对象
     */
    public List<ProofCatalogLicenseItemImplItemInfoDto> buildByImplItemInfoJson() {
        if (StringUtils.isNotBlank(this.implementItemInfoJson)) {
            return JacksonUtil.toList(this.implementItemInfoJson, ProofCatalogLicenseItemImplItemInfoDto.class);
        }
        return Lists.newArrayList();
    }

    /**
     * 审核关系对象唯一键
     *
     * @return 唯一键
     */
    public String buildAuditRelationTemplateKey() {
        return ARTIFICIAL + this.proofCatalogCode;
    }

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getInvestigationDeptName() {
        return investigationDeptName;
    }

    public void setInvestigationDeptName(String investigationDeptName) {
        this.investigationDeptName = investigationDeptName;
    }

    public String getInvestigationDeptCode() {
        return investigationDeptCode;
    }

    public void setInvestigationDeptCode(String investigationDeptCode) {
        this.investigationDeptCode = investigationDeptCode;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName;
    }

    public Integer getAssistTimeHour() {
        return assistTimeHour;
    }

    public void setAssistTimeHour(Integer assistTimeHour) {
        this.assistTimeHour = assistTimeHour;
    }

    public Integer getAssistTimeMinute() {
        return assistTimeMinute;
    }

    public void setAssistTimeMinute(Integer assistTimeMinute) {
        this.assistTimeMinute = assistTimeMinute;
    }

    public String getProofCatalogCode() {
        return proofCatalogCode;
    }

    public void setProofCatalogCode(String proofCatalogCode) {
        this.proofCatalogCode = proofCatalogCode;
    }

    public String getAuditRelationTemplateKey() {
        return auditRelationTemplateKey;
    }

    public void setAuditRelationTemplateKey(String auditRelationTemplateKey) {
        this.auditRelationTemplateKey = auditRelationTemplateKey;
    }

    public List<AuditRelationTemplateDto> getAuditRelationTemplateList() {
        return auditRelationTemplateList;
    }

    public void setAuditRelationTemplateList(List<AuditRelationTemplateDto> auditRelationTemplateList) {
        this.auditRelationTemplateList = auditRelationTemplateList;
    }

    public IssueProofLicenseTypeEnum getIssueProofLicense() {
        return issueProofLicense;
    }

    public void setIssueProofLicense(IssueProofLicenseTypeEnum issueProofLicense) {
        this.issueProofLicense = issueProofLicense;
    }

    public IssueProofLicenseWay getIssueProofLicenseWay() {
        return issueProofLicenseWay;
    }

    public void setIssueProofLicenseWay(IssueProofLicenseWay issueProofLicenseWay) {
        this.issueProofLicenseWay = issueProofLicenseWay;
    }

    public String getLicenseName() {
        return licenseName;
    }

    public void setLicenseName(String licenseName) {
        this.licenseName = licenseName;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getImplementItemInfoJson() {
        return implementItemInfoJson;
    }

    public void setImplementItemInfoJson(String implementItemInfoJson) {
        this.implementItemInfoJson = implementItemInfoJson;
    }

    public List<ProofCatalogLicenseItemImplItemInfoDto> getImplementItemInfo() {
        return implementItemInfo;
    }

    public void setImplementItemInfo(List<ProofCatalogLicenseItemImplItemInfoDto> implementItemInfo) {
        this.implementItemInfo = implementItemInfo;
    }

    public Integer getExamineLevel() {
        return examineLevel;
    }

    public void setExamineLevel(Integer examineLevel) {
        this.examineLevel = examineLevel;
    }

    @Override
    public String toString() {
        return "ProofCatalogArtificialRelationDto{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogCode='" + proofCatalogCode + '\'' +
                ", investigationDeptName='" + investigationDeptName + '\'' +
                ", investigationDeptCode='" + investigationDeptCode + '\'' +
                ", note='" + note + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", divisionName='" + divisionName + '\'' +
                ", assistTimeHour=" + assistTimeHour +
                ", assistTimeMinute=" + assistTimeMinute +
                ", auditRelationTemplateKey='" + auditRelationTemplateKey + '\'' +
                ", auditRelationTemplateList=" + auditRelationTemplateList +
                ", issueProofLicense=" + issueProofLicense +
                ", issueProofLicenseWay=" + issueProofLicenseWay +
                ", licenseName='" + licenseName + '\'' +
                ", licenseCode='" + licenseCode + '\'' +
                ", implementItemInfoJson='" + implementItemInfoJson + '\'' +
                ", implementItemInfo=" + implementItemInfo +
                ", examineLevel=" + examineLevel +
                '}';
    }
}
