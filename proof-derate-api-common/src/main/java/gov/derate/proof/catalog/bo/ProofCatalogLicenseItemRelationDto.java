package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import gov.derate.proof.catalog.dto.ProofCatalogLicenseItemImplItemInfoDto;
import gov.derate.proof.common.bo.BaseBo;
import gov.license.common.tools.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 证明目录关联的电子证明
 *
 * <AUTHOR>
 * @date 2023/03/22.
 */
public class ProofCatalogLicenseItemRelationDto extends BaseBo {
    private static final long serialVersionUID = -6428972075575069802L;

    /**
     * 证明目录ID
     */
    @JsonProperty("proof_catalog_id")
    private String proofCatalogId;
    /**
     * 证明目录code
     */
    @JsonProperty("proof_catalog_code")
    private String proofCatalogCode;

    /**
     * 证照名称
     */
    @NotBlank(message = "关联电子证明-证照名称不能为空")
    @JsonProperty("license_name")
    private String licenseName;

    /**
     * 证照编码
     */
    @JsonProperty("license_code")
    @NotBlank(message = "关联电子证明-证照目录编码不能为空")
    private String licenseCode;

    /**
     * 行业部门名称
     */
    @NotBlank(message = "关联电子证明-行业部门不能为空")
    @JsonProperty("dept_name")
    private String deptName;
    /**
     * 实施目录信息
     * 解析对象 List<ProofCatalogLicenseItemImplItemInfo>
     */
    @JsonIgnore
    private String implementItemInfoJson;
    @JsonProperty("implement_item_info")
    private List<ProofCatalogLicenseItemImplItemInfoDto> implementItemInfo;

    /**
     * 协查说明
     */
    @JsonProperty("assist_desc")
    private String assistDesc;

    /**
     * 构造目录信息对象
     *
     * @return 实施目录信息对象
     */
    public List<ProofCatalogLicenseItemImplItemInfoDto> buildByImplItemInfoJson() {
        if (StringUtils.isNotBlank(this.implementItemInfoJson)) {
            return JacksonUtil.toList(this.implementItemInfoJson, ProofCatalogLicenseItemImplItemInfoDto.class);
        }
        return Lists.newArrayList();
    }

    /**
     * 审核关系对象唯一键
     *
     * @return 唯一键
     */
    public String buildAuditRelationTemplateKey() {
        return super.getId() + this.proofCatalogCode + this.licenseCode;
    }

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getLicenseName() {
        return licenseName;
    }

    public void setLicenseName(String licenseName) {
        this.licenseName = licenseName;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }


    public String getProofCatalogCode() {
        return proofCatalogCode;
    }

    public void setProofCatalogCode(String proofCatalogCode) {
        this.proofCatalogCode = proofCatalogCode;
    }


    public String getImplementItemInfoJson() {
        return implementItemInfoJson;
    }

    public void setImplementItemInfoJson(String implementItemInfoJson) {
        this.implementItemInfoJson = implementItemInfoJson;
    }

    public List<ProofCatalogLicenseItemImplItemInfoDto> getImplementItemInfo() {
        return implementItemInfo;
    }

    public void setImplementItemInfo(List<ProofCatalogLicenseItemImplItemInfoDto> implementItemInfo) {
        this.implementItemInfo = implementItemInfo;
    }

    public String getAssistDesc() {
        return assistDesc;
    }

    public void setAssistDesc(String assistDesc) {
        this.assistDesc = assistDesc;
    }

    @Override
    public String toString() {
        return "ProofCatalogLicenseItemRelationDto{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogCode='" + proofCatalogCode + '\'' +
                ", licenseName='" + licenseName + '\'' +
                ", licenseCode='" + licenseCode + '\'' +
                ", deptName='" + deptName + '\'' +
                ", implementItemInfoJson='" + implementItemInfoJson + '\'' +
                ", implementItemInfo=" + implementItemInfo +
                ", assistDesc='" + assistDesc + '\'' +
                '}';
    }
}
