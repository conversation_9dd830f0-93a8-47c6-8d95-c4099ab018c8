package gov.derate.proof.catalog.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * SubjectDataDto
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/29
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/29；
 */
public class SubjectDataDto implements Serializable {
    /**
     * 证照结构化数据。采用 key/value 记录。
     */
    @JsonProperty("data_fields")
    private Map<String,String> dataFields;
    /**
     * 备注说明
     */
    @JsonProperty("description")
    private String description;
    /**
     * 扩展属性。
     */
    @JsonProperty("extend_props")
    private String extendProps;
    /**
     * 哈希值。
     */
    @JsonProperty("s_hash")
    private String hash;
    /**
     * 用于存放当前记录各字段值拼装后的数
     */
    @JsonProperty("s_sign_data")
    private String signData;
    /**
     * 当前单位所使用的密码服务器签名证书
     */
    @JsonProperty("s_sign_cert")
    private String signCert;
    /**
     * 创建者
     */
    @JsonProperty("creator")
    private String creator;
    /**
     * 创建时间
     */
    @JsonProperty("creation_time")
    private String creationTime;
    /**
     * 最后修改者
     */
    @JsonProperty("last_modificator")
    private String lastModificator;
    /**
     * 最后修改时间
     */
    @JsonProperty("last_modification_time")
    private String lastModificationTime;
    /**
     * 主题表名,主题服务化批量新增时,必填
     */
    @JsonProperty("subject_name")
    private String subjectName;

    public Map<String,String> getDataFields() {
        return dataFields;
    }

    public void setDataFields(Map<String,String> dataFields) {
        this.dataFields = dataFields;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getExtendProps() {
        return extendProps;
    }

    public void setExtendProps(String extendProps) {
        this.extendProps = extendProps;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getSignData() {
        return signData;
    }

    public void setSignData(String signData) {
        this.signData = signData;
    }

    public String getSignCert() {
        return signCert;
    }

    public void setSignCert(String signCert) {
        this.signCert = signCert;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(String creationTime) {
        this.creationTime = creationTime;
    }

    public String getLastModificator() {
        return lastModificator;
    }

    public void setLastModificator(String lastModificator) {
        this.lastModificator = lastModificator;
    }

    public String getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(String lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    @Override
    public String toString() {
        return "SubjectDataDto{" +
                "dataFields='" + dataFields + '\'' +
                ", description='" + description + '\'' +
                ", extendProps='" + extendProps + '\'' +
                ", hash='" + hash + '\'' +
                ", signData='" + signData + '\'' +
                ", signCert='" + signCert + '\'' +
                ", creator='" + creator + '\'' +
                ", creationTime='" + creationTime + '\'' +
                ", lastModificator='" + lastModificator + '\'' +
                ", lastModificationTime='" + lastModificationTime + '\'' +
                ", subjectName='" + subjectName + '\'' +
                '}';
    }
}
