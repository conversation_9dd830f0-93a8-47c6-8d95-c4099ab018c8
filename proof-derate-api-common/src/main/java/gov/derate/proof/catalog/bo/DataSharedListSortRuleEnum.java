package gov.derate.proof.catalog.bo;

/**
 * <p>
 * 主题显示排序规则枚举
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/10/11
 * </p>
 *
 * <AUTHOR>
 */
public enum DataSharedListSortRuleEnum {
    /**
     * 按主题名称升序排序
     */
    ASC("升序",0),
    /**
     * 按主题名称降序排序
     */
    DESC("倒序",1),
    ;
    /**
     * 排序规则中文
     */
    private final String cn;
    /**
     * 排序规则枚举下标
     */
    private final int index;

    DataSharedListSortRuleEnum(String cn, int index) {
        this.cn = cn;
        this.index = index;
    }

    public String getCn() {
        return cn;
    }

    public int getIndex() {
        return index;
    }
}
