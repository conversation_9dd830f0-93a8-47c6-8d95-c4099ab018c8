package gov.derate.proof.catalog.entity;

/**
 * <p>
 * 日志加密类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/7/24
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2023/7/24；
 */
public interface LogSafeHelper {
    /**
     * sm4加密
     *
     * @param content 原文字符串
     * @return sm4加密后字符串
     */
    String sign(String content);

    /**
     * sm4解密
     *
     * @param content 加密字符串
     * @return 解密后的字符串
     */
    String design(String content);

    /**
     * 手机号脱敏
     *
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    String maskPhone(String phone);

    /**
     * 身份证号脱敏
     *
     * @param idCard 身份证号
     * @return 脱敏后的身份证号
     */
    String maskIdCard(String idCard);

    /**
     * 名称脱敏
     *
     * @param name 名称
     * @return 脱敏后的名称
     */
    String maskName(String name);

    /**
     * 邮箱脱敏
     *
     * @param email 邮箱脱敏
     * @return 脱敏后的邮箱
     */
    String maskEmail(String email);

    /**
     * 内容脱敏
     * 保留第一位/最后一位 中间内容均脱敏
     *
     * @param content 原文
     * @return 脱敏后的内容
     */
    String maskExceptFirstAndLast(String content);
}
