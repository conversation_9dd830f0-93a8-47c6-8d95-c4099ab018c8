package gov.derate.proof.catalog.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import gov.derate.proof.catalog.bo.*;
import gov.derate.proof.common.entity.AbstractDomainEntity;
import gov.license.common.tools.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.List;

/**
 * <p>
 * 数据共享配置
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/28；
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_R_CATALOG_SHARED_CONF")
public class ProofCatalogDataSharedConfigDo extends AbstractDomainEntity  {
    /**
     * 系统名称
     */
    @JsonProperty("system_name")
    @Column(name = "SYSTEM_NAME",nullable = true,length = 200)
    private String systemName;
    /**
     * 系统编码
     */
    @JsonProperty("system_code")
    @Column(name = "SYSTEM_CODE",nullable = true,length = 50)
    private String systemCode;
    /**
     * 数据主题名称
     */
    @JsonProperty("data_theme_name")
    @Column(name = "DATA_THEME_NAME",nullable = true,length = 200)
    private String dataThemeName;
    /**
     * 数据主题编码
     */
    @JsonProperty("data_theme_code")
    @Column(name = "DATA_THEME_CODE",nullable = true,length = 200)
    private String dataThemeCode;
    /**
     * 接口访问地址
     * 例如： http://{api_root}/subject/{subject_name}/list
     */
    @JsonProperty("system_api_url")
    @Column(name = "API_URL",nullable = true,length = 500)
    private String apiUrl;
    /**
     * 数据项
     * List<ProofCatalogDataSharedConfigDataItemBo>
     */
    @JsonProperty("data_item_list")
    @Column(name = "DATA_ITEM_LIST",nullable = true)
    @Lob
    private String  dataItemList;
    /**
     * 查询条件
     * List<ProofCatalogDataSharedConfigSearchConditionBo>
     */
    @JsonProperty("search_condition_list")
    @Column(name = "SEARCH_CONDITION_LIST",nullable = true)
    @Lob
    private String searchConditionList;
    /**
     * 状态：启用/禁用
     */
    @JsonProperty("data_theme_status")
    @Column(name = "DATA_THEME_STATUS",nullable = true)
    @Enumerated(EnumType.STRING)
    private DataSharedStatusEnum dataThemeStatus;

    /**
     * 主题查询类策略值
     */
    @JsonProperty("strategy_flag")
    @Column(name = "STRATEGY_FLAG",nullable = true,length = 100)
    private String strategyFlag;

    /**
     * 主题显示排序类型
     * 默认排序、自定义排序
     */
    @JsonProperty("list_sort_type")
    @Column(name = "LIST_SORT_TYPE",nullable = true,length = 2)
    private DataSharedListSortTypeEnum listSortType;

    /**
     * 排序数据项
     */
    @JsonProperty("list_sort_type_data_item_name")
    @Column(name = "LIST_SORT_TYPE_DATA_ITEM_NAME",nullable = true,length = 50)
    private String listSortTypeDataItemName;
    /**
     * 主题显示排序规则
     * 自定义排序该字段才有值
     */
    @JsonProperty("list_sort_rule")
    @Column(name = "LIST_SORT_RULE",nullable = true,length = 2)
    private DataSharedListSortRuleEnum listSortRule;

    /**
     * 主题查询次数限制
     */
    @JsonProperty("search_limit_count")
    @Column(name = "SEARCH_LIMIT_COUNT",nullable = true,length = 10)
    private Integer searchLimitCount;
    /**
     * 查询条件 json转实际对象
     *
     * @return 实际对象
     */
    public List<ProofCatalogDataSharedConfigSearchConditionDto> getSearchConditionListEntity() {
        if (StringUtils.isEmpty(this.searchConditionList)) {
            return Lists.newArrayList();
        }
        return JacksonUtil.toBean(searchConditionList, new TypeReference<List<ProofCatalogDataSharedConfigSearchConditionDto>>() {
        });
    }

    /**
     * 数据项 json转实际对象
     *
     * @return 实际对象
     */
    public List<ProofCatalogDataSharedConfigDataItemDto> getDataItemListEntity() {

        return JacksonUtil.toBean(dataItemList, new TypeReference<List<ProofCatalogDataSharedConfigDataItemDto>>() {
        });
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public String getDataThemeName() {
        return dataThemeName;
    }

    public void setDataThemeName(String dataThemeName) {
        this.dataThemeName = dataThemeName;
    }

    public String getDataThemeCode() {
        return dataThemeCode;
    }

    public void setDataThemeCode(String dataThemeCode) {
        this.dataThemeCode = dataThemeCode;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getDataItemList() {
        return dataItemList;
    }

    public void setDataItemList(String dataItemList) {
        this.dataItemList = dataItemList;
    }

    public String getSearchConditionList() {
        return searchConditionList;
    }

    public void setSearchConditionList(String searchConditionList) {
        this.searchConditionList = searchConditionList;
    }

    public DataSharedStatusEnum getDataThemeStatus() {
        return dataThemeStatus;
    }

    public void setDataThemeStatus(DataSharedStatusEnum dataThemeStatus) {
        this.dataThemeStatus = dataThemeStatus;
    }

    public String getStrategyFlag() {
        return strategyFlag;
    }

    public void setStrategyFlag(String strategyFlag) {
        this.strategyFlag = strategyFlag;
    }

    public DataSharedListSortTypeEnum getListSortType() {
        return listSortType;
    }

    public void setListSortType(DataSharedListSortTypeEnum listSortType) {
        this.listSortType = listSortType;
    }

    public String getListSortTypeDataItemName() {
        return listSortTypeDataItemName;
    }

    public void setListSortTypeDataItemName(String listSortTypeDataItemName) {
        this.listSortTypeDataItemName = listSortTypeDataItemName;
    }

    public DataSharedListSortRuleEnum getListSortRule() {
        return listSortRule;
    }

    public void setListSortRule(DataSharedListSortRuleEnum listSortRule) {
        this.listSortRule = listSortRule;
    }

    public Integer getSearchLimitCount() {
        return searchLimitCount;
    }

    public void setSearchLimitCount(Integer searchLimitCount) {
        this.searchLimitCount = searchLimitCount;
    }

    @Override
    public String toString() {
        return "ProofCatalogDataSharedConfigDo{" +
                "systemName='" + systemName + '\'' +
                ", systemCode='" + systemCode + '\'' +
                ", dataThemeName='" + dataThemeName + '\'' +
                ", dataThemeCode='" + dataThemeCode + '\'' +
                ", apiUrl='" + apiUrl + '\'' +
                ", dataItemList='" + dataItemList + '\'' +
                ", searchConditionList='" + searchConditionList + '\'' +
                ", dataThemeStatus=" + dataThemeStatus +
                ", strategyFlag='" + strategyFlag + '\'' +
                ", listSortType=" + listSortType +
                ", listSortTypeDataItemName='" + listSortTypeDataItemName + '\'' +
                ", listSortRule=" + listSortRule +
                ", searchLimitCount=" + searchLimitCount +
                '}';
    }
}
