package gov.derate.proof.catalog.bo;


import gov.derate.proof.exempt.bo.ExemptDataSharedConfigSearchConditionBo;
import org.springframework.util.Assert;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Optional;

/**
 * <p>
 * 证照数据项数据类型
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2016年5月11日
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:Liangguiming；Date:2016年5月11日；
 */
public enum DataSharedDataTypeEnum {

    /**
     * 字符型
     */
    STRING("字符型"),

    /**
     * 数字型
     */
    NUMBER("数字型"),
    /**
     * 数字型
     */
    INTEGER("整数型"),

    /**
     * 日期型
     */
    DATETIME("日期型"),

    /**
     * 二进制
     */
    BINARY("二进制型"),
    /**
     * 布尔值
     */
    BOOLEAN("布尔型"),
    /**
     * 文本型
     */
    TEXT("文本型"),
    /**
     * 文本型
     */
    TIMESTAMP("时间戳"),
    /**
     * 浮点型
     */
    FLOAT("时间戳"),
    /**
     * 枚举型
     */
    ENUM("枚举型"),

    ;

    /**
     * 中文名称
     */
    private final String nameCn;

    DataSharedDataTypeEnum(String nameCn) {
        this.nameCn = nameCn;
    }

    /**
     * 将中文解析为枚举类型
     *
     * @return 枚举
     */
    public static DataSharedDataTypeEnum parseEnumType(String chinese) {
        Optional<DataSharedDataTypeEnum> first = Arrays.stream(DataSharedDataTypeEnum.values()).filter(item -> item.getNameCn().equals(chinese)).findFirst();
        return first.orElse(DataSharedDataTypeEnum.STRING);
    }

    public String getNameCn() {
        return nameCn;
    }


    /**
     * 根据枚举对接口对象继续宁设值
     *
     * @param qf          数据对象同步设值对象
     * @param firstValue  值1
     * @param secondValue 值2
     * @return 设值成功返回1，否则返回2
     */
    public boolean settingQueryFilterDto(@NotNull ExemptDataSharedConfigSearchConditionBo qf, String firstValue, String secondValue) {
        Assert.notNull(qf, "ExemptDataSharedConfigSearchConditionBo can't be null");
        switch (this) {
            case STRING:
                qf.setSearchValue(firstValue);
                return true;
            case NUMBER:
                qf.setSearchValue(firstValue);
                return true;
            case INTEGER:
                qf.setSearchValue(firstValue);
                return true;
            case DATETIME:
                qf.setSearchValue(firstValue);
                qf.setSearchSecondValue(secondValue);
                return true;
            case BINARY:
                qf.setSearchValue(firstValue);
                return true;
            case BOOLEAN:
                qf.setSearchValue(firstValue);
                return true;
            case TEXT:
                qf.setSearchValue(firstValue);
                return true;
            case TIMESTAMP:
                qf.setSearchValue(firstValue);
                return true;
            case FLOAT:
                qf.setSearchValue(firstValue);
                return true;
            case ENUM:
                qf.setSearchValue(firstValue);
                return true;
            default:
                return false;
        }
    }

}
