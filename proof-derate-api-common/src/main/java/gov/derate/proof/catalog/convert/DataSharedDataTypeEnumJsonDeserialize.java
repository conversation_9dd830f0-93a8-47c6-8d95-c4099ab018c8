package gov.derate.proof.catalog.convert;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import gov.derate.proof.catalog.bo.DataSharedDataTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;

/**
 * <p>
 * ExemptIdentityTypeEnumJsonDeserialize
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022年8月11日
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022年8月11日；
 */
@Component
public class DataSharedDataTypeEnumJsonDeserialize extends JsonDeserializer<DataSharedDataTypeEnum> {
    private static final Logger LOGGER = LoggerFactory.getLogger(DataSharedDataTypeEnumJsonDeserialize.class);

    @Override
    public DataSharedDataTypeEnum deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
        if (Objects.isNull(jsonParser)) {
            return null;
        }
        String text = jsonParser.getText();
        DataSharedDataTypeEnum result = null;
        try {
            result = DataSharedDataTypeEnum.valueOf(text);
        } catch (IllegalArgumentException e) {
            LOGGER.warn("DataSharedDataTypeEnum valueOf error text is [{}]",text,e);
        }
        return result;
    }
}
