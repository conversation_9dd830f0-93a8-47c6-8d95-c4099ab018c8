package gov.derate.proof.catalog.bo;


import java.util.Arrays;
import java.util.Optional;

/**
 * <p>
 * 数据共享状态
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2016年5月11日
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:Liangguiming；Date:2016年5月11日；
 */
public enum DataSharedStatusEnum {

    /**
     * 启用
     */
    ENABLE("启用"),

    /**
     * 禁用
     */
    DISABLE("禁用"),

    ;

    /**
     * 中文名称
     */
    private final String nameCn;

    DataSharedStatusEnum(String nameCn) {
        this.nameCn = nameCn;
    }

    /**
     * 将中文解析为枚举类型
     *
     * @return 枚举
     */
    public static DataSharedStatusEnum parseEnumType(String chinese) {
        Optional<DataSharedStatusEnum> first = Arrays.stream(DataSharedStatusEnum.values()).filter(item -> item.getNameCn().equals(chinese)).findFirst();
        return first.orElse(null);
    }

    public String getNameCn() {
        return nameCn;
    }

}
