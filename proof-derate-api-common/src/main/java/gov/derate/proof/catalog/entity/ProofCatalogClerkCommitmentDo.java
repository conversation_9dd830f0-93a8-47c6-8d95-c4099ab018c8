package gov.derate.proof.catalog.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import gov.derate.proof.common.entity.AbstractDomainEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

/**
 * <p>
 * 证明目录关联的告知承诺
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/8/10 14:42
 * </p>
 *
 * <AUTHOR>
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_R_PROOF_CATALOG_COMMITMENT")
public class ProofCatalogClerkCommitmentDo extends AbstractDomainEntity {
    /**
     * 证明目录ID
     */
    @Column(name = "PROOF_CATALOG_ID", length = 50, nullable = false)
    private String proofCatalogId;
    /**
     * 证明目录code
     */
    @Column(name = "PROOF_CATALOG_CODE", length = 50, nullable = false)
    private String proofCatalogCode;
    /**
     * 承诺书说明
     */
    @Column(name = "COMMIT_BOOK_DESCRIPTION", length = 500, nullable = false)
    private String commitBookDescription;

    /**
     * 承诺书模板附件ID
     */
    @Column(name = "COMMIT_ATTACHMENT_ID", length = 50, nullable = false)
    private String commitAttachmentId;
    /**
     * 承诺书模板附件数据
     */
    @JsonIgnore
    @Lob
    @Column(name = "FILE_DATA", nullable = false)
    private byte[] fileData;

    /**
     * 承诺书模板文件名称
     */
    @Column(name = "COMMIT_ATTACHMENT_NAME", length = 100, nullable = false)
    private String commitAttachmentName;

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getCommitBookDescription() {
        return commitBookDescription;
    }

    public void setCommitBookDescription(String commitBookDescription) {
        this.commitBookDescription = commitBookDescription;
    }

    public String getCommitAttachmentId() {
        return commitAttachmentId;
    }

    public void setCommitAttachmentId(String commitAttachmentId) {
        this.commitAttachmentId = commitAttachmentId;
    }

    public byte[] getFileData() {
        return fileData;
    }

    public void setFileData(byte[] fileData) {
        this.fileData = fileData;
    }

    public String getCommitAttachmentName() {
        return commitAttachmentName;
    }

    public void setCommitAttachmentName(String commitAttachmentName) {
        this.commitAttachmentName = commitAttachmentName;
    }

    public String getProofCatalogCode() {
        return proofCatalogCode;
    }

    public void setProofCatalogCode(String proofCatalogCode) {
        this.proofCatalogCode = proofCatalogCode;
    }

    @Override
    public String toString() {
        return "ProofCatalogClerkCommitmentDo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogCode='" + proofCatalogCode + '\'' +
                ", commitBookDescription='" + commitBookDescription + '\'' +
                ", commitAttachmentId='" + commitAttachmentId + '\'' +
                ", commitAttachmentName='" + commitAttachmentName + '\'' +
                '}';
    }
}
