package gov.derate.proof.catalog.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.catalog.bo.ProofCatalogDataSharedConfigSearchConditionDto;
import gov.derate.proof.exempt.bo.ExemptCertificatesDetailBo;
import gov.derate.proof.item.entity.ItemDo;
import gov.license.common.tools.date.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * QueryFilterDto
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/29
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/29；
 */
public class QueryFilterDto implements Serializable {
    /**
     * 参数名称
     */
    @JsonProperty("name")
    private String name;
    /**
     * 条件操作符。有三种操作符： EQUAL、	LIKE、BETWEEN，其中对于 BETWEEN 需要	设置 first_value 与 second_value，其	它两种操作符设置 first_value 即可
     */
    @JsonProperty("operation")
    private QueryFilterOperationDto operation;
    /**
     * 参数值 1
     */
    @JsonProperty("first_value")
    private String firstValue;
    /**
     * 参数值 2
     */
    @JsonProperty("second_value")
    private String secondValue;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public QueryFilterOperationDto getOperation() {
        return operation;
    }

    public void setOperation(QueryFilterOperationDto operation) {
        this.operation = operation;
    }

    public String getFirstValue() {
        return firstValue;
    }

    public void setFirstValue(String firstValue) {
        this.firstValue = firstValue;
    }

    public String getSecondValue() {
        return secondValue;
    }

    public void setSecondValue(String secondValue) {
        this.secondValue = secondValue;
    }

    @Override
    public String toString() {
        return "QueryFilterDto{" +
                "name='" + name + '\'' +
                ", operation=" + operation +
                ", firstValue='" + firstValue + '\'' +
                ", secondValue='" + secondValue + '\'' +
                '}';
    }

    /**
     * 根据值，设值对象
     *
     * @param item     查询条件
     * @param detailBo 免证办
     * @param itemDo   事项
     */
    public void settingByConditionAndExemptAndItem(ProofCatalogDataSharedConfigSearchConditionDto item, ExemptCertificatesDetailBo detailBo, ItemDo itemDo) {
        this.name = item.getDataItemName();
        this.operation = QueryFilterOperationDto.valueOf(item.getConditionOperationSymbol());
        boolean errorStatus = false;
        switch (item.getConditionKey()) {
            case HANDLE_AFFAIRS_NAME:
                errorStatus = operation.settingQueryFilterDto(this, detailBo.getHandleAffairsName(), item.getConditionValue());
                break;
            case EXEMPT_IDENTITY_TYPE:
                errorStatus = operation.settingQueryFilterDto(this, String.valueOf(detailBo.getIdentityType().getIndex()), item.getConditionValue());
                break;
            case EXEMPT_IDENTITY_NUMBER:
                errorStatus = operation.settingQueryFilterDto(this, detailBo.getIdentityNumber(), item.getConditionValue());
                break;
            case BIZ_ORG_NAME:
                errorStatus = operation.settingQueryFilterDto(this, detailBo.getBizOrgName(), item.getConditionValue());
                break;
            case BIZ_ORG_IDENTITY_TYPE:
                errorStatus = operation.settingQueryFilterDto(this, String.valueOf(detailBo.getBizOrgIdentityType().getIndex()), item.getConditionValue());
                break;
            case BIZ_ORG_IDENTITY_NUM:
                errorStatus = operation.settingQueryFilterDto(this, detailBo.getBizOrgIdentityNum(), item.getConditionValue());

                break;
            case ITEM_CODE:
                errorStatus = operation.settingQueryFilterDto(this, detailBo.getItemCode(), item.getConditionValue());

                break;
            case ITEM_NAME:
                errorStatus = operation.settingQueryFilterDto(this, detailBo.getItemName(), item.getConditionValue());

                break;
            case ITEM_ORG:
                errorStatus = operation.settingQueryFilterDto(this, itemDo.getImplOrgName(), item.getConditionValue());

                break;
            case ITEM_CREDIT_CODE:
                errorStatus = operation.settingQueryFilterDto(this, itemDo.getCreditCode(), item.getConditionValue());

                break;
            case ITEM_DIVISION_CODE:
                errorStatus = operation.settingQueryFilterDto(this, itemDo.getDivisionCode(), item.getConditionValue());

                break;
            case CUSTOM:
                errorStatus = operation.settingQueryFilterDto(this, item.getConditionValue(), item.getConditionValueOther());
                break;
            default:
                // 没有任何类型，则报错，需要检查配置数据
                break;
        }
        Assert.isTrue(errorStatus, "buildBySearchConditionList 发生异常，检查数据配置config配置。");
    }

    /**
     * 是否过滤该值
     * @param value  接口响应值
     * @return true，代表需要过滤，false代表不要过滤
     */
    public boolean isFilter(Object value) {
        boolean flag = false;
        if (Objects.isNull(value)) {
            return flag;
        }
        switch (operation) {
            case LIKE:
                return  !String.valueOf(value).contains(firstValue);
            case BETWEEN:
                Date firstDate = StringUtils.isEmpty(firstValue)?null:DateUtil.parseDate(firstValue);
                Date secondDate = StringUtils.isEmpty(secondValue)?null: DateUtil.parseDate(secondValue);
                if (value instanceof ZonedDateTime) {
                    ZonedDateTime valueDate = (ZonedDateTime) value;
                    if (Objects.nonNull(firstDate) && Objects.nonNull(secondDate)) {
                        return  valueDate.isBefore(ZonedDateTime.ofInstant(firstDate.toInstant(), ZoneId.systemDefault()))
                                && valueDate.isAfter(ZonedDateTime.ofInstant(secondDate.toInstant(), ZoneId.systemDefault()));
                    }
                } else if (value instanceof LocalDateTime) {
                    LocalDateTime valueDate = (LocalDateTime) value;
                    if (Objects.nonNull(firstDate) && Objects.nonNull(secondDate)) {
                        return  valueDate.isBefore(LocalDateTime.ofInstant(firstDate.toInstant(), ZoneId.systemDefault()))
                                && valueDate.isAfter(LocalDateTime.ofInstant(secondDate.toInstant(), ZoneId.systemDefault()));
                    }
                } else if (value instanceof Date) {
                    Date valueDate = (Date) value;
                    if (Objects.nonNull(firstDate) && Objects.nonNull(secondDate)) {
                        return  valueDate.before(firstDate) && valueDate.after(secondDate);
                    }
                }else{
                    return  false;
                }
            case EQUAL:
                return !String.valueOf(value).equals(firstValue);
            case CUSTOM:
            default:
        }
        return flag;
    }
}
