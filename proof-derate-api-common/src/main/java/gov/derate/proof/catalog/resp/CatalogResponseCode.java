package gov.derate.proof.catalog.resp;

import gov.derate.proof.common.response.ResponseCode;
import gov.license.common.api.resp.BaseResponseCode;

/**
 * 证明目录 异常编码类
 *
 * <AUTHOR>
 * @date 2024/1/17
 */
public class CatalogResponseCode extends BaseResponseCode {

    /**
     * 服务级别 新增证明目录异常
     */
    public static final ResponseCode SERVICE_CATALOG_CREATE_ERROR =
            new ResponseCode("2C020501000", "新增证明目录异常");
    /**
     * 证明目录-证明目录名称已存在
     */
    public static final ResponseCode CATALOG_CREATE_NAME_EXISTS_ERROR =
            new ResponseCode("2C020501001", "证明目录-证明目录名称已存在");

    /**
     * 证明目录-证明目录不存在
     */
    public static final ResponseCode CATALOG_NOT_EXISTS_ERROR =
            new ResponseCode("2C020501002", "证明目录-证明目录不存在", true);
    /**
     * 证明目录-电子证明不存在
     */
    public static final ResponseCode CATALOG_LICENSE_ITEM_NOT_EXISTS_ERROR =
            new ResponseCode("2C020501003", "证明目录-电子证明不存在", true);
    /**
     * 证明目录-电子证明实施码不存在
     */
    public static final ResponseCode CATALOG_LICENSE_ITEM_IMPL_CODE_NOT_EXISTS_ERROR =
            new ResponseCode("2C020501004", "证明目录-电子证明实施码不存在", true);

    public CatalogResponseCode(String code, String message) {
        super(code, message);
    }
}
