package gov.derate.proof.catalog.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 证明目录-电子证明-实施目录信息对象dto
 * <p>
 * Company: Zsoft
 * CreateDate:2024/8/7
 *
 * <AUTHOR>
 */
public class ProofCatalogLicenseItemImplItemInfoDto {
    /**
     * 实施目录-实施码
     */
    @JsonProperty("implement_code")
    private String implementCode;
    /**
     * 实施目录-实施部门编码
     */
    @JsonProperty("implement_org_code")
    private String implementOrgCode;
    /**
     * 实施目录-实施部门统一社会信用代码
     */
    @JsonProperty("implement_credit_code")
    private String implementCreditCode;
    /**
     * 实施目录-实施部门
     */
    @JsonProperty("implement_org")
    private String implementOrg;
    /**
     * 实施目录-基本码
     */
    @JsonProperty("basic_code")
    private String basicCode;
    /**
     * 实施目录-实施机构行政区划代码
     */
    @JsonProperty("implement_division_code")
    private String implementDivisionCode;
    /**
     * 实施目录-实施机构行政区划
     */
    @JsonProperty("implement_division")
    private String implementDivision;

    public String getImplementCode() {
        return implementCode;
    }

    public void setImplementCode(String implementCode) {
        this.implementCode = implementCode;
    }

    public String getImplementOrgCode() {
        return implementOrgCode;
    }

    public void setImplementOrgCode(String implementOrgCode) {
        this.implementOrgCode = implementOrgCode;
    }

    public String getImplementOrg() {
        return implementOrg;
    }

    public void setImplementOrg(String implementOrg) {
        this.implementOrg = implementOrg;
    }

    public String getBasicCode() {
        return basicCode;
    }

    public void setBasicCode(String basicCode) {
        this.basicCode = basicCode;
    }

    public String getImplementDivisionCode() {
        return implementDivisionCode;
    }

    public void setImplementDivisionCode(String implementDivisionCode) {
        this.implementDivisionCode = implementDivisionCode;
    }

    public String getImplementDivision() {
        return implementDivision;
    }

    public void setImplementDivision(String implementDivision) {
        this.implementDivision = implementDivision;
    }

    public String getImplementCreditCode() {
        return implementCreditCode;
    }

    public void setImplementCreditCode(String implementCreditCode) {
        this.implementCreditCode = implementCreditCode;
    }

    @Override
    public String toString() {
        return "ProofCatalogLicenseItemImplItemInfo{" +
                "implementCode='" + implementCode + '\'' +
                ", implementOrgCode='" + implementOrgCode + '\'' +
                ", implementCreditCode='" + implementCreditCode + '\'' +
                ", implementOrg='" + implementOrg + '\'' +
                ", basicCode='" + basicCode + '\'' +
                ", implementDivisionCode='" + implementDivisionCode + '\'' +
                ", implementDivision='" + implementDivision + '\'' +
                '}';
    }
}
