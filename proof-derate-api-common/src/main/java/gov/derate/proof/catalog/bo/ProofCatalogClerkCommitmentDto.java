package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.bo.BaseBo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 证明目录关联的告知承诺
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/8/10 14:42
 * </p>
 *
 * <AUTHOR>
 */
public class ProofCatalogClerkCommitmentDto extends BaseBo {
    /**
     * 证明目录ID
     */
    @JsonProperty("proof_catalog_id")
    private String proofCatalogId;
    /**
     * 证明目录code
     */
    @JsonProperty("proof_catalog_code")
    private String proofCatalogCode;

    /**
     * 承诺书说明
     */
    @NotNull(message = "告知承诺-承诺书说明不能为空")
    @JsonProperty("commit_book_description")
    private String commitBookDescription;

    /**
     * 承诺书模板附件ID
     */
    @JsonProperty("commit_attachment_id")
    @NotBlank(message = "告知承诺-承诺书模板id不能为空")
    private String commitAttachmentId;
    /**
     * 承诺书模板附件数据
     * base64，可能不传
     */
    @JsonProperty("file_data")
    private String fileData;

    /**
     * 承诺书模板文件名称
     */
    @NotBlank(message = "告知承诺-承诺书文件名臣故不能为空")
    @JsonProperty("commit_attachment_name")
    private String commitAttachmentName;

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getCommitBookDescription() {
        return commitBookDescription;
    }

    public void setCommitBookDescription(String commitBookDescription) {
        this.commitBookDescription = commitBookDescription;
    }

    public String getCommitAttachmentId() {
        return commitAttachmentId;
    }

    public void setCommitAttachmentId(String commitAttachmentId) {
        this.commitAttachmentId = commitAttachmentId;
    }

    public String getFileData() {
        return fileData;
    }

    public void setFileData(String fileData) {
        this.fileData = fileData;
    }

    public String getCommitAttachmentName() {
        return commitAttachmentName;
    }

    public void setCommitAttachmentName(String commitAttachmentName) {
        this.commitAttachmentName = commitAttachmentName;
    }

    public String getProofCatalogCode() {
        return proofCatalogCode;
    }

    public void setProofCatalogCode(String proofCatalogCode) {
        this.proofCatalogCode = proofCatalogCode;
    }

    @Override
    public String toString() {
        return "ProofCatalogClerkCommitmentBo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogCode='" + proofCatalogCode + '\'' +
                ", commitBookDescription='" + commitBookDescription + '\'' +
                ", commitAttachmentId='" + commitAttachmentId + '\'' +
                ", fileData='" + fileData + '\'' +
                ", commitAttachmentName='" + commitAttachmentName + '\'' +
                '}';
    }
}
