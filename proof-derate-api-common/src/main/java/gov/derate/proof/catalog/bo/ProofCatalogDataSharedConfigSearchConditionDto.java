package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import gov.derate.proof.catalog.convert.DataSharedDataTypeEnumJsonDeserialize;
import gov.derate.proof.catalog.convert.DataSharedSearchKeyEnumJsonDeserialize;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 数据共享配置- 查询条件
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/28；
 */
public class ProofCatalogDataSharedConfigSearchConditionDto implements Serializable {
    /**
     * 序号
     */
    @JsonProperty("order_num")
    private Integer orderNum;

    /**
     * 数据项名称
     */
    @JsonProperty("data_item_name")
    private String dataItemName;

    /**
     * 条件操作符
     */
    @JsonProperty("condition_operation_symbol")
    private String conditionOperationSymbol;
    /**
     * 值
     */
    @JsonProperty("condition_key")
    @JsonDeserialize(using = DataSharedSearchKeyEnumJsonDeserialize.class)
    private DataSharedSearchKeyEnum conditionKey;
    /**
     * 值域
     */
    @JsonProperty("condition_value")
    private String conditionValue;
    /**
     * 值域2
     */
    @JsonProperty("condition_value_other")
    private String conditionValueOther;
    /**
     * 数据类型枚举类
     */
    @JsonProperty("data_type")
    @JsonDeserialize(using = DataSharedDataTypeEnumJsonDeserialize.class)
    private DataSharedDataTypeEnum dataType;

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getDataItemName() {
        return dataItemName;
    }

    public void setDataItemName(String dataItemName) {
        this.dataItemName = dataItemName;
    }

    public String getConditionOperationSymbol() {
        return conditionOperationSymbol;
    }

    public void setConditionOperationSymbol(String conditionOperationSymbol) {
        this.conditionOperationSymbol = conditionOperationSymbol;
    }

    public DataSharedSearchKeyEnum getConditionKey() {
        return conditionKey;
    }

    public void setConditionKey(DataSharedSearchKeyEnum conditionKey) {
        this.conditionKey = conditionKey;
    }

    public String getConditionValue() {
        return conditionValue;
    }

    public void setConditionValue(String conditionValue) {
        this.conditionValue = conditionValue;
    }

    public String getConditionValueOther() {
        return conditionValueOther;
    }

    public void setConditionValueOther(String conditionValueOther) {
        this.conditionValueOther = conditionValueOther;
    }

    public DataSharedDataTypeEnum getDataType() {
        return dataType;
    }

    public void setDataType(DataSharedDataTypeEnum dataType) {
        this.dataType = dataType;
    }

    @Override
    public String toString() {
        return "ProofCatalogDataSharedConfigSearchConditionBo{" +
                "orderNum=" + orderNum +
                ", dataItemName='" + dataItemName + '\'' +
                ", conditionOperationSymbol='" + conditionOperationSymbol + '\'' +
                ", conditionKey=" + conditionKey +
                ", conditionValue='" + conditionValue + '\'' +
                ", conditionValueOther='" + conditionValueOther + '\'' +
                ", dataType=" + dataType +
                '}';
    }

    /**
     * 设值条件数据项值
     *
     * @param dataItemList 数据项list
     */
    public void settingByDataItem(List<ProofCatalogDataSharedConfigDataItemDto> dataItemList) {
        if (CollectionUtils.isEmpty(dataItemList)) {
            return;
        }
        Optional<ProofCatalogDataSharedConfigDataItemDto> firstOptional = dataItemList.stream().filter(item -> StringUtils.equals(item.getDataItemName(), this.dataItemName))
                .findFirst();
        firstOptional.ifPresent(dataItem -> {
            this.dataType = dataItem.getDataType();
        });

    }
}
