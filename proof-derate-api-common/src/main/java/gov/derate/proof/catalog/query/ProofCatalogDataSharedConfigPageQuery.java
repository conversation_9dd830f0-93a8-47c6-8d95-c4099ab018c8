package gov.derate.proof.catalog.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.catalog.entity.ProofCatalogDataSharedConfigDo;
import gov.derate.proof.common.query.OrganizationQuery;
import gov.license.jpa.Specifications;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.io.Serializable;
import java.util.Objects;

/**
 * <p>
 * 数据共享配置
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/7/28
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/7/28；
 */
public class ProofCatalogDataSharedConfigPageQuery implements Serializable {
    /**
     * 系统名称
     */
    @JsonProperty("system_name")
    private String systemName;
    /**
     * 系统编码
     */
    @JsonProperty("system_code")
    private String systemCode;
    /**
     * 数据主题名称
     */
    @JsonProperty("data_theme_name")
    private String dataThemeName;
    /**
     * 数据主题编码
     */
    @JsonProperty("data_theme_code")
    private String dataThemeCode;
    /**
     * 服务id
     */
    @JsonProperty("service_id")
    private String serviceId;
    /**
     * 状态：启用/禁用
     */
    @JsonProperty("data_theme_status")
    private Integer dataThemeStatus;
    /**
     * 分页
     */
    private Pageable pageable;
    /**
     * 权限查询条件
     */
    private OrganizationQuery organizationQuery;

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public String getDataThemeName() {
        return dataThemeName;
    }

    public void setDataThemeName(String dataThemeName) {
        this.dataThemeName = dataThemeName;
    }

    public String getDataThemeCode() {
        return dataThemeCode;
    }

    public void setDataThemeCode(String dataThemeCode) {
        this.dataThemeCode = dataThemeCode;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public Integer getDataThemeStatus() {
        return dataThemeStatus;
    }

    public void setDataThemeStatus(Integer dataThemeStatus) {
        this.dataThemeStatus = dataThemeStatus;
    }

    public Pageable getPageable() {
        return pageable;
    }

    public void setPageable(Pageable pageable) {
        this.pageable = pageable;
    }

    public OrganizationQuery getOrganizationQuery() {
        return organizationQuery;
    }

    public void setOrganizationQuery(OrganizationQuery organizationQuery) {
        this.organizationQuery = organizationQuery;
    }

    @Override
    public String toString() {
        return "ProofCatalogDataSharedConfigPageQuery{" +
                "systemName='" + systemName + '\'' +
                ", systemCode='" + systemCode + '\'' +
                ", dataThemeName='" + dataThemeName + '\'' +
                ", dataThemeCode='" + dataThemeCode + '\'' +
                ", serviceId='" + serviceId + '\'' +
                ", dataThemeStatus=" + dataThemeStatus +
                ", pageable=" + pageable +
                ", organizationQuery=" + organizationQuery +
                '}';
    }

    public Specification<ProofCatalogDataSharedConfigDo> toSpec(){
        return Specifications.<ProofCatalogDataSharedConfigDo>and()
                .like(StringUtils.isNotBlank(this.systemName), "systemName", "%" + this.systemName + "%")
                .eq(StringUtils.isNotBlank(this.systemCode), "systemCode", this.systemCode)
                .like(StringUtils.isNotBlank(this.dataThemeName), "dataThemeName", "%" + this.dataThemeName + "%")
                .eq(StringUtils.isNotBlank(this.dataThemeCode), "dataThemeCode", this.dataThemeCode)
                .eq(StringUtils.isNotBlank(this.serviceId), "serviceId", this.serviceId)
                .eq(Objects.nonNull(this.dataThemeStatus),"dataThemeStatus",this.dataThemeStatus)
                .build();
    }


}
