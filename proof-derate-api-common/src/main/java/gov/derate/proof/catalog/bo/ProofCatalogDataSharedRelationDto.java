package gov.derate.proof.catalog.bo;

import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * <p>
 * 证明目录关联的数据共享
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/8/2 15:11
 * </p>
 *
 * <AUTHOR>
 */
public class ProofCatalogDataSharedRelationDto extends ProofCatalogDataSharedConfigManagerDto {
    /**
     * 证明目录ID
     */
    @JsonProperty("proof_catalog_id")
    private String proofCatalogId;
    /**
     * 证明目录code
     */
    @JsonProperty("proof_catalog_code")
    private String proofCatalogCode;
    /**
     * 协查部门名称
     */
    @JsonProperty("investigation_dept_name")
    @Deprecated
    private String investigationDeptName;
    /**
     * 协查部门编码
     */
    @JsonProperty("investigation_dept_code")
    @Deprecated
    private String investigationDeptCode;
    /**
     * 数据协查说明
     */
    @JsonProperty("note")
    @Deprecated
    private String note;
    /**
     * 数据参数
     */
    @JsonProperty("investigation_parameter")
    @Deprecated
    private String investigationParameter;
    /**
     * 实施区域代码
     */
    @JsonProperty("division_code")
    @Deprecated
    private String divisionCode;
    /**
     * 实施区域名称
     */
    @JsonProperty("division_name")
    @Deprecated
    private String divisionName;

    /**
     * 配置json
     * 解析类： ProofCatalogDataSharedConfigBo
     */
    @JsonProperty("config_json")
    private String configJson;
    /**
     * 数据共享id
     */
    @JsonProperty("data_shared_config_id")
    private String dataSharedConfigId;

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getInvestigationDeptName() {
        return investigationDeptName;
    }

    public void setInvestigationDeptName(String investigationDeptName) {
        this.investigationDeptName = investigationDeptName;
    }

    public String getInvestigationDeptCode() {
        return investigationDeptCode;
    }

    public void setInvestigationDeptCode(String investigationDeptCode) {
        this.investigationDeptCode = investigationDeptCode;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getInvestigationParameter() {
        return investigationParameter;
    }

    public void setInvestigationParameter(String investigationParameter) {
        this.investigationParameter = investigationParameter;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName;
    }

    public String getConfigJson() {
        return configJson;
    }

    public void setConfigJson(String configJson) {
        this.configJson = configJson;
    }

    public String getDataSharedConfigId() {
        return dataSharedConfigId;
    }

    public void setDataSharedConfigId(String dataSharedConfigId) {
        this.dataSharedConfigId = dataSharedConfigId;
    }

    public String getProofCatalogCode() {
        return proofCatalogCode;
    }

    public void setProofCatalogCode(String proofCatalogCode) {
        this.proofCatalogCode = proofCatalogCode;
    }

    @Override
    public String toString() {
        return "ProofCatalogDataSharedRelationBo{" +
                "proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogCode='" + proofCatalogCode + '\'' +
                ", investigationDeptName='" + investigationDeptName + '\'' +
                ", investigationDeptCode='" + investigationDeptCode + '\'' +
                ", note='" + note + '\'' +
                ", investigationParameter='" + investigationParameter + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", divisionName='" + divisionName + '\'' +
                ", configJson='" + configJson + '\'' +
                ", dataSharedConfigId='" + dataSharedConfigId + '\'' +
                '}';
    }
}
