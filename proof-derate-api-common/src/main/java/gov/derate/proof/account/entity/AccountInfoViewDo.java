package gov.derate.proof.account.entity;

import gov.derate.proof.common.enums.ExemptIdentityTypeEnum;
import gov.derate.proof.common.enums.convert.ExemptIdentityTypeEnumConvert;
import gov.license.ca.converter.EncryptStringConverter;
import gov.license.common.api.entity.BaseDo;
import org.hibernate.annotations.Subselect;
import org.springframework.data.annotation.Immutable;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;

/**
 * 账号与用户信息 视图类
 *
 * <AUTHOR>
 * @date 2024/1/9
 */
@Entity
@Immutable
@Subselect(value = "select a.account_name,a.user_name,ai.*,o.id org_id,o.org_name,o.division_name,o.division_code,o.credit_code " +
        "from tbl_account_info ai " +
        "left join tbl_account a on a.id = ai.account_id " +
        "left join tbl_account_org_relation r on a.id = r.user_id " +
        "left join tbl_organization o on o.id = r.org_id ")
public class AccountInfoViewDo extends BaseDo {

    private static final long serialVersionUID = -3827829004423989817L;

    /**
     * 用户id
     */
    @Column(name = "ACCOUNT_ID")
    private String accountId;

    /**
     * 账号
     */
    @Column(name = "ACCOUNT_NAME")
    @Convert(converter = EncryptStringConverter.class)
    private String accountName;

    /**
     * 姓名
     */
    @Column(name = "USER_NAME")
    private String userName;

    /**
     * 身份证件类型
     */
    @Column(name = "IDENTITY_TYPE")
    @Convert(converter = ExemptIdentityTypeEnumConvert.class)
    private ExemptIdentityTypeEnum identityType;

    /**
     * 身份证件号码
     */
    @Column(name = "IDENTITY_NUMBER")
    @Convert(converter = EncryptStringConverter.class)
    private String identityNumber;

    /**
     * 联系电话(固定电话)
     */
    @Column(name = "CONTACT_PHONE")
    private String contactPhone;

    /**
     * 移动电话(手机号)
     */
    @Column(name = "MOBILE_PHONE")
    private String mobilePhone;

    /**
     * 电子邮箱
     */
    @Column(name = "EMAIL")
    private String email;

    /**
     * 行政区划名称
     */
    @Column(name = "DIVISION_NAME")
    private String divisionName;

    /**
     * 行政区划代码(实施区划)
     */
    @Column(name = "DIVISION_CODE")
    private String divisionCode;

    /**
     * 统一社会信用代码
     */
    @Column(name = "CREDIT_CODE")
    private String creditCode;

    /**
     * 实施机构
     */
    @Column(name = "ORG_ID")
    private String orgId;

    /**
     * 实施机构名称
     */
    @Column(name = "ORG_NAME")
    private String orgName;
    /**
     * 粤政易业务id
     */
    @Column(name = "GOV_EASY_ID")
    private String govEasyId;

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public ExemptIdentityTypeEnum getIdentityType() {
        return identityType;
    }

    public void setIdentityType(ExemptIdentityTypeEnum identityType) {
        this.identityType = identityType;
    }

    public String getIdentityNumber() {
        return identityNumber;
    }

    public void setIdentityNumber(String identityNumber) {
        this.identityNumber = identityNumber;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getGovEasyId() {
        return govEasyId;
    }

    public void setGovEasyId(String govEasyId) {
        this.govEasyId = govEasyId;
    }

    @Override
    public String toString() {
        return "AccountInfoViewDo{" +
                "accountId='" + accountId + '\'' +
                ", accountName='" + accountName + '\'' +
                ", userName='" + userName + '\'' +
                ", identityType=" + identityType +
                ", identityNumber='" + identityNumber + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", mobilePhone='" + mobilePhone + '\'' +
                ", email='" + email + '\'' +
                ", divisionName='" + divisionName + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", creditCode='" + creditCode + '\'' +
                ", orgId='" + orgId + '\'' +
                ", orgName='" + orgName + '\'' +
                ", govEasyId='" + govEasyId + '\'' +
                '}';
    }
}
