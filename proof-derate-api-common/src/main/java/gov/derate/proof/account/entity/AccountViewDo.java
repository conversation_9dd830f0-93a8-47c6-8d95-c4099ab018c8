package gov.derate.proof.account.entity;

import gov.licc.func.api.auth.enums.AccountStatusEnum;
import gov.licc.func.api.auth.enums.AuthTypeEnum;
import gov.license.ca.converter.EncryptStringConverter;
import gov.license.common.api.entity.BaseDo;
import org.hibernate.annotations.Subselect;
import org.springframework.data.annotation.Immutable;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import java.util.Date;

/**
 * 账号视图类
 *
 * <AUTHOR>
 * @Date 2024/1/18 14:18
 */
@Entity
@Immutable
@Subselect(value = "select a.*, i.id as account_info_id " +
        "from tbl_account a left join tbl_account_info i on a.id = i.account_id")
public class AccountViewDo extends BaseDo {
    private static final long serialVersionUID = -2270258819153240744L;

    /**
     * 用户名
     */
    @Column(name = "USER_NAME")
    private String userName;

    /**
     * 账号
     */
    @Column(name = "ACCOUNT_NAME")
    @Convert(converter = EncryptStringConverter.class)
    private String account;

    /**
     * 密码
     */
    @Column(name = "PASSWORD")
    @Convert(converter = EncryptStringConverter.class)
    private String password;

    /**
     * 账号状态
     */
    @Column(name = "STATUS")
    private AccountStatusEnum status;

    /**
     * 密码最后修改时间
     */
    @Column(name = "PASSWORD_LAST_MODIFIED")
    private Date passwordLastModified;

    /**
     * 系统账号类型
     */
    @Column(name = "AUTH_TYPE")
    private AuthTypeEnum authType;

    /**
     * 最后登陆时间
     */
    @Column(name = "LAST_LOGIN_TIME")
    private Date lastLoginTime;

    /**
     * 签名值
     */
    @Column(name = "SIGN")
    private String sign;

    /**
     * 用户信息id
     */
    @Column(name = "ACCOUNT_INFO_ID")
    private String accountInfoId;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public AccountStatusEnum getStatus() {
        return status;
    }

    public void setStatus(AccountStatusEnum status) {
        this.status = status;
    }

    public Date getPasswordLastModified() {
        return passwordLastModified;
    }

    public void setPasswordLastModified(Date passwordLastModified) {
        this.passwordLastModified = passwordLastModified;
    }

    public AuthTypeEnum getAuthType() {
        return authType;
    }

    public void setAuthType(AuthTypeEnum authType) {
        this.authType = authType;
    }

    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getAccountInfoId() {
        return accountInfoId;
    }

    public void setAccountInfoId(String accountInfoId) {
        this.accountInfoId = accountInfoId;
    }
}
