package gov.derate.proof.account.entity;

import gov.derate.proof.common.enums.ExemptIdentityTypeEnum;
import gov.derate.proof.common.enums.convert.ExemptIdentityTypeEnumConvert;
import gov.license.ca.converter.EncryptStringConverter;
import gov.license.common.api.entity.BaseDo;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 用户明细信息类
 *
 * <AUTHOR>
 * @Date 2023/12/22 16:17
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_ACCOUNT_INFO")
public class AccountInfoDo extends BaseDo {

    private static final long serialVersionUID = 5026936343956109766L;

    /**
     * 用户id
     */
    @Column(name = "ACCOUNT_ID")
    private String accountId;
    /**
     * 身份证件号码
     */
    @Column(name = "IDENTITY_NUMBER")
    @Convert(converter = EncryptStringConverter.class)
    private String identityNumber;
    /**
     * 身份证件类型
     */
    @Column(name = "IDENTITY_TYPE")
    @Convert(converter = ExemptIdentityTypeEnumConvert.class)
    private ExemptIdentityTypeEnum identityType;
    /**
     * 联系电话(固定电话)
     */
    @Column(name = "CONTACT_PHONE")
    private String contactPhone;
    /**
     * 移动电话(手机号)
     */
    @Column(name = "MOBILE_PHONE")
    private String mobilePhone;
    /**
     * 电子邮箱
     */
    @Column(name = "EMAIL")
    private String email;
    /**
     * 粤政易用户id
     */
    @Column(name = "GOV_EASY_ID")
    private String govEasyId;

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getIdentityNumber() {
        return identityNumber;
    }

    public void setIdentityNumber(String identityNumber) {
        this.identityNumber = identityNumber;
    }

    public ExemptIdentityTypeEnum getIdentityType() {
        return identityType;
    }

    public void setIdentityType(ExemptIdentityTypeEnum identityType) {
        this.identityType = identityType;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getGovEasyId() {
        return govEasyId;
    }

    public void setGovEasyId(String govEasyId) {
        this.govEasyId = govEasyId;
    }

    @Override
    public String toString() {
        return "AccountInfoDo{" +
                "accountId='" + accountId + '\'' +
                ", identityNumber='" + identityNumber + '\'' +
                ", identityType=" + identityType +
                ", contactPhone='" + contactPhone + '\'' +
                ", mobilePhone='" + mobilePhone + '\'' +
                ", email='" + email + '\'' +
                ", govEasyId='" + govEasyId + '\'' +
                '}';
    }
}

