package gov.derate.proof.account.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.enums.ExemptIdentityTypeEnum;
import gov.derate.proof.common.utils.MD5Utils;
import gov.license.common.api.dto.BaseDto;
import org.apache.commons.lang3.StringUtils;

/**
 * 用户明细信息传输类
 *
 * <AUTHOR>
 * @Date 2023/12/22 16:33
 */
@JsonIgnoreProperties({"creatorId", "lastModificatorId", "creationTime", "lastModificationTime"})
public class AccountInfoDto extends BaseDto {

    private static final long serialVersionUID = 6024936343956109686L;
    /**
     * 用户id
     */
    @JsonProperty("account_id")
    private String accountId;
    /**
     * 账号
     */
    @JsonProperty("account_name")
    private String accountName;
    /**
     * 姓名
     */
    @JsonProperty("user_name")
    private String userName;
    /**
     * 身份证件号码
     */
    @JsonProperty("identity_number")
    private String identityNumber;


    /**
     * 身份证件类型
     */
    @JsonProperty("identity_type")
    private ExemptIdentityTypeEnum identityType;
    /**
     * 身份证件类型名称
     */
    @JsonProperty("identity_type_name")
    private String identityTypeName;
    /**
     * 联系电话(固定电话)
     */
    @JsonProperty("contact_phone")
    private String contactPhone;

    /**
     * 移动电话(手机号)
     */
    @JsonProperty("mobile_phone")
    private String mobilePhone;

    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 粤政易用户id
     */
    @JsonProperty("gov_easy_id")
    private String govEasyId;
    /**
     * 行政区划
     */
    @JsonProperty("division_code")
    private String divisionCode;
    /**
     * 行政区划
     */
    @JsonProperty("division_name")
    private String divisionName;
    /**
     * 统一社会信用代码
     */
    @JsonProperty("credit_code")
    private String creditCode;
    /**
     * 社会统一信用代码
     */
    @JsonIgnore
    private String tyshxxdm;

    /**
     * 部门id
     */
    @JsonIgnore
    private String orgId;
    /**
     * 组织编码
     */
    @JsonIgnore
    private String orgCode;
    /**
     * 组织名称
     */
    @JsonProperty("org_name")
    private String orgName;

    /**
     * 比对是不是同一个用户信息
     *
     * @param compareDto 比对用户信息
     * @return md5数据字段一致，则一致，否则返回false
     */
    public boolean isSameUserInfo(AccountInfoDto compareDto) {
        String currentUserMd5 = MD5Utils.getMd5(String.join(",", this.accountId, this.accountName, this.userName, this.identityNumber, this.mobilePhone));
        String compareUserMd5 = MD5Utils.getMd5(String.join(",", compareDto.accountId, compareDto.accountName, compareDto.userName, compareDto.identityNumber, compareDto.mobilePhone));
        return StringUtils.equals(currentUserMd5, compareUserMd5);
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getIdentityNumber() {
        return identityNumber;
    }

    public void setIdentityNumber(String identityNumber) {
        this.identityNumber = identityNumber;
    }

    public ExemptIdentityTypeEnum getIdentityType() {
        return identityType;
    }

    public void setIdentityType(ExemptIdentityTypeEnum identityType) {
        this.identityType = identityType;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTyshxxdm() {
        return tyshxxdm;
    }

    public void setTyshxxdm(String tyshxxdm) {
        this.tyshxxdm = tyshxxdm;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getIdentityTypeName() {
        return identityTypeName;
    }

    public void setIdentityTypeName(String identityTypeName) {
        this.identityTypeName = identityTypeName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getGovEasyId() {
        return govEasyId;
    }

    public void setGovEasyId(String govEasyId) {
        this.govEasyId = govEasyId;
    }

    @Override
    public String toString() {
        return "AccountInfoDto{" +
                "accountId='" + accountId + '\'' +
                ", accountName='" + accountName + '\'' +
                ", userName='" + userName + '\'' +
                ", identityNumber='" + identityNumber + '\'' +
                ", identityType=" + identityType +
                ", identityTypeName='" + identityTypeName + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", mobilePhone='" + mobilePhone + '\'' +
                ", email='" + email + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", divisionName='" + divisionName + '\'' +
                ", creditCode='" + creditCode + '\'' +
                ", tyshxxdm='" + tyshxxdm + '\'' +
                ", orgId='" + orgId + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", orgName='" + orgName + '\'' +
                '}';
    }
}
