package gov.derate.proof.account.resp;

import gov.license.common.api.resp.BaseResponseCode;

/**
 * 用户信息管理 异常编码类
 * <p>
 * 错误级别 + 系统 + 子系统 + 模块/功能 + 动作 + 具体异常
 * 平台管理->用户与权限->用户管理
 *
 * <AUTHOR>
 * @date 2024/1/11
 */
public class AccountInfoResponseCode extends BaseResponseCode {

    /**
     * 服务级别 - 查询用户信息异常
     */
    public static final AccountInfoResponseCode SERVICE_QUERY_ERROR =
            new AccountInfoResponseCode("2C010101000", "查询用户信息异常");
    /**
     * 服务级别 - 用户信息不存在
     */
    public static final AccountInfoResponseCode ACCOUNT_INFO_QUERY_NOT_EXISTS_ERROR =
            new AccountInfoResponseCode("2C010101001", "用户信息不存在", true);
    /**
     * 参数级别 - 查询用户信息异常
     */
    public static final AccountInfoResponseCode PARAM_QUERY_ERROR =
            new AccountInfoResponseCode("3C010101000", "查询用户信息异常");
    /**
     * 参数级别 - 移动电话号码(手机号)为空
     */
    public static final AccountInfoResponseCode ACCOUNT_INFO_QUERY_MOBILE_PHONE_EMPTY =
            new AccountInfoResponseCode("3C010101001", "手机号为空");
    /**
     * 参数级别 - 用户信息id为空
     */
    public static final AccountInfoResponseCode ACCOUNT_INFO_QUERY_ID_EMPTY =
            new AccountInfoResponseCode("3C010101002", "用户信息id为空");


    /**
     * 服务级别 - 创建用户信息异常
     */
    public static final AccountInfoResponseCode SERVICE_CREATE_ERROR =
            new AccountInfoResponseCode("2C010102000", "创建用户信息异常");
    /**
     * 服务级别 - 联系电话(手机号)已存在,请重新输入
     */
    public static final AccountInfoResponseCode ACCOUNT_INFO_CREATE_MOBILE_PHONE_EXISTS_ERROR =
            new AccountInfoResponseCode("2C010102001", "联系电话(手机号)已存在,请重新输入");
    /**
     * 服务级别 - 账号不存在
     */
    public static final AccountInfoResponseCode ACCOUNT_INFO_CREATE_NOT_EXISTS_ERROR =
            new AccountInfoResponseCode("2C010102002", "账号不存在");
    /**
     * 参数级别 - 创建用户信息异常
     */
    public static final AccountInfoResponseCode PARAM_CREATE_ERROR =
            new AccountInfoResponseCode("3C010102000", "创建用户信息异常");
    /**
     * 参数级别 - 账号为空
     */
    public static final AccountInfoResponseCode ACCOUNT_INFO_CREATE_ACCOUNT_EMPTY =
            new AccountInfoResponseCode("3C010102001", "账号为空");


    /**
     * 服务级别 - 编辑用户信息异常
     */
    public static final AccountInfoResponseCode SERVICE_EDIT_ERROR =
            new AccountInfoResponseCode("2C010103000", "编辑用户信息异常");
    /**
     * 服务级别 - 联系电话(手机号)已存在,请重新输入
     */
    public static final AccountInfoResponseCode ACCOUNT_INFO_EDIT_MOBILE_PHONE_EXISTS_ERROR =
            new AccountInfoResponseCode("2C010103001", "联系电话(手机号)已存在,请重新输入");
    /**
     * 服务级别 - 用户信息不存在
     */
    public static final AccountInfoResponseCode ACCOUNT_INFO_EDIT_NOT_EXISTS_ERROR =
            new AccountInfoResponseCode("2C010103002", "用户信息不存在");
    /**
     * 参数级别 - 编辑用户信息异常
     */
    public static final AccountInfoResponseCode PARAM_EDIT_ERROR =
            new AccountInfoResponseCode("3C010103000", "编辑用户信息异常");
    /**
     * 参数级别 - 用户信息id为空
     */
    public static final AccountInfoResponseCode ACCOUNT_INFO_EDIT_ACCOUNT_INFO_ID_EMPTY =
            new AccountInfoResponseCode("3C010103001", "用户信息id为空");
    /**
     * 参数级别 - 账号为空
     */
    public static final AccountInfoResponseCode ACCOUNT_INFO_EDIT_ACCOUNT_EMPTY =
            new AccountInfoResponseCode("3C010103002", "账号为空");
    /**
     * 参数级别 - 账号id为空
     */
    public static final AccountInfoResponseCode ACCOUNT_INFO_EDIT_ACCOUNT_ID_EMPTY =
            new AccountInfoResponseCode("3C010103003", "账号id为空");


    /**
     * 参数级别 - 删除用户信息异常
     */
    public static final AccountInfoResponseCode PARAM_DELETE_ERROR =
            new AccountInfoResponseCode("3C010104000", "删除用户信息异常");
    /**
     * 参数级别 - 用户信息id为空
     */
    public static final AccountInfoResponseCode ACCOUNT_INFO_DELETE_ID_EMPTY =
            new AccountInfoResponseCode("3C010104001", "用户信息id为空");


    /**
     * 服务级别 - 粤证易登录异常
     */
    public static final AccountInfoResponseCode SERVICE_YZY_LOGIN_ERROR =
            new AccountInfoResponseCode("2C010105000", "粤证易登录异常");
    /**
     * 服务级别 - 账号不存在
     */
    public static final AccountInfoResponseCode ACCOUNT_YZY_LOGIN_NOT_EXISTS_ERROR =
            new AccountInfoResponseCode("2C010105001", "账号不存在");


    public AccountInfoResponseCode(String code, String message) {
        super(code, message, true);
    }

    public AccountInfoResponseCode(String code, String message, boolean replaceFlag) {
        super(code, message, replaceFlag);
    }
}
