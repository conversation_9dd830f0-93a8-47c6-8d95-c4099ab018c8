package gov.derate.proof.assist.enums;

/**
 * 开具电子证明 类型
 *
 * <AUTHOR>
 * @date 2025/9/1.
 */
public enum IssueProofLicenseTypeEnum {
    /**
     * 不开具
     */
    NOT_ISSUE,
    /**
     * 开具
     */
    ISSUE,
    /**
     * 不开具,任一部门审核不通过则不开具
     */
    NOT_ISSUE_ANY_NO_PASS,
    /**
     * 不开具,最后一级部门审核不通过则不开具
     */
    NOT_ISSUE_LAST_NO_PASS,

    ;

    public static Boolean isIssue(IssueProofLicenseTypeEnum type) {
        return !type.equals(NOT_ISSUE);
    }
}
