package gov.derate.proof.assist.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.common.collect.Lists;
import gov.derate.proof.assist.convert.LegalPersonIdentityTypeJsonSerialize;
import gov.derate.proof.assist.enums.IssueProofLicenseTypeEnum;
import gov.derate.proof.catalog.dto.ProofCatalogLicenseItemImplItemInfoDto;
import gov.derate.proof.catalog.enums.IssueProofLicenseWay;
import gov.derate.proof.common.bo.BaseBo;
import gov.derate.proof.common.enums.*;
import gov.license.common.tools.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 协查服务页面返回dto
 * </p>
 * rights reserved. </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022-04-07
 * </p>
 *
 * <AUTHOR>
 */
public class AssistRecordDto extends BaseBo {

    private static final long serialVersionUID = -1771801782811623692L;

    /**
     * 办事人姓名
     */
    @JsonProperty("handle_affairs_name")
    private String handleAffairsName;

    /**
     * 办事人证件类型 1:身份证  2:军官证   3:护照  4:港澳通行证   5:其他
     */
    @JsonProperty("handle_affairs_identity_type")
    private AssistIdentityType handleAffairsAssistIdentityType;

    /**
     * 办事人证件号码
     */
    @JsonProperty("handle_affairs_identity_number")
    private String handleAffairsIdentityNumber;

    /**
     * 协查部门名称
     */
    @JsonProperty("to_assist_org_name")
    @JsonIgnore
    private String toAssistOrgName;

    /**
     * 协查部门统一信用代码
     */
    @JsonProperty("to_assist_credit_code")
    @JsonIgnore
    private String toAssistCreditCode;


    /**
     * 协查发起人部门名称
     */
    @JsonProperty("from_assist_org_name")
    private String fromAssistOrgName;

    /**
     * 协查发起人部门统一信用代码
     */
    @JsonProperty("from_assist_credit_code")
    private String fromAssistCreditCode;
    /**
     * 协查发起人联系方式
     */
    @JsonProperty("from_assist_contain")
    private String fromAssistContain;

    /**
     * 协查业务序列号
     */
    @JsonProperty("business_serial_number")
    private String businessSerialNumber;
    /**
     * 协查发起时间
     */
    @JsonProperty("assist_time")
    private Date assistTime;

    /**
     * 协查流水号
     */
    @JsonProperty("assist_serial_number")
    private String assistSerialNumber;

    /**
     * 协查审核结果
     */
    @JsonProperty("audit_result")
    private AssistResultEnum auditResult;

    /**
     * 粤证易-协查人发送消息用户id
     */
    @JsonProperty("to_user_id")
    @Deprecated
    @JsonIgnore
    private String toUserId;
    /**
     * 粤证易-协查人发送消息用户名称
     */
    @JsonProperty("to_user_name")
    @Deprecated
    @JsonIgnore
    private String toUserName;
    /**
     * 粤证易-审核消息用户id
     */
    @JsonProperty("audit_user_id")
    @Deprecated
    private String auditUserId;
    /**
     * 协查审核意见
     */
    @JsonProperty("audit_suggestion")
    @Deprecated
    private String auditSuggestion;

    /**
     * 协查审核时间
     */
    @JsonProperty("audit_time")
    private Date auditTime;

    /**
     * 历史关联的协查号码
     */
    @JsonProperty("history_assist_serial_number")
    private String historyAssistSerialNumber;


    /**
     * 事项编码
     */
    @JsonProperty("item_code")
    private String itemCode;
    /**
     * 事项名称
     */
    @JsonProperty("item_name")
    private String itemName;
    /**
     * 材料id[对应无证明城市系统，审核通过的清单的材料id]
     */
    @JsonProperty("material_id")
    private String materialId;

    /**
     * 材料名称
     */
    @JsonProperty("material_name")
    private String materialName;

    /**
     * 办事人类型
     */
    @JsonProperty("handle_affairs_type")
    private AssistHandleAffairsTypeEnum handleAffairsType;

    /**
     * 协查需求描述
     */
    @JsonProperty("from_demand")
    private String fromDemand;
    /**
     * 协查人联系方式
     */
    @JsonProperty("to_user_contain")
    @Deprecated
    @JsonIgnore
    private String toUserContain;

    /**
     * 协查发起人名称
     */
    @JsonProperty("from_assist_user_name")
    private String fromAssistUserName;

    /**
     * 法人身份类型
     */
    @JsonProperty("legal_person_identity_type")
    @JsonSerialize(using = LegalPersonIdentityTypeJsonSerialize.class)
    private LegalPersonIdentityType legalPersonIdentityType;

    /**
     * 协查内容List
     */
    @JsonProperty("assist_data_list")
    private List<AssistInfoBo> assistDataList;
    /**
     * 剩余处理时限(分钟)
     */
    @JsonProperty("residual_treatment_limit")
    private Integer residualTreatmentLimit;

    /**
     * 剩余处理时限(小时)
     */
    @JsonProperty("residual_treatment_hour_limit")
    private Integer residualTreatmentHourLimit;
    /**
     * 证明目录id
     */
    @JsonProperty("proof_catalog_id")
    private String proofCatalogId;
    /**
     * 证明目录名称
     */
    @JsonProperty("proof_catalog_name")
    private String proofCatalogName;

    /**
     * 过期时间
     */
    @JsonProperty("invalid_date")
    private Date invalidDate;
    /**
     * 是否回调成功flag
     */
    @JsonProperty("call_back")
    @JsonIgnore
    private Boolean callBack = false;

    /**
     * 电子证明归档authCode
     */
    @JsonProperty("licc_item_auth_code")
    private String licenseItemAuthCode;
    /**
     * 协查方式
     */
    @JsonProperty("catalog_repl_type")
    @JsonIgnore
    private AssistProofCatalogReplaceType assistProofCatalogReplaceType = AssistProofCatalogReplaceType.ARTIFICIAL;
    /**
     * 替代方式-电子证明的证照目录实施码
     */
    @JsonProperty("implement_code")
    private String implementCode;
    /**
     * 实施目录信息
     * 解析对象 List<ProofCatalogLicenseItemImplItemInfo>
     */
    @JsonProperty("impl_item_info_json")
    @JsonIgnore
    private String implementItemInfoJson;
    /**
     * 是否开具电子证明
     */
    @JsonProperty("issue_p_license")
    private IssueProofLicenseTypeEnum issueProofLicense;
    /**
     * 电子证明开具方式;系统生成，人工开具
     */
    @JsonProperty("issue_p_license_way")
    private IssueProofLicenseWay issueProofLicenseWay;

    /**
     * 构造目录信息对象
     *
     * @return 实施目录信息对象
     */
    public List<ProofCatalogLicenseItemImplItemInfoDto> buildByImplItemInfoJson() {
        if (StringUtils.isNotBlank(this.implementItemInfoJson)) {
            return JacksonUtil.toList(this.implementItemInfoJson, ProofCatalogLicenseItemImplItemInfoDto.class);
        }
        return Lists.newArrayList();
    }

    public String getHandleAffairsName() {
        return handleAffairsName;
    }

    public void setHandleAffairsName(String handleAffairsName) {
        this.handleAffairsName = handleAffairsName;
    }

    public AssistIdentityType getHandleAffairsAssistIdentityType() {
        return handleAffairsAssistIdentityType;
    }

    public void setHandleAffairsAssistIdentityType(AssistIdentityType handleAffairsAssistIdentityType) {
        this.handleAffairsAssistIdentityType = handleAffairsAssistIdentityType;
    }

    public String getHandleAffairsIdentityNumber() {
        return handleAffairsIdentityNumber;
    }

    public void setHandleAffairsIdentityNumber(String handleAffairsIdentityNumber) {
        this.handleAffairsIdentityNumber = handleAffairsIdentityNumber;
    }

    public String getToAssistOrgName() {
        return toAssistOrgName;
    }

    public void setToAssistOrgName(String toAssistOrgName) {
        this.toAssistOrgName = toAssistOrgName;
    }

    public String getToAssistCreditCode() {
        return toAssistCreditCode;
    }

    public void setToAssistCreditCode(String toAssistCreditCode) {
        this.toAssistCreditCode = toAssistCreditCode;
    }

    public String getFromAssistOrgName() {
        return fromAssistOrgName;
    }

    public void setFromAssistOrgName(String fromAssistOrgName) {
        this.fromAssistOrgName = fromAssistOrgName;
    }

    public String getFromAssistCreditCode() {
        return fromAssistCreditCode;
    }

    public void setFromAssistCreditCode(String fromAssistCreditCode) {
        this.fromAssistCreditCode = fromAssistCreditCode;
    }

    public String getFromAssistContain() {
        return fromAssistContain;
    }

    public void setFromAssistContain(String fromAssistContain) {
        this.fromAssistContain = fromAssistContain;
    }

    public String getBusinessSerialNumber() {
        return businessSerialNumber;
    }

    public void setBusinessSerialNumber(String businessSerialNumber) {
        this.businessSerialNumber = businessSerialNumber;
    }

    public Date getAssistTime() {
        return assistTime;
    }

    public void setAssistTime(Date assistTime) {
        this.assistTime = assistTime;
    }

    public String getAssistSerialNumber() {
        return assistSerialNumber;
    }

    public void setAssistSerialNumber(String assistSerialNumber) {
        this.assistSerialNumber = assistSerialNumber;
    }

    public AssistResultEnum getAuditResult() {
        return auditResult;
    }

    public void setAuditResult(AssistResultEnum auditResult) {
        this.auditResult = auditResult;
    }

    public String getToUserId() {
        return toUserId;
    }

    public void setToUserId(String toUserId) {
        this.toUserId = toUserId;
    }

    public String getToUserName() {
        return toUserName;
    }

    public void setToUserName(String toUserName) {
        this.toUserName = toUserName;
    }

    public String getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(String auditUserId) {
        this.auditUserId = auditUserId;
    }

    public String getAuditSuggestion() {
        return auditSuggestion;
    }

    public void setAuditSuggestion(String auditSuggestion) {
        this.auditSuggestion = auditSuggestion;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getHistoryAssistSerialNumber() {
        return historyAssistSerialNumber;
    }

    public void setHistoryAssistSerialNumber(String historyAssistSerialNumber) {
        this.historyAssistSerialNumber = historyAssistSerialNumber;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getMaterialId() {
        return materialId;
    }

    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public AssistHandleAffairsTypeEnum getHandleAffairsType() {
        return handleAffairsType;
    }

    public void setHandleAffairsType(AssistHandleAffairsTypeEnum handleAffairsType) {
        this.handleAffairsType = handleAffairsType;
    }

    public String getFromDemand() {
        return fromDemand;
    }

    public void setFromDemand(String fromDemand) {
        this.fromDemand = fromDemand;
    }

    public String getFromAssistUserName() {
        return fromAssistUserName;
    }

    public void setFromAssistUserName(String fromAssistUserName) {
        this.fromAssistUserName = fromAssistUserName;
    }

    public LegalPersonIdentityType getLegalPersonIdentityType() {
        return legalPersonIdentityType;
    }

    public void setLegalPersonIdentityType(LegalPersonIdentityType legalPersonIdentityType) {
        this.legalPersonIdentityType = legalPersonIdentityType;
    }

    public List<AssistInfoBo> getAssistDataList() {
        return assistDataList;
    }

    public void setAssistDataList(List<AssistInfoBo> assistDataList) {
        this.assistDataList = assistDataList;
    }

    public Integer getResidualTreatmentLimit() {
        return residualTreatmentLimit;
    }

    public void setResidualTreatmentLimit(Integer residualTreatmentLimit) {
        this.residualTreatmentLimit = residualTreatmentLimit;
    }

    public Integer getResidualTreatmentHourLimit() {
        return residualTreatmentHourLimit;
    }

    public void setResidualTreatmentHourLimit(Integer residualTreatmentHourLimit) {
        this.residualTreatmentHourLimit = residualTreatmentHourLimit;
    }

    public String getProofCatalogName() {
        return proofCatalogName;
    }

    public void setProofCatalogName(String proofCatalogName) {
        this.proofCatalogName = proofCatalogName;
    }

    public Date getInvalidDate() {
        return invalidDate;
    }

    public void setInvalidDate(Date invalidDate) {
        this.invalidDate = invalidDate;
    }

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getToUserContain() {
        return toUserContain;
    }

    public void setToUserContain(String toUserContain) {
        this.toUserContain = toUserContain;
    }

    public Boolean getCallBack() {
        return callBack;
    }

    public void setCallBack(Boolean callBack) {
        this.callBack = callBack;
    }

    public String getLicenseItemAuthCode() {
        return licenseItemAuthCode;
    }

    public void setLicenseItemAuthCode(String licenseItemAuthCode) {
        this.licenseItemAuthCode = licenseItemAuthCode;
    }

    public AssistProofCatalogReplaceType getAssistProofCatalogReplaceType() {
        return assistProofCatalogReplaceType;
    }

    public void setAssistProofCatalogReplaceType(AssistProofCatalogReplaceType assistProofCatalogReplaceType) {
        this.assistProofCatalogReplaceType = assistProofCatalogReplaceType;
    }

    public String getImplementCode() {
        return implementCode;
    }

    public void setImplementCode(String implementCode) {
        this.implementCode = implementCode;
    }

    public String getImplementItemInfoJson() {
        return implementItemInfoJson;
    }

    public void setImplementItemInfoJson(String implementItemInfoJson) {
        this.implementItemInfoJson = implementItemInfoJson;
    }

    public IssueProofLicenseTypeEnum getIssueProofLicense() {
        return issueProofLicense;
    }

    public void setIssueProofLicense(IssueProofLicenseTypeEnum issueProofLicense) {
        this.issueProofLicense = issueProofLicense;
    }

    public IssueProofLicenseWay getIssueProofLicenseWay() {
        return issueProofLicenseWay;
    }

    public void setIssueProofLicenseWay(IssueProofLicenseWay issueProofLicenseWay) {
        this.issueProofLicenseWay = issueProofLicenseWay;
    }

    @Override
    public String toString() {
        return "AssistRecordDto{" +
                "handleAffairsName='" + handleAffairsName + '\'' +
                ", handleAffairsAssistIdentityType=" + handleAffairsAssistIdentityType +
                ", handleAffairsIdentityNumber='" + handleAffairsIdentityNumber + '\'' +
                ", toAssistOrgName='" + toAssistOrgName + '\'' +
                ", toAssistCreditCode='" + toAssistCreditCode + '\'' +
                ", fromAssistOrgName='" + fromAssistOrgName + '\'' +
                ", fromAssistCreditCode='" + fromAssistCreditCode + '\'' +
                ", fromAssistContain='" + fromAssistContain + '\'' +
                ", businessSerialNumber='" + businessSerialNumber + '\'' +
                ", assistTime=" + assistTime +
                ", assistSerialNumber='" + assistSerialNumber + '\'' +
                ", auditResult=" + auditResult +
                ", toUserId='" + toUserId + '\'' +
                ", toUserName='" + toUserName + '\'' +
                ", auditUserId='" + auditUserId + '\'' +
                ", auditSuggestion='" + auditSuggestion + '\'' +
                ", auditTime=" + auditTime +
                ", historyAssistSerialNumber='" + historyAssistSerialNumber + '\'' +
                ", itemCode='" + itemCode + '\'' +
                ", itemName='" + itemName + '\'' +
                ", materialId='" + materialId + '\'' +
                ", materialName='" + materialName + '\'' +
                ", handleAffairsType=" + handleAffairsType +
                ", fromDemand='" + fromDemand + '\'' +
                ", toUserContain='" + toUserContain + '\'' +
                ", fromAssistUserName='" + fromAssistUserName + '\'' +
                ", legalPersonIdentityType=" + legalPersonIdentityType +
                ", assistDataList=" + assistDataList +
                ", residualTreatmentLimit=" + residualTreatmentLimit +
                ", residualTreatmentHourLimit=" + residualTreatmentHourLimit +
                ", proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogName='" + proofCatalogName + '\'' +
                ", invalidDate=" + invalidDate +
                ", callBack=" + callBack +
                ", licenseItemAuthCode='" + licenseItemAuthCode + '\'' +
                ", assistProofCatalogReplaceType=" + assistProofCatalogReplaceType +
                ", implementCode='" + implementCode + '\'' +
                ", implementItemInfoJson='" + implementItemInfoJson + '\'' +
                ", issueProofLicense=" + issueProofLicense +
                ", issueProofLicenseWay=" + issueProofLicenseWay +
                '}';
    }
}
