package gov.derate.proof.assist.entity;

import com.google.common.collect.Lists;
import gov.derate.proof.assist.enums.IssueProofLicenseTypeEnum;
import gov.derate.proof.catalog.dto.ProofCatalogLicenseItemImplItemInfoDto;
import gov.derate.proof.catalog.enums.IssueProofLicenseWay;
import gov.derate.proof.common.entity.AbstractDomainEntity;
import gov.derate.proof.common.enums.AssistHandleAffairsTypeEnum;
import gov.derate.proof.common.enums.AssistIdentityType;
import gov.derate.proof.common.enums.AssistResultEnum;
import gov.derate.proof.common.enums.LegalPersonIdentityType;
import gov.derate.proof.common.enums.convert.AssistHandleAffairsTypeEnumConvert;
import gov.derate.proof.common.enums.convert.AssistIdentityTypeEnumConvert;
import gov.derate.proof.common.enums.convert.AssistResultEnumConvert;
import gov.derate.proof.common.enums.convert.LegalPersonIdentityTypeConvert;
import gov.license.ca.converter.EncryptStringConverter;
import gov.license.common.tools.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 协查记录
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-10-22
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:yxh;Date:2021-10-22;
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_ASSIST_RECORD")
public class AssistRecordDo extends AbstractDomainEntity {
    /**
     * 办事人姓名
     */
    @Column(name = "HANDLE_AFFAIRS_NAME", length = 300, nullable = false)
    @Convert(converter = EncryptStringConverter.class)
    private String handleAffairsName;

    /**
     * 办事人证件类型 1:身份证  2:军官证   3:护照  4:港澳通行证   5:其他
     */
    @Column(name = "HANDLE_AFFAIRS_ID_TYPE", length = 2)
    @Convert(converter = AssistIdentityTypeEnumConvert.class)
    private AssistIdentityType handleAffairsAssistIdentityType;

    /**
     * 办事人证件号码
     */
    @Column(name = "HANDLE_AFFAIRS_ID_NUMBER", length = 300, nullable = false)
    @Convert(converter = EncryptStringConverter.class)
    private String handleAffairsIdentityNumber;

    /**
     * 协查部门名称
     */
    @Column(name = "TO_ASSIST_ORG_NAME", length = 100, nullable = false)
    private String toAssistOrgName;

    /**
     * 协查部门统一信用代码
     */
    @Column(name = "TO_ASSIST_CREDIT_CODE", length = 100, nullable = false)
    private String toAssistCreditCode;


    /**
     * 协查发起人部门名称
     */
    @Column(name = "FROM_ASSIST_ORG_NAME", length = 100, nullable = false)
    private String fromAssistOrgName;

    /**
     * 协查发起人部门统一信用代码
     */
    @Column(name = "FROM_ASSIST_CREDIT_CODE", length = 100, nullable = false)
    private String fromAssistCreditCode;

    /**
     * 协查发起人名称
     */
    @Column(name = "FROM_ASSIST_USER_NAME", length = 100, nullable = false)
    private String fromAssistUserName;


    /**
     * 协查发起人联系方式
     */
    @Column(name = "FROM_ASSIST_CONTAIN", length = 50)
    private String fromAssistContain;

    /**
     * 业务流水号
     */
    @Column(name = "BUSINESS_SERIAL_NUMBER", length = 50)
    private String businessSerialNumber;
    /**
     * 协查发起时间
     */
    @Column(name = "ASSIST_TIME")
    private Date assistTime;
    /**
     * 协查流水号
     */
    @Column(name = "ASSIST_SERIAL_NUMBER", length = 50, nullable = false)
    private String assistSerialNumber;

    /**
     * 协查审核结果
     */
    @Column(name = "AUDIT_RESULT", length = 2)
    @Convert(converter = AssistResultEnumConvert.class)
    private AssistResultEnum auditResult;

    /**
     * 协查处理人用户id
     */
    @Column(name = "TO_USER_ID", length = 100, nullable = false)
    private String toUserId;
    /**
     * 协查处理人用户名称
     */
    @Column(name = "TO_USER_NAME", length = 200)
    @Convert(converter = EncryptStringConverter.class)
    private String toUserName;

    /**
     * 协查处理人用户id
     */
    @Column(name = "AUDIT_USER_ID", length = 100)
    private String auditUserId;

    /**
     * 审核意见
     */
    @Column(name = "AUDIT_SUGGESTION", length = 255)
    private String auditSuggestion;

    /**
     * 协查审核时间
     */
    @Column(name = "AUDIT_TIME")
    private Date auditTime;

    /**
     * 历史关联的协查号码
     */
    @Column(name = "HISTORY_SERI_NUMBER")
    @Lob
    private String historyAssistSerialNumber;

    /**
     * 事项编码
     */
    @Column(name = "ITEM_CODE", length = 100)
    private String itemCode;
    /**
     * 事项名称
     */
    @Column(name = "ITEM_NAME", length = 100)
    private String itemName;
    /**
     * 材料id[对应无证明城市系统，审核通过的清单的材料id]
     */
    @Column(name = "MATERIAL_ID", length = 100)
    private String materialId;

    /**
     * 办事人类型
     */
    @Column(name = "HANDLE_AFFAIRS_TYPE", length = 10)
    @Convert(converter = AssistHandleAffairsTypeEnumConvert.class)
    private AssistHandleAffairsTypeEnum handleAffairsType;

    /**
     * 协查需求描述
     */
    @Column(name = "FROM_DEMAND", length = 500)
    private String fromDemand;

    /**
     * 协查人联系方式
     */
    @Column(name = "TO_USER_CONTAIN", length = 50)
    private String toUserContain;
    /**
     * 法人身份类型
     */
    @Column(name = "LEGAL_PERSON_IDENTITY_TYPE", length = 2)
    @Convert(converter = LegalPersonIdentityTypeConvert.class)
    private LegalPersonIdentityType legalPersonIdentityType;

    /**
     * 证明目录id
     */
    @Column(name = "PROOF_CATALOG_ID", length = 100)
    private String proofCatalogId;

    /**
     * 证明目录名称
     */
    @Column(name = "PROOF_CATALOG_NAME", length = 100)
    private String proofCatalogName;

    /**
     * 过期时间
     */
    @Column(name = "INVALID_DATE", length = 100)
    private Date invalidDate;
    /**
     * 是否回调成功flag
     */
    @Column(name = "CALL_BACK", length = 1)
    private Boolean callBack = false;

    /**
     * 电子证明归档authCode
     */
    @Column(name = "LICC_ITEM_AUTH_CODE", length = 200)
    private String licenseItemAuthCode;
    /**
     * 证明目录-电子证明-实施码
     */
    @Column(name = "IMPLEMENT_CODE", length = 100)
    private String implementCode;
    /**
     * 实施目录信息
     * 解析对象 List<ProofCatalogLicenseItemImplItemInfo>
     */
    @Column(name = "IMPL_ITEM_INFO_JSON", nullable = true)
    @Lob
    private String implementItemInfoJson;

    /**
     * 是否开具电子证明
     */
    @Column(name = "ISSUE_P_LICENSE", nullable = false)
    private IssueProofLicenseTypeEnum issueProofLicense;
    /**
     * 电子证明开具方式;系统生成，人工开具
     */
    @Column(name = "ISSUE_P_LICENSE_WAY", length = 50, nullable = true)
    @Enumerated(EnumType.STRING)
    private IssueProofLicenseWay issueProofLicenseWay;

    /**
     * 构造目录信息对象
     *
     * @return 实施目录信息对象
     */
    public List<ProofCatalogLicenseItemImplItemInfoDto> buildByImplItemInfoJson() {
        if (StringUtils.isNotBlank(this.implementItemInfoJson)) {
            return JacksonUtil.toList(this.implementItemInfoJson, ProofCatalogLicenseItemImplItemInfoDto.class);
        }
        return Lists.newArrayList();
    }

    public String getHandleAffairsName() {
        return handleAffairsName;
    }

    public void setHandleAffairsName(String handleAffairsName) {
        this.handleAffairsName = handleAffairsName;
    }

    public AssistIdentityType getHandleAffairsAssistIdentityType() {
        return handleAffairsAssistIdentityType;
    }

    public void setHandleAffairsAssistIdentityType(AssistIdentityType handleAffairsAssistIdentityType) {
        this.handleAffairsAssistIdentityType = handleAffairsAssistIdentityType;
    }

    public String getHandleAffairsIdentityNumber() {
        return handleAffairsIdentityNumber;
    }

    public void setHandleAffairsIdentityNumber(String handleAffairsIdentityNumber) {
        this.handleAffairsIdentityNumber = handleAffairsIdentityNumber;
    }

    public String getToAssistOrgName() {
        return toAssistOrgName;
    }

    public void setToAssistOrgName(String toAssistOrgName) {
        this.toAssistOrgName = toAssistOrgName;
    }

    public String getToAssistCreditCode() {
        return toAssistCreditCode;
    }

    public void setToAssistCreditCode(String toAssistCreditCode) {
        this.toAssistCreditCode = toAssistCreditCode;
    }

    public String getFromAssistOrgName() {
        return fromAssistOrgName;
    }

    public void setFromAssistOrgName(String fromAssistOrgName) {
        this.fromAssistOrgName = fromAssistOrgName;
    }

    public String getFromAssistCreditCode() {
        return fromAssistCreditCode;
    }

    public void setFromAssistCreditCode(String fromAssistCreditCode) {
        this.fromAssistCreditCode = fromAssistCreditCode;
    }

    public String getFromAssistUserName() {
        return fromAssistUserName;
    }

    public void setFromAssistUserName(String fromAssistUserName) {
        this.fromAssistUserName = fromAssistUserName;
    }

    public String getFromAssistContain() {
        return fromAssistContain;
    }

    public void setFromAssistContain(String fromAssistContain) {
        this.fromAssistContain = fromAssistContain;
    }

    public String getBusinessSerialNumber() {
        return businessSerialNumber;
    }

    public void setBusinessSerialNumber(String businessSerialNumber) {
        this.businessSerialNumber = businessSerialNumber;
    }

    public Date getAssistTime() {
        return assistTime;
    }

    public void setAssistTime(Date assistTime) {
        this.assistTime = assistTime;
    }

    public String getAssistSerialNumber() {
        return assistSerialNumber;
    }

    public void setAssistSerialNumber(String assistSerialNumber) {
        this.assistSerialNumber = assistSerialNumber;
    }

    public AssistResultEnum getAuditResult() {
        return auditResult;
    }

    public void setAuditResult(AssistResultEnum auditResult) {
        this.auditResult = auditResult;
    }

    public String getToUserId() {
        return toUserId;
    }

    public void setToUserId(String toUserId) {
        this.toUserId = toUserId;
    }

    public String getToUserName() {
        return toUserName;
    }

    public void setToUserName(String toUserName) {
        this.toUserName = toUserName;
    }

    public String getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(String auditUserId) {
        this.auditUserId = auditUserId;
    }

    public String getAuditSuggestion() {
        return auditSuggestion;
    }

    public void setAuditSuggestion(String auditSuggestion) {
        this.auditSuggestion = auditSuggestion;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getHistoryAssistSerialNumber() {
        return historyAssistSerialNumber;
    }

    public void setHistoryAssistSerialNumber(String historyAssistSerialNumber) {
        this.historyAssistSerialNumber = historyAssistSerialNumber;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getMaterialId() {
        return materialId;
    }

    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }

    public AssistHandleAffairsTypeEnum getHandleAffairsType() {
        return handleAffairsType;
    }

    public void setHandleAffairsType(AssistHandleAffairsTypeEnum handleAffairsType) {
        this.handleAffairsType = handleAffairsType;
    }

    public String getFromDemand() {
        return fromDemand;
    }

    public void setFromDemand(String fromDemand) {
        this.fromDemand = fromDemand;
    }

    public String getToUserContain() {
        return toUserContain;
    }

    public void setToUserContain(String toUserContain) {
        this.toUserContain = toUserContain;
    }

    public LegalPersonIdentityType getLegalPersonIdentityType() {
        return legalPersonIdentityType;
    }

    public void setLegalPersonIdentityType(LegalPersonIdentityType legalPersonIdentityType) {
        this.legalPersonIdentityType = legalPersonIdentityType;
    }

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    public String getProofCatalogName() {
        return proofCatalogName;
    }

    public void setProofCatalogName(String proofCatalogName) {
        this.proofCatalogName = proofCatalogName;
    }

    public Date getInvalidDate() {
        return invalidDate;
    }

    public void setInvalidDate(Date invalidDate) {
        this.invalidDate = invalidDate;
    }

    public Boolean getCallBack() {
        return callBack;
    }

    public void setCallBack(Boolean callBack) {
        this.callBack = callBack;
    }

    public String getLicenseItemAuthCode() {
        return licenseItemAuthCode;
    }

    public void setLicenseItemAuthCode(String licenseItemAuthCode) {
        this.licenseItemAuthCode = licenseItemAuthCode;
    }

    public String getImplementCode() {
        return implementCode;
    }

    public void setImplementCode(String implementCode) {
        this.implementCode = implementCode;
    }

    public String getImplementItemInfoJson() {
        return implementItemInfoJson;
    }

    public void setImplementItemInfoJson(String implementItemInfoJson) {
        this.implementItemInfoJson = implementItemInfoJson;
    }

    public IssueProofLicenseTypeEnum getIssueProofLicense() {
        return issueProofLicense;
    }

    public void setIssueProofLicense(IssueProofLicenseTypeEnum issueProofLicense) {
        this.issueProofLicense = issueProofLicense;
    }

    public IssueProofLicenseWay getIssueProofLicenseWay() {
        return issueProofLicenseWay;
    }

    public void setIssueProofLicenseWay(IssueProofLicenseWay issueProofLicenseWay) {
        this.issueProofLicenseWay = issueProofLicenseWay;
    }

    @Override
    public String toString() {
        return "AssistRecordDo{" +
                "handleAffairsName='" + handleAffairsName + '\'' +
                ", handleAffairsAssistIdentityType=" + handleAffairsAssistIdentityType +
                ", handleAffairsIdentityNumber='" + handleAffairsIdentityNumber + '\'' +
                ", toAssistOrgName='" + toAssistOrgName + '\'' +
                ", toAssistCreditCode='" + toAssistCreditCode + '\'' +
                ", fromAssistOrgName='" + fromAssistOrgName + '\'' +
                ", fromAssistCreditCode='" + fromAssistCreditCode + '\'' +
                ", fromAssistUserName='" + fromAssistUserName + '\'' +
                ", fromAssistContain='" + fromAssistContain + '\'' +
                ", businessSerialNumber='" + businessSerialNumber + '\'' +
                ", assistTime=" + assistTime +
                ", assistSerialNumber='" + assistSerialNumber + '\'' +
                ", auditResult=" + auditResult +
                ", toUserId='" + toUserId + '\'' +
                ", toUserName='" + toUserName + '\'' +
                ", auditUserId='" + auditUserId + '\'' +
                ", auditSuggestion='" + auditSuggestion + '\'' +
                ", auditTime=" + auditTime +
                ", historyAssistSerialNumber='" + historyAssistSerialNumber + '\'' +
                ", itemCode='" + itemCode + '\'' +
                ", itemName='" + itemName + '\'' +
                ", materialId='" + materialId + '\'' +
                ", handleAffairsType=" + handleAffairsType +
                ", fromDemand='" + fromDemand + '\'' +
                ", toUserContain='" + toUserContain + '\'' +
                ", legalPersonIdentityType=" + legalPersonIdentityType +
                ", proofCatalogId='" + proofCatalogId + '\'' +
                ", proofCatalogName='" + proofCatalogName + '\'' +
                ", invalidDate=" + invalidDate +
                ", callBack=" + callBack +
                ", licenseItemAuthCode='" + licenseItemAuthCode + '\'' +
                ", implementCode='" + implementCode + '\'' +
                ", implementItemInfoJson='" + implementItemInfoJson + '\'' +
                ", issueProofLicense=" + issueProofLicense +
                ", issueProofLicenseWay=" + issueProofLicenseWay +
                '}';
    }
}
