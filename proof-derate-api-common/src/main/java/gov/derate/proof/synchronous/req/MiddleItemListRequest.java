package gov.derate.proof.synchronous.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import gov.derate.proof.common.enums.ItemTypeEnum;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.common.query.ListCondition;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 事项中间表请求数据
 *
 * <AUTHOR>
 * @date 2022/4/24.
 */
public class MiddleItemListRequest implements ListCondition, Serializable {
    private static final long serialVersionUID = 4292064593745076928L;

    /**
     * 事项名称
     */
    @JsonProperty("item_name")
    private String itemName;
    /**
     * 事项编码
     */
    @JsonProperty("itemCode")
    private String itemCode;

    /**
     * 实施机构
     */
    @JsonProperty("dept_name")
    private String deptName;

    /**
     * 同步状态
     */
    @JsonProperty("synchronize_status")
    private List<SynchronizeStatusEnum> synchronizeStatus;

    /**
     * 是否查询全部版本
     * true = 查询全部版本
     */
    @JsonProperty("search_all_version")
    private Boolean searchAllVersion;
    /**
     * 是否查询全部版本
     * true = 查询最新
     */
    @JsonProperty("search_max_new_version")
    private Boolean searchMaxNewVersion;
    /**
     * 事项版本版本的List
     */
    private Map<String, String> taskVersionMap;

    /**
     * 事项类型
     */
    @JsonProperty
    private List<ItemTypeEnum> itemTypeList;

    /**
     * 当前登录用户所拥有权限/数据范围能查看到的统一社会信用代码集合
     */
    @JsonProperty("credit_code_list")
    private List<String> creditCodeList;


    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public List<SynchronizeStatusEnum> getSynchronizeStatus() {
        return synchronizeStatus;
    }

    public void setSynchronizeStatus(List<SynchronizeStatusEnum> synchronizeStatus) {
        this.synchronizeStatus = synchronizeStatus;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public Boolean getSearchAllVersion() {
        return searchAllVersion;
    }

    public void setSearchAllVersion(Boolean searchAllVersion) {
        this.searchAllVersion = searchAllVersion;
    }

    public Boolean getSearchMaxNewVersion() {
        return searchMaxNewVersion;
    }

    public void setSearchMaxNewVersion(Boolean searchMaxNewVersion) {
        this.searchMaxNewVersion = searchMaxNewVersion;
    }


    public Map<String, String> getTaskVersionMap() {
        return taskVersionMap;
    }

    public void setTaskVersionMap(Map<String, String> taskVersionMap) {
        this.taskVersionMap = taskVersionMap;
    }

    public List<ItemTypeEnum> getItemTypeList() {
        return itemTypeList;
    }

    public void setItemTypeList(List<ItemTypeEnum> itemTypeList) {
        this.itemTypeList = itemTypeList;
    }

    public List<String> getCreditCodeList() {
        return creditCodeList;
    }

    public void setCreditCodeList(List<String> creditCodeList) {
        this.creditCodeList = creditCodeList;
    }

    public void setItemTypeList(String itemTypeStr) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(itemTypeStr)) {
            List<String> itemTypeStrLists = Arrays.asList(itemTypeStr.split(","));
            List<ItemTypeEnum> itemTypeLists = Lists.newArrayList();
            itemTypeStrLists.forEach(strItem ->
                    itemTypeLists.add(Enum.valueOf(ItemTypeEnum.class, strItem))
            );
            this.itemTypeList = itemTypeLists;
        }
    }

    @Override
    public String toString() {
        return "MiddleItemListRequest{" +
                "itemName='" + itemName + '\'' +
                ", itemCode='" + itemCode + '\'' +
                ", deptName='" + deptName + '\'' +
                ", synchronizeStatus=" + synchronizeStatus +
                ", searchAllVersion=" + searchAllVersion +
                ", searchMaxNewVersion=" + searchMaxNewVersion +
                ", taskVersionMap=" + taskVersionMap +
                ", itemTypeList=" + itemTypeList +
                ", creditCodeList=" + creditCodeList +
                '}';
    }

}
