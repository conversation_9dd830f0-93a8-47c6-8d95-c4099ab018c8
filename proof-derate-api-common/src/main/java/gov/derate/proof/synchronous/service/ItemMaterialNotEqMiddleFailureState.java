package gov.derate.proof.synchronous.service;

import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;

/**
 * <p>
 * 中间表事项与材料不一致逻辑处理
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2024/3/26
 * </p>
 *
 * <AUTHOR>
 */
public class ItemMaterialNotEqMiddleFailureState implements State {
    @Override
    public void handle(SynchronizeDataContext context) {
        //办事清单是否为空，为空新增办事清单
        //材料不一致同步失败
        context.getMiddleItemDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_FAILED);
        context.getMiddleItemDo().setFailedResult("事项下增加或删除了证明，需要重新梳理");
    }
}
