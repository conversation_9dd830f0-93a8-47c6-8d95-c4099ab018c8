package gov.derate.proof.synchronous.entity;

import gov.derate.proof.common.entity.AbstractDomainEntity;
import gov.derate.proof.common.enums.*;
import gov.derate.proof.common.enums.convert.ItemStatusConvert;
import gov.derate.proof.common.enums.convert.ItemTypeEnumConvert;
import gov.derate.proof.common.enums.convert.ProjectTypeEnumConvert;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;

/**
 * 事项中间表
 *
 * <AUTHOR>
 * @date 2022/4/12.
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_MIDDLE_ITEM")
public class MiddleItemDo extends AbstractDomainEntity {
    private static final long serialVersionUID = -3871455167313790748L;

    /**
     * 实施编码（事项编码）
     */
    @Column(name = "TASK_CODE")
    private String taskCode;

    /**
     * 办理项编码（事项编码）
     */
    @Column(name = "SITUATION_CODE")
    private String situationCode;

    /**
     * 实施名称（事项名称）
     */
    @Column(name = "TASK_NAME")
    private String taskName;

    /**
     * 办理项名称（事项名称）
     */
    @Column(name = "SITUATION_NAME")
    private String situationName;

    /**
     * 实施主体编码（实施机构的统一社会信用代码代码）
     */
    @Column(name = "TONGYI_CODE")
    private String tongYiCode;

    /**
     * 实施主体（实施机构）
     */
    @Column(name = "DEPT_NAME")
    private String deptName;

    /**
     * 行政区划代码（实施机构的行政区划代码）
     */
    @Column(name = "AREA_CODE")
    private String areaCode;

    /**
     * 事项类型（事项类型）
     */
    @Convert(converter = ItemTypeEnumConvert.class)
    @Column(name = "TASK_TYPE")
    private ItemTypeEnum taskType;

    /**
     * 事项来源
     */
    @Column(name = "ITEM_SOURCE")
    private ItemSourceEnum itemSource;

    /**
     * 事项状态（事项状态：1在用 2暂停 3取消，默认在用）
     */
    @Convert(converter = ItemStatusConvert.class)
    @Column(name = "TASK_STATE")
    private ItemStatus taskState;

    /**
     * 办件类型（新增属性）
     */
    @Convert(converter = ProjectTypeEnumConvert.class)
    @Column(name = "PROJECT_TYPE")
    private ProjectTypeEnum projectType;

    /**
     * 事项系统中事项id
     */
    @Column(name = "ROW_GUID")
    private String rowGuid;

    /**
     * 事项系统中事项唯一标识
     */
    @Column(name = "ITEM_ID")
    private String itemId;

    /**
     * 事项版本
     */
    @Column(name = "TASK_VERSION")
    private String taskVersion;

    /**
     * 事项版本创建时间
     */
    @Column(name = "VERSION_DATE")
    private Date versionDate;

    /**
     * 上次返回结果集最新一条记录版本时间和序列号
     */
    @Column(name = "LAST_TIME_STAMP")
    private String lastTimeStamp;

    /**
     * 上级事项编码
     */
    @Column(name = "PAR_TASK_CODE", length = 50, nullable = true)
    private String parentTaskCode;

    /**
     * 同步状态
     */
    @Column(name = "SYNCHRONIZE_STATUS")
    private SynchronizeStatusEnum synchronizeStatus;

    /**
     * 同步失败的原因
     */
    @Column(name = "FAILED_RESULT")
    private String failedResult;
    /**
     * 受理条件
     */
    @Lob
    @Column(name = "ACCEPT_CONDITION")
    private String acceptCondition;

    /**
     * 获取事项表对应的无证明城市事项编码逻辑
     *
     * @return 唯一标识
     */
    public String getProofItemCode() {
        return StringUtils.isNotBlank(this.getSituationCode()) ?
                this.getSituationCode() : this.getTaskCode();
    }

    public Integer getTaskVersionByInt() {
        return Integer.valueOf(this.taskVersion);
    }

    /**
     * 获取事项唯一标识
     *
     * @param middleItemDo 事项中间表
     * @return 唯一标识
     */
    public String getMiddleItemDoKey() {
        return this.getItemId() + this.getRowGuid();
    }

    public MiddleItemDo() {

    }

    public MiddleItemDo(String taskCode, String situationCode, String taskName, String situationName, String tongYiCode, String deptName, String areaCode, ItemTypeEnum taskType, ItemSourceEnum itemSource, ItemStatus taskState, ProjectTypeEnum projectType, String rowGuid, String itemId, String taskVersion, Date versionDate, String lastTimeStamp, String parentTaskCode, SynchronizeStatusEnum synchronizeStatus, String failedResult, String acceptCondition) {
        this.taskCode = taskCode;
        this.situationCode = situationCode;
        this.taskName = taskName;
        this.situationName = situationName;
        this.tongYiCode = tongYiCode;
        this.deptName = deptName;
        this.areaCode = areaCode;
        this.taskType = taskType;
        this.itemSource = itemSource;
        this.taskState = taskState;
        this.projectType = projectType;
        this.rowGuid = rowGuid;
        this.itemId = itemId;
        this.taskVersion = taskVersion;
        this.versionDate = versionDate;
        this.lastTimeStamp = lastTimeStamp;
        this.parentTaskCode = parentTaskCode;
        this.synchronizeStatus = synchronizeStatus;
        this.failedResult = failedResult;
        this.acceptCondition = acceptCondition;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getSituationCode() {
        return situationCode;
    }

    public void setSituationCode(String situationCode) {
        this.situationCode = situationCode;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getSituationName() {
        return situationName;
    }

    public void setSituationName(String situationName) {
        this.situationName = situationName;
    }

    public String getTongYiCode() {
        return tongYiCode;
    }

    public void setTongYiCode(String tongYiCode) {
        this.tongYiCode = tongYiCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public ItemTypeEnum getTaskType() {
        return taskType;
    }

    public void setTaskType(ItemTypeEnum taskType) {
        this.taskType = taskType;
    }

    public ItemSourceEnum getItemSource() {
        return itemSource;
    }

    public void setItemSource(ItemSourceEnum itemSource) {
        this.itemSource = itemSource;
    }

    public ItemStatus getTaskState() {
        return taskState;
    }

    public void setTaskState(ItemStatus taskState) {
        this.taskState = taskState;
    }

    public ProjectTypeEnum getProjectType() {
        return projectType;
    }

    public void setProjectType(ProjectTypeEnum projectType) {
        this.projectType = projectType;
    }

    public String getRowGuid() {
        return rowGuid;
    }

    public void setRowGuid(String rowGuid) {
        this.rowGuid = rowGuid;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getTaskVersion() {
        return taskVersion;
    }

    public void setTaskVersion(String taskVersion) {
        this.taskVersion = taskVersion;
    }

    public Date getVersionDate() {
        return versionDate;
    }

    public void setVersionDate(Date versionDate) {
        this.versionDate = versionDate;
    }

    public String getLastTimeStamp() {
        return lastTimeStamp;
    }

    public void setLastTimeStamp(String lastTimeStamp) {
        this.lastTimeStamp = lastTimeStamp;
    }

    public String getParentTaskCode() {
        return parentTaskCode;
    }

    public void setParentTaskCode(String parentTaskCode) {
        this.parentTaskCode = parentTaskCode;
    }

    public SynchronizeStatusEnum getSynchronizeStatus() {
        return synchronizeStatus;
    }

    public void setSynchronizeStatus(SynchronizeStatusEnum synchronizeStatus) {
        this.synchronizeStatus = synchronizeStatus;
    }

    public String getFailedResult() {
        return failedResult;
    }

    public void setFailedResult(String failedResult) {
        this.failedResult = failedResult;
    }

    public String getAcceptCondition() {
        return acceptCondition;
    }

    public void setAcceptCondition(String acceptCondition) {
        this.acceptCondition = acceptCondition;
    }

    @Override
    public String toString() {
        return "MiddleItemDo{" +
                "taskCode='" + taskCode + '\'' +
                ", situationCode='" + situationCode + '\'' +
                ", taskName='" + taskName + '\'' +
                ", situationName='" + situationName + '\'' +
                ", tongYiCode='" + tongYiCode + '\'' +
                ", deptName='" + deptName + '\'' +
                ", areaCode='" + areaCode + '\'' +
                ", taskType=" + taskType +
                ", itemSource=" + itemSource +
                ", taskState=" + taskState +
                ", projectType=" + projectType +
                ", rowGuid='" + rowGuid + '\'' +
                ", itemId='" + itemId + '\'' +
                ", taskVersion='" + taskVersion + '\'' +
                ", versionDate=" + versionDate +
                ", lastTimeStamp='" + lastTimeStamp + '\'' +
                ", parentTaskCode='" + parentTaskCode + '\'' +
                ", synchronizeStatus=" + synchronizeStatus +
                ", failedResult='" + failedResult + '\'' +
                ", acceptCondition='" + acceptCondition + '\'' +
                '}';
    }
}
