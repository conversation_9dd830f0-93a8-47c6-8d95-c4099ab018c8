package gov.derate.proof.synchronous.service;

import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.common.service.BeanFactoryUtils;
import gov.derate.proof.item.entity.ItemProofRelationDo;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;

import java.util.Objects;

/**
 * <p>
 * 中间表新增事项和材料逻辑处理
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2024/3/26
 * </p>
 *
 * <AUTHOR>
 */
public class NewItemAndMaterialState implements State {

    @Override
    public void handle(SynchronizeDataContext context) {
        SynchronizeDataService synchronizeDataService = BeanFactoryUtils.getBean(SynchronizeDataServiceImpl.class);
        if (Objects.isNull(context.getItemProofRelation())) {
            ItemProofRelationDo itemProofRelation = new ItemProofRelationDo();
            itemProofRelation.setItemCode(context.getMiddleItemDoItemCode(context.getMiddleItemDo()));
            itemProofRelation.setItemProofStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);
            context.setItemProofRelation(itemProofRelation);
        }
        //办事清单是否为空，为空新增办事清单
        // 先基于中间表构建新的事项与材料数据
        context.buildItemAndMaterialByMiddleItem(context, synchronizeDataService);
        //重新生成证明清单
        context.state(new AddProofListState());
        context.getMiddleItemDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_SUCCESS);
    }
}
