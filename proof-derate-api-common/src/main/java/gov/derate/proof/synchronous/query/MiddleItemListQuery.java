package gov.derate.proof.synchronous.query;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.enums.ItemTypeEnum;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.common.query.BaseOrganizationQuery;
import gov.derate.proof.common.query.ListCondition;
import org.apache.commons.collections4.CollectionUtils;
import gov.derate.proof.synchronous.req.MiddleItemListRequest;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 事项中间表请求数据
 *
 * <AUTHOR>
 * @date 2022/4/24.
 */
public class MiddleItemListQuery extends BaseOrganizationQuery implements ListCondition, Serializable {
    private static final long serialVersionUID = 4292064593745076928L;

    /**
     * 事项名称
     */
    @JsonProperty("item_name")
    private String itemName;
    /**
     * 事项编码
     */
    @JsonProperty("itemCode")
    private String itemCode;

    /**
     * 实施机构
     */
    @JsonProperty("dept_name")
    private String deptName;

    /**
     * 同步状态
     */
    @JsonProperty("synchronize_status")
    private List<SynchronizeStatusEnum> synchronizeStatus;

    /**
     * 同步状态为空条件
     */
    private String synchronizeStatusEmpty;

    /**
     * 是否查询全部版本
     * true = 查询最新
     */
    @JsonProperty("search_max_new_version")
    private Integer searchMaxNewVersion;
    /**
     * 权限查询对象
     */
    @JsonProperty("permission_credit_code_list")
    private List<String> permissionCreditCodeList;
    /**
     * 同步状态为空条件
     */
    @JsonProperty("permission_credit_code_list_empty")
    private String permissionCreditCodeListEmpty;
    /**
     * 事项类型
     */
    @JsonProperty
    private List<ItemTypeEnum> itemTypeList;
    /**
     * 事项类型
     */
    @JsonProperty
    private String  itemTypeListEmpty;

    /**
     * 当前登录用户所拥有权限/数据范围能查看到的统一社会信用代码集合
     */
    @JsonProperty("credit_code_list")
    private List<String> creditCodeList;


    /**
     * 查询对象构造
     *
     * @param listVo 页面ListVo
     */
    public MiddleItemListQuery(MiddleItemListRequest listVo) {
        String likeSqlString = "%";
        String emptyListElement = "-1";
        if (StringUtils.isNotBlank(listVo.getItemName())) {
            this.itemName = likeSqlString + listVo.getItemName() + likeSqlString;
        }
        if (StringUtils.isBlank(listVo.getItemCode())) {
            this.itemCode = null;
        } else {
            this.itemCode = listVo.getItemCode();
        }
        if (StringUtils.isNotBlank(listVo.getDeptName())) {
            this.deptName = likeSqlString + listVo.getDeptName() + likeSqlString;
        }
        if (CollectionUtils.isNotEmpty(listVo.getSynchronizeStatus())) {
            this.synchronizeStatus = listVo.getSynchronizeStatus();
            this.synchronizeStatusEmpty = emptyListElement;
        }
        if (Objects.isNull(listVo.getSearchMaxNewVersion()) || !listVo.getSearchMaxNewVersion()) {
            this.searchMaxNewVersion = null;
        }
        if (Objects.nonNull(listVo.getSearchMaxNewVersion()) && listVo.getSearchMaxNewVersion()) {
            this.searchMaxNewVersion = 1;
        }
        if (CollectionUtils.isNotEmpty(listVo.getCreditCodeList())) {
            this.creditCodeList = listVo.getCreditCodeList();
            this.permissionCreditCodeListEmpty = emptyListElement;
        }
        if (CollectionUtils.isNotEmpty(listVo.getItemTypeList())) {
            this.itemTypeList = listVo.getItemTypeList();
            this.itemTypeListEmpty = emptyListElement;
        }
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public List<SynchronizeStatusEnum> getSynchronizeStatus() {
        return synchronizeStatus;
    }

    public void setSynchronizeStatus(List<SynchronizeStatusEnum> synchronizeStatus) {
        this.synchronizeStatus = synchronizeStatus;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }


    public String getSynchronizeStatusEmpty() {
        return synchronizeStatusEmpty;
    }

    public void setSynchronizeStatusEmpty(String synchronizeStatusEmpty) {
        this.synchronizeStatusEmpty = synchronizeStatusEmpty;
    }

    public Integer getSearchMaxNewVersion() {
        return searchMaxNewVersion;
    }

    public void setSearchMaxNewVersion(Integer searchMaxNewVersion) {
        this.searchMaxNewVersion = searchMaxNewVersion;
    }

    public List<String> getPermissionCreditCodeList() {
        return permissionCreditCodeList;
    }

    public void setPermissionCreditCodeList(List<String> permissionCreditCodeList) {
        this.permissionCreditCodeList = permissionCreditCodeList;
    }

    public String getPermissionCreditCodeListEmpty() {
        return permissionCreditCodeListEmpty;
    }

    public void setPermissionCreditCodeListEmpty(String permissionCreditCodeListEmpty) {
        this.permissionCreditCodeListEmpty = permissionCreditCodeListEmpty;
    }

    public List<ItemTypeEnum> getItemTypeList() {
        return itemTypeList;
    }

    public void setItemTypeList(List<ItemTypeEnum> itemTypeList) {
        this.itemTypeList = itemTypeList;
    }

    public String getItemTypeListEmpty() {
        return itemTypeListEmpty;
    }

    public void setItemTypeListEmpty(String itemTypeListEmpty) {
        this.itemTypeListEmpty = itemTypeListEmpty;
    }

    public List<String> getCreditCodeList() {
        return creditCodeList;
    }

    public void setCreditCodeList(List<String> creditCodeList) {
        this.creditCodeList = creditCodeList;
    }

    @Override
    public String toString() {
        return "MiddleItemListQuery{" +
                "itemName='" + itemName + '\'' +
                ", itemCode='" + itemCode + '\'' +
                ", deptName='" + deptName + '\'' +
                ", synchronizeStatus=" + synchronizeStatus +
                ", synchronizeStatusEmpty='" + synchronizeStatusEmpty + '\'' +
                ", searchMaxNewVersion=" + searchMaxNewVersion +
                ", permissionCreditCodeList=" + permissionCreditCodeList +
                ", permissionCreditCodeListEmpty='" + permissionCreditCodeListEmpty + '\'' +
                ", itemTypeList=" + itemTypeList +
                ", itemTypeListEmpty='" + itemTypeListEmpty + '\'' +
                ", creditCodeList=" + creditCodeList +
                '}';
    }

}
