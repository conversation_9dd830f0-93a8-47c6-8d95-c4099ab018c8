package gov.derate.proof.synchronous.service;

import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.common.service.BeanFactoryUtils;
import gov.derate.proof.item.entity.ItemProofRelationDo;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;

import java.util.Objects;

/**
 * <p>
 * 中间表更新事项和材料情况
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2024/3/26
 * </p>
 *
 * <AUTHOR>
 */
public class UpdateItemAndMaterialState implements State{
    @Override
    public void handle(SynchronizeDataContext context) {
        //办事清单是否为空，为空新增办事清单
        if (Objects.isNull(context.getItemProofRelation())) {
            ItemProofRelationDo itemProofRelation = new ItemProofRelationDo();
            itemProofRelation.setItemCode(context.getItemDo().getItemCode());
            itemProofRelation.setItemProofStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);
            context.setItemProofRelation(itemProofRelation);
        }
        SynchronizeDataService synchronizeDataService = BeanFactoryUtils.getBean(SynchronizeDataServiceImpl.class);
        //事项状态为待清理
        if (ItemProofStatusEnum.WAIT_FOR_CLEAN == context.getItemProofRelation().getItemProofStatus()) {
            // 先基于中间表构建新的事项与材料数据
            context.buildItemAndMaterialByMiddleItem(context, synchronizeDataService);
            //重新生成证明清单
            context.state(new AddProofListState());
            return;
        }
        //事项非待清理，校验材料是否一致
        if (synchronizeDataService.checkItemMaterialEquals(context)) {
            //材料一致，则更新事项和材料
            context.buildItemAndMaterialByMiddleItem(context, synchronizeDataService);
            context.getMiddleItemDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_SUCCESS);
        }else{
            context.getMiddleItemDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_INVALID);
        }
    }
}
