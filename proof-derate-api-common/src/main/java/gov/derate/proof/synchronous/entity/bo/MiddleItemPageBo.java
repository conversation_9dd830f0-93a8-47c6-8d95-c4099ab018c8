package gov.derate.proof.synchronous.entity.bo;


import java.io.Serializable;
import java.util.Date;

/**
 * 事项中间表
 *
 * <AUTHOR>
 * @date 2022/4/12.
 */
public class MiddleItemPageBo implements Serializable {
    private static final long serialVersionUID = -3871455167313790748L;

    private String id;
    private String creatorId;
    private Date creationTime;
    private String lastModificatorId;
    private Date lastModificationTime;

    /**
     * 实施编码（事项编码）
     */
    private String taskCode;

    /**
     * 办理项编码（事项编码）
     */
    private String situationCode;

    /**
     * 实施名称（事项名称）
     */
    private String taskName;

    /**
     * 办理项名称（事项名称）
     */
    private String situationName;

    /**
     * 实施主体编码（实施机构的统一社会信用代码代码）
     */
    private String tongYiCode;

    /**
     * 实施主体（实施机构）
     */
    private String deptName;

    /**
     * 行政区划代码（实施机构的行政区划代码）
     */
    private String areaCode;

    /**
     * 事项类型（事项类型）
     */
    private String taskType;

    /**
     * 事项来源
     */
    private Integer itemSource;

    /**
     * 事项状态（事项状态：1在用 2暂停 3取消，默认在用）
     */
    private String taskState;

    /**
     * 办件类型（新增属性）
     */
    private String projectType;

    /**
     * 事项系统中事项id
     */
    private String rowGuid;

    /**
     * 事项系统中事项唯一标识
     */
    private String itemId;

    /**
     * 事项版本
     */
    private String taskVersion;

    /**
     * 事项版本创建时间
     */
    private Date versionDate;

    /**
     * 上次返回结果集最新一条记录版本时间和序列号
     */
    private String lastTimeStamp;

    /**
     * 上级事项编码
     */
    private String parentTaskCode;

    /**
     * 同步状态
     */
    private Integer synchronizeStatus;

    /**
     * 同步失败的原因
     */
    private String failedResult;
    /**
     * 受理条件
     */
    private String acceptCondition;

    public MiddleItemPageBo() {

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public String getLastModificatorId() {
        return lastModificatorId;
    }

    public void setLastModificatorId(String lastModificatorId) {
        this.lastModificatorId = lastModificatorId;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    public String getTaskCode() {
        return taskCode;
    }

    public void setTaskCode(String taskCode) {
        this.taskCode = taskCode;
    }

    public String getSituationCode() {
        return situationCode;
    }

    public void setSituationCode(String situationCode) {
        this.situationCode = situationCode;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getSituationName() {
        return situationName;
    }

    public void setSituationName(String situationName) {
        this.situationName = situationName;
    }

    public String getTongYiCode() {
        return tongYiCode;
    }

    public void setTongYiCode(String tongYiCode) {
        this.tongYiCode = tongYiCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public Integer getItemSource() {
        return itemSource;
    }

    public void setItemSource(Integer itemSource) {
        this.itemSource = itemSource;
    }

    public String getTaskState() {
        return taskState;
    }

    public void setTaskState(String taskState) {
        this.taskState = taskState;
    }

    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public String getRowGuid() {
        return rowGuid;
    }

    public void setRowGuid(String rowGuid) {
        this.rowGuid = rowGuid;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getTaskVersion() {
        return taskVersion;
    }

    public void setTaskVersion(String taskVersion) {
        this.taskVersion = taskVersion;
    }

    public Date getVersionDate() {
        return versionDate;
    }

    public void setVersionDate(Date versionDate) {
        this.versionDate = versionDate;
    }

    public String getLastTimeStamp() {
        return lastTimeStamp;
    }

    public void setLastTimeStamp(String lastTimeStamp) {
        this.lastTimeStamp = lastTimeStamp;
    }

    public String getParentTaskCode() {
        return parentTaskCode;
    }

    public void setParentTaskCode(String parentTaskCode) {
        this.parentTaskCode = parentTaskCode;
    }

    public Integer getSynchronizeStatus() {
        return synchronizeStatus;
    }

    public void setSynchronizeStatus(Integer synchronizeStatus) {
        this.synchronizeStatus = synchronizeStatus;
    }

    public String getFailedResult() {
        return failedResult;
    }

    public void setFailedResult(String failedResult) {
        this.failedResult = failedResult;
    }

    public String getAcceptCondition() {
        return acceptCondition;
    }

    public void setAcceptCondition(String acceptCondition) {
        this.acceptCondition = acceptCondition;
    }

    @Override
    public String toString() {
        return "MiddleItemPageBo{" +
                "id='" + id + '\'' +
                ", creatorId='" + creatorId + '\'' +
                ", creationTime=" + creationTime +
                ", lastModificatorId='" + lastModificatorId + '\'' +
                ", lastModificationTime=" + lastModificationTime +
                ", taskCode='" + taskCode + '\'' +
                ", situationCode='" + situationCode + '\'' +
                ", taskName='" + taskName + '\'' +
                ", situationName='" + situationName + '\'' +
                ", tongYiCode='" + tongYiCode + '\'' +
                ", deptName='" + deptName + '\'' +
                ", areaCode='" + areaCode + '\'' +
                ", taskType='" + taskType + '\'' +
                ", itemSource=" + itemSource +
                ", taskState='" + taskState + '\'' +
                ", projectType='" + projectType + '\'' +
                ", rowGuid='" + rowGuid + '\'' +
                ", itemId='" + itemId + '\'' +
                ", taskVersion='" + taskVersion + '\'' +
                ", versionDate=" + versionDate +
                ", lastTimeStamp='" + lastTimeStamp + '\'' +
                ", parentTaskCode='" + parentTaskCode + '\'' +
                ", synchronizeStatus=" + synchronizeStatus +
                ", failedResult='" + failedResult + '\'' +
                ", acceptCondition='" + acceptCondition + '\'' +
                '}';
    }
}
