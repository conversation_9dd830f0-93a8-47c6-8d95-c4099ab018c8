package gov.derate.proof.synchronous.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.enums.MaterialTypeEnum;

import java.io.Serializable;
import java.util.List;

/**
 * 证明材料展示对象
 *
 * <AUTHOR>
 * @date 2022/4/25.
 */
public class MiddleMaterialVo implements Serializable {
    private static final long serialVersionUID = -7242384507753546477L;

    /**
     * 材料id
     */
    @JsonProperty("material_id")
    private String materialId;

    /**
     * 材料名称
     */
    @JsonProperty("material_name")
    private String materialName;

    /**
     * 材料分类
     */
    @JsonProperty("material_type")
    private MaterialTypeEnum materialType;

    /**
     * 已关联电子证照目录编码
     */
    @JsonProperty("license_code")
    private String licenseCode;

    /**
     * 已关联电子证照目录名称
     */
    @JsonProperty("license_name")
    private String licenseName;

    /**
     * 材料样例列表
     */
    @JsonProperty("sample_file_list")
    private List<MaterialAttachmentVo> sampleFileList;

    /**
     * 空白表格列表
     */
    @JsonProperty("blank_file_list")
    private List<MaterialAttachmentVo> blankFileList;

    /**
     * 变更详情
     */
    @JsonProperty("change_details")
    private String changeDetails;

    /**
     * 免提交措施
     */
    @JsonProperty("submission_measure")
    private String submissionMeasure;

    /**
     * 材料的biz标识
     */
    private transient String materialBizId;

    public String getMaterialId() {
        return materialId;
    }

    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public MaterialTypeEnum getMaterialType() {
        return materialType;
    }

    public void setMaterialType(MaterialTypeEnum materialType) {
        this.materialType = materialType;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getLicenseName() {
        return licenseName;
    }

    public void setLicenseName(String licenseName) {
        this.licenseName = licenseName;
    }

    public List<MaterialAttachmentVo> getSampleFileList() {
        return sampleFileList;
    }

    public void setSampleFileList(List<MaterialAttachmentVo> sampleFileList) {
        this.sampleFileList = sampleFileList;
    }

    public List<MaterialAttachmentVo> getBlankFileList() {
        return blankFileList;
    }

    public void setBlankFileList(List<MaterialAttachmentVo> blankFileList) {
        this.blankFileList = blankFileList;
    }

    public String getChangeDetails() {
        return changeDetails;
    }

    public void setChangeDetails(String changeDetails) {
        this.changeDetails = changeDetails;
    }

    public String getSubmissionMeasure() {
        return submissionMeasure;
    }

    public void setSubmissionMeasure(String submissionMeasure) {
        this.submissionMeasure = submissionMeasure;
    }

    public String getMaterialBizId() {
        return materialBizId;
    }

    public void setMaterialBizId(String materialBizId) {
        this.materialBizId = materialBizId;
    }

    @Override
    public String toString() {
        return "MiddleMaterialVo{" +
                "materialId='" + materialId + '\'' +
                ", materialName='" + materialName + '\'' +
                ", materialType=" + materialType +
                ", licenseCode='" + licenseCode + '\'' +
                ", licenseName='" + licenseName + '\'' +
                ", sampleFileList=" + sampleFileList +
                ", blankFileList=" + blankFileList +
                ", changeDetails='" + changeDetails + '\'' +
                ", submissionMeasure='" + submissionMeasure + '\'' +
                ", materialBizId='" + materialBizId + '\'' +
                '}';
    }
}
