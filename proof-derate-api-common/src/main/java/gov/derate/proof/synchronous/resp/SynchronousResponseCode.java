package gov.derate.proof.synchronous.resp;

import gov.license.common.api.resp.BaseResponseCode;


/**
 * 事项同步 异常编码类
 * Company: Zsoft
 * CreateDate:2024/5/24
 *
 * <AUTHOR>
 */
public class SynchronousResponseCode extends BaseResponseCode {
    /**
     * 参数报错序号
     */
    private static final String PARAM_PREFIX_NUM = "3";
    /**
     * 系统报错序号
     */
    private static final String BIZ_PREFIX_NUM = "2";
    /**
     * 无证明城市序号
     */
    private static final String PROOF_DERATE_SYSTEM_PREFIX_NUM = "C02";
    /**
     * 同步模块序号
     */
    private static final String SYNCHRONOUS_PREFIX_NUM = "08";
    /**
     * 模块序号
     */
    private static final String MODULE_PREFIX_NUM = "01";
    /**
     * 格式。6位字符的拼接
     */
    private static final String FORMAT_STR = "%s%s%s%s%s";


    public static final SynchronousResponseCode SERVICE_SYNCHRONOUS_ERROR = new SynchronousResponseCode(String.format(FORMAT_STR, BIZ_PREFIX_NUM, PROOF_DERATE_SYSTEM_PREFIX_NUM, SYNCHRONOUS_PREFIX_NUM, MODULE_PREFIX_NUM, "000"), "事项同步-服务未知异常");
    public static final SynchronousResponseCode MIDDLE_EMPTY_ERROR = new SynchronousResponseCode(String.format(FORMAT_STR, BIZ_PREFIX_NUM, PROOF_DERATE_SYSTEM_PREFIX_NUM, SYNCHRONOUS_PREFIX_NUM, MODULE_PREFIX_NUM, "001"), "事项同步-中间表事项不能为空", Boolean.TRUE);
    public static final SynchronousResponseCode MIDDLE_SYNC_STATUS_NOT_FAILURE_ERROR = new SynchronousResponseCode(String.format(FORMAT_STR, BIZ_PREFIX_NUM, PROOF_DERATE_SYSTEM_PREFIX_NUM, SYNCHRONOUS_PREFIX_NUM, MODULE_PREFIX_NUM, "002"), "事项同步-同步事项状态非同步失败状态，不允许同步", Boolean.TRUE);


    public SynchronousResponseCode(String code, String message) {
        super(code, message);
    }

    public SynchronousResponseCode(String code, String message, boolean replaceFlag) {
        super(code, message, replaceFlag);
    }

}
