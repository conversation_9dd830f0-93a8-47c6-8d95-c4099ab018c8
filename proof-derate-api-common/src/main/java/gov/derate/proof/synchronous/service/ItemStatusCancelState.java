package gov.derate.proof.synchronous.service;

import com.google.common.collect.Lists;
import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.enums.OperatorLogEnum;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.common.service.BeanFactoryUtils;
import org.apache.commons.collections4.CollectionUtils;
import gov.derate.proof.item.entity.ItemProofRelationDo;
import gov.derate.proof.list.entity.ProofListProcedureDo;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 事项同步处理 事项状态为取消 service impl
 * </p>
 * rights reserved. </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022-06-13
 * </p>
 *
 * <AUTHOR>
 */
public class ItemStatusCancelState implements State {

    @Override
    public void handle(SynchronizeDataContext context) {
        SynchronizeDataService synchronizeDataService = BeanFactoryUtils.getBean(SynchronizeDataServiceImpl.class);
        if (Objects.isNull(context.getItemProofRelation())) {
            ItemProofRelationDo itemProofRelation = new ItemProofRelationDo();
            itemProofRelation.setItemCode(Objects.isNull(context.getItemDo())?context.getMiddleItemDo().getProofItemCode():context.getItemDo().getItemCode());
            itemProofRelation.setItemProofStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);
            context.setItemProofRelation(itemProofRelation);
        }
        //同步事项和材料
        synchronizeDataService.synchronizeItemAndMaterial(context);
        //材料未清理完成，进行办结，并记录办结日志
        if (!ItemProofStatusEnum.APPROVED.equals(context.getItemProofRelation().getItemProofStatus())) {
            context.getItemProofRelation().setItemProofStatus(ItemProofStatusEnum.TRANSFERRED);
            context.getItemDo().setItemClearStatus(ItemProofStatusEnum.TRANSFERRED);
            context.getItemMaterialDoList().forEach(item->item.setItemClearStatus(ItemProofStatusEnum.TRANSFERRED));
            if (CollectionUtils.isNotEmpty(context.getProofListDoList())) {
                context.getProofListDoList().forEach(item -> item.setItemProofStatus(ItemProofStatusEnum.TRANSFERRED));
            }
            List<ProofListProcedureDo> proofListProcedureDos = Lists.newArrayList();
            ProofListProcedureDo proofListProcedureDo = new ProofListProcedureDo();
            proofListProcedureDo.setAccount("系统自动同步");
            proofListProcedureDo.setAccountName("系统自动同步");
            proofListProcedureDo.setAccountDeptCode("系统自动同步");
            proofListProcedureDo.setAccountDeptName("系统自动同步");
            proofListProcedureDo.setProcedureDate(new Date());
            proofListProcedureDo.setItemCode(context.getItemDo().getItemCode());
            proofListProcedureDo.setItemProofStatus(context.getItemProofRelation().getItemProofStatus());
            proofListProcedureDo.setProcedureMessage("事项状态为已取消");
            proofListProcedureDo.setOperatorLog(OperatorLogEnum.SYNCHRONIZE_UPDATE);
            proofListProcedureDo.setCreatorId("系统自动同步");
            proofListProcedureDo.setLastModificatorId("系统自动同步");
            proofListProcedureDo.setOperation(ItemProofStatusEnum.TRANSFERRED.getName());
            proofListProcedureDos.add(proofListProcedureDo);
            context.setProofListProcedureDoList(proofListProcedureDos);
        }
        context.getMiddleItemDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_SUCCESS);
    }
}
