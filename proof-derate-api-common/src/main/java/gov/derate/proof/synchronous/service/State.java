package gov.derate.proof.synchronous.service;


import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;

/**
 * <p>
 * 事项同步处理状态接口
 * </p>
 * rights reserved. </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022-06-13
 * </p>
 *
 * <AUTHOR>
 */
public interface State {

    /**
     * 同步处理方法
     *
     * @param context 同步上下文对象
     */
    void handle(SynchronizeDataContext context);
}
