package gov.derate.proof.synchronous.entity;

import gov.derate.proof.common.entity.AbstractDomainEntity;
import gov.derate.proof.common.enums.AttachmentTypeEnum;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 材料附件表
 *
 * <AUTHOR>
 * @date 2022/4/19.
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_MATERIAL_ATTACHMENT")
public class MaterialAttachmentDo extends AbstractDomainEntity {
    private static final long serialVersionUID = -756296155540973856L;

    /**
     * 所属材料版本标识
     */
    @Column(name = "MATERIAL_ID")
    private String materialId;

    /**
     * 附件类型（0空白表格|1示例样表）
     */
    @Column(name = "ATTACHMENT_TYPE")
    private AttachmentTypeEnum attachmentType;

    /**
     * 附件下载地址
     */
    @Column(name = "FILE_PATH")
    private String filePath;

    /**
     * 附件名称
     */
    @Column(name = "ATTACHMENT_NAME")
    private String attachmentName;

    /**
     * 附件编码
     */
    @Column(name = "ATTACHMENT_GUID")
    private String attachmentGuid;

    /**
     * 附件内网下载地址
     */
    @Column(name = "INTRANET_PATH")
    private String intranetPath;

    public String getMaterialId() {
        return materialId;
    }

    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }

    public AttachmentTypeEnum getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(AttachmentTypeEnum attachmentType) {
        this.attachmentType = attachmentType;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public String getAttachmentGuid() {
        return attachmentGuid;
    }

    public void setAttachmentGuid(String attachmentGuid) {
        this.attachmentGuid = attachmentGuid;
    }

    public String getIntranetPath() {
        return intranetPath;
    }

    public void setIntranetPath(String intranetPath) {
        this.intranetPath = intranetPath;
    }

    @Override
    public String toString() {
        return "MaterialAttachmentDo{" +
                "materialId='" + materialId + '\'' +
                ", attachmentType=" + attachmentType +
                ", filePath='" + filePath + '\'' +
                ", attachmentName='" + attachmentName + '\'' +
                ", attachmentGuid='" + attachmentGuid + '\'' +
                ", intranetPath='" + intranetPath + '\'' +
                '}';
    }
}
