package gov.derate.proof.synchronous.repository;

import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.synchronous.entity.MiddleItemDo;
import gov.derate.proof.synchronous.query.MiddleItemListQuery;
import gov.license.common.api.repository.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 事项中间表 数据访问
 *
 * <AUTHOR>
 * @date 2022/4/13.
 */
@Repository
public interface MiddleItemRepository extends BaseRepository<MiddleItemDo, String> {
    /**
     * 查询事项同步管理-查询列表
     *
     * @param query 查询对象
     * @param page  分页
     * @return 数据
     */
    @Query(value =
            "select t from MiddleItemDo t " +
                    " where " +
                    " (t.taskName like  :#{#queryObj.itemName} or t.situationName like  :#{#queryObj.itemName} or :#{#queryObj.itemName} is null ) " +
                    " and (t.taskCode  = :#{#queryObj.itemCode} or :#{#queryObj.itemCode} is null ) " +
                    " and (t.deptName  like :#{#queryObj.deptName} or :#{#queryObj.deptName} is null ) " +
                    " and (t.synchronizeStatus  in :#{#queryObj.synchronizeStatus} or :#{#queryObj.synchronizeStatus==null?null:'-1'} is null ) " +
                    " and (t.tongYiCode  in :#{#queryObj.creditCodeList} or :#{#queryObj.creditCodeList==null?null:'-1'} is null ) " +
                    " and (t.taskType  in :#{#queryObj.itemTypeList} or :#{#queryObj.itemTypeList==null?null:'-1'} is null ) " +
                    " and ( t.versionDate = (select max(p.versionDate) from MiddleItemDo p where p.taskCode = t.taskCode and :#{#queryObj.searchMaxNewVersion} = 1  )   or :#{#queryObj.searchMaxNewVersion} is null ) ",
            countQuery =
                    "select count(t.id) from MiddleItemDo t " +
                            " where " +
                            " (t.taskName like  :#{#queryObj.itemName} or t.situationName like  :#{#queryObj.itemName} or :#{#queryObj.itemName} is null ) " +
                            " and (t.taskCode  = :#{#queryObj.itemCode} or :#{#queryObj.itemCode} is null ) " +
                            " and (t.deptName  like :#{#queryObj.deptName} or :#{#queryObj.deptName} is null ) " +
                            " and (t.synchronizeStatus  in :#{#queryObj.synchronizeStatus} or :#{#queryObj.synchronizeStatus==null?null:'-1'} is null ) " +
                            " and (t.tongYiCode  in :#{#queryObj.creditCodeList} or :#{#queryObj.creditCodeList==null?null:'-1'} is null ) " +
                            " and (t.taskType  in :#{#queryObj.itemTypeList} or :#{#queryObj.itemTypeList==null?null:'-1'} is null ) " +
                            " and ( t.versionDate = (select max(p.versionDate) from MiddleItemDo p where p.taskCode = t.taskCode and :#{#queryObj.searchMaxNewVersion} = 1  )   or :#{#queryObj.searchMaxNewVersion} is null ) "
    )
    Page<MiddleItemDo> querySynchronizeList(@Param("queryObj") MiddleItemListQuery query, Pageable page);

    /**
     * 查询事项同步管理-查询列表
     *
     * @param query 查询对象
     * @return 数据
     */
    @Query("select count(t.id) from MiddleItemDo t " +
            " where " +
            " (t.taskName like  :#{#queryObj.itemName} or t.situationName like  :#{#queryObj.itemName} or :#{#queryObj.itemName} is null ) " +
            " and (t.taskCode  = :#{#queryObj.itemCode} or :#{#queryObj.itemCode} is null ) " +
            " and (t.deptName  like :#{#queryObj.deptName} or :#{#queryObj.deptName} is null ) " +
            " and (t.synchronizeStatus  in :#{#queryObj.synchronizeStatus} or :#{#queryObj.synchronizeStatus==null?null:'-1'} is null ) " +
            " and (t.tongYiCode  in :#{#queryObj.creditCodeList} or :#{#queryObj.creditCodeList==null?null:'-1'} is null ) " +
            " and (t.taskType  in :#{#queryObj.itemTypeList} or :#{#queryObj.itemTypeList==null?null:'-1'} is null ) " +
            " and ( t.versionDate = (select max(p.versionDate) from MiddleItemDo p where p.taskCode = t.taskCode and p.situationCode = t.situationCode and :#{#queryObj.searchMaxNewVersion} = 1  )   or :#{#queryObj.searchMaxNewVersion} is null ) ")
    Long countSynchronizeListView(@Param("queryObj") MiddleItemListQuery query);
    /**
     * 分页查询最新版本数据
     *
     * @param itemName          事项名称
     * @param deptName          部门名称
     * @param taskCode          事项编码
     * @param synchronizeStatus 同步状态
     * @param pageable          分页参数
     * @return
     */
    @Query(value = "select * from (" +
            "select m.*,i.VERSION_DATE " +
            "from tbl_middle_item m , (select i.TASK_CODE,i.SITUATION_CODE, max(i.VERSION_DATE) VERSION_DATE from tbl_middle_item i group by i.TASK_CODE,i.SITUATION_CODE) i " +
            "where m.TASK_CODE =i.TASK_CODE and m.VERSION_DATE =i.VERSION_DATE and m.SITUATION_CODE = i.SITUATION_CODE " +
            ") m"
//            " And if(:itemName != '',m.task_name like CONCAT('%',:itemName,'%'), 1=1)" +
//            " And if(:taskCode != '',m.task_code = :taskCode, 1=1)" +
//            " And if(:deptName != '',m.dept_name like CONCAT('%',:deptName,'%'), 1=1)" +
//            " And m.synchronize_status in (:synchronizeStatus)"
            , nativeQuery = true
    )
    Slice<MiddleItemDo> querySynchronizeListViewSlice(@Param("itemName") String itemName,
                                                      @Param("deptName") String deptName,
                                                      @Param("taskCode") String taskCode,
                                                      @Param("synchronizeStatus") List<Integer> synchronizeStatus, Pageable pageable);

    /**
     * 查询中间表中，最大版本的编码与版本标识符
     *
     * @return 数据
     */
    @Query("select o.taskCode as taskCode,max(o.taskVersion) as taskVersion from MiddleItemDo o group by  o.taskCode")
    List<Map<String, String>> queryMaxNewVersionGroupByItemCode();
    /**
     * 分页查询最新版本数据
     *
     * @param itemName          事项名称
     * @param deptName          部门名称
     * @param taskCode          事项编码
     * @param synchronizeStatus 同步状态
     * @param pageable          分页参数
     * @return
     */
    @Query(value = "" +
            "select * " +
            "from tbl_middle_item m , (select i.TASK_CODE,i.SITUATION_CODE, max(i.VERSION_DATE) VERSION_DATE from tbl_middle_item i group by i.TASK_CODE,i.SITUATION_CODE) i " +
            "where m.TASK_CODE =i.TASK_CODE and m.VERSION_DATE =i.VERSION_DATE and m.SITUATION_CODE = i.SITUATION_CODE " +
            " And if(:itemName != '',m.task_name like CONCAT('%',:itemName,'%'), 1=1)" +
            " And if(:taskCode != '',m.task_code = :taskCode, 1=1)" +
            " And if(:deptName != '',m.dept_name like CONCAT('%',:deptName,'%'), 1=1)" +
            " And m.synchronize_status in (:synchronizeStatus)"
            , nativeQuery = true
    )
    Page<MiddleItemDo> querySynchronizeListViewPage(@Param("itemName") String itemName,
                                                    @Param("deptName") String deptName,
                                                    @Param("taskCode") String taskCode,
                                                    @Param("synchronizeStatus") List<Integer> synchronizeStatus, Pageable pageable);


    /**
     * 根据事项编码查询事项
     *
     * @param itemCode 事项编码
     * @return 事项
     */
    java.util.List<MiddleItemDo> findAllByTaskCode(String itemCode);
    /**
     * 查询大于创建时间的记录
     *
     * @param creationTime 创建时间
     * @return 记录
     */
    List<MiddleItemDo> findAllByCreationTimeAfter(Date creationTime);

    /**
     * 根据事项id查询记录
     *
     * @param itemId 事项id
     * @return 事项记录
     */
    MiddleItemDo findByItemId(String itemId);

    /**
     * 根据获取时间获取最新的事项记录
     *
     * @return 事项记录
     */
    MiddleItemDo findFirstByOrderByCreationTimeDesc();

    /**
     * 查询已经存在表中的事项rowId
     *
     * @param itemRowGuidList
     * @return RowGuidList
     */
    @Query("select o.rowGuid from MiddleItemDo o where o.rowGuid in (:item) ")
    List<String> findAllByRowGuidIn(@Param("item") List<String> itemRowGuidList);

    /**
     * 根据据事项编码查询中间表记录
     *
     * @param itemCodeList 事项鞭名马
     * @return 中间表数据
     */
    List<MiddleItemDo> findAllByTaskCodeIn(Set<String> itemCodeList);

    /**
     * 更新中间事项表状态
     * @param deptName 部门名称
     * @param areaCode 行政区划编码
     * @param tongYiCode 统一社会信用代码
     */
    @Query("update MiddleItemDo o set o.tongYiCode = :tongYiCode where o.deptName = :deptName and o.areaCode = :areaCode")
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    @Modifying
    void updateForStatus(@Param("deptName") String deptName, @Param("areaCode") String areaCode, @Param("tongYiCode") String tongYiCode);

    /**
     * 根据同步状态查询事项中间表
     *
     * @param statusEnum 同步状态
     * @param pageable   分页对象
     * @return 事项中间表
     */
    @Query("select o  from MiddleItemDo o where o.synchronizeStatus =:statusEnum ")
    List<MiddleItemDo> findAllBySynchronizeStatusEqualsOrderByTaskCodeAscTaskVersionAsc(@Param("statusEnum") SynchronizeStatusEnum statusEnum,
                                                                                        Pageable pageable);

    /**
     * 根据同步状态查询事项中间表
     *
     * @param statusEnum 同步状态
     * @param id         唯一标识
     * @param pageable   分页对象
     * @return 事项中间表
     */
    @Query("select o  from MiddleItemDo o where o.synchronizeStatus =:statusEnum and o.id > :id ")
    List<MiddleItemDo> findAllBySynchronizeStatusEqualsOrderByTaskCodeAscTaskVersionAsc(@Param("statusEnum") SynchronizeStatusEnum statusEnum,
                                                                                        @Param("id") String id ,
                                                                                        Pageable pageable);

    /**
     * 查询中间表待处理的事项编码
     * @param statusEnum  同步状态
     * @return task_code
     */
    @Query("select distinct o.taskCode  from MiddleItemDo o where o.synchronizeStatus =:statusEnum ")
    List<String> findAllBySynchronizeStatusEquals(@Param("statusEnum") SynchronizeStatusEnum statusEnum);
}
