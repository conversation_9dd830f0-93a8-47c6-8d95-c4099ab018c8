package gov.derate.proof.synchronous.entity;

import gov.derate.proof.common.entity.AbstractDomainEntity;
import gov.derate.proof.common.enums.ItemMaterialSourceEnum;
import gov.derate.proof.common.enums.MaterialTypeEnum;
import gov.derate.proof.common.enums.SourceTypeEnum;
import gov.derate.proof.common.enums.convert.MaterialTypeEnumConvert;
import gov.derate.proof.common.enums.convert.SourceTypeEnumConvert;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

/**
 * 事项材料中间表
 *
 * <AUTHOR>
 * @date 2022/4/12.
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_MIDDLE_ITEM_MATERIAL")
public class MiddleItemMaterialDo extends AbstractDomainEntity {
    private static final long serialVersionUID = 2653338013540243784L;

    /**
     * 事项材料来源
     * 人工添加（非标准材料）、对接获取 （标准材料）
     */
    @Column(name = "ITEM_MATERIAL_SOURCE")
    private ItemMaterialSourceEnum itemMaterialSource;

    /**
     * 材料名称
     */
    @Column(name = "MATERIAL_NAME")
    private String materialName;

    /**
     * 材料分类
     */
    @Convert(converter = MaterialTypeEnumConvert.class)
    @Column(name = "MATERIAL_TYPE")
    private MaterialTypeEnum materialType;

    /**
     * 已关联电子证照目录编码
     */
    @Column(name = "LICENSE_CODE")
    private String licenseCode;
    /**
     * 已关联电子证照目录名称
     */
    @Column(name = "LICENSE_NAME")
    private String licenseName;

    /**
     * 材料来源渠道
     */
    @Convert(converter = SourceTypeEnumConvert.class)
    @Column(name = "SOURCE_TYPE")
    private SourceTypeEnum sourceType;

    /**
     * 来源渠道说明
     */
    @Column(name = "SOURCE_EXPLAIN")
    private String sourceExplain;

    /**
     * 是否免提交(1是|0否)
     */
    @Column(name = "SUBMISSION_REQUIRED")
    private boolean submissionRequired;

    /**
     * 示例样表
     */
    @Column(name = "EXAMPLE_GUID")
    private String exampleGuid;

    /**
     * 空白表格
     */
    @Column(name = "FORM_GUID")
    private String formGuid;

    /**
     * 材料版本标识
     */
    @Column(name = "MATERIAL_ID")
    private String materialId;

    /**
     * 材料排序
     */
    @Column(name = "ORDER_NUM")
    private Integer orderNum;

    /**
     * 材料必要性
     */
    @Column(name = "IS_NEED")
    private String isNeed;

    /**
     * 记录唯一标识
     */
    @Column(name = "ROW_GUID")
    private String rowGuid;

    /**
     * 事项基本信息记录标识
     */
    @Column(name = "ITEM_GUID")
    private String itemGuid;

    /**
     * 免提交措施
     */
    @Column(name = "SUBMISSION_MEASURE")
    private String submissionMeasure;

    /**
     * 无证明系统材料uuid(不持久化)
     */
    @Transient
    private String materialRowGuid;

    /**
     * 获取材料中间表唯一标识
     *
     * @return 唯一标识
     */
    public String getMiddleItemMaterialDoKey() {
        return this.getMaterialId() + this.getItemGuid() + this.getRowGuid();
    }

    public ItemMaterialSourceEnum getItemMaterialSource() {
        return itemMaterialSource;
    }

    public void setItemMaterialSource(ItemMaterialSourceEnum itemMaterialSource) {
        this.itemMaterialSource = itemMaterialSource;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public MaterialTypeEnum getMaterialType() {
        return materialType;
    }

    public void setMaterialType(MaterialTypeEnum materialType) {
        this.materialType = materialType;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getLicenseName() {
        return licenseName;
    }

    public void setLicenseName(String licenseName) {
        this.licenseName = licenseName;
    }

    public SourceTypeEnum getSourceType() {
        return sourceType;
    }

    public void setSourceType(SourceTypeEnum sourceType) {
        this.sourceType = sourceType;
    }

    public String getSourceExplain() {
        return sourceExplain;
    }

    public void setSourceExplain(String sourceExplain) {
        this.sourceExplain = sourceExplain;
    }

    public boolean isSubmissionRequired() {
        return submissionRequired;
    }

    public void setSubmissionRequired(boolean submissionRequired) {
        this.submissionRequired = submissionRequired;
    }

    public String getExampleGuid() {
        return exampleGuid;
    }

    public void setExampleGuid(String exampleGuid) {
        this.exampleGuid = exampleGuid;
    }

    public String getFormGuid() {
        return formGuid;
    }

    public void setFormGuid(String formGuid) {
        this.formGuid = formGuid;
    }

    public String getMaterialId() {
        return materialId;
    }

    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getIsNeed() {
        return isNeed;
    }

    public void setIsNeed(String isNeed) {
        this.isNeed = isNeed;
    }

    public String getRowGuid() {
        return rowGuid;
    }

    public void setRowGuid(String rowGuid) {
        this.rowGuid = rowGuid;
    }

    public String getItemGuid() {
        return itemGuid;
    }

    public void setItemGuid(String itemGuid) {
        this.itemGuid = itemGuid;
    }

    public String getSubmissionMeasure() {
        return submissionMeasure;
    }

    public void setSubmissionMeasure(String submissionMeasure) {
        this.submissionMeasure = submissionMeasure;
    }

    public String getMaterialRowGuid() {
        return materialRowGuid;
    }

    public void setMaterialRowGuid(String materialRowGuid) {
        this.materialRowGuid = materialRowGuid;
    }

    @Override
    public String toString() {
        return "MiddleItemMaterialDo{" +
                "itemMaterialSource=" + itemMaterialSource +
                ", materialName='" + materialName + '\'' +
                ", materialType=" + materialType +
                ", licenseCode='" + licenseCode + '\'' +
                ", licenseName='" + licenseName + '\'' +
                ", sourceType=" + sourceType +
                ", sourceExplain='" + sourceExplain + '\'' +
                ", submissionRequired=" + submissionRequired +
                ", exampleGuid='" + exampleGuid + '\'' +
                ", formGuid='" + formGuid + '\'' +
                ", materialId='" + materialId + '\'' +
                ", orderNum=" + orderNum +
                ", isNeed='" + isNeed + '\'' +
                ", rowGuid='" + rowGuid + '\'' +
                ", itemGuid='" + itemGuid + '\'' +
                ", submissionMeasure='" + submissionMeasure + '\'' +
                ", materialRowGuid='" + materialRowGuid + '\'' +
                '}';
    }
}
