package gov.derate.proof.synchronous.service;

import com.google.common.collect.Lists;
import gov.derate.proof.common.enums.ItemMaterialSourceEnum;
import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.enums.ItemStatus;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.common.service.BeanFactoryUtils;
import gov.derate.proof.item.bo.ItemChangeLogFactory;
import gov.derate.proof.item.entity.ItemChangeLogDo;
import gov.derate.proof.item.entity.ItemDo;
import gov.derate.proof.item.entity.ItemMaterialDo;
import gov.derate.proof.item.entity.ItemProofRelationDo;
import gov.derate.proof.synchronous.entity.MiddleItemDo;
import gov.derate.proof.synchronous.entity.MiddleItemMaterialDo;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;

import java.util.List;

/**
 * <p>
 * 事项同步处理 新增 service impl
 * </p>
 * rights reserved. </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022-06-13
 * </p>
 *
 * <AUTHOR>
 */
@Deprecated
public class AddItemAndMaterialServiceImpl implements State {

    @Override
    public void handle(SynchronizeDataContext context) {
        if (context.isUpdate()) {
            return;
        }
        SynchronizeDataService synchronizeDataService = BeanFactoryUtils.getBean(SynchronizeDataServiceImpl.class);
        List<ItemMaterialDo> itemMaterialDoList = Lists.newArrayList();
        MiddleItemDo middleItemDo = context.getMiddleItemDo();
        List<MiddleItemMaterialDo> middleItemMaterialDoList = context.getMiddleItemMaterialDoList();
        List<ItemChangeLogDo> itemChangeLogDoList = Lists.newArrayList();
        //同步事项
        ItemDo itemDo = new ItemDo();
        synchronizeDataService.buildItemDo(itemDo, middleItemDo);
        ItemProofStatusEnum itemProofStatus = ItemStatus.CANCEL.equals(itemDo.getItemStatus()) ? ItemProofStatusEnum.TRANSFERRED : ItemProofStatusEnum.WAIT_FOR_CLEAN;
        itemDo.setItemClearStatus(itemProofStatus);
        //记录事项变更日志
        itemDo.prePersist();
        ItemChangeLogDo itemChangeLogDo = ItemChangeLogFactory.buildChangeLog("新建事项", itemDo.getId(), "系统自动同步", "系统自动同步");
        itemChangeLogDoList.add(itemChangeLogDo);
        context.setItemChangeLogDoList(itemChangeLogDoList);
        //同步材料
        for (MiddleItemMaterialDo middleItemMaterialDo : middleItemMaterialDoList) {
            ItemMaterialDo itemMaterialDo = new ItemMaterialDo();
            itemMaterialDo.setId(java.util.UUID.randomUUID().toString());
            itemMaterialDo.setItemCode(itemDo.getItemCode());
            synchronizeDataService.buildMaterial(itemMaterialDo, middleItemMaterialDo);
            itemMaterialDo.setItemMaterialSource(ItemMaterialSourceEnum.STANDARD_MATERIAL);
            itemMaterialDo.setItemClearStatus(itemProofStatus);
            itemMaterialDoList.add(itemMaterialDo);
        }
        context.setItemDo(itemDo);
        context.setItemMaterialDoList(itemMaterialDoList);
        //记录办事清单
        ItemProofRelationDo itemProofRelation = new ItemProofRelationDo();
        itemProofRelation.setItemCode(itemDo.getItemCode());
        itemProofRelation.setItemProofStatus(itemProofStatus);
        context.setItemProofRelation(itemProofRelation);
        middleItemDo.setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_SUCCESS);

        //生成证明清单
        context.state(new AddProofListState());
    }
}
