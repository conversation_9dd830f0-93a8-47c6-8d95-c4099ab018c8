package gov.derate.proof.synchronous.repository;

import gov.derate.proof.synchronous.entity.MaterialAttachmentDo;
import gov.license.common.api.repository.BaseRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 材料附件数据访问
 *
 * <AUTHOR>
 * @date 2022/4/25.
 */
@Repository
public interface MaterialAttachmentRepository extends BaseRepository<MaterialAttachmentDo, String> {

    /**
     * 根据材料id列表查询材料附件
     *
     * @param materialIdList 材料id列表
     * @return 材料附件
     */
    List<MaterialAttachmentDo> findAllByMaterialIdIn(List<String> materialIdList);
}
