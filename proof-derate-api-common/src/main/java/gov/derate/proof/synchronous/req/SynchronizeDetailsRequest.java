package gov.derate.proof.synchronous.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.common.query.BaseOrganizationQuery;
import gov.derate.proof.common.query.ListCondition;

import java.io.Serializable;

/**
 * 同步数据详情请求体
 *
 * <AUTHOR>
 * @date 2022/4/25.
 */
public class SynchronizeDetailsRequest extends BaseOrganizationQuery implements ListCondition, Serializable {
    private static final long serialVersionUID = 8440219274797529964L;

    /**
     * 中间表事项id
     */
    @JsonProperty("middle_item_id")
    private String middleItemId;

    /**
     * 事项编码
     */
    @JsonProperty("item_code")
    private String itemCode;

    /**
     * 同步状态（同步成功0,同步失败1,未同步2）
     */
    @JsonProperty("synchronize_status")
    private SynchronizeStatusEnum synchronizeStatus;

    public String getMiddleItemId() {
        return middleItemId;
    }

    public void setMiddleItemId(String middleItemId) {
        this.middleItemId = middleItemId;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public SynchronizeStatusEnum getSynchronizeStatus() {
        return synchronizeStatus;
    }

    public void setSynchronizeStatus(SynchronizeStatusEnum synchronizeStatus) {
        this.synchronizeStatus = synchronizeStatus;
    }

}
