package gov.derate.proof.synchronous.service;

import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;

/**
 * <p>
 * 中间表和事项表版本一致逻辑处理
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2024/3/26
 * </p>
 *
 * <AUTHOR>
 */
public class SameItemVersionState implements State {

    @Override
    public void handle(SynchronizeDataContext context) {
        context.getMiddleItemDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_SUCCESS);
    }
}
