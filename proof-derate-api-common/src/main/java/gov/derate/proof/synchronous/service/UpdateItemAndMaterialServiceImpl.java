package gov.derate.proof.synchronous.service;

import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.enums.ItemStatus;
import gov.derate.proof.item.entity.ItemProofRelationDo;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;

import java.util.Objects;

/**
 * <p>
 * 事项同步处理 更新 service impl
 * </p>
 * rights reserved. </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022-06-13
 * </p>
 *
 * <AUTHOR>
 */
@Deprecated
public class UpdateItemAndMaterialServiceImpl implements State {

    @Override
    public void handle(SynchronizeDataContext context) {
        if (!context.isUpdate()) {
            return;
        }
        //办事清单是否为空，为空新增办事清单
        if (Objects.isNull(context.getItemProofRelation())) {
            ItemProofRelationDo itemProofRelation = new ItemProofRelationDo();
            itemProofRelation.setItemCode(context.getItemDo().getItemCode());
            itemProofRelation.setItemProofStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);
            context.setItemProofRelation(itemProofRelation);
        }
        //事项状态为取消
        context.state(ItemStatus.CANCEL == context.getMiddleItemDo().getTaskState() ?
                new ItemStatusCancelState() : new ItemStatusNotCancelServiceImpl());
    }
}
