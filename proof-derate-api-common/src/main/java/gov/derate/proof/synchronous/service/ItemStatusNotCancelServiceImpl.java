package gov.derate.proof.synchronous.service;

import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.common.service.BeanFactoryUtils;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;

/**
 * <p>
 * 事项同步处理 事项状态不为取消 service impl
 * </p>
 * rights reserved. </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022-06-13
 * </p>
 *
 * <AUTHOR>
 */
@Deprecated
public class ItemStatusNotCancelServiceImpl implements State {

    @Override
    public void handle(SynchronizeDataContext context) {
        SynchronizeDataService synchronizeDataService = BeanFactoryUtils.getBean(SynchronizeDataServiceImpl.class);
        //事项状态为待清理
        if (ItemProofStatusEnum.WAIT_FOR_CLEAN == context.getItemProofRelation().getItemProofStatus()) {
            // 先基于中间表构建新的事项与材料数据
            context.buildItemAndMaterialByMiddleItem(context, synchronizeDataService);
            //重新生成证明清单
            context.state(new AddProofListState());
            return;
        }
        //事项非待清理，校验材料是否一致
        if (synchronizeDataService.checkItemMaterialEquals(context)) {
            //材料一致，则更新事项和材料
            context.buildItemAndMaterialByMiddleItem(context, synchronizeDataService);
            return;
        }
        //材料不一致同步失败
        context.getMiddleItemDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_FAILED);
        context.getMiddleItemDo().setFailedResult("事项下增加或删除了证明，需要重新梳理");
    }
}
