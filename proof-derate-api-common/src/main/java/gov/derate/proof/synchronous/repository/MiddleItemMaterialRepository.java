package gov.derate.proof.synchronous.repository;

import gov.derate.proof.synchronous.entity.MiddleItemMaterialDo;
import gov.license.common.api.repository.BaseRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 事项材料中间表 数据访问
 *
 * <AUTHOR>
 * @date 2022/4/13.
 */
@Repository
public interface MiddleItemMaterialRepository extends BaseRepository<MiddleItemMaterialDo, String> {
    /**
     * 查询大于创建时间的记录
     *
     * @param creationTime 创建时间
     * @return 记录
     */
    List<MiddleItemMaterialDo> findAllByCreationTimeAfter(Date creationTime);
    /**
     * 根据事项的guid查询关联的材料
     *
     * @param itemGuid 事项guid
     * @return 关联的材料
     */
    List<MiddleItemMaterialDo> findAllByItemGuid(String itemGuid);
}
