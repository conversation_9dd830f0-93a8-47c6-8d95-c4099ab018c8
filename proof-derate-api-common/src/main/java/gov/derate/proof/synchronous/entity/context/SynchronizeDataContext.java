package gov.derate.proof.synchronous.entity.context;

import com.google.common.collect.Lists;
import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.enums.ItemStatus;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.common.service.BeanFactoryUtils;
import gov.derate.proof.item.entity.ItemChangeLogDo;
import gov.derate.proof.item.entity.ItemDo;
import gov.derate.proof.item.entity.ItemMaterialDo;
import gov.derate.proof.item.entity.ItemProofRelationDo;
import gov.derate.proof.list.entity.ProofListDo;
import gov.derate.proof.list.entity.ProofListProcedureDo;
import gov.derate.proof.list.entity.ReplaceLicenseDo;
import gov.derate.proof.synchronous.entity.MiddleItemDo;
import gov.derate.proof.synchronous.entity.MiddleItemMaterialDo;
import gov.derate.proof.synchronous.entity.SynchronizeLogDo;
import gov.derate.proof.synchronous.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Objects;

/**
 * 同步数据上下文
 *
 * <AUTHOR>
 * @date 2022/4/15.
 */
public class SynchronizeDataContext {
    private static final Logger LOGGER = LoggerFactory.getLogger(SynchronizeDataContext.class);

    /**
     * 需要同步的事项
     */
    private ItemDo itemDo;

    /**
     * 需要同步的材料
     */
    private List<ItemMaterialDo> itemMaterialDoList = Lists.newArrayList();

    /**
     * 待同步的事项
     */
    private MiddleItemDo middleItemDo;

    /**
     * 待同步的材料
     */
    private List<MiddleItemMaterialDo> middleItemMaterialDoList = Lists.newArrayList();

    /**
     * 需要记录的同步日志
     */
    private SynchronizeLogDo synchronizeLogDo;

    /**
     * 需要记录的办事清单
     */
    private ItemProofRelationDo itemProofRelation;

    /**
     * 需要记录的事项更新日志
     */
    private List<ItemChangeLogDo> itemChangeLogDoList = Lists.newArrayList();

    /**
     * 已存在的清单
     */
    private List<ProofListDo> proofListDoList = Lists.newArrayList();

    /**
     * 证明目录关联的电子证照
     */
    private List<ReplaceLicenseDo> replaceLicenseDoList = Lists.newArrayList();

    /**
     * 事项证明清单过程信息表
     */
    private List<ProofListProcedureDo> proofListProcedureDoList = Lists.newArrayList();
    /**
     * 是否为数据更新
     */
    private boolean isUpdate;
    /**
     * 事项同步处理状态接口
     */
    private State state;

    /**
     * 赋值并且执行策略
     *
     * @param state 策略
     * @return 上下文，链式编程
     */
    public SynchronizeDataContext state(State state) {
        this.state = state;
        getState().handle(this);
        return this;
    }

    /**
     * 更新事项与材料
     *
     * @param context                事项上下文
     * @param synchronizeDataService 同步服务
     */
    public void buildItemAndMaterialByMiddleItem(SynchronizeDataContext context, SynchronizeDataService synchronizeDataService) {
        synchronizeDataService.synchronizeItemAndMaterial(context);
        context.getItemDo().setItemClearStatus(context.getItemProofRelation().getItemProofStatus());
        if (CollectionUtils.isNotEmpty(context.getItemMaterialDoList())) {
            context.getItemMaterialDoList().forEach(item -> {
                item.setItemClearStatus(context.getItemProofRelation().getItemProofStatus());
            });
        }
        if (CollectionUtils.isNotEmpty(context.getProofListDoList())) {
            context.getProofListDoList().forEach(item -> {
                item.setItemProofStatus(context.getItemProofRelation().getItemProofStatus());
            });
        }
        context.getMiddleItemDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_SUCCESS);
    }

    /**
     * 构造state对象
     */
    public void initState() {
        boolean isExistsItem = Objects.nonNull(itemDo);
        LOGGER.debug("middleItem is [{}] itemDo is [{}]", this.middleItemDo, this.itemDo);
       /* if (isExistsItem && middleItemDo.getVersionDate().equals(itemDo.getVersionDate())) {
            // 版本时间相等则同步成功，无需再同步，
            this.setState(new SameItemVersionState());
            return;
        }*/
        if (isExistsItem && middleItemDo.getVersionDate().before(itemDo.getVersionDate())) {
            // 版本晚则同步无效,无需再同步，
            this.setState(new AfterItemVersionState());
            return;
        }

        if (ItemStatus.CANCEL == this.getMiddleItemDo().getTaskState()) {
            // 事项取消,无论事项是否存在，均进行写入或更新。进行事项取消状态
            LOGGER.debug("code [{}] ,version [{}], cancel item ", this.getMiddleItemDo().getTaskCode(), this.getMiddleItemDo().getTaskVersion());
            this.setState(new ItemStatusCancelState());
            return;
        }
        if (!isExistsItem) {
            // 不存在库中，新增数据
            LOGGER.debug("code [{}] ,version [{}], add item ", this.getMiddleItemDo().getTaskCode(), this.getMiddleItemDo().getTaskVersion());
            this.setState(new NewItemAndMaterialState());
            return;
        }

        boolean isMiddleItemNewVersion = Objects.isNull(itemDo.getVersionDate()) || middleItemDo.getVersionDate().getTime() >= itemDo.getVersionDate().getTime();
        if (isMiddleItemNewVersion) {
            //中间表版本比本地晚则更新标识
            this.setUpdate(true);
        }

        boolean isWaitForCleanStatus = Objects.nonNull(this.getItemProofRelation()) && ItemProofStatusEnum.WAIT_FOR_CLEAN == this.getItemProofRelation().getItemProofStatus();
        if (isMiddleItemNewVersion && isWaitForCleanStatus) {
            // 最新版本+待清理，更新或新增材料
            LOGGER.debug("code [{}] ,version [{}], update item  ", this.getMiddleItemDo().getTaskCode(), this.getMiddleItemDo().getTaskVersion());
            this.setState(new UpdateItemAndMaterialState());
            return;
        }

        SynchronizeDataService synchronizeDataService = BeanFactoryUtils.getBean(SynchronizeDataServiceImpl.class);
        boolean isItemMaterialEq = synchronizeDataService.checkItemMaterialEquals(this);

        if (isMiddleItemNewVersion && !isItemMaterialEq) {
            // 非待清理+最新版本+材料不一致，记录同步失败
            LOGGER.debug("code [{}] ,version [{}], material not eq ", this.getMiddleItemDo().getTaskCode(), this.getMiddleItemDo().getTaskVersion());
            this.setState(new ItemMaterialNotEqMiddleFailureState());
        }
        if (isMiddleItemNewVersion && isItemMaterialEq) {
            // 非待清理+最新版本+材料一致，更新材料
            LOGGER.debug("code [{}] ,version [{}], material  eq ", this.getMiddleItemDo().getTaskCode(), this.getMiddleItemDo().getTaskVersion());
            this.setState(new UpdateItemAndMaterialState());
        }

    }

    /**
     * 获取事项中间表，事项编码
     *
     * @param middleItemDo 事项中间表
     * @return 唯一标识
     */
    public String getMiddleItemDoItemCode(MiddleItemDo middleItemDo) {
        if (Objects.isNull(middleItemDo)) {
            throw new IllegalArgumentException("getMiddleItemDoKey fail -> middleItemDo is null ");
        }
        return StringUtils.isNotBlank(middleItemDo.getSituationCode()) ?
                middleItemDo.getSituationCode() : middleItemDo.getTaskCode();
    }

    public ItemDo getItemDo() {
        return itemDo;
    }

    public void setItemDo(ItemDo itemDo) {
        this.itemDo = itemDo;
    }

    public List<ItemMaterialDo> getItemMaterialDoList() {
        return itemMaterialDoList;
    }

    public void setItemMaterialDoList(List<ItemMaterialDo> itemMaterialDoList) {
        this.itemMaterialDoList = itemMaterialDoList;
    }

    public MiddleItemDo getMiddleItemDo() {
        return middleItemDo;
    }

    public void setMiddleItemDo(MiddleItemDo middleItemDo) {
        this.middleItemDo = middleItemDo;
    }

    public List<MiddleItemMaterialDo> getMiddleItemMaterialDoList() {
        return middleItemMaterialDoList;
    }

    public void setMiddleItemMaterialDoList(List<MiddleItemMaterialDo> middleItemMaterialDoList) {
        this.middleItemMaterialDoList = middleItemMaterialDoList;
    }

    public SynchronizeLogDo getSynchronizeLogDo() {
        return synchronizeLogDo;
    }

    public void setSynchronizeLogDo(SynchronizeLogDo synchronizeLogDo) {
        this.synchronizeLogDo = synchronizeLogDo;
    }

    public ItemProofRelationDo getItemProofRelation() {
        return itemProofRelation;
    }

    public void setItemProofRelation(ItemProofRelationDo itemProofRelation) {
        this.itemProofRelation = itemProofRelation;
    }

    public List<ItemChangeLogDo> getItemChangeLogDoList() {
        return itemChangeLogDoList;
    }

    public void setItemChangeLogDoList(List<ItemChangeLogDo> itemChangeLogDoList) {
        this.itemChangeLogDoList = itemChangeLogDoList;
    }

    public List<ProofListDo> getProofListDoList() {
        return proofListDoList;
    }

    public void setProofListDoList(List<ProofListDo> proofListDoList) {
        this.proofListDoList = proofListDoList;
    }

    public List<ReplaceLicenseDo> getReplaceLicenseDoList() {
        return replaceLicenseDoList;
    }

    public void setReplaceLicenseDoList(List<ReplaceLicenseDo> replaceLicenseDoList) {
        this.replaceLicenseDoList = replaceLicenseDoList;
    }

    public List<ProofListProcedureDo> getProofListProcedureDoList() {
        return proofListProcedureDoList;
    }

    public void setProofListProcedureDoList(List<ProofListProcedureDo> proofListProcedureDoList) {
        this.proofListProcedureDoList = proofListProcedureDoList;
    }

    public boolean isUpdate() {
        return isUpdate;
    }

    public void setUpdate(boolean update) {
        isUpdate = update;
    }


    public State getState() {
        LOGGER.debug("state strategy [{}]", this.state);
        return state;
    }

    public void setState(State state) {
        this.state = state;
        LOGGER.debug("code [{}],version [{}] state strategy [{}]", this.middleItemDo.getTaskCode(), this.middleItemDo.getTaskVersion(), this.state);
    }

    @Override
    public String toString() {
        return "SynchronizeDataContext{" +
                "itemDo=" + itemDo +
                ", itemMaterialDoList=" + itemMaterialDoList +
                ", middleItemDo=" + middleItemDo +
                ", middleItemMaterialDoList=" + middleItemMaterialDoList +
                ", itemProofRelation=" + itemProofRelation +
                ", itemChangeLogDoList=" + itemChangeLogDoList +
                ", proofListDoList=" + proofListDoList +
                ", replaceLicenseDoList=" + replaceLicenseDoList +
                ", proofListProcedureDoList=" + proofListProcedureDoList +
                ", isUpdate=" + isUpdate +
                '}';
    }


}
