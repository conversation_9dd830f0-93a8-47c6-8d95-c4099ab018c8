package gov.derate.proof.synchronous.service;

import gov.derate.proof.item.entity.ItemDo;
import gov.derate.proof.item.entity.ItemMaterialDo;
import gov.derate.proof.synchronous.entity.MiddleItemDo;
import gov.derate.proof.synchronous.entity.MiddleItemMaterialDo;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;

import javax.annotation.Nonnull;
import java.util.List;

/**
 * 同步数据服务
 *
 * <AUTHOR>
 * @date 2022/4/14.
 */
public interface SynchronizeDataService {

    /**
     * 同步数据
     */
    void synchronizeData(List<String> itemCodeList);

    /**
     * 同步数据
     *
     * @param middleItemDos 待同步中间表数据
     */
    void syncMiddleItemDoList(List<MiddleItemDo> middleItemDos);


    /**
     * 同步区县的清理关系到街道事项中。
     *
     * @param param 要处理得行政区划，多个通过,号区分。 也有可能直接只有一个 ALL过来。此时查全局的
     * @return 执行结果
     */
    Boolean syncSourceItemCleanWayToTargetItemCleanWay(@Nonnull String param);


    /**
     * 构建事项
     *
     * @param itemDo       事项
     * @param middleItemDo 事项中间表
     */
    void buildItemDo(ItemDo itemDo, MiddleItemDo middleItemDo);

    /**
     * 构建材料
     *
     * @param itemMaterialDo       材料
     * @param middleItemMaterialDo 材料中间表
     */
    void buildMaterial(ItemMaterialDo itemMaterialDo, MiddleItemMaterialDo middleItemMaterialDo);

    /**
     * 同步事项和材料
     *
     * @param context 同步上下文对象
     */
    void synchronizeItemAndMaterial(SynchronizeDataContext context);

    /**
     * 校验材料是否一致
     *
     * @param context 同步上下文对象
     * @return true ->一致  false ->不一致
     */
    boolean checkItemMaterialEquals(SynchronizeDataContext context);

    /**
     * 获取中间表同步到事项表中。
     * @param middleItemDoList 中间表事项数据
     * @param middleItemMaterialDoList 中间表材料数据
     * @return 数据上下文，未保存状态
     */
    List<SynchronizeDataContext> getSyncDataContextByMiddleItemAndMaterial(List<MiddleItemDo> middleItemDoList, List<MiddleItemMaterialDo> middleItemMaterialDoList);

    /**
     * 保存中间表同步上下文对象数据。
     *
     * @param context 上下文数据
     */
    void saveSynchronizeDataContext(List<SynchronizeDataContext> context);

    /**
     * 比对无证明城市清理事项与中间表方式
     *
     * @param itemCodeList 多个事项编码
     */
    void itemMaterialCompareHandle(List<String> itemCodeList);
}
