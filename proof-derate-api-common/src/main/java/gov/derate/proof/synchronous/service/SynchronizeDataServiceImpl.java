package gov.derate.proof.synchronous.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import gov.derate.proof.common.enums.*;
import gov.derate.proof.common.service.BeanFactoryUtils;
import gov.derate.proof.common.service.ItemBizEventPublisherManager;
import gov.derate.proof.common.utils.BeanCopyUtils;
import gov.derate.proof.common.utils.DivisionUtils;
import gov.derate.proof.common.utils.ThreadPoolRunUtils;
import gov.derate.proof.dictionary.entity.DictionaryTypeItemConstant;
import gov.derate.proof.item.bo.ItemChangeLogFactory;
import gov.derate.proof.item.bo.ItemMaterialCompareBuilder;
import gov.derate.proof.item.entity.*;
import gov.derate.proof.item.event.CreateItemStatusChangeEvent;
import gov.derate.proof.item.event.ProofListStatusChangeEvent;
import gov.derate.proof.item.exception.ItemServiceException;
import gov.derate.proof.item.repository.ItemChangeLogDoRepository;
import gov.derate.proof.item.repository.ItemMaterialCompareRepository;
import gov.derate.proof.item.repository.ItemMaterialRepository;
import gov.derate.proof.item.repository.ItemRepository;
import gov.derate.proof.item.resp.ItemResponseCode;
import gov.derate.proof.item.service.ItemSystemSyncFactory;
import gov.derate.proof.item.service.ItemSystemSyncInterface;
import gov.derate.proof.list.entity.*;
import gov.derate.proof.list.repository.*;
import gov.derate.proof.synchronous.entity.MiddleItemDo;
import gov.derate.proof.synchronous.entity.MiddleItemMaterialDo;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;
import gov.derate.proof.synchronous.repository.MiddleItemMaterialRepository;
import gov.derate.proof.synchronous.repository.MiddleItemRepository;
import gov.licc.func.api.amp.service.DictPublicService;
import gov.license.common.api.entity.BaseDo;
import gov.license.common.tools.date.DateUtil;
import gov.license.jpa.PredicateBuilder;
import gov.license.jpa.Specifications;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 同步数据服务
 *
 * <AUTHOR>
 * @date 2022/4/14.
 */
@Service
public class SynchronizeDataServiceImpl implements SynchronizeDataService {

    @Autowired
    private MiddleItemRepository middleItemRepository;

    @Autowired
    private MiddleItemMaterialRepository middleItemMaterialRepository;

    @Autowired
    private ItemRepository itemRepository;

    @Autowired
    private ItemProofRelationRepository itemProofRelationRepository;

    /**
     * 新版本操作日志
     */
    @Autowired
    private ProofListProcedureRepository procedureLogRepository;

    @Autowired
    private ItemMaterialRepository itemMaterialRepository;

    @Autowired
    private ItemChangeLogDoRepository itemChangeLogDoRepository;

    @Autowired
    private ProofListRepository proofListDoRepository;

    @Autowired
    private ItemBizEventPublisherManager itemBizEventPublisherManager;

    /**
     *
     */
    @Autowired
    private ReplaceClerkCommitmentRepository replaceClerkCommitmentRepository;
    /**
     *
     */
    @Autowired
    private ReplaceDataSharedRepository replaceDataSharedRepository;
    /**
     *
     */
    @Autowired
    private ReplaceDeptSurveyRepository replaceDeptSurveyRepository;
    /**
     *
     */
    @Autowired
    private ReplaceInvestigationRepository replaceInvestigationRepository;
    /**
     *
     */
    @Autowired
    private ReplaceLicenseRepository replaceLicenseRepository;
    /**
     *
     */
    @Autowired
    private ReplaceOtherRepository replaceOtherRepository;
    /**
     * 批次
     */
    @Value("${proof.derate.syncSourceItemCleanWayToTargetItemCleanWayBatch:1000}")
    private Integer itemCleanWayBatch = 1000;
    /**
     * 事项材料比对仓库
     */
    @Autowired
    private ItemMaterialCompareRepository itemMaterialCompareRepository;
    /**
     * 事项清理清单仓库
     */
    @Autowired
    private ProofListRepository proofListRepository;

    private static final Logger LOGGER = LoggerFactory.getLogger(SynchronizeDataServiceImpl.class);

    @Override
    public synchronized void synchronizeData(List<String> itemCodeList) {
        //中间表同步到事项表的默认线程池
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 50, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(1000), new ThreadFactoryBuilder().setNameFormat("synchronizeData-pool-%d").build());
        List<String> taskCodeList = middleItemRepository.findAllBySynchronizeStatusEquals(SynchronizeStatusEnum.WAIT_SYNCHRONIZE);
        if (CollectionUtils.isNotEmpty(itemCodeList)) {
            taskCodeList = Lists.newArrayList(taskCodeList);
            LOGGER.info("synchronizeData execute Item code is [{}]", taskCodeList);
        }
        try {
            taskCodeList.forEach(taskCode -> {
                List<MiddleItemDo> middleItemDoList = middleItemRepository.findAllByTaskCode(taskCode);
                //当前批次中间表事项是否存在同一事项不同版本，存在则将旧版本事项状态设为同步失效
                middleItemDoList = middleItemDoList.stream().filter(item -> SynchronizeStatusEnum.WAIT_SYNCHRONIZE.equals(item.getSynchronizeStatus())).collect(Collectors.toList());
                filterMiddleItemDoVersion(middleItemDoList);
                //更新无效同步得中间表数据。
                List<MiddleItemDo> syncInvalidData = middleItemDoList.stream().filter(item -> SynchronizeStatusEnum.SYNCHRONIZE_INVALID.equals(item.getSynchronizeStatus())).collect(Collectors.toList());
                middleItemRepository.saveAll(syncInvalidData);
                middleItemDoList = middleItemDoList.stream().filter(item -> SynchronizeStatusEnum.WAIT_SYNCHRONIZE.equals(item.getSynchronizeStatus())).collect(Collectors.toList());
                // 仅仅处理最新版本的数据。
                if (CollectionUtils.isNotEmpty(middleItemDoList)) {
                    List<MiddleItemDo> middleItemDos = Lists.newCopyOnWriteArrayList(middleItemDoList);
                    LOGGER.debug("sync middle item is [{}]", middleItemDos);
                    threadPoolExecutor.execute(() -> {
                        //中间表获取待同步的事项及材料，事项分批获取
                        long startTime = System.currentTimeMillis();
                        LOGGER.debug("middleItemDoList size [{}] query time [{}]", middleItemDos.size(),
                                System.currentTimeMillis() - startTime);
                        syncMiddleItemDoList(middleItemDos);
                    });
                }
            });
        } catch (Exception e) {
            LOGGER.error("synchronizeData error", e);
        } finally {
            threadPoolExecutor.shutdown();
            // 等待所有任务执行完毕后再退出程序
            while (!threadPoolExecutor.isTerminated()) {
                // 等待所有任务执行完毕后再退出程序
                try {
                    TimeUnit.MILLISECONDS.sleep(100);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        LOGGER.debug("synchronizeData success");
    }

    /**
     * 中间表数据同步到库中
     *
     * @param middleItemDos 中间表数据
     */
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public void syncMiddleItemDoList(List<MiddleItemDo> middleItemDos) {
        long startTime;
        if (CollectionUtils.isNotEmpty(middleItemDos)) {
            startTime = System.currentTimeMillis();

            LOGGER.debug("filterMiddleItemDoVersion time [{}]", System.currentTimeMillis() - startTime);
            //中间表根据事项获取关联材料
            startTime = System.currentTimeMillis();
            List<String> rowGuids = middleItemDos.stream()
                    .map(MiddleItemDo::getRowGuid).distinct()
                    .collect(Collectors.toList());
            List<MiddleItemMaterialDo> middleItemMaterialDoList = getAllMiddleItemMaterialDoByItemGuid(rowGuids);
            LOGGER.debug("middleItemMaterialDoList size [{}] query time [{}]",
                    middleItemMaterialDoList.size(), System.currentTimeMillis() - startTime);

            //数据同步整合
            startTime = System.currentTimeMillis();
            List<SynchronizeDataContext> context = getSyncDataContextByMiddleItemAndMaterial(middleItemDos, middleItemMaterialDoList);
            saveSynchronizeDataContext(context);

            LOGGER.debug("synchronizeData getSyncDataContextByMiddleItemAndMaterial syn time [{}]", System.currentTimeMillis() - startTime);
        }
    }

    /**
     * 根据事项标识获取所有关联的材料集合
     *
     * @param rowGuids 事项标识集合
     * @return 事项关联的材料集合
     */
    private List<MiddleItemMaterialDo> getAllMiddleItemMaterialDoByItemGuid(List<String> rowGuids) {
        List<MiddleItemMaterialDo> collection = Lists.newCopyOnWriteArrayList();
        //中间表根据关联字段获取所有待同步材料
        List<List<String>> rowGuidList = Lists.partition(rowGuids, 1000);
        CountDownLatch countDownLatch = new CountDownLatch(rowGuidList.size());
        List<Runnable> itemGuidRunnable = rowGuidList.stream().map(item -> (Runnable) () -> {
            try {
                collection.addAll(middleItemMaterialRepository.findAll(Specifications.<MiddleItemMaterialDo>and().in("itemGuid", item).build()));
            } catch (Exception e) {
                LOGGER.debug("middleItemMaterialRepository.findAll error,skip", e);
            } finally {
                countDownLatch.countDown();
            }
        }).collect(Collectors.toList());
        ThreadPoolRunUtils.runMutilateByThreadPool(itemGuidRunnable, countDownLatch, "getAllMiddleItemMaterialDoByItemGuid");
        return collection;
    }

    /**
     * 中间表事项版本过滤
     * 是否存在同一事项不同版本，存在则将旧版本事项状态设为同步失效
     *
     * @param middleItemDoList 事项中间表
     */
    private void filterMiddleItemDoVersion(List<MiddleItemDo> middleItemDoList) {
        //不存在taskCode和situationCode相同但是版本不同的情况
        Map<String, List<MiddleItemDo>> middleItemDoMap = middleItemDoList.stream()
                .collect(Collectors.groupingBy(MiddleItemDo::getProofItemCode));

        middleItemDoMap.values().stream()
                .filter(list -> list.size() > 1).forEach(middleItemDos -> {
                    Optional<MiddleItemDo> middleItemDo = middleItemDos.stream()
                            .max(Comparator.comparing(MiddleItemDo::getVersionDate));
                    MiddleItemDo maxMiddleItem = middleItemDo.get();
                    middleItemDos.stream()
                            .filter(item -> !item.getItemId().equals(maxMiddleItem.getItemId()))
                            .forEach(item -> item.setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_INVALID));
                });
    }


    /**
     * 同步源清理关系到目标事项中。
     * <p>
     * 通过行政区划进行过滤数据
     * 分批，使用itemCode进行顺序排序后查询处理数据，用itemCode解决深度分页问题。
     *
     * @param param 要处理得行政区划，多个通过,号区分。 也有可能直接只有一个 ALL过来。此时查全局的
     * @return 执行结果
     */
    @Override
    public Boolean syncSourceItemCleanWayToTargetItemCleanWay(@Nonnull String param) {
        List<String> syncTargetDivisionCode = Lists.newArrayList(param.split(","));
        LOGGER.info("syncCountyToStreetClean starting params is  [{}]", syncTargetDivisionCode);
        // 无证明城市-同步源清理关系到目标事项功能，查询批次，最大是1000，超过则报错
        int batch = 1000;
        if (itemCleanWayBatch > batch) {
            throw new RuntimeException("proof.derate.syncSourceItemCleanWayToTargetItemCleanWayBatch size only <= 1000");
        }
        String lastItemCode = "";
        Pageable pageable = PageRequest.of(0, itemCleanWayBatch, Sort.by(Sort.Direction.ASC, "itemCode"));

        Page<ItemDo> targetItemDoPage;
        // 分页分批处理。
        do {
            PredicateBuilder<ItemDo> spec = Specifications.and();
            spec.eq("itemStatus", ItemStatus.WORK);
            String all = "ALL";
            if (!syncTargetDivisionCode.contains(all)) {
                // 只查询处理行政区划为6位的地市或区县行政区划。
                syncTargetDivisionCode.removeIf(item -> item.length() != 6);
                PredicateBuilder<ItemDo> divisionCodeOr = Specifications.or();
                syncTargetDivisionCode.forEach(divisionCode -> {
                    // 只处理街道的行政区划，但不包括6位【区和地市】的行政区划数据
                    divisionCodeOr.like("divisionCode", DivisionUtils.getShortCode(divisionCode) + "%");
                    spec.ne("divisionCode", divisionCode);
                });
                spec.predicate(divisionCodeOr.build());
            }
            spec.gt(StringUtils.isNotBlank(lastItemCode), "itemCode", lastItemCode);
            targetItemDoPage = itemRepository.findAll(spec.build(), pageable);
            if (!targetItemDoPage.hasContent()) {
                LOGGER.warn("itemRepository.syncSourceItemCleanWayToTargetItemCleanWay.findAll(spec.build(), pageable) is blank end process lastItemCode  [{}] divisionCode[{}]", lastItemCode, syncTargetDivisionCode);
            } else {
                List<ItemDo> content = targetItemDoPage.getContent();
                ItemDo lastItemDo = content.get(content.size() - 1);
                lastItemCode = lastItemDo.getItemCode();
                processTargetServiceItem(content);
            }
        } while (targetItemDoPage.hasContent());
        return true;
    }

    /**
     * 处理目标的事项逻辑
     *
     * @param targetItemDoList 目标事项List
     * @return 处理成功，失败
     */
    private boolean processTargetServiceItem(List<ItemDo> targetItemDoList) {
        Set<String> targetItemCodeList = targetItemDoList.stream().map(ItemDo::getItemCode).collect(Collectors.toSet());
        List<ItemProofRelationDo> targetItemProofRelations = itemProofRelationRepository
                .findAllByItemCodeIn(Lists.newArrayList(targetItemCodeList));
        Map<String, ItemProofRelationDo> targetItemCodeAndRelationMap = targetItemProofRelations.stream()
                .collect(Collectors.toMap(ItemProofRelationDo::getItemCode, v -> v, (before, after) -> after));
        Map<String, ItemDo> targetItemCodeAndStreetItemDoMap = targetItemDoList.stream()
                .collect(Collectors.toMap(ItemDo::getItemCode, v -> v, (before, after) -> after));

        List<ProofListProcedureDo> targetProcedureLogDos = procedureLogRepository
                .findAllByItemCodeInAndAccountNot(targetItemCodeList, "系统自动同步");
        Map<String, List<ProofListProcedureDo>> targetItemCodeAndLogMap = targetProcedureLogDos.stream()
                .collect(Collectors.toMap(ProofListProcedureDo::getItemCode, Lists::newArrayList, (before, after) -> {
                    before.addAll(after);
                    return before;
                }));

        // 1. 查询出这些街道行政区划的中间事项表
        List<MiddleItemDo> middleItemDos = middleItemRepository.findAllByTaskCodeIn(targetItemCodeList);
        // 过滤只获取最新一条的事项，并且同步成功的事项
        Map<String, List<MiddleItemDo>> taskCodeMiddleItemMap = middleItemDos.stream()
                .collect(Collectors.toMap(MiddleItemDo::getTaskCode, Lists::newArrayList, (before, after) -> {
                    before.addAll(after);
                    return before;
                }));
        Map<String, MiddleItemDo> syncMiddleItemMap = Maps.newHashMap();
        taskCodeMiddleItemMap.values().forEach(item -> {
            // 只保留最新版本的是事项数据
            Optional<MiddleItemDo> first = item.stream().max(Comparator.comparing(MiddleItemDo::getVersionDate));
            if (first.isPresent()) {
                MiddleItemDo middleItemDo = first.get();
                syncMiddleItemMap.put(middleItemDo.getTaskCode(), middleItemDo);
            }
        });


        Set<String> parentItemCodeSet = syncMiddleItemMap.values().stream()
                .map(MiddleItemDo::getParentTaskCode)
                .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        // 查询父事项的清理情况
        List<List<String>> parentItemProofRelationList = Lists.partition(Lists.newArrayList(parentItemCodeSet), 500);
        for (List<String> parentItemCodeList : parentItemProofRelationList) {
            List<ItemProofRelationDo> parentItemProofRelations = itemProofRelationRepository.findAllByItemCodeIn(parentItemCodeList);
            Map<String, ItemProofRelationDo> parentItemCodeAndRelationMap = parentItemProofRelations.stream()
                    .collect(Collectors.toMap(ItemProofRelationDo::getItemCode, v -> v, (before, after) -> after));
            try {
                for (Map.Entry<String, MiddleItemDo> entry : syncMiddleItemMap.entrySet()) {
                    String streetItemCode = entry.getKey();
                    MiddleItemDo middleItemDo = entry.getValue();
                    // 2. 查询中间表事项，是否存在父事项编码。
                    // 3. 判断子事项编码，是否存在人工操作记录表。
                    // 3.1 存在，忽略该操作
                    if (StringUtils.isNotBlank(middleItemDo.getParentTaskCode())
                            && Objects.isNull(targetItemCodeAndLogMap.get(streetItemCode))) {
                        // 4. 不存在，判断父事项清理状态，是否为审核通过状态
                        ItemProofRelationDo parentItemProofRelation = parentItemCodeAndRelationMap.get(middleItemDo.getParentTaskCode());
                        boolean approvedFlag = Objects.nonNull(parentItemProofRelation)
                                && ItemProofStatusEnum.APPROVED.equals(parentItemProofRelation.getItemProofStatus())
                                && Objects.nonNull(targetItemCodeAndRelationMap.get(streetItemCode))
                                && !ItemProofStatusEnum.APPROVED.equals(targetItemCodeAndRelationMap.get(streetItemCode).getItemProofStatus());
                        if (approvedFlag) {
                            LOGGER.debug("父事项审核通过，并且子事项存在清理状态的数据。copy 父事项的清理数据到子事项中。" +
                                    "并且记录日志 parent is 【{}】 children is [{}]", parentItemProofRelation, streetItemCode);
                            // 父事项审核通过，并且子事项存在清理状态的数据。copy 父事项的清理数据到子事项中。并且记录日志
                            ItemDo streetItemDo = targetItemCodeAndStreetItemDoMap.get(streetItemCode);
                            try {
                                processParentItemCopy(targetItemCodeAndRelationMap, streetItemCode, parentItemProofRelation, streetItemDo);
                            } catch (Exception e) {
                                LOGGER.warn("父事项审核通过，并且子事项存在清理状态的数据。copy 父事项的清理数据到子事项中。" +
                                        " 处理异常 parent 【{}】 children [{}]", parentItemProofRelation, streetItemDo, e);
                            }
                        }
                        boolean flag = Objects.nonNull(parentItemProofRelation)
                                && !ItemProofStatusEnum.APPROVED.equals(parentItemProofRelation.getItemProofStatus())
                                && Objects.nonNull(targetItemCodeAndRelationMap.get(streetItemCode))
                                && !(ItemProofStatusEnum.WAIT_FOR_CLEAN.equals(targetItemCodeAndRelationMap.get(streetItemCode).getItemProofStatus())
                                && ItemProofStatusEnum.APPROVED.equals(parentItemProofRelation.getItemProofStatus()));
                        if (flag) {
                            LOGGER.debug("父事项非审核通过，并且子事项存在清理状态的数据。 子事项状态改为待清理，清空事项清单等数据" +
                                    " 并且记录日志 parent is 【{}】 children is [{}]", parentItemProofRelation, streetItemCode);
                            // 父事项非审核通过，并且子事项存在清理状态的数据。 子事项状态改为待清理，清空事项清单等数据
                            ItemProofRelationDo streetItemProofRelation = targetItemCodeAndRelationMap.get(streetItemCode);
                            streetItemProofRelation.setItemProofStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);
                            itemProofRelationRepository.save(streetItemProofRelation);
                            logOperationLogByStreetItemCodeInSystemSync(streetItemCode,
                                    ItemProofStatusEnum.WAIT_FOR_CLEAN, "父事项发生更变，系统自动驳回清理", "驳回清理");
                            saveItemProofRelation(streetItemProofRelation);
                        }
                    }
                }
            } catch (Exception e) {
                LOGGER.error("syncCountyToStreetClean sync process error", e);
                return true;
            }
        }
        return false;
    }

    /**
     * 记录清理日志
     *
     * @param itemCode        事项编码
     * @param itemProofStatus 清理状态
     * @param message         信息
     * @param operation       操作人
     */
    public void logOperationLogByStreetItemCodeInSystemSync(String itemCode, ItemProofStatusEnum itemProofStatus, String message, String operation) {
        ProofListProcedureDo proofListProcedureDo = new ProofListProcedureDo();
        proofListProcedureDo.setAccount("系统自动同步");
        proofListProcedureDo.setAccountName("系统自动同步");
        proofListProcedureDo.setAccountDeptCode("系统自动同步");
        proofListProcedureDo.setAccountDeptName("系统自动同步");
        proofListProcedureDo.setProcedureDate(new Date());
        proofListProcedureDo.setItemCode(itemCode);
        proofListProcedureDo.setItemProofStatus(itemProofStatus);
        proofListProcedureDo.setProcedureMessage(message);
        proofListProcedureDo.setOperatorLog(OperatorLogEnum.SYNCHRONIZE_UPDATE);
        proofListProcedureDo.setCreatorId("系统自动同步");
        proofListProcedureDo.setLastModificatorId("系统自动同步");
        proofListProcedureDo.setOperation(operation);
        procedureLogRepository.save(proofListProcedureDo);
    }

    /**
     * 保存清理状态
     *
     * @param itemProofRelation 清理状态
     */
    public void saveItemProofRelation(ItemProofRelationDo itemProofRelation) {
        if (Objects.nonNull(itemProofRelation)) {
            itemProofRelationRepository.save(itemProofRelation);
        }
    }

    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public void processParentItemCopy(Map<String, ItemProofRelationDo> streetItemCodeAndRelationMap, String streetItemCode, ItemProofRelationDo parentItemProofRelation, ItemDo streetItemDo) {
        copyProofListAndRelationToOtherProofList(parentItemProofRelation, streetItemDo, ItemProofStatusEnum.APPROVED);
        logChangeLogInSystemSync(streetItemDo, "同步结果");
        logOperationLogByStreetItemCodeInSystemSync(streetItemCode, ItemProofStatusEnum.APPROVED, "系统自动同步父事项的清理状态", "同步结果");
        ItemProofRelationDo streetItemProofRelation = streetItemCodeAndRelationMap.get(streetItemCode);
        streetItemProofRelation.setItemProofStatus(ItemProofStatusEnum.APPROVED);
        saveItemProofRelation(streetItemProofRelation);
    }

    public void logChangeLogInSystemSync(ItemDo itemDo, String operationName) {
        String itemId = Optional.ofNullable(itemDo).isPresent() ? itemDo.getId() : null;
        ItemChangeLogDo itemChangeLogDo = ItemChangeLogFactory.buildChangeLog(operationName, itemId, ItemChangeLogFactory.SYSTEM_SYNC_OPERATION_STR, ItemChangeLogFactory.SYSTEM_SYNC_OPERATION_STR);
        itemChangeLogDoRepository.save(itemChangeLogDo);
    }

    /**
     * copy 源事项的清理数据到目标事项中
     *
     * @param sourceItemProofRelation sourceItemProofRelation
     * @param targetItemDo            targetItemDo
     * @param itemProofStatus         itemProofStatus
     */
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public void copyProofListAndRelationToOtherProofList(ItemProofRelationDo sourceItemProofRelation, ItemDo targetItemDo, ItemProofStatusEnum itemProofStatus) {
        if (Objects.isNull(sourceItemProofRelation)) {
            LOGGER.error("copyProofListAndRelationToOtherProofList 源事项办件单不存在 【{}】", sourceItemProofRelation);
            throw new RuntimeException("源事项办件单不存在 【" + sourceItemProofRelation + "】");
        }
        if (Objects.isNull(targetItemDo)) {
            LOGGER.error("copyProofListAndRelationToOtherProofList 目标事项不存在 【{}】", targetItemDo);
            throw new RuntimeException("目标事项不存在 【" + targetItemDo + "】");
        }
        // 先删除所有目标事项的所有清理的数据
        // 此处，一个事项不会超过1000个材料。所以可以直接in

        List<ProofListDo> sourceProofList = proofListDoRepository.findAllByItemCode(sourceItemProofRelation.getItemCode());
        Map<String, ProofListDo> sourceProofNameAndProofListMap = sourceProofList.stream().collect(Collectors.toMap(ProofListDo::getProofName, v -> v, (before, after) -> after));
        Set<String> sourceProofListId = sourceProofList.stream().map(BaseDo::getId).collect(Collectors.toSet());
        List<ReplaceClerkCommitmentDo> sourceReplaceClerkCommit = replaceClerkCommitmentRepository.findByProofListIdIn(sourceProofListId);
        Map<String, List<ReplaceClerkCommitmentDo>> sourceProofListIdAndReplaceClerkMap = sourceReplaceClerkCommit.stream().collect(Collectors.toMap(ReplaceClerkCommitmentDo::getProofListId, Lists::newArrayList, (before, after) -> {
            before.addAll(after);
            return before;
        }));
        List<ReplaceDataSharedDo> sourceReplaceDataShared = replaceDataSharedRepository.findByProofListIdIn(sourceProofListId);
        Map<String, List<ReplaceDataSharedDo>> sourceProofListIdAndReplaceDataMap = sourceReplaceDataShared.stream().collect(Collectors.toMap(ReplaceDataSharedDo::getProofListId, Lists::newArrayList, (before, after) -> {
            before.addAll(after);
            return before;
        }));
        List<ReplaceDeptSurveyDo> sourceReplaceDeptSurvey = replaceDeptSurveyRepository.findByProofListIdIn(sourceProofListId);
        Map<String, List<ReplaceDeptSurveyDo>> sourceProofListIdAndReplaceDeptMap = sourceReplaceDeptSurvey.stream().collect(Collectors.toMap(ReplaceDeptSurveyDo::getProofListId, Lists::newArrayList, (before, after) -> {
            before.addAll(after);
            return before;
        }));
        List<ReplaceInvestigationDo> sourceReplaceInvestigation = replaceInvestigationRepository.findByProofListIdIn(sourceProofListId);
        Map<String, List<ReplaceInvestigationDo>> sourceProofListIdAndInvestigationMap = sourceReplaceInvestigation.stream().collect(Collectors.toMap(ReplaceInvestigationDo::getProofListId, Lists::newArrayList, (before, after) -> {
            before.addAll(after);
            return before;
        }));
        List<ReplaceLicenseDo> sourceReplaceLicense = replaceLicenseRepository.findByProofListIdIn(sourceProofListId);
        Map<String, List<ReplaceLicenseDo>> sourceProofListIdAndLicenseMap = sourceReplaceLicense.stream().collect(Collectors.toMap(ReplaceLicenseDo::getProofListId, Lists::newArrayList, (before, after) -> {
            before.addAll(after);
            return before;
        }));
        List<ReplaceOtherDo> sourceReplaceOtherDo = replaceOtherRepository.findByProofListIdIn(sourceProofListId);
        Map<String, List<ReplaceOtherDo>> sourceProofListIdAndOtherMap = sourceReplaceOtherDo.stream().collect(Collectors.toMap(ReplaceOtherDo::getProofListId, Lists::newArrayList, (before, after) -> {
            before.addAll(after);
            return before;
        }));

        List<ProofListDo> targetProofList = proofListDoRepository.findAllByItemCode(targetItemDo.getItemCode());
        Set<String> targetProofListIdSet = targetProofList.stream().map(BaseDo::getId).collect(Collectors.toSet());
        if (!org.springframework.util.CollectionUtils.isEmpty(targetProofListIdSet)) {
            proofListDoRepository.deleteAllByItemCode(targetItemDo.getItemCode());
            replaceClerkCommitmentRepository.deleteAllByProofListIdIn(targetProofListIdSet);
            replaceDataSharedRepository.deleteAllByProofListIdIn(targetProofListIdSet);
            replaceDeptSurveyRepository.deleteAllByProofListIdIn(targetProofListIdSet);
            replaceInvestigationRepository.deleteAllByProofListIdIn(targetProofListIdSet);
            replaceLicenseRepository.deleteAllByProofListIdIn(targetProofListIdSet);
            replaceOtherRepository.deleteAllByProofListIdIn(targetProofListIdSet);

        }

        List<ItemMaterialDo> targetItemMaterialList = itemMaterialRepository.findAllByItemCode(targetItemDo.getItemCode());
        Map<String, ItemMaterialDo> targetMaterialNameAndMaterial = targetItemMaterialList.stream().collect(Collectors.toMap(ItemMaterialDo::getMaterialName, v -> v, (before, after) -> after));

        // 重新构造proofList,并且记录审核日志
        List<ProofListDo> targetSaveProofListDoList = buildTargetProofList(targetItemDo, targetItemMaterialList, sourceProofNameAndProofListMap, itemProofStatus);
        Map<String, ProofListDo> targetProofNameAndProofListMap = targetSaveProofListDoList.stream().collect(Collectors.toMap(ProofListDo::getProofName, v -> v, (before, after) -> after));

        for (Map.Entry<String, ProofListDo> entry : targetProofNameAndProofListMap.entrySet()) {
            String targetMaterialName = entry.getKey();
            ProofListDo targetProofListDo = entry.getValue();
            // get出来不为空，代表着，目标的材料名称和来源的材料名称一致，可以进行复制。
            ProofListDo sourceProofListDo = sourceProofNameAndProofListMap.get(targetMaterialName);
            if (Objects.nonNull(sourceProofListDo)) {
                String proofListId = sourceProofListDo.getId();
                List<ReplaceClerkCommitmentDo> replaceClerkCommitmentDos = sourceProofListIdAndReplaceClerkMap.get(proofListId);
                List<ReplaceDataSharedDo> replaceDataSharedDos = sourceProofListIdAndReplaceDataMap.get(proofListId);
                List<ReplaceDeptSurveyDo> replaceDeptSurveyDos = sourceProofListIdAndReplaceDeptMap.get(proofListId);
                List<ReplaceInvestigationDo> replaceInvestigationDos = sourceProofListIdAndInvestigationMap.get(proofListId);
                List<ReplaceLicenseDo> replaceLicenseDos = sourceProofListIdAndLicenseMap.get(proofListId);
                List<ReplaceOtherDo> replaceOtherDos = sourceProofListIdAndOtherMap.get(proofListId);
                if (!org.springframework.util.CollectionUtils.isEmpty(replaceClerkCommitmentDos)) {
                    replaceClerkCommitmentDos = BeanCopyUtils.copyList(replaceClerkCommitmentDos, ReplaceClerkCommitmentDo.class);
                    if (!org.springframework.util.CollectionUtils.isEmpty(replaceClerkCommitmentDos)) {
                        replaceClerkCommitmentDos.forEach(item -> {
                            item.setProofListId(targetProofListDo.getId());
                            item.setId(UUID.randomUUID().toString());
                        });
                        replaceClerkCommitmentRepository.saveAll(replaceClerkCommitmentDos);
                    }
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(replaceDataSharedDos)) {
                    replaceDataSharedDos = BeanCopyUtils.copyList(replaceDataSharedDos, ReplaceDataSharedDo.class);
                    if (!org.springframework.util.CollectionUtils.isEmpty(replaceDataSharedDos)) {
                        replaceDataSharedDos.forEach(item -> {
                            item.setProofListId(targetProofListDo.getId());
                            item.setId(UUID.randomUUID().toString());
                        });
                        replaceDataSharedRepository.saveAll(replaceDataSharedDos);
                    }
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(replaceDeptSurveyDos)) {
                    replaceDeptSurveyDos = BeanCopyUtils.copyList(replaceDeptSurveyDos, ReplaceDeptSurveyDo.class);
                    if (!org.springframework.util.CollectionUtils.isEmpty(replaceDeptSurveyDos)) {
                        replaceDeptSurveyDos.forEach(item -> {
                            item.setProofListId(targetProofListDo.getId());
                            item.setId(UUID.randomUUID().toString());
                        });
                        replaceDeptSurveyRepository.saveAll(replaceDeptSurveyDos);
                    }
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(replaceInvestigationDos)) {
                    replaceInvestigationDos = BeanCopyUtils.copyList(replaceInvestigationDos, ReplaceInvestigationDo.class);
                    if (!org.springframework.util.CollectionUtils.isEmpty(replaceInvestigationDos)) {
                        replaceInvestigationDos.forEach(item -> {
                            item.setProofListId(targetProofListDo.getId());
                            item.setId(UUID.randomUUID().toString());
                        });
                        replaceInvestigationRepository.saveAll(replaceInvestigationDos);
                    }
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(replaceLicenseDos)) {
                    replaceLicenseDos = BeanCopyUtils.copyList(replaceLicenseDos, ReplaceLicenseDo.class);
                    if (!org.springframework.util.CollectionUtils.isEmpty(replaceLicenseDos)) {
                        replaceLicenseDos.forEach(item -> {
                            item.setProofListId(targetProofListDo.getId());
                            item.setId(UUID.randomUUID().toString());
                        });
                        replaceLicenseRepository.saveAll(replaceLicenseDos);
                    }
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(replaceOtherDos)) {
                    replaceOtherDos = BeanCopyUtils.copyList(replaceOtherDos, ReplaceOtherDo.class);
                    if (!org.springframework.util.CollectionUtils.isEmpty(replaceOtherDos)) {
                        replaceOtherDos.forEach(item -> {
                            item.setProofListId(targetProofListDo.getId());
                            item.setId(UUID.randomUUID().toString());
                        });
                        replaceOtherRepository.saveAll(replaceOtherDos);
                    }
                }
            }
        }
        proofListDoRepository.saveAll(targetSaveProofListDoList);
    }


    /**
     * 数据整合将中间表数据同步至本地
     *
     * @param middleItemDoList         中间表事项集合
     * @param middleItemMaterialDoList 中间表材料集合
     */
    public List<SynchronizeDataContext> getSyncDataContextByMiddleItemAndMaterial(List<MiddleItemDo> middleItemDoList,
                                                                                  List<MiddleItemMaterialDo> middleItemMaterialDoList) {
        try {
            //初始化上下文对象
            List<SynchronizeDataContext> synchronizeDataContextList = convertSynchronizeDataContext
                    (middleItemDoList, middleItemMaterialDoList);
            //构建同步上下文对象集合
            long startTime = System.currentTimeMillis();
            buildSynchronizeContext(synchronizeDataContextList);
            LOGGER.debug("buildSynchronizeContext size [{}] query time [{}]", synchronizeDataContextList.size(),
                    System.currentTimeMillis() - startTime);
            //上下文对象数据处理,同步事项及关联表
            startTime = System.currentTimeMillis();
            // 处理逻辑
            synchronizeDataContextList.stream()
                    .filter(item -> Objects.nonNull(item.getState()))
                    .forEach(item -> item.getState().handle(item));
            LOGGER.debug("handleSynchronizeDataContext handle time [{}]", System.currentTimeMillis() - startTime);
            //数据更新
            startTime = System.currentTimeMillis();

            LOGGER.debug("saveSynchronizeDataContext save time [{}]", System.currentTimeMillis() - startTime);
            return synchronizeDataContextList;
        } catch (Exception e) {
            LOGGER.error("synchronousConsolidationOfData thread run error", e);
        }
        return Lists.newArrayList();
    }

    /**
     * 构造目标ProofList
     *
     * @param targetItemDo                   targetItemDo
     * @param targetItemMaterialList         targetItemMaterialList
     * @param sourceProofNameAndProofListMap 来源proofListMap
     * @param itemProofStatus                清理状态
     * @return List<ProofListDo>
     */
    private List<ProofListDo> buildTargetProofList(ItemDo targetItemDo, List<ItemMaterialDo> targetItemMaterialList, Map<String, ProofListDo> sourceProofNameAndProofListMap, ItemProofStatusEnum itemProofStatus) {
        List<ProofListDo> result = Lists.newArrayList();
        targetItemMaterialList.forEach(itemMaterialDo -> {
            ProofListDo sourceProofList = sourceProofNameAndProofListMap.get(itemMaterialDo.getMaterialName());
            ProofListDo proofListDo = new ProofListDo();
            proofListDo.setItemCode(targetItemDo.getItemCode());
            proofListDo.setMaterialId(itemMaterialDo.getId());
            proofListDo.setProofName(itemMaterialDo.getMaterialName());
            proofListDo.setItemMaterialSource(sourceProofList.getItemMaterialSource());
            proofListDo.setItemProofStatus(itemProofStatus);
            proofListDo.setProofClearType(sourceProofList.getProofClearType());
            proofListDo.setProofCatalogId(sourceProofList.getProofCatalogId());
            proofListDo.setDirectDescription(sourceProofList.getDirectDescription());
            proofListDo.setReplaceCancelWay(sourceProofList.getReplaceCancelWay());
            proofListDo.setNotCleanDescription(sourceProofList.getNotCleanDescription());
            proofListDo.setId(UUID.randomUUID().toString());
            proofListDo.setCreatorId("系统自动同步");
            proofListDo.setCreationTime(new Date());
            proofListDo.setLastModificatorId("系统自动同步");
            proofListDo.setLastModificationTime(new Date());
            result.add(proofListDo);
        });
        return result;
    }

    /**
     * 更新保存事项及关联表数据
     *
     * @param context 同步上下文对象
     */
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public void saveSynchronizeDataContext(List<SynchronizeDataContext> context) {
        //将历史同步失败的记录改为同步无效
        updateFailMiddleItemDo(context);
        //更新事项中间表
        List<MiddleItemDo> middleItemDos = context.stream()
                .map(SynchronizeDataContext::getMiddleItemDo).collect(Collectors.toList());
        middleItemRepository.saveAll(middleItemDos);

        List<SynchronizeDataContext> updateContext = context.stream()
                .filter(index -> SynchronizeStatusEnum.SYNCHRONIZE_SUCCESS == index.getMiddleItemDo()
                        .getSynchronizeStatus()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateContext)) {
            return;
        }
        //保存事项
        List<ItemDo> itemDos = updateContext.stream().map(SynchronizeDataContext::getItemDo)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itemDos)) {
            itemRepository.saveAll(itemDos);
            // 对事项保存或者更新后得itemChangeLog得itemId进行fu'zhi
        }

        //保存材料
        List<List<ItemMaterialDo>> itemMaterialDos = updateContext.stream()
                .map(SynchronizeDataContext::getItemMaterialDoList)
                .filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty).collect(Collectors.toList());
        itemMaterialDos.forEach(index -> itemMaterialRepository.saveAll(index));

        //保存事项材料更变日志表
        List<List<ItemChangeLogDo>> itemChangeLogDos = updateContext.stream()
                .map(SynchronizeDataContext::getItemChangeLogDoList)
                .filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty)
                .collect(Collectors.toList());
        itemChangeLogDos.forEach(index -> itemChangeLogDoRepository.saveAll(index));

        //保存事项办事清单
        List<ItemProofRelationDo> itemProofRelations = updateContext.stream()
                .map(SynchronizeDataContext::getItemProofRelation)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(itemProofRelations)) {
            itemProofRelationRepository.saveAll(itemProofRelations);
        }

        //保存更新的清单
        List<List<ProofListDo>> proofListDos = updateContext.stream()
                .map(SynchronizeDataContext::getProofListDoList)
                .filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty)
                .collect(Collectors.toList());
        proofListDos.forEach(index -> proofListDoRepository.saveAll(index));

        //保存事项证明清单过程信息表
        List<List<ProofListProcedureDo>> proofListProcedureDos = updateContext.stream()
                .map(SynchronizeDataContext::getProofListProcedureDoList)
                .filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty)
                .collect(Collectors.toList());
        proofListProcedureDos.forEach(index -> procedureLogRepository.saveAll(index));

        // 触发记录事项与材料更变日志记录事件。以每一个事项为单位
        context.stream()
                .filter(item -> Objects.nonNull(item.getItemProofRelation()))
                .filter(item -> Objects.nonNull(item.getItemProofRelation().getItemProofStatus()))
                .forEach(item -> {
                    if (ItemProofStatusEnum.WAIT_FOR_CLEAN == item.getItemProofRelation().getItemProofStatus()) {
                        itemBizEventPublisherManager.productEvent(new CreateItemStatusChangeEvent("", item.getItemDo(), item.getItemProofRelation(), item.getItemMaterialDoList()));
                    }
                    if (ItemProofStatusEnum.TRANSFERRED == item.getItemProofRelation().getItemProofStatus()) {
                        itemBizEventPublisherManager.productEvent(new ProofListStatusChangeEvent("", item.getItemDo(), item.getItemProofRelation(), item.getProofListDoList()));
                    }
                });
    }

    @Override
    public void itemMaterialCompareHandle(List<String> itemCodeList) {
        List<ItemDo> allByItemStatusInAndItemClearStatusIn = itemRepository.findAllByItemStatusInAndItemClearStatusIn(Lists.newArrayList(ItemStatus.WORK), Lists.newArrayList(ItemProofStatusEnum.APPROVED));
        if (CollectionUtils.isEmpty(itemCodeList)) {
            if (CollectionUtils.isEmpty(allByItemStatusInAndItemClearStatusIn)) {
                return;
            }
            itemCodeList = allByItemStatusInAndItemClearStatusIn.stream().map(ItemDo::getItemCode).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        } else {
            List<String> finalItemCodeList = itemCodeList;
            itemCodeList = allByItemStatusInAndItemClearStatusIn.stream().map(ItemDo::getItemCode).filter(StringUtils::isNotBlank).filter(finalItemCodeList::contains).collect(Collectors.toList());
        }
        itemCodeList.forEach(itemCode -> {
            // 1. 调用事项系统，拉取最新版本数据到中间表
            try {
                itemRecordCompareDetailUpdate(itemCode);
            } catch (Exception e) {
                LOGGER.warn("itemMaterialCompareHandle error,skip item [{}]", itemCode, e);
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void itemRecordCompareDetailUpdate(String itemCode) {
        DictPublicService dictPublicService = BeanFactoryUtils.getBean(DictPublicService.class);
        Optional<String> itemSystemBizSelectOptional = dictPublicService.getDetailValue(DictionaryTypeItemConstant.ITEM_SYSTEM_BIZ_SELECT.getDictType().name(), DictionaryTypeItemConstant.ITEM_SYSTEM_BIZ_SELECT.name());

        // 1. 调用事项系统，拉取最新版本数据到中间表
        String itemSystem = itemSystemBizSelectOptional.orElse(ItemSystemSyncFactory.ItemSystemSyncEmuns.DEFAULT.name()).trim().toUpperCase();
        ItemSystemSyncFactory.ItemSystemSyncEmuns itemSystemSyncEmuns = ItemSystemSyncFactory.ItemSystemSyncEmuns.valueOf(itemSystem);
        ItemSystemSyncInterface itemSystem1 = ItemSystemSyncFactory.getItemSystem(itemSystemSyncEmuns);
        itemSystem1.syncItemSystemByItemCode(itemCode);

        ItemDo byItemCode = itemRepository.findByItemCode(itemCode);
        gov.license.common.api.utils.Assert.notNull(byItemCode, ItemResponseCode.ITEM_QUERY_NOT_EXISTS_ERROR.format("事项查询不存在"));
        gov.license.common.api.utils.Assert.isTrue(ItemStatus.WORK.equals(byItemCode.getItemStatus()), ItemResponseCode.ITEM_QUERY_NOT_EXISTS_ERROR.format("事项非在用情况"));
        List<ProofListDo> allByItemCode = proofListRepository.findAllByItemCode(itemCode);
        gov.license.common.api.utils.Assert.notEmpty(allByItemCode, ItemResponseCode.ITEM_QUERY_NOT_EXISTS_ERROR.format("事项清理清单查询无法找到数据"));
        List<ProofListDo> allAuditedList = allByItemCode.stream().filter(item -> ItemProofStatusEnum.APPROVED.equals(item.getItemProofStatus())).collect(Collectors.toList());
        gov.license.common.api.utils.Assert.notEmpty(allAuditedList, ItemResponseCode.ITEM_QUERY_NOT_EXISTS_ERROR.format("事项清理清单查询无法找到审核完成数据"));
        List<MiddleItemDo> allByTaskCode = middleItemRepository.findAllByTaskCode(itemCode);
        gov.license.common.api.utils.Assert.notEmpty(allByTaskCode, ItemResponseCode.ITEM_QUERY_NOT_EXISTS_ERROR.format("中间表查询异常"));
        Optional<MiddleItemDo> maxVersionMiddleItemOptional = allByTaskCode.stream().max(Comparator.comparing(MiddleItemDo::getTaskVersionByInt));
        MiddleItemDo maxVersionMiddleItem = maxVersionMiddleItemOptional.orElseThrow(() -> new ItemServiceException(ItemResponseCode.ITEM_QUERY_NOT_EXISTS_ERROR.format("中间表缺失最新版本数据")));
        List<MiddleItemMaterialDo> maxVersionMiddleMaterialList = middleItemMaterialRepository.findAllByItemGuid(maxVersionMiddleItem.getRowGuid());
        gov.license.common.api.utils.Assert.notEmpty(maxVersionMiddleMaterialList, ItemResponseCode.ITEM_QUERY_NOT_EXISTS_ERROR.format("中间材料表查询异常"));
        // 构造重新比对记录，当前清理数据与最新中间表材料数据进行比对，保存最新比对结果
        ItemMaterialCompareBuilder builder = new ItemMaterialCompareBuilder(itemSystem1, byItemCode, allAuditedList, maxVersionMiddleItem, maxVersionMiddleMaterialList);
        List<ItemMaterialCompareDo> newCompareResultList = builder.buildItemMaterialCompareDo();
        // 2. 触发事项材料比对逻辑，将最新的事项材料比对结果保存到记录中。旧记录删除
        itemMaterialCompareRepository.deleteAllByItemCode(itemCode);
        itemMaterialCompareRepository.saveAll(newCompareResultList);
    }

    /**
     * 将历史同步失败的记录改为同步无效
     *
     * @param synchronizeDataContexts 同步上下文数据对象
     */
    private void updateFailMiddleItemDo(List<SynchronizeDataContext> synchronizeDataContexts) {
        if (CollectionUtils.isEmpty(synchronizeDataContexts)) {
            return;
        }
        //获取更新的事项中间表数据
        List<MiddleItemDo> middleItemDos = synchronizeDataContexts.stream().filter(SynchronizeDataContext::isUpdate)
                .map(SynchronizeDataContext::getMiddleItemDo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(middleItemDos)) {
            return;
        }
        //根据事项编码和同步失败状态获取事项中间表
        List<String> itemIds = middleItemDos.stream().map(MiddleItemDo::getItemId).distinct()
                .collect(Collectors.toList());
        List<List<String>> itemIdList = Lists.partition(itemIds, 1000);

        PredicateBuilder<MiddleItemDo> middleItemDoPredicateBuilder = Specifications.or();
        itemIdList.forEach(index -> middleItemDoPredicateBuilder.in("itemId", index));

        Specification<MiddleItemDo> middleItemDoSpecification = Specifications.<MiddleItemDo>and()
                .eq("synchronizeStatus", SynchronizeStatusEnum.SYNCHRONIZE_FAILED)
                .predicate(middleItemDoPredicateBuilder.build()).build();

        List<MiddleItemDo> failMiddleItemDoList = middleItemRepository.findAll(middleItemDoSpecification);
        if (CollectionUtils.isEmpty(failMiddleItemDoList)) {
            return;
        }
        failMiddleItemDoList.forEach(index -> index.setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_INVALID));
        middleItemRepository.saveAll(failMiddleItemDoList);
    }


    /**
     * 构建需要同步的上下文
     *
     * @param synchronizeDataContexts 需要同步的上下文
     */
    private void buildSynchronizeContext(List<SynchronizeDataContext> synchronizeDataContexts) {
        if (CollectionUtils.isEmpty(synchronizeDataContexts)) {
            return;
        }
        List<MiddleItemDo> middleItemDos = synchronizeDataContexts.stream().map(SynchronizeDataContext::getMiddleItemDo)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(middleItemDos)) {
            return;
        }
        //根据事项id查询已存在的事项
        long startTime = System.currentTimeMillis();
        List<String> itemIds = middleItemDos.stream().map(MiddleItemDo::getProofItemCode).collect(Collectors.toList());
        List<ItemDo> itemDoList = itemRepository.findBySyncItemIdIn(itemIds);
        LOGGER.debug("buildSynchronizeContext itemDoList size [{}] time [{}]", itemDoList.size(),
                System.currentTimeMillis() - startTime);
        //事项 key->事项唯一标识
        Map<String, ItemDo> itemDoMap = itemDoList.stream()
                .collect(Collectors.toMap(this::getItemDoKey, Function.identity(), (before, after) -> after));

        //事项材料  key->事项编码
        Map<String, List<ItemMaterialDo>> itemMaterialDoMap = Maps.newHashMap();
        //事项办事清单 key->事项编码
        Map<String, ItemProofRelationDo> itemProofRelationMap = Maps.newHashMap();
        startTime = System.currentTimeMillis();
        if (!itemDoMap.isEmpty()) {
            List<ItemMaterialDo> itemMaterialDoList = itemMaterialRepository
                    .findAllByItemCodeIn(Lists.newArrayList(itemDoMap.keySet()));
            if (CollectionUtils.isNotEmpty(itemMaterialDoList)) {
                itemMaterialDoMap.putAll(itemMaterialDoList.stream()
                        .collect(Collectors.groupingBy(ItemMaterialDo::getItemCode)));
            }
            LOGGER.debug("buildSynchronizeContext itemMaterialDoList size [{}] time [{}]", itemMaterialDoList.size(),
                    System.currentTimeMillis() - startTime);

            startTime = System.currentTimeMillis();
            List<ItemProofRelationDo> itemProofRelationList = itemProofRelationRepository
                    .findAllByItemCodeIn(Lists.newArrayList(itemDoMap.keySet()));

            if (CollectionUtils.isNotEmpty(itemProofRelationList)) {
                itemProofRelationMap.putAll(itemProofRelationList.stream()
                        .collect(Collectors.toMap(ItemProofRelationDo::getItemCode, Function.identity(), (before, after) -> after)));
            }
            LOGGER.debug("buildSynchronizeContext itemProofRelationList size [{}] time [{}]", itemProofRelationList.size(),
                    System.currentTimeMillis() - startTime);
        }

        //处理上下文对象
        for (SynchronizeDataContext synchronizeDataContext : synchronizeDataContexts) {
            MiddleItemDo middleItemDo = synchronizeDataContext.getMiddleItemDo();
            String key = synchronizeDataContext.getMiddleItemDoItemCode(middleItemDo);
            if (itemDoMap.containsKey(key)) {
                ItemDo itemDo = itemDoMap.get(key);
                synchronizeDataContext.setItemDo(itemDo);
                if (CollectionUtils.isNotEmpty(itemMaterialDoMap.get(key))) {
                    synchronizeDataContext.setItemMaterialDoList(itemMaterialDoMap.get(key));
                }
                synchronizeDataContext.setItemProofRelation(itemProofRelationMap.get(key));
            }
            synchronizeDataContext.initState();
        }
    }

    /**
     * 事项中间表转换同步数据上下文对象集合
     *
     * @param middleItemDoList         事项中间表对象集合
     * @param middleItemMaterialDoList 材料中间表对象集合
     * @return 同步数据上下文对象集合
     */
    private List<SynchronizeDataContext> convertSynchronizeDataContext(List<MiddleItemDo> middleItemDoList,
                                                                       List<MiddleItemMaterialDo> middleItemMaterialDoList) {
        //获取事项关联的材料
        List<String> rowGuids = middleItemDoList.stream()
                .map(MiddleItemDo::getRowGuid)
                .collect(Collectors.toList());
        List<MiddleItemMaterialDo> middleItemMaterialDos = middleItemMaterialDoList.stream()
                .filter(middleItemMaterialDo -> rowGuids.contains(middleItemMaterialDo.getItemGuid()))
                .collect(Collectors.toList());
        List<SynchronizeDataContext> synchronizeDataContexts = Lists.newArrayList();
        Map<String, List<MiddleItemMaterialDo>> middleItemMaterialDoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(middleItemMaterialDos)) {
            middleItemMaterialDoMap = middleItemMaterialDoList.stream()
                    .collect(Collectors.groupingBy(MiddleItemMaterialDo::getItemGuid));
        }
        for (MiddleItemDo middleItemDo : middleItemDoList) {
            SynchronizeDataContext synchronizeDataContext = new SynchronizeDataContext();
            synchronizeDataContext.setMiddleItemDo(middleItemDo);
            if (middleItemMaterialDoMap.containsKey(middleItemDo.getRowGuid())) {
                synchronizeDataContext.setMiddleItemMaterialDoList(middleItemMaterialDoMap.get(middleItemDo.getRowGuid()));
            }
            synchronizeDataContexts.add(synchronizeDataContext);
        }
        return synchronizeDataContexts;
    }

    /**
     * 获取事项唯一标识
     *
     * @param itemDo 事项对象
     * @return 唯一标识
     */
    public String getItemDoKey(ItemDo itemDo) {
        if (Objects.isNull(itemDo)) {
            throw new IllegalArgumentException("getItemDoKey fail -> itemDo is null ");
        }
        return itemDo.getItemCode();
    }


    /**
     * 同步事项以及下面的材料到无证明城市系统中
     *
     * @param context 上下文数据
     */
    @Override
    public void synchronizeItemAndMaterial(SynchronizeDataContext context) {
        //更新事项
        MiddleItemDo middleItemDo = context.getMiddleItemDo();
        ItemDo itemDo = new ItemDo();
        buildItemDo(itemDo, middleItemDo);
        if (Objects.nonNull(context.getItemDo())) {
            itemDo.setId(context.getItemDo().getId());
        }
        //校验事项是否更新
        List<ItemChangeLogDo> itemChangeLogDoList = generateItemChangeLog(context.getItemDo(), itemDo);
        context.setItemDo(itemDo);

        //更新材料
        Map<String, ItemMaterialDo> inDbItemMaterialDoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(context.getItemMaterialDoList())) {
            inDbItemMaterialDoMap.putAll(context.getItemMaterialDoList().stream()
                    .collect(Collectors.toMap(ItemMaterialDo::getMaterialName, Function.identity(), (before, after) -> after)));
        }

        List<ItemMaterialDo> newBuildItemMaterialDoListUpdate = Lists.newArrayList();
        DictPublicService dictPublicService = BeanFactoryUtils.getBean(DictPublicService.class);
        Optional<String> flagOptional = dictPublicService.getDetailValue(DictionaryTypeItemConstant.SYNC_ITEM_USE_MATERIAL_BIZ_ID.getDictType().getNames(), DictionaryTypeItemConstant.SYNC_ITEM_USE_MATERIAL_BIZ_ID.name());
        // 转换中间材料表到材料表中。
        for (MiddleItemMaterialDo middleItemMaterialDo : context.getMiddleItemMaterialDoList()) {
            ItemMaterialDo itemMaterialDo = new ItemMaterialDo();
            itemMaterialDo.setId(UUID.randomUUID().toString());
            // 判断字典，是否存在配置，若true取material_biz_id，false则自己生成id，默认false
            flagOptional.ifPresent(s -> {
                boolean isUseMaterialBizId = Boolean.parseBoolean(s);
                if (isUseMaterialBizId) {
                    itemMaterialDo.setId(middleItemMaterialDo.getMaterialId());
                }
            });
            itemMaterialDo.setItemCode(itemDo.getItemCode());
            buildMaterial(itemMaterialDo, middleItemMaterialDo);
            itemMaterialDo.setItemMaterialSource(ItemMaterialSourceEnum.STANDARD_MATERIAL);
            itemMaterialDo.setItemClearStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);
            if (inDbItemMaterialDoMap.containsKey(middleItemMaterialDo.getMaterialName())) {
                // 存在库中。设置id，更新材料
                itemMaterialDo.setId(inDbItemMaterialDoMap.get(middleItemMaterialDo.getMaterialName()).getId());
                itemMaterialDo.setItemClearStatus(inDbItemMaterialDoMap.get(middleItemMaterialDo.getMaterialName())
                        .getItemClearStatus());
                generateMaterialChangeLog(itemChangeLogDoList,
                        inDbItemMaterialDoMap.get(middleItemMaterialDo.getMaterialName()), itemMaterialDo, itemDo.getId());
            }
            newBuildItemMaterialDoListUpdate.add(itemMaterialDo);
        }
        // 判断存库的材料，是否存在删除材料的情况。
        Map<String, ItemMaterialDo> newMaterialNameAndItemMaterialMap = newBuildItemMaterialDoListUpdate.stream().collect(Collectors.toMap(ItemMaterialDo::getMaterialName, v -> v, (before, after) -> after));
        Set<String> materialNameSet = newMaterialNameAndItemMaterialMap.keySet();

        Map<String, ItemMaterialDo> inDbMaterialNameAndMaterialMap = inDbItemMaterialDoMap.values().stream().collect(Collectors.toMap(ItemMaterialDo::getMaterialName, v -> v, (before, after) -> after));
        Set<String> inDbMaterialNameSet = inDbMaterialNameAndMaterialMap.keySet();
        // 处理已经删除的材料
        // 材料表差集差集名称
        HashSet<String> differenceMaterialIdSet = Sets.newHashSet();
        differenceMaterialIdSet.addAll(inDbMaterialNameSet);
        differenceMaterialIdSet.removeAll(materialNameSet);
        inDbItemMaterialDoMap.values().stream()
                .filter(item -> differenceMaterialIdSet.contains(item.getMaterialName()))
                .forEach(item -> {
                    item.setProofClearType(ProofClearTypeEnum.DIRECTLY_CANCEL);
                    item.setLogicCancel(true);
                    item.setLastModificationTime(new Date());
                    newBuildItemMaterialDoListUpdate.add(item);
                });
        context.setItemMaterialDoList(newBuildItemMaterialDoListUpdate);
        context.setItemChangeLogDoList(itemChangeLogDoList);
    }

    /**
     * 构建当前系统事项对象
     *
     * @param itemDo       需要同步的事项
     * @param middleItemDo 待同步的事项
     */
    @Override
    public void buildItemDo(ItemDo itemDo, MiddleItemDo middleItemDo) {
        itemDo.setSyncItemId(middleItemDo.getItemId());
        itemDo.setItemCode(StringUtils.isBlank(middleItemDo.getSituationCode())
                ? middleItemDo.getTaskCode() : middleItemDo.getSituationCode());
        itemDo.setItemName(middleItemDo.getTaskName());
        itemDo.setCreditCode(middleItemDo.getTongYiCode());
        itemDo.setImplOrgName(middleItemDo.getDeptName());
        itemDo.setDivisionCode(middleItemDo.getAreaCode());
        itemDo.setItemType(middleItemDo.getTaskType());
        itemDo.setItemSource(middleItemDo.getItemSource());
        itemDo.setItemStatus(middleItemDo.getTaskState());
        itemDo.setProjectType(middleItemDo.getProjectType());
        itemDo.setHandingItem(middleItemDo.getSituationName());
        itemDo.setItemVersion(middleItemDo.getTaskVersion());
        itemDo.setVersionDate(middleItemDo.getVersionDate());
        itemDo.setAcceptCondition(middleItemDo.getAcceptCondition());
        itemDo.setItemClearStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);
    }

    /**
     * 构建材料对象
     *
     * @param itemMaterialDo       需要同步的对象
     * @param middleItemMaterialDo 待同步的对象
     */
    @Override
    public void buildMaterial(ItemMaterialDo itemMaterialDo, MiddleItemMaterialDo middleItemMaterialDo) {
        itemMaterialDo.setMaterialName(middleItemMaterialDo.getMaterialName());
        itemMaterialDo.setMaterialType(middleItemMaterialDo.getMaterialType());
        itemMaterialDo.setLicenseCode(middleItemMaterialDo.getLicenseCode());
        itemMaterialDo.setLicenseName(middleItemMaterialDo.getLicenseName());
        itemMaterialDo.setMaterialSource(null);
        if (Objects.nonNull(middleItemMaterialDo.getSourceType())) {
            switch (middleItemMaterialDo.getSourceType().getCode()) {
                case "10":
                    itemMaterialDo.setMaterialSource("01");
                    break;
                case "20":
                    itemMaterialDo.setMaterialSource("02");
                    break;
                case "99":
                    itemMaterialDo.setMaterialSource("03");
                    break;
                default:
                    break;
            }
        }
        itemMaterialDo.setMaterialSourceRemark(middleItemMaterialDo.getSourceExplain());
        itemMaterialDo.setNotCommit(middleItemMaterialDo.isSubmissionRequired());
        itemMaterialDo.setMaterialBizId(middleItemMaterialDo.getMaterialId());
        itemMaterialDo.setOrderNum(middleItemMaterialDo.getOrderNum());
    }

    /**
     * 校验事项是否变更，变更则添加到日志中
     *
     * @param oldItemDo 旧的事项
     * @param newItemDo 新的事项
     * @return 变更记录
     */
    private List<ItemChangeLogDo> generateItemChangeLog(ItemDo oldItemDo, ItemDo newItemDo) {
        List<ItemChangeLogDo> itemChangeLogDoList = Lists.newArrayList();
        String addOperationName = "新增事项";
        String editOperationName = "编辑事项";
        String updateItemOperationName = "更新事项";
        String updateVersionOperationName = "更新版本";
        String updateVersionDateOperationName = "更新事项版本创建时间";
        String emptyStr = "";
        if (Objects.isNull(oldItemDo)) {
            String newItemId = newItemDo.getItemCode();
            ItemChangeLogDo itemChangeLogDo = buildItemChangeLogDo("事项编码：",
                    emptyStr, newItemDo.getItemCode(), newItemId, editOperationName);
            itemChangeLogDoList.add(itemChangeLogDo);
            itemChangeLogDoList.add(buildItemChangeLogDo("事项名称：", emptyStr, newItemDo.getItemName(), newItemId, addOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("实施机构的统一社会信用代码代码：", emptyStr, newItemDo.getCreditCode(), newItemId, addOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("实施机构：", emptyStr, emptyStr, newItemId, addOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("实施机构的行政区划代码：", emptyStr, newItemDo.getDivisionCode(), newItemId, addOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("事项类型：", emptyStr, Objects.isNull(newItemDo.getItemType()) ? emptyStr : newItemDo.getItemType().name(), newItemId, addOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("事项状态：", emptyStr, Objects.isNull(newItemDo.getItemStatus()) ? emptyStr : newItemDo.getItemStatus().name(), newItemId, updateItemOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("办件类型：", emptyStr, Objects.isNull(newItemDo.getProjectType()) ? emptyStr : newItemDo.getProjectType().name(), newItemId, updateItemOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("事项版本：", emptyStr, newItemDo.getItemVersion(), newItemId, updateVersionOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("事项版本创建时间：", emptyStr, DateUtil.format(newItemDo.getVersionDate()), newItemId, updateVersionDateOperationName));
        } else {
            if (!verifyString(oldItemDo.getItemCode()).equals(verifyString(newItemDo.getItemCode()))) {
                itemChangeLogDoList.add(buildItemChangeLogDo("事项编码：", oldItemDo.getItemCode(), newItemDo.getItemCode(), newItemDo.getId(), editOperationName));
            }
            if (!verifyString(oldItemDo.getItemName()).equals(verifyString(newItemDo.getItemName()))) {
                itemChangeLogDoList.add(buildItemChangeLogDo("事项名称：", oldItemDo.getItemName(), newItemDo.getItemName(), newItemDo.getId(), editOperationName));
            }
            if (!verifyString(oldItemDo.getCreditCode()).equals(verifyString(newItemDo.getCreditCode()))) {
                itemChangeLogDoList.add(buildItemChangeLogDo("实施机构的统一社会信用代码代码：", oldItemDo.getCreditCode(), newItemDo.getCreditCode(), newItemDo.getId(), editOperationName));
            }
            if (!verifyString(oldItemDo.getImplOrgName()).equals(verifyString(newItemDo.getImplOrgName()))) {
                itemChangeLogDoList.add(buildItemChangeLogDo("实施机构：", oldItemDo.getImplOrgName(), newItemDo.getImplOrgName(), newItemDo.getId(), editOperationName));
            }
            if (!verifyString(oldItemDo.getDivisionCode()).equals(verifyString(newItemDo.getDivisionCode()))) {
                itemChangeLogDoList.add(buildItemChangeLogDo("实施机构的行政区划代码：", oldItemDo.getDivisionCode(), newItemDo.getDivisionCode(), newItemDo.getId(), editOperationName));
            }
            if (!oldItemDo.getItemType().equals(newItemDo.getItemType())) {
                String changePrefixValue = Objects.isNull(oldItemDo.getItemType()) ? emptyStr : oldItemDo.getItemType().name();
                String changeAfterValue = Objects.isNull(newItemDo.getItemType()) ? emptyStr : newItemDo.getItemType().name();
                itemChangeLogDoList.add(buildItemChangeLogDo("事项类型：", changePrefixValue, changeAfterValue, newItemDo.getId(), editOperationName));
            }
            if (!oldItemDo.getItemStatus().equals(newItemDo.getItemStatus())) {
                String changePrefixValue = Objects.isNull(oldItemDo.getItemStatus()) ? emptyStr : oldItemDo.getItemType().name();
                String changeAfterValue = Objects.isNull(newItemDo.getItemStatus()) ? emptyStr : newItemDo.getItemType().name();
                itemChangeLogDoList.add(buildItemChangeLogDo("事项状态：", changePrefixValue, changeAfterValue, newItemDo.getId(), updateItemOperationName));
            }
            if (!oldItemDo.getProjectType().equals(newItemDo.getProjectType())) {
                String changePrefixValue = Objects.isNull(oldItemDo.getProjectType()) ? emptyStr : oldItemDo.getItemType().name();
                String changeAfterValue = Objects.isNull(newItemDo.getProjectType()) ? emptyStr : newItemDo.getItemType().name();
                itemChangeLogDoList.add(buildItemChangeLogDo("办件类型：", changePrefixValue, changeAfterValue, newItemDo.getId(), updateItemOperationName));
            }
            if (!StringUtils.equals(oldItemDo.getItemVersion(), newItemDo.getItemVersion())) {
                itemChangeLogDoList.add(buildItemChangeLogDo("事项版本：", oldItemDo.getItemVersion(), newItemDo.getItemVersion(), newItemDo.getId(), updateVersionOperationName));
            }
            if (oldItemDo.getVersionDate() != newItemDo.getVersionDate()) {
                itemChangeLogDoList.add(buildItemChangeLogDo("事项版本创建时间：", DateUtil.format(oldItemDo.getVersionDate())
                        , DateUtil.format(newItemDo.getVersionDate()), newItemDo.getId(), updateVersionDateOperationName));
            }
        }
        return itemChangeLogDoList;
    }

    /**
     * 构造更变日志
     *
     * @param changeStr         更变前缀
     * @param changePrefixValue 更变前得值
     * @param changeAfterValue  更变后得值
     * @param newItemId         事项id
     * @param operationName     操作名称
     * @return 更变日志对象
     */
    private ItemChangeLogDo buildItemChangeLogDo(String changeStr, String changePrefixValue, String changeAfterValue, String newItemId, String operationName) {
        return ItemChangeLogFactory.buildChangeLog(operationName, newItemId, changeStr + changePrefixValue, changeStr + changeAfterValue, ItemChangeLogFactory.SYSTEM_SYNC_OPERATION_STR, ItemChangeLogFactory.SYSTEM_SYNC_OPERATION_STR);
    }

    /**
     * 校验材料是否更新，更新则保存到更新日志中
     *
     * @param itemChangeLogDoList 更新日志
     * @param oldItemMaterialDo   旧的材料
     * @param newItemMaterialDo   新的材料
     * @param itemId              事项的id
     */
    private void generateMaterialChangeLog(List<ItemChangeLogDo> itemChangeLogDoList, ItemMaterialDo oldItemMaterialDo,
                                           ItemMaterialDo newItemMaterialDo, String itemId) {
        String emptyStr = "";
        String addItemOperationName = "新增事项";
        String editItemOperationName = "编辑事项";
        if (Objects.isNull(oldItemMaterialDo)) {
            itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-材料名称：", emptyStr, newItemMaterialDo.getMaterialName(), itemId, addItemOperationName));
            String changeAfterValue = Objects.isNull(newItemMaterialDo.getMaterialType()) ? emptyStr : newItemMaterialDo.getMaterialType().name();
            itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-材料类型：", emptyStr, changeAfterValue, itemId, addItemOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-关联的电子证照的目录编码：", emptyStr, newItemMaterialDo.getLicenseCode(), itemId, addItemOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-关联的电子证照的名称：", emptyStr, newItemMaterialDo.getLicenseName(), itemId, addItemOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-来源渠道：", emptyStr, newItemMaterialDo.getMaterialSource(), itemId, addItemOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-来源渠道说明：", emptyStr, newItemMaterialDo.getMaterialSourceRemark(), itemId, addItemOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-是否免提交：", emptyStr, String.valueOf(newItemMaterialDo.getNotCommit()), itemId, addItemOperationName));
            itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-材料排序：", emptyStr, String.valueOf(newItemMaterialDo.getOrderNum()), itemId, addItemOperationName));
        } else {
            if (!verifyString(oldItemMaterialDo.getMaterialName()).equals(verifyString(newItemMaterialDo.getMaterialName()))) {
                itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-材料名称：", oldItemMaterialDo.getMaterialName(), newItemMaterialDo.getMaterialName(), itemId, editItemOperationName));
            }
            if (!oldItemMaterialDo.getMaterialType().equals(newItemMaterialDo.getMaterialType())) {
                String changePrefixValue = Objects.isNull(oldItemMaterialDo.getMaterialType()) ? emptyStr : oldItemMaterialDo.getMaterialType().name();
                String changeAfterValue = Objects.isNull(newItemMaterialDo.getMaterialType()) ? emptyStr : newItemMaterialDo.getMaterialType().name();
                itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-材料类型：", changePrefixValue, changeAfterValue, itemId, editItemOperationName));
            }
            if (!verifyString(oldItemMaterialDo.getLicenseCode()).equals(verifyString(newItemMaterialDo.getLicenseCode()))) {
                itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-关联的电子证照的目录编码：", oldItemMaterialDo.getLicenseCode(), newItemMaterialDo.getLicenseCode(), itemId, editItemOperationName));
            }
            if (!verifyString(oldItemMaterialDo.getLicenseName()).equals(verifyString(newItemMaterialDo.getLicenseName()))) {
                itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-关联的电子证照的名称：", oldItemMaterialDo.getLicenseName(), newItemMaterialDo.getLicenseName(), itemId, editItemOperationName));
            }
            if (!verifyString(oldItemMaterialDo.getMaterialSource()).equals(verifyString(newItemMaterialDo.getMaterialSource()))) {
                itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-来源渠道：", oldItemMaterialDo.getMaterialSource(), newItemMaterialDo.getMaterialSource(), itemId, editItemOperationName));
            }
            if (!verifyString(oldItemMaterialDo.getMaterialSourceRemark()).equals(verifyString(newItemMaterialDo.getMaterialSourceRemark()))) {
                itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-来源渠道说明：", oldItemMaterialDo.getMaterialSourceRemark(), newItemMaterialDo.getMaterialSourceRemark(), itemId, editItemOperationName));
            }
            if (!oldItemMaterialDo.getNotCommit().equals(newItemMaterialDo.getNotCommit())) {
                itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-是否免提交：", String.valueOf(oldItemMaterialDo.getNotCommit()), String.valueOf(newItemMaterialDo.getNotCommit()), itemId, editItemOperationName));
            }
            if (!Objects.equals(oldItemMaterialDo.getOrderNum(), newItemMaterialDo.getOrderNum())) {
                itemChangeLogDoList.add(buildItemChangeLogDo("材料管理-材料排序：", String.valueOf(oldItemMaterialDo.getOrderNum()), String.valueOf(newItemMaterialDo.getOrderNum()), itemId, editItemOperationName));
            }
        }
    }


    /**
     * 校验中间表材料与本地材料是否完全相等
     *
     * @param context 同步数据上下文对象
     * @return true ->相等  false ->不相等
     */
    @Override
    public boolean checkItemMaterialEquals(SynchronizeDataContext context) {
        if (CollectionUtils.isEmpty(context.getMiddleItemMaterialDoList())
                && CollectionUtils.isNotEmpty(context.getItemMaterialDoList())) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(context.getMiddleItemMaterialDoList())
                && CollectionUtils.isEmpty(context.getItemMaterialDoList())) {
            return false;
        }
        Set<String> materialIds = context.getMiddleItemMaterialDoList().stream()
                .map(MiddleItemMaterialDo::getMaterialName).collect(Collectors.toSet());
        Set<String> materialBizIds = context.getItemMaterialDoList().stream()
                .map(ItemMaterialDo::getMaterialName).collect(Collectors.toSet());
        if (materialIds.size() != materialBizIds.size()) {
            return false;
        }
        return materialIds.containsAll(materialBizIds) && materialBizIds.containsAll(materialIds);
    }


    /**
     * 校验字符串的值，为null则返回""
     *
     * @param str 字符串
     * @return 返回值
     */
    private String verifyString(String str) {
        if (str == null) {
            return "";
        }
        return str;
    }


}
