package gov.derate.proof.synchronous.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.enums.ItemTypeEnum;
import gov.derate.proof.common.enums.ProjectTypeEnum;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * 事项展示对象
 *
 * <AUTHOR>
 * @date 2022/4/25.
 */
public class MiddleItemVo implements Serializable {
    private static final long serialVersionUID = -3885755206505721918L;

    /**
     * 事项编码
     */
    @JsonProperty("item_code")
    private String itemCode;
    /**
     * 办理项编码（事项编码）
     */
    @JsonProperty("situation_code")
    private String situationCode;
    /**
     * 事项名称
     */
    @JsonProperty("item_name")
    private String itemName;
    /**
     * 办理项名称（事项名称）
     */
    @JsonProperty("situation_name")
    private String situationName;
    /**
     * 实施机构
     */
    @JsonProperty("impl_org_name")
    private String implOrgName;
    /**
     * 实施机构的行政区划代码
     */
    @JsonProperty("division_code")
    private String divisionCode;

    /**
     * 事项版本
     */
    @JsonProperty("task_version")
    private String taskVersion;

    /**
     * 事项类型
     */
    @JsonProperty("item_type")
    private ItemTypeEnum itemType;

    /**
     * 办件类型（新增属性）
     */
    @JsonProperty("project_type")
    private ProjectTypeEnum projectType;

    /**
     * 同步状态（同步成功0,同步失败1,未同步2）
     */
    @JsonProperty("synchronize_status")
    private SynchronizeStatusEnum synchronizeStatus;

    /**
     * 最近更新时间
     */
    @JsonProperty("last_modification_time")
    private Date lastModificationTime;
    /**
     * 受理条件
     */
    @JsonProperty("accept_condition")
    private String acceptCondition;

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getSituationCode() {
        return situationCode;
    }

    public void setSituationCode(String situationCode) {
        this.situationCode = situationCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getSituationName() {
        return situationName;
    }

    public void setSituationName(String situationName) {
        this.situationName = situationName;
    }

    public String getImplOrgName() {
        return implOrgName;
    }

    public void setImplOrgName(String implOrgName) {
        this.implOrgName = implOrgName;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public String getTaskVersion() {
        return taskVersion;
    }

    public void setTaskVersion(String taskVersion) {
        this.taskVersion = taskVersion;
    }

    public ItemTypeEnum getItemType() {
        return itemType;
    }

    public void setItemType(ItemTypeEnum itemType) {
        this.itemType = itemType;
    }

    public ProjectTypeEnum getProjectType() {
        return projectType;
    }

    public void setProjectType(ProjectTypeEnum projectType) {
        this.projectType = projectType;
    }

    public SynchronizeStatusEnum getSynchronizeStatus() {
        return synchronizeStatus;
    }

    public void setSynchronizeStatus(SynchronizeStatusEnum synchronizeStatus) {
        this.synchronizeStatus = synchronizeStatus;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    public String getAcceptCondition() {
        return acceptCondition;
    }

    public void setAcceptCondition(String acceptCondition) {
        this.acceptCondition = acceptCondition;
    }

    @Override
    public String toString() {
        return "MiddleItemVo{" +
                "itemCode='" + itemCode + '\'' +
                ", situationCode='" + situationCode + '\'' +
                ", itemName='" + itemName + '\'' +
                ", situationName='" + situationName + '\'' +
                ", implOrgName='" + implOrgName + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", taskVersion='" + taskVersion + '\'' +
                ", itemType=" + itemType +
                ", projectType=" + projectType +
                ", synchronizeStatus=" + synchronizeStatus +
                ", lastModificationTime=" + lastModificationTime +
                ", acceptCondition='" + acceptCondition + '\'' +
                '}';
    }
}
