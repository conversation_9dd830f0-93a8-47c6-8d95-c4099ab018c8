package gov.derate.proof.synchronous.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.common.enums.AttachmentTypeEnum;

import java.io.Serializable;

/**
 * 材料附件展示对象
 *
 * <AUTHOR>
 * @date 2022/4/25.
 */
public class MaterialAttachmentVo implements Serializable {
    private static final long serialVersionUID = 4682982344814142872L;

    /**
     * 所属材料版本标识
     */
    @JsonProperty("material_id")
    private String materialId;

    /**
     * 附件类型（0空白表格|1示例样表）
     */
    @JsonProperty("attachment_type")
    private AttachmentTypeEnum attachmentType;

    /**
     * 附件下载地址
     */
    @JsonProperty("file_path")
    private String filePath;

    /**
     * 附件名称
     */
    @JsonProperty("attachment_name")
    private String attachmentName;

    /**
     * 附件编码
     */
    @JsonProperty("attachment_guid")
    private String attachmentGuid;

    /**
     * 附件内网下载地址
     */
    @JsonProperty("intranet_path")
    private String intranetPath;

    public String getMaterialId() {
        return materialId;
    }

    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }

    public AttachmentTypeEnum getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(AttachmentTypeEnum attachmentType) {
        this.attachmentType = attachmentType;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public String getAttachmentGuid() {
        return attachmentGuid;
    }

    public void setAttachmentGuid(String attachmentGuid) {
        this.attachmentGuid = attachmentGuid;
    }

    public String getIntranetPath() {
        return intranetPath;
    }

    public void setIntranetPath(String intranetPath) {
        this.intranetPath = intranetPath;
    }

    @Override
    public String toString() {
        return "MaterialAttachmentVo{" +
                "materialId='" + materialId + '\'' +
                ", attachmentType=" + attachmentType +
                ", filePath='" + filePath + '\'' +
                ", attachmentName='" + attachmentName + '\'' +
                ", attachmentGuid='" + attachmentGuid + '\'' +
                ", intranetPath='" + intranetPath + '\'' +
                '}';
    }
}
