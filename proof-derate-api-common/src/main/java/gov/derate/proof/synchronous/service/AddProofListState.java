package gov.derate.proof.synchronous.service;

import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.service.BeanFactoryUtils;
import gov.derate.proof.item.entity.ItemDo;
import gov.derate.proof.item.entity.ItemMaterialDo;
import gov.derate.proof.item.entity.ItemProofRelationDo;
import gov.derate.proof.list.entity.ProofListDo;
import gov.derate.proof.list.repository.ProofListRepository;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 事项同步处理 新增清单 service impl
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/1/6
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2023/1/6；
 */
public class AddProofListState implements State {

    @Override
    public void handle(SynchronizeDataContext context) {
        ProofListRepository proofListDoRepository = BeanFactoryUtils.getBean(ProofListRepository.class);
        ItemProofRelationDo itemProofRelation = context.getItemProofRelation();
        List<ItemMaterialDo> itemMaterialDoList = context.getItemMaterialDoList();
        ItemDo itemDo = context.getItemDo();
        List<ProofListDo> proofListDoList = generatorByItemMaterial(itemDo, itemMaterialDoList);
        proofListDoList.forEach(item->item.setItemProofStatus(itemProofRelation.getItemProofStatus()));
        proofListDoRepository.deleteAllByItemCode(itemDo.getItemCode());
        context.setProofListDoList(proofListDoList);
    }

    /**
     * 根据材料，生成清单
     *
     * @param itemDo         事项
     * @param materialDoList 材料
     * @return 清单List
     */
    private List<ProofListDo> generatorByItemMaterial(ItemDo itemDo, List<ItemMaterialDo> materialDoList) {
        return materialDoList.stream().map(item -> {
            ProofListDo proofListDo = new ProofListDo();
            proofListDo.setItemCode(itemDo.getItemCode());
            proofListDo.setMaterialId(item.getId());
            proofListDo.setProofName(item.getMaterialName());
            proofListDo.setItemMaterialSource(item.getItemMaterialSource());
            proofListDo.setItemProofStatus(ItemProofStatusEnum.WAIT_FOR_CLEAN);
            proofListDo.setProofClearType(item.getProofClearType());
            return proofListDo;
        }).collect(Collectors.toList());
    }
}
