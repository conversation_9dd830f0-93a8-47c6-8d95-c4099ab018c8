package gov.derate.proof.synchronous.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 同步操作过程信息展示
 *
 * <AUTHOR>
 * @date 2022/4/27.
 */
public class ProofListProcedureVo implements Serializable {
    private static final long serialVersionUID = -9046783193626138975L;

    /**
     * 事项编码
     */
    @JsonProperty("item_code")
    private String itemCode;
    /**
     * 工作人员账号（经办人）
     */
    @JsonProperty("account")
    private String account;
    /**
     * 工作人员名称
     */
    @JsonProperty("account_name")
    private String accountName;
    /**
     * 工作人员所属部门编码（统一社会信用代码）
     */
    @JsonProperty("account_dept_code")
    private String accountDeptCode;
    /**
     * 工作人员所属部门名称（实施机构）
     */
    @JsonProperty("account_dept_name")
    private String accountDeptName;
    /**
     * 提交时间
     */
    @JsonProperty("procedure_date")
    private Date procedureDate;

    /**
     * 过程信息说明
     */
    @JsonProperty("procedure_message")
    private String procedureMessage;

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountDeptCode() {
        return accountDeptCode;
    }

    public void setAccountDeptCode(String accountDeptCode) {
        this.accountDeptCode = accountDeptCode;
    }

    public String getAccountDeptName() {
        return accountDeptName;
    }

    public void setAccountDeptName(String accountDeptName) {
        this.accountDeptName = accountDeptName;
    }

    public Date getProcedureDate() {
        return procedureDate;
    }

    public void setProcedureDate(Date procedureDate) {
        this.procedureDate = procedureDate;
    }

    public String getProcedureMessage() {
        return procedureMessage;
    }

    public void setProcedureMessage(String procedureMessage) {
        this.procedureMessage = procedureMessage;
    }

    @Override
    public String toString() {
        return "ProofListProcedureVo{" +
                "itemCode='" + itemCode + '\'' +
                ", account='" + account + '\'' +
                ", accountName='" + accountName + '\'' +
                ", accountDeptCode='" + accountDeptCode + '\'' +
                ", accountDeptName='" + accountDeptName + '\'' +
                ", procedureDate=" + procedureDate +
                ", procedureMessage='" + procedureMessage + '\'' +
                '}';
    }
}
