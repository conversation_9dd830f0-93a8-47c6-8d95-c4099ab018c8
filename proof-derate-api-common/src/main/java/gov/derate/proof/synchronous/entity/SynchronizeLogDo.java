package gov.derate.proof.synchronous.entity;

import gov.derate.proof.common.entity.AbstractDomainEntity;
import gov.derate.proof.common.enums.ItemStatus;
import gov.derate.proof.common.enums.ItemTypeEnum;
import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.common.enums.convert.ItemStatusConvert;
import gov.derate.proof.common.enums.convert.ItemTypeEnumConvert;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * 同步记录表
 *
 * <AUTHOR>
 * @date 2022/4/14.
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "TBL_SYNCHRONIZE_LOG")
@Deprecated
public class SynchronizeLogDo extends AbstractDomainEntity {
    private static final long serialVersionUID = -2649499602639523353L;

    /**
     * 事项的标识
     */
    @Column(name = "item_guid")
    private String itemGuid;

    /**
     * 事项编码
     */
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 事项名称
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 实施机构
     */
    @Column(name = "impl_org_name")
    private String implOrgName;

    /**
     * 事项类型
     */
    @Convert(converter = ItemTypeEnumConvert.class)
    @Column(name = "item_type")
    private ItemTypeEnum itemType;

    /**
     * 事项状态（事项状态：1在用 2暂停 3取消，默认在用）
     */
    @Convert(converter = ItemStatusConvert.class)
    @Column(name = "item_status")
    private ItemStatus itemStatus;

    /**
     * 同步状态（同步成功0,同步失败1,未同步2）
     */
    @Column(name = "synchronize_status")
    private SynchronizeStatusEnum synchronizeStatus;

    /**
     * 中间表获取事项的时间
     */
    @Column(name = "middle_item_time")
    private Date middleItemTime;

    /**
     * 中间表获取材料的时间
     */
    @Column(name = "middle_material_time")
    private Date middleMaterialTime;

    /**
     * 同步记录的来源，0是定时调度，1是手动操作，默认为0
     */
    @Column(name = "log_source")
    private boolean logSource;

    /**
     * 同步失败的原因
     */
    @Column(name = "failed_result")
    private String failedResult;

    public String getItemGuid() {
        return itemGuid;
    }

    public void setItemGuid(String itemGuid) {
        this.itemGuid = itemGuid;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getImplOrgName() {
        return implOrgName;
    }

    public void setImplOrgName(String implOrgName) {
        this.implOrgName = implOrgName;
    }

    public ItemTypeEnum getItemType() {
        return itemType;
    }

    public void setItemType(ItemTypeEnum itemType) {
        this.itemType = itemType;
    }

    public ItemStatus getItemStatus() {
        return itemStatus;
    }

    public void setItemStatus(ItemStatus itemStatus) {
        this.itemStatus = itemStatus;
    }

    public SynchronizeStatusEnum getSynchronizeStatus() {
        return synchronizeStatus;
    }

    public void setSynchronizeStatus(SynchronizeStatusEnum synchronizeStatus) {
        this.synchronizeStatus = synchronizeStatus;
    }

    public Date getMiddleItemTime() {
        return middleItemTime;
    }

    public void setMiddleItemTime(Date middleItemTime) {
        this.middleItemTime = middleItemTime;
    }

    public Date getMiddleMaterialTime() {
        return middleMaterialTime;
    }

    public void setMiddleMaterialTime(Date middleMaterialTime) {
        this.middleMaterialTime = middleMaterialTime;
    }

    public boolean isLogSource() {
        return logSource;
    }

    public void setLogSource(boolean logSource) {
        this.logSource = logSource;
    }

    public String getFailedResult() {
        return failedResult;
    }

    public void setFailedResult(String failedResult) {
        this.failedResult = failedResult;
    }
}
