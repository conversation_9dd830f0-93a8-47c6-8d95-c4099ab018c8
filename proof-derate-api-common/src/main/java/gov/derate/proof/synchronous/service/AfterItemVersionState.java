package gov.derate.proof.synchronous.service;

import gov.derate.proof.common.enums.SynchronizeStatusEnum;
import gov.derate.proof.synchronous.entity.context.SynchronizeDataContext;

/**
 * <p>
 * 中间表比事项表版本大逻辑处理
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2024/3/26
 * </p>
 *
 * <AUTHOR>
 */
public class AfterItemVersionState implements State {

    @Override
    public void handle(SynchronizeDataContext context) {
        context.getMiddleItemDo().setSynchronizeStatus(SynchronizeStatusEnum.SYNCHRONIZE_INVALID);
    }
}
