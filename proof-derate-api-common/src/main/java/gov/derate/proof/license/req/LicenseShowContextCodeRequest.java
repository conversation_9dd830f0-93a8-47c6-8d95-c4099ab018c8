package gov.derate.proof.license.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.license.dto.OperatorDto;
import gov.license.common.api.req.BaseRequest;

/**
 * <p>
 * 用户出示码;获取上下文接口(show/context_code/get) 请求类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class LicenseShowContextCodeRequest extends BaseRequest {
    /**
     * 事项编码
     * 必填
     */
    @JsonProperty("service_item_code")
    private String serviceItemCode;
    /**
     * 事项名称
     * 必填
     */
    @JsonProperty("service_item_name")
    private String serviceItemName;
    /**
     * 事项所属单位
     * 必填
     */
    @JsonProperty("service_item_org")
    private String serviceItemOrg;
    /**
     * 业务流水号
     * 必填
     */
    @JsonProperty("biz_num")
    private String bizNum;
    /**
     * 窗口人员信息(详附录)
     * 必填
     */
    @JsonProperty("operator")
    private OperatorDto operator;
    /**
     * 办事人员身份信息(身份核验码)（详对接规范）
     * 必填
     */
    @JsonProperty("identity_info_token")
    private String identityInfoToken;
    /**
     * 证照材料查询范围(详附录)
     */
    @JsonProperty("scope_license_materials")
    private String scopeLicenseMaterials;
    /**
     * 业务系统唯一标识(详附录)
     */
    @JsonProperty("biz_system_id")
    private String bizSystemId;

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getServiceItemOrg() {
        return serviceItemOrg;
    }

    public void setServiceItemOrg(String serviceItemOrg) {
        this.serviceItemOrg = serviceItemOrg;
    }

    public String getBizNum() {
        return bizNum;
    }

    public void setBizNum(String bizNum) {
        this.bizNum = bizNum;
    }

    public OperatorDto getOperator() {
        return operator;
    }

    public void setOperator(OperatorDto operator) {
        this.operator = operator;
    }

    public String getIdentityInfoToken() {
        return identityInfoToken;
    }

    public void setIdentityInfoToken(String identityInfoToken) {
        this.identityInfoToken = identityInfoToken;
    }

    public String getScopeLicenseMaterials() {
        return scopeLicenseMaterials;
    }

    public void setScopeLicenseMaterials(String scopeLicenseMaterials) {
        this.scopeLicenseMaterials = scopeLicenseMaterials;
    }

    public String getBizSystemId() {
        return bizSystemId;
    }

    public void setBizSystemId(String bizSystemId) {
        this.bizSystemId = bizSystemId;
    }

    @Override
    public String toString() {
        return "LicenseShowContextCodeRequest{" +
                "serviceItemCode='" + serviceItemCode + '\'' +
                ", serviceItemName='" + serviceItemName + '\'' +
                ", serviceItemOrg='" + serviceItemOrg + '\'' +
                ", bizNum='" + bizNum + '\'' +
                ", operator='" + operator + '\'' +
                ", identityInfoToken='" + identityInfoToken + '\'' +
                ", scopeLicenseMaterials='" + scopeLicenseMaterials + '\'' +
                ", bizSystemId='" + bizSystemId + '\'' +
                '}';
    }
}
