package gov.derate.proof.license.sdk;

import gov.derate.proof.common.dto.CurrentAccountAndInfoDto;
import gov.derate.proof.license.dto.ImplementLicenseItemInfoDto;
import gov.derate.proof.license.dto.LicenseItemDTO;
import gov.derate.proof.license.dto.OperatorDto;
import gov.derate.proof.license.exception.LicenseException;
import gov.derate.proof.license.req.LicenseItemListRequest;
import gov.derate.proof.license.resp.LicenseAttachmentArchivingResponse;
import gov.derate.proof.license.resp.LicenseAuthCodeResponse;
import gov.derate.proof.license.resp.LicenseGetTokenResponse;
import gov.derate.proof.license.resp.LicenseItemGetGroupSealResponse;

import java.util.List;
import java.util.Map;

/**
 * 远程调用电子证明服务包
 *
 * <AUTHOR>
 * @date 2023/04/11.
 */
public interface LicenseProofSdk {

    /**
     * 查询证照目录
     *
     * @param licenseItemListRequest 证照目录请求体
     * @return 证照目录列表
     * @throws LicenseException 证照异常
     */
    List<LicenseItemDTO> queryLicenseItemList(LicenseItemListRequest licenseItemListRequest) throws LicenseException;

    /**
     * 获取电子证明用证码响应
     *
     * @param itemCode                 事项编码
     * @param itemName                 事项名称
     * @param identityNumber           持有人身份证号码
     * @param currentAccountAndInfoDto 当前账号
     * @param idCode                   证照号码
     * @param licenseItemCode          目录基本码
     * @return 用证码信息响应
     * @throws Exception 异常
     */
    LicenseAuthCodeResponse getLicenseAuthCodes(String itemCode, String itemName, String identityNumber,
                                                CurrentAccountAndInfoDto currentAccountAndInfoDto, String idCode, String licenseItemCode) throws Exception;

    /**
     * 获取授权码信息
     *
     * @param authCode 核验码
     * @return 数据
     * @throws Exception 异常
     */
    LicenseGetTokenResponse getLicenseTokenResponse(String authCode) throws Exception;

    /**
     * 电子证明归档
     *
     * @param authCode 授权码
     * @return 电子证明归档
     * @throws Exception 异常
     */
    LicenseAttachmentArchivingResponse licenseAttachmentArchiving(String authCode) throws Exception;

    /**
     * 获取电子证明系统-实施目录信息
     *
     * @param basicCode 基本码
     * @return 实施目录信息
     */
    List<ImplementLicenseItemInfoDto> getImplementLicenseItemInfoByBasicCode(String basicCode);


    /**
     * 查看电子证明-印章信息
     *
     * @param implementCode 实施码
     * @param basicCode     基本码
     * @param orgCode       部门
     * @return 印章信息
     */
    LicenseItemGetGroupSealResponse getSealInfo(String implementCode, String basicCode, String orgCode);

    /**
     * 签发接口
     *
     * @param itemName      事项名称
     * @param itemCode      事项编码
     * @param bizNum        业务号
     * @param licenseGroup  实施码印章组别
     * @param implementCode 实施码
     * @param sealCode      印章编码
     * @param bizNum        业务编码
     * @param operatorDto   操作人
     * @param dataItemMap   数据项 key = 实施码配置key，value = assistItemInfo的value
     * @return authCode
     */
    String issueLicense(String itemName, String itemCode, String licenseGroup, String sealCode, String bizNum, String implementCode, OperatorDto operatorDto, Map<String, String> dataItemMap);
}
