package gov.derate.proof.license.enums;

/**
 * <p>
 * 持有人类型
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-19
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-19;
 */
public enum HolderType {

    /**
     * 个人
     */
    PERSON("个人"),
    /**
     * 机构
     */
    ORG("机构"),
    /**
     * 个人和机构
     */
    PERSON_AND_ORG("个人和机构"),
    OTHER("其他"),
    ;

    private String desc;

    HolderType(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
