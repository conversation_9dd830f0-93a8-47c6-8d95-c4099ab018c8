package gov.derate.proof.license.enums;

/**
 * <p>
 * LicenseType
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public enum LicenseType {
    /**
     * 证件执照
     */
    CERTIFICATE(0, "证件执照"),
    /**
     * 证明文件
     */
    PROOF(1, "证明文件"),
    /**
     * 批文批复
     */
    APPROVAL(2, "批文批复"),
    /**
     * 鉴定报告
     */
    AUTHENTICATION_REPORT(3, "鉴定报告"),
    /**
     * 办事结果
     */
    WORK_RESULT(4, "办事结果"),
    ;

    /**
     * 描述
     */
    private final String describe;
    /**
     * 下标
     */
    private final Integer value;

    LicenseType(Integer value, String describe) {
        this.value = value;
        this.describe = describe;
    }

    public String getDescribe() {
        return describe;
    }

    public Integer getValue() {
        return value;
    }

}
