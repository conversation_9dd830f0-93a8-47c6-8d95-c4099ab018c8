package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 电子证明token接口响应传输类 v1/license/token
 *
 * <AUTHOR>
 * @date 2023/04/10.
 */
public class LicenseGetTokenResponse extends LicenseBaseResponse {
    private static final long serialVersionUID = -7879081900188075684L;

    /**
     * 数据
     */
    @JsonProperty("data")
    private LicenseGetTokenDataResponse data;

    public LicenseGetTokenDataResponse getData() {
        return data;
    }

    public void setData(LicenseGetTokenDataResponse data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "LicenseGetTokenResponse{" +
                "data=" + data +
                '}';
    }
}
