package gov.derate.proof.license.resp;

import gov.derate.proof.license.dto.LicenseItemUnionDTO;

import java.util.List;

/**
 * <p>
 * 组合目录接口响应类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-20
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-20;
 */
public class LicenseItemUnionResponse extends LicenseBaseResponse {

    /**
     * 组合目录
     */
    private List<LicenseItemUnionDTO> data;

    /**
     * Gets the value of 组合目录.
     *
     * @return the value of 组合目录
     */
    public List<LicenseItemUnionDTO> getData() {
        return data;
    }

    /**
     * Sets the 组合目录.
     *
     * @param data 组合目录
     */
    public void setData(List<LicenseItemUnionDTO> data) {
        this.data = data;
    }
}
