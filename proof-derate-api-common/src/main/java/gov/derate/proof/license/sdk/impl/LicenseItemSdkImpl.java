package gov.derate.proof.license.sdk.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import gov.derate.proof.account.dto.AccountInfoDto;
import gov.derate.proof.common.dto.CurrentAccountAndInfoDto;
import gov.derate.proof.common.response.ResponseCode;
import gov.derate.proof.dictionary.entity.DictionaryTypeItemConstant;
import gov.derate.proof.license.config.LicenseApp;
import gov.derate.proof.license.dto.LicenseItemDTO;
import gov.derate.proof.license.dto.LicenseItemUnionDTO;
import gov.derate.proof.license.dto.LicenseItemUnionPageDTO;
import gov.derate.proof.license.dto.OperatorDto;
import gov.derate.proof.license.enums.AckCode;
import gov.derate.proof.license.exception.LicenseException;
import gov.derate.proof.license.req.*;
import gov.derate.proof.license.resp.*;
import gov.derate.proof.license.sdk.LicenseItemSdk;
import gov.derate.proof.license.sdk.TokenSdk;
import gov.licc.func.api.amp.service.DictPublicService;
import gov.licc.func.api.auth.dto.CurrentAccountDto;
import gov.license.common.tools.http.HttpUtil;
import gov.license.common.tools.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 * 远程调用证照服务包
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-19
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-19;
 */
@Component
public class LicenseItemSdkImpl implements LicenseItemSdk {

    private static final Logger LOGGER = LoggerFactory.getLogger(LicenseItemSdkImpl.class);

    @Autowired
    private TokenSdk tokenSdk;
    @Autowired
    private LicenseApp licenseApp;
    /**
     * 字典服务
     */
    @Autowired
    private DictPublicService dictPublicService;

    @Override
    public List<LicenseItemDTO> queryLicenseItemList(LicenseItemListRequest request) throws LicenseException {
        if (request == null) {
            return Collections.emptyList();
        }

        String accessToken = tokenSdk.getAccessToken(licenseApp);
        Map<String, Object> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("page_index", String.valueOf(request.getPageIndex()));
        param.put("page_size", String.valueOf(request.getPageSize()));
//        param.put("keyword", request.getKeyword());
        param.put("status", StringUtils.join(request.getStatus(), ","));
        param.put("is_open", "all");
//        param.put("rtn_icon", String.valueOf(request.getRtnIcon()));
        LicenseItemResponse response = null;
        String url = licenseApp.getUrl() + "/license_item/list";
        HttpUtil.HttpResp httpCallerResp = null;
        try {
            LOGGER.debug("queryLicenseItemList request param [{}],url[{}]", param, url);
            httpCallerResp = HttpUtil.doGet(url, param);
//            LOGGER.debug("queryLicenseItemList request response [{}]", httpCallerResp.getResponseString());
        } catch (Exception e) {
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format(e.getMessage()));
        }
        if (HttpStatus.OK != httpCallerResp.getStatusCode()) {
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR);
        }
        String responseString = httpCallerResp.getResponseString();
        try {
            response = JacksonUtil.toBean(responseString, LicenseItemResponse.class);
        } catch (Exception e) {
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format(e.getMessage()));
        }
        if (AckCode.SUCCESS != response.getAckCode()) {
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format(response.getErrorMessage()));
        }

        return response.getData();
    }

    @Override
    public List<LicenseItemUnionDTO> queryLicenseItemUnion(LicenseItemUnionListRequest request) throws LicenseException {
        if (request == null) {
            return Collections.emptyList();
        }

        String accessToken = tokenSdk.getAccessToken(licenseApp);
        Map<String, Object> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("page_index", String.valueOf(request.getPageIndex()));
        param.put("page_size", String.valueOf(request.getPageSize()));
        param.put("rtn_field", String.valueOf(request.getReturnField()));
        param.put("name", request.getName());
        param.put("code", request.getCode());
        String url = licenseApp.getUrl() + "/license_item/union";
        LicenseItemUnionResponse response = null;
        HttpUtil.HttpResp httpCallerResp = null;
        try {
            LOGGER.debug("queryLicenseItemUnion request param [{}],url[{}]", param, url);
            httpCallerResp = HttpUtil.doGet(url, param);
            LOGGER.debug("queryLicenseItemUnion request response [{}]", httpCallerResp.getResponseString());
        } catch (Exception e) {
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format(e.getMessage()));
        }
        if (HttpStatus.OK != httpCallerResp.getStatusCode()) {
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR);
        }
        String responseString = httpCallerResp.getResponseString();
        try {
            response = JacksonUtil.toBean(responseString, LicenseItemUnionResponse.class);
        } catch (Exception e) {
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format(e.getMessage()));
        }
        if (AckCode.SUCCESS != response.getAckCode()) {
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format(response.getErrorMessage()));
        }


        return response.getData();
    }

    @Override
    public List<LicenseItemUnionPageDTO> queryLicenseItemUnion(LicenseItemUnionPageRequest request) throws LicenseException {
        if (request == null) {
            return Collections.emptyList();
        }
        String accessToken = tokenSdk.getAccessToken(licenseApp);
        Map<String, Object> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("page_index", String.valueOf(request.getPageIndex()));
        param.put("page_size", String.valueOf(request.getPageSize()));
        param.put("union_name", request.getUnionName());
        param.put("union_code", request.getUnionCode());
        param.put("union_resp_org", request.getUnionRespOrg());
        String url = licenseApp.getUrl() + "/license_item/union/list";
        HttpUtil.HttpResp httpCallerResp = null;
        try {
            LOGGER.debug("queryLicenseItemUnion request param [{}],url[{}]", param, url);
            httpCallerResp = HttpUtil.doGet(url, param);
            LOGGER.debug("queryLicenseItemUnion request response [{}]", httpCallerResp.getResponseString());
        } catch (Exception e) {
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format(e.getMessage()));
        }
        if (HttpStatus.OK != httpCallerResp.getStatusCode()) {
            LOGGER.error("queryLicenseItemUnion request response [{}]", httpCallerResp.getResponseString());

            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR);
        }
        String responseString = httpCallerResp.getResponseString();
        LicenseItemUnionPageResponse response;
        try {
            response = JacksonUtil.toBean(responseString, LicenseItemUnionPageResponse.class);
        } catch (Exception e) {
            LOGGER.error("queryLicenseItemUnion request response [{}]", httpCallerResp.getResponseString());
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_ERROR_ERROR.format(e.getMessage()));
        }
        if (AckCode.SUCCESS != response.getAckCode()) {
            LOGGER.error("queryLicenseItemUnion request response [{}]", httpCallerResp.getResponseString());
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format(response.getErrorMessage()));
        }
        return response.getData();
    }


    @Override
    public LicenseShowContextCodeResponse getHolderAuthShowContextCode(String itemCode, String itemName, String serviceItemOrg, String serialNumber, String authorizationCode, CurrentAccountAndInfoDto currentAccountAndInfoDto) throws Exception {
        Optional<String> config = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_HOLDER_AUTH_SHOW_CONTEXT_CODE_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_HOLDER_AUTH_SHOW_CONTEXT_CODE_URL.name());
        // 电子证照，根据业务信息换取上下文编码url
        String holderAuthShowContextCodeUrl = config.orElse("");

        String accessToken = tokenSdk.getAccessToken(licenseApp);
        String url = holderAuthShowContextCodeUrl + "?access_token=" + accessToken;
        LicenseShowContextCodeRequest request = new LicenseShowContextCodeRequest();
        request.setServiceItemCode(itemCode);
        request.setServiceItemName(itemName);
        request.setServiceItemOrg(serviceItemOrg);
        request.setBizNum(serialNumber);
        CurrentAccountDto currentAccountDto = currentAccountAndInfoDto.getCurrentAccountDto();
        AccountInfoDto accountInfoDto = currentAccountAndInfoDto.getAccountInfoDto();
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setAccount(currentAccountDto.getAccount().getAccount());
        operatorDto.setName(currentAccountDto.getAccount().getUserName());
        operatorDto.setIdentityNum(accountInfoDto.getIdentityNumber());
        operatorDto.setDivisionCode(accountInfoDto.getDivisionCode());
        operatorDto.setServiceOrg(accountInfoDto.getOrgName());
        operatorDto.setServiceOrgCode(accountInfoDto.getOrgCode());
        request.setOperator(operatorDto);
        // 数据来源于实名验证的验证码
        request.setIdentityInfoToken(authorizationCode);
        // 传值为0或1，具体原因未知，接口wiki地址：http://ip:端口/pages/viewpage.action?pageId=********#id-%E6%8C%81%E8%AF%81%E4%BA%BA%E6%8E%88%E6%9D%83%E7%94%A8%E8%AF%81-%E7%94%A8%E6%88%B7%E5%87%BA%E7%A4%BA%E7%A0%81;%E8%8E%B7%E5%8F%96%E4%B8%8A%E4%B8%8B%E6%96%87%E6%8E%A5%E5%8F%A3(show/context_code/get)
        request.setScopeLicenseMaterials("1");
        String jsonRequest = JacksonUtil.toJsonStr(request);
        LOGGER.debug("getHolderAuthShowContextCode request url[{}],param[{}]", url, jsonRequest);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doPostJson(url, jsonRequest);
        LOGGER.debug("getHolderAuthShowContextCode response [{}],status[{}] [{}]", httpCallerResp.getResponseString(), httpCallerResp.getStatusCode(), httpCallerResp);
        String responseString = httpCallerResp.getResponseString();
        return JacksonUtil.toBean(responseString, new TypeReference<LicenseShowContextCodeResponse>() {
        });
    }

    @Override
    public LicenseShowAuthCodeResponse getHolderAuthShowAuthCode(String contextCode) throws Exception {
        // 电子证照，获取用证码和证照摘要
        Optional<String> config = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_HOLDER_AUTH_SHOW_AUTH_CODE_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_HOLDER_AUTH_SHOW_AUTH_CODE_URL.name());
        String url = config.orElse("");
        String accessToken = tokenSdk.getAccessToken(licenseApp);
        Map<String, Object> param = Maps.newHashMap();
        param.put("context_code", contextCode);
        param.put("access_token", accessToken);
        LOGGER.debug("getHolderAuthShowAuthCode request url [{}],param [{}]", url, param);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doGet(url, param);
        String responseString = httpCallerResp.getResponseString();
        LOGGER.debug("getHolderAuthShowAuthCode response url [{}],[{}] ", url, responseString);
        return JacksonUtil.toBean(responseString, new TypeReference<LicenseShowAuthCodeResponse>() {
        });
    }

    /**
     * 电子证照-持有人授权用证
     *
     * @param itemCode                 事项编码
     * @param itemName                 事项名称
     * @param idNumber                 免证办-单据办事人身份证号码
     * @param licenseItemCode          全省目录基本码
     * @param credential               免证办-江西政务服务码-解码接口ywxx.code字段
     * @param currentAccountAndInfoDto 当前用户信息
     * @return 带授权码的证照
     * @throws Exception 请求错误抛出错误
     */
    @Override
    public LicenseHolderResponse getLicenseByHolderAuthCodeInterface(String itemCode, String itemName, String idNumber, String licenseItemCode, String credential, CurrentAccountAndInfoDto currentAccountAndInfoDto) throws Exception {
        Optional<String> config = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_HOLDER_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_HOLDER_URL.name());
        Optional<String> casNoOptional = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_HOLDER_CAS_NO.getDictType().name(), DictionaryTypeItemConstant.LICENSE_HOLDER_CAS_NO.name());
        // 电子证照，根据业务信息换取上下文编码url
        String holderUrl = config.orElse("");
        String casNo = casNoOptional.orElse("033");

        String accessToken = tokenSdk.getAccessToken(licenseApp);
        String url = holderUrl + "?access_token=" + accessToken;
        LicenseHolderRequest request = new LicenseHolderRequest();
        request.setCredential(credential);
        request.setIdentityNumber(idNumber);
        request.setLicenseItemCode(licenseItemCode);
        request.setCasNo(casNo);
        request.setServiceItemCode(itemCode);
        request.setServiceItemName(itemName);
        CurrentAccountDto currentAccountDto = currentAccountAndInfoDto.getCurrentAccountDto();
        AccountInfoDto accountInfoDto = currentAccountAndInfoDto.getAccountInfoDto();
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setAccount(currentAccountDto.getAccount().getAccount());
        operatorDto.setName(currentAccountDto.getAccount().getUserName());
        operatorDto.setIdentityNum(accountInfoDto.getIdentityNumber());
        operatorDto.setDivisionCode(accountInfoDto.getDivisionCode());
        operatorDto.setServiceOrg(accountInfoDto.getOrgName());
        operatorDto.setServiceOrgCode(accountInfoDto.getOrgCode());
        request.setOperator(operatorDto);
        if ("020".equals(casNo)) {
            request.setCredential(idNumber);
        }
        String jsonRequest = JacksonUtil.toJsonStr(request);

        LOGGER.debug("getLicenseByHolderAuthCodeInterface request url[{}],param[{}]", url, jsonRequest);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doPostJson(url, jsonRequest);
        String responseString = httpCallerResp.getResponseString();
        LOGGER.debug("getLicenseByHolderAuthCodeInterface response [{}],status[{}] [{}]", httpCallerResp.getResponseString(), httpCallerResp.getStatusCode(), httpCallerResp);
        return JacksonUtil.toBean(responseString, new TypeReference<LicenseHolderResponse>() {
        });
    }

    /**
     * 归档证照-license/archive接口
     *
     * @param authCode 核验码
     * @return 文件数据
     */
    @Override
    public LicenseAttachmentArchivingResponse licenseArchiveInterface(String authCode) throws Exception {
        String accessToken = tokenSdk.getAccessToken(licenseApp);
        // 电子证照，获取受理审批证照访问令牌
        Optional<String> urlConfig = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_ARCHIVE_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_ARCHIVE_URL.name());
        String url = urlConfig.orElse("");
        Map<String, Object> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("auth_code", authCode);
        LOGGER.debug("licenseArchiveInterface req url [{}] param [{}]", url, param);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doGet(url, param);
        String responseString = httpCallerResp.getResponseString();
        LOGGER.debug("licenseArchiveInterface resp url [{}] param [{}],resp [{}]", url, param, responseString);
        LicenseAttachmentArchivingResponse response = JacksonUtil.toBean(responseString, new TypeReference<LicenseAttachmentArchivingResponse>() {
        });
        if (response.getAckCode() == AckCode.SUCCESS) {
            return response;
        } else {
            LOGGER.error("licenseArchiveInterface request error url [{}] resp[{}]", url, httpCallerResp);
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format("电子证照归档接口失败"));
        }
    }

    /**
     * 查看电子证照-license/get_license接口
     *
     * @param authCode 核验码
     * @return 文件数据
     */
    @Override
    public LicenseGetLicenseResponse licenseGetLicenseInterface(String authCode) throws Exception {
        String accessToken = tokenSdk.getAccessToken(licenseApp);
        // 电子证照，获取受理审批证照访问令牌
        Optional<String> urlConfig = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_GET_LICENSE_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_GET_LICENSE_URL.name());
        String url = urlConfig.orElse("");
        Map<String, Object> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("auth_code", authCode);
        LOGGER.debug("licenseGetLicenseInterface req url [{}] param [{}]", url, param);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doGet(url, param);
        String responseString = httpCallerResp.getResponseString();
        LOGGER.debug("licenseGetLicenseInterface resp url [{}] param [{}],resp [{}]", url, param, responseString);
        LicenseGetLicenseResponse response = JacksonUtil.toBean(responseString, new TypeReference<LicenseGetLicenseResponse>() {
        });
        if (response.getAckCode() == AckCode.SUCCESS) {
            return response;
        } else {
            LOGGER.error("licenseGetLicenseInterface request error url [{}] resp[{}]", url, httpCallerResp);
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format("电子证照查看电子证照接口失败"));
        }
    }

    @Override
    public LicenseItemGetGroupSealResponse getSealInfo(String implementCode, String basicCode, String orgCode) {
        String accessToken = tokenSdk.getAccessToken(licenseApp);
        // 电子证照，获取受理审批证照访问令牌
        Optional<String> urlConfig = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_SEAL_INFO_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_SEAL_INFO_URL.name());
        String url = urlConfig.orElse("");
        Map<String, Object> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("implement_code", implementCode);
        LOGGER.debug("getSealInfo req url [{}] param [{}]", url, param);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doGet(url, param);
        String responseString = httpCallerResp.getResponseString();
        LOGGER.debug("getSealInfo resp url [{}] param [{}],resp [{}]", url, param, responseString);
        LicenseItemGetGroupSealResponse response = JacksonUtil.toBean(responseString, new TypeReference<LicenseItemGetGroupSealResponse>() {
        });
        if (response.getAckCode() == AckCode.SUCCESS) {
            return response;
        } else {
            LOGGER.error("getSealInfo request error url [{}] resp[{}]", url, httpCallerResp);
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format("电子证照查看电子证照接口失败"));
        }
    }

    @Override
    public String issueLicense(String itemName, String itemCode, String licenseGroup, String sealCode, String bizNum, String implementCode, OperatorDto operatorDto, Map<String, String> dataItemMap) {
        String accessToken = tokenSdk.getAccessToken(licenseApp);
        // 电子证照，获取受理审批证照访问令牌
        Optional<String> urlConfig = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_ISSUE_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_ISSUE_URL.name());
        String url = urlConfig.orElse("");
        url = String.format(url + "?access_token=%s", implementCode, accessToken);
        LicenseItemIssueReq req = new LicenseItemIssueReq();
        LicenseItemIssueReq.DataBean dataBean = new LicenseItemIssueReq.DataBean();
        dataBean.setServiceItemCode(itemCode);
        dataBean.setServiceItemName(itemName);
        dataBean.setLicenseGroup(licenseGroup);
        dataBean.setBizNum(bizNum);
        dataBean.setSealCode(sealCode);
        dataBean.setOperator(operatorDto);
        dataBean.setDataFields(dataItemMap);
        req.setData(dataBean);
        String jsonParam = req.toJsonParam();
        LOGGER.debug("issueLicense req url [{}] param [{}]", url, jsonParam);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doPostJson(url, jsonParam);
        String responseString = httpCallerResp.getResponseString();
        LOGGER.debug("issueLicense resp url [{}] param [{}],resp [{}]", url, jsonParam, responseString);
        LicenseItemIssueResponse response = JacksonUtil.toBean(responseString, new TypeReference<LicenseItemIssueResponse>() {
        });
        if (response.getAckCode() == AckCode.SUCCESS) {
            return response.getData().getAuthCode();
        } else {
            LOGGER.error("issueLicense request error url [{}] resp[{}]", url, httpCallerResp);
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format("签发接口接口失败"));
        }
    }


    /**
     * 归档证照
     *
     * @param authCode                 核验码
     * @param itemCode                 事项编码
     * @param itemName                 事项名称
     * @param implOrgName              事项部门
     * @param serialNumber             办件号码
     * @param currentAccountAndInfoDto 当前用户
     * @param licenseCode              归档licenseCode
     * @return 文件数据
     */
    @Override
    public LicenseAttachmentArchivingResponse licenseAttachmentArchiving(String authCode, String itemCode, String itemName, String implOrgName, String serialNumber, CurrentAccountAndInfoDto currentAccountAndInfoDto, String licenseCode) throws Exception {
        String accessToken = tokenSdk.getAccessToken(licenseApp);
        LicenseAttachmentArchivingRequest request = new LicenseAttachmentArchivingRequest(authCode, itemCode, itemName, implOrgName, serialNumber, currentAccountAndInfoDto, licenseCode);
        // 电子证照，获取受理审批证照访问令牌
        Optional<String> urlConfig = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_ATTACHMENT_ARCHIVING_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_ATTACHMENT_ARCHIVING_URL.name());
        String attachmentArchivingUrl = urlConfig.orElse("");
        String url = attachmentArchivingUrl + "?access_token=" + accessToken;
        Map<String, LicenseAttachmentArchivingRequest> param = Maps.newHashMap();
        param.put("data", request);
        String jsonParam = JacksonUtil.toJsonStr(param);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doPostJson(url, jsonParam);
        String responseString = httpCallerResp.getResponseString();
        LicenseAttachmentArchivingResponse response = JacksonUtil.toBean(responseString, new TypeReference<LicenseAttachmentArchivingResponse>() {
        });
        if (response.getAckCode() == AckCode.SUCCESS) {
            return response;
        } else {
            LOGGER.error("licenseAttachmentArchiving request error url [{}] resp[{}]", url, httpCallerResp);
            throw new LicenseException(ResponseCode.SERVICE_ERROR.format("电子证照归档接口失败"));
        }
    }

    /**
     * 提取受理审批证照数据
     *
     * @param authCode                 核验码
     * @param itemCode                 事项编码
     * @param itemName                 事项名称
     * @param implOrgName              事项部门
     * @param serialNumber             办件号码
     * @param currentAccountAndInfoDto 当前用户
     * @param licenseCode              归档licenseCode
     * @return 文件数据
     */
    @Override
    public LicenseGetLicenseAuditResponse licenseGetLicenseAudit(String authCode, String itemCode, String itemName, String implOrgName, String serialNumber, CurrentAccountAndInfoDto currentAccountAndInfoDto, String licenseCode) throws Exception {
        String accessToken = tokenSdk.getAccessToken(licenseApp);
        LicenseAttachmentArchivingRequest request = new LicenseAttachmentArchivingRequest(authCode, itemCode, itemName, implOrgName, serialNumber, currentAccountAndInfoDto, licenseCode);
        // 电子证照，获取受理审批证照访问令牌
        Optional<String> urlConfig = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_GET_LICENSE_AUDIT_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_GET_LICENSE_AUDIT_URL.name());
        String attachmentArchivingUrl = urlConfig.orElse("");
        String url = attachmentArchivingUrl + "?access_token=" + accessToken;
        Map<String, LicenseAttachmentArchivingRequest> param = Maps.newHashMap();
        param.put("data", request);
        String jsonParam = JacksonUtil.toJsonStr(param);
        LOGGER.debug("{} licenseGetLicenseAudit req url [{}] param [{}] ", this.getClass().getSimpleName(), url, param);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doPostJson(url, jsonParam);
        String responseString = httpCallerResp.getResponseString();
        LOGGER.debug("{} licenseGetLicenseAudit resp url [{}] param [{}] resp [{}]", this.getClass().getSimpleName(), url, param, responseString);
        LicenseGetLicenseAuditResponse response = JacksonUtil.toBean(responseString, new TypeReference<LicenseGetLicenseAuditResponse>() {
        });
        if (response.getAckCode() == AckCode.SUCCESS) {
            return response;
        } else {
            LOGGER.error("licenseGetLicenseAudit request error url [{}] resp[{}]", url, responseString);
            throw new LicenseException(ResponseCode.SERVICE_ERROR.format("电子证照提取受理审批证照数据接口失败"));
        }
    }

    /**
     * 获取授权码信息
     *
     * @param authCode                 核验码
     * @param itemCode                 事项编码
     * @param itemName                 事项名称
     * @param implOrgName              事项部门
     * @param serialNumber             办件号码
     * @param licenseCode              电子证照licenseCode
     * @param currentAccountAndInfoDto 当前用户
     * @return 数据
     */
    @Override
    public LicenseGetTokenAuditResponse getTokenAuditResponse(String authCode, String itemCode, String itemName, String implOrgName, String serialNumber, String licenseCode, CurrentAccountAndInfoDto currentAccountAndInfoDto) throws Exception {
        String accessToken = tokenSdk.getAccessToken(licenseApp);
        LicenseGetTokenAuditRequest request = new LicenseGetTokenAuditRequest(authCode, itemCode, itemName, implOrgName, serialNumber, licenseCode, currentAccountAndInfoDto);
        // 电子证照，获取受理审批证照访问令牌
        Optional<String> config = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_GET_TOKEN_AUDIT_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_GET_TOKEN_AUDIT_URL.name());
        String getTokenAuditUrl = config.orElse("");

        String url = getTokenAuditUrl + "?access_token=" + accessToken;
        Map<String, LicenseGetTokenAuditRequest> param = Maps.newHashMap();
        param.put("data", request);
        String jsonRequest = JacksonUtil.toJsonStr(param);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doPostJson(url, jsonRequest);
        if (HttpStatus.OK == httpCallerResp.getStatusCode()) {
            String responseString = httpCallerResp.getResponseString();
            return JacksonUtil.toBean(responseString, new TypeReference<LicenseGetTokenAuditResponse>() {
            });
        } else {
            LOGGER.error("getTokenAuditResponse request error url [{}] msg[{}]", url, httpCallerResp);
            throw new LicenseException(ResponseCode.SERVICE_ERROR.format("getTokenAuditResponse 请求失败"));
        }
    }

}
