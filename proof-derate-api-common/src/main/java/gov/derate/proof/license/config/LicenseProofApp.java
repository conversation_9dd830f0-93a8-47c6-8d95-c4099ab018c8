package gov.derate.proof.license.config;

import gov.derate.proof.dictionary.entity.DictionaryDicTypeConstant;
import gov.derate.proof.dictionary.entity.DictionaryTypeItemConstant;
import gov.licc.func.api.amp.service.DictPublicService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 电子证明接入应用信息
 *
 * <AUTHOR>
 * @date 2023/04/11.
 */
@Component
public class LicenseProofApp implements PlatformApp, InitializingBean {

    @Autowired
    private DictPublicService dictPublicService;
    /**
     * 应用账号
     */
    private String account;
    /**
     * 应用账号密码
     */
    private String password;
    /**
     * 应用app key
     */
    private String appKey;
    /**
     * 应用app 密码
     */
    private String appSecret;
    /**
     * 应用url
     */
    private String url;

    @Override
    public void afterPropertiesSet() throws Exception {
        getDictConfig();
    }

    /**
     * 获取字典中的配置值。
     *
     * @return 值对象
     */
    @Override
    public LicenseProofApp getDictConfig() {
        Optional<String> appKeyOptional = dictPublicService.getDetailValue(DictionaryDicTypeConstant.LICENSE_PROOF.name(), DictionaryTypeItemConstant.LICENSE_PROOF_APP_KEY.name());
        appKeyOptional.ifPresent(item -> this.appKey = item);
        Optional<String> secretOptional = dictPublicService.getDetailValue(DictionaryDicTypeConstant.LICENSE_PROOF.name(), DictionaryTypeItemConstant.LICENSE_PROOF_APP_SECRET.name());
        secretOptional.ifPresent(item -> this.appSecret = item);
        Optional<String> passwordOptional = dictPublicService.getDetailValue(DictionaryDicTypeConstant.LICENSE_PROOF.name(), DictionaryTypeItemConstant.LICENSE_PROOF_APP_PASSWORD.name());
        passwordOptional.ifPresent(item -> this.password = item);
        Optional<String> accountOptional = dictPublicService.getDetailValue(DictionaryDicTypeConstant.LICENSE_PROOF.name(), DictionaryTypeItemConstant.LICENSE_PROOF_APP_ACCOUNT.name());
        accountOptional.ifPresent(item -> this.account = item);
        Optional<String> urlOptional = dictPublicService.getDetailValue(DictionaryDicTypeConstant.LICENSE_PROOF.name(), DictionaryTypeItemConstant.LICENSE_PROOF_APP_URL.name());
        urlOptional.ifPresent(item -> this.url = item);
        return this;
    }


    /**
     * Gets the value of account.
     *
     * @return the value of account
     */
    @Override
    public String getAccount() {
        return account;
    }

    /**
     * Sets the account.
     *
     * @param account account
     */
    public void setAccount(String account) {
        this.account = account;
    }

    /**
     * Gets the value of password.
     *
     * @return the value of password
     */
    @Override
    public String getPassword() {
        return password;
    }

    /**
     * Sets the password.
     *
     * @param password password
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * Gets the value of appKey.
     *
     * @return the value of appKey
     */
    @Override
    public String getAppKey() {
        return appKey;
    }

    /**
     * Sets the appKey.
     *
     * @param appKey appKey
     */
    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    /**
     * Gets the value of appSecret.
     *
     * @return the value of appSecret
     */
    @Override
    public String getAppSecret() {
        return appSecret;
    }

    /**
     * Sets the appSecret.
     *
     * @param appSecret appSecret
     */
    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    /**
     * Gets the value of url.
     *
     * @return the value of url
     */
    @Override
    public String getUrl() {
        return url;
    }

    /**
     * Sets the url.
     *
     * @param url url
     */
    public void setUrl(String url) {
        this.url = url;
    }


}
