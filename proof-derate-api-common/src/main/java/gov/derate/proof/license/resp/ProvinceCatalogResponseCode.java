package gov.derate.proof.license.resp;

import gov.license.common.api.resp.BaseResponseCode;

/**
 * 省一网共享平台目录管理 异常编码类
 *
 * <AUTHOR>
 * @date 2024/1/17
 */
public class ProvinceCatalogResponseCode extends BaseResponseCode {

    /**
     * 服务级别 查询异常
     */
    public static final ProvinceCatalogResponseCode SERVICE_QUERY_ERROR =
            new ProvinceCatalogResponseCode("2C070101000", "查询异常");
    /**
     * 省一网共享，请求异常
     */
    public static final ProvinceCatalogResponseCode PROVINCE_CATALOG_REQUEST_ERROR =
            new ProvinceCatalogResponseCode("2C070101001", "省一网共享，请求异常");
    /**
     * 省一网共享，请求业务异常
     */
    public static final ProvinceCatalogResponseCode PROVINCE_CATALOG_BIZ_REQUEST_ERROR =
            new ProvinceCatalogResponseCode("2C070101002", "省一网共享，请求业务异常");
    /**
     * 参数级别 查询异常
     */
    public static final ProvinceCatalogResponseCode PARAM_QUERY_ERROR =
            new ProvinceCatalogResponseCode("3C070101000", "查询异常");
    /**
     * 省一网共享，passid为空
     */
    public static final ProvinceCatalogResponseCode PROVINCE_CATALOG_PASSID_EMPTY =
            new ProvinceCatalogResponseCode("3C070101001", "省一网共享，passid为空");
    /**
     * 省一网共享，serviceId为空
     */
    public static final ProvinceCatalogResponseCode PROVINCE_CATALOG_SERVICE_ID_EMPTY =
            new ProvinceCatalogResponseCode("3C070101002", "省一网共享，serviceId为空");
    /**
     * 省一网共享，passToken为空
     */
    public static final ProvinceCatalogResponseCode PROVINCE_CATALOG_PASS_TOKEN_ID_EMPTY =
            new ProvinceCatalogResponseCode("3C070101003", "省一网共享，passToken为空");
    /**
     * 省一网共享，url请求为空
     */
    public static final ProvinceCatalogResponseCode PROVINCE_CATALOG_URL_CONFIG_EMPTY =
            new ProvinceCatalogResponseCode("3C070101004", "省一网共享，url请求为空");

    public ProvinceCatalogResponseCode(String code, String message) {
        super(code, message, true);
    }
}
