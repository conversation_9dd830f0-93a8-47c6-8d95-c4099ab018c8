package gov.derate.proof.license.resp;

import gov.derate.proof.license.dto.LicenseItemUnionPageDTO;

import java.util.List;

/**
 * <p>
 * 组合目录接口响应类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022年5月11日
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:lym;Date:2022年5月11日;
 */
public class LicenseItemUnionPageResponse extends LicenseBaseResponse {

    private static final long serialVersionUID = 2941870990138432663L;

    /**
     * 证照目录
     */
    private List<LicenseItemUnionPageDTO> data;

    public List<LicenseItemUnionPageDTO> getData() {
        return data;
    }

    public void setData(List<LicenseItemUnionPageDTO> data) {
        this.data = data;
    }
}
