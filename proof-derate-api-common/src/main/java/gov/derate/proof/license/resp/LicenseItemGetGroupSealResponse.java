package gov.derate.proof.license.resp;

import java.util.List;

/**
 * 电子证明-获取电子印章信息对象
 * Company: Zsoft
 * CreateDate:2024/9/20
 *
 * <AUTHOR>
 */
public class LicenseItemGetGroupSealResponse extends LicenseBaseResponse {

    private static final long serialVersionUID = 2941870990138432663L;

    /**
     * 证照目录
     */
    private List<LicenseItemGetGroupSealResp> data;

    public List<LicenseItemGetGroupSealResp> getData() {
        return data;
    }

    public void setData(List<LicenseItemGetGroupSealResp> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "LicenseItemGetGroupSealResponse{" +
                "data=" + data +
                '}';
    }
}
