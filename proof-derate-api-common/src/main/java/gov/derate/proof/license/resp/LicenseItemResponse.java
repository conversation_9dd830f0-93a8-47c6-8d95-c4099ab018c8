package gov.derate.proof.license.resp;

import gov.derate.proof.license.dto.LicenseItemDTO;

import java.util.List;

/**
 * <p>
 * 证照目录接口响应类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2020-08-10
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2020-08-10;
 */
public class LicenseItemResponse extends LicenseBaseResponse {

    private static final long serialVersionUID = 2941870990138432663L;

    /**
     * 证照目录
     */
    private List<LicenseItemDTO> data;

    public List<LicenseItemDTO> getData() {
        return data;
    }

    public void setData(List<LicenseItemDTO> data) {
        this.data = data;
    }
}
