package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.license.dto.LicenseIndexDto;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 用户出示码;获取上下文接口(show/context_code/get) 请求类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class LicenseShowAuthCodeDataResponse implements Serializable {
    /**
     * 证照摘要列表
     */
    @JsonProperty("content")
    private java.util.List<LicenseIndexDto> contextCode;
    /**
     * 用证码
     */
    @JsonProperty("auth_code")
    private String authCode;

    public List<LicenseIndexDto> getContextCode() {
        return contextCode;
    }

    public void setContextCode(List<LicenseIndexDto> contextCode) {
        this.contextCode = contextCode;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    @Override
    public String toString() {
        return "LicenseShowAuthCodeDataResponse{" +
                "contextCode=" + contextCode +
                ", authCode='" + authCode + '\'' +
                '}';
    }
}
