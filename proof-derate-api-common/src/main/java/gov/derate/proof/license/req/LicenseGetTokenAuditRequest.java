package gov.derate.proof.license.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import gov.derate.proof.account.dto.AccountInfoDto;
import gov.derate.proof.common.dto.CurrentAccountAndInfoDto;
import gov.derate.proof.license.dto.OperatorDto;
import gov.licc.func.api.auth.dto.CurrentAccountDto;
import gov.license.common.api.req.BaseRequest;
import gov.license.common.tools.jackson.JacksonUtil;

/**
 * <p>
 * 用户出示码;获取上下文接口(show/context_code/get) 请求类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class LicenseGetTokenAuditRequest extends BaseRequest {
    /**
     * 证照用证码
     * 必填
     */
    @JsonProperty("auth_code")
    private String authCode;
    /**
     * 证照标识码
     * 非必填
     */
    @JsonProperty("license_code")
    private String licenseCode;
    /**
     * 当前操作人信息
     * 必填
     */
    @JsonProperty("operator")
    private OperatorDto operator;
    /**
     * 事项编码
     * 必填
     */
    @JsonProperty("service_item_code")
    private String serviceItemCode;
    /**
     * 事项名称
     * 必填
     */
    @JsonProperty("service_item_name")
    private String serviceItemName;
    /**
     * 事项所属单位
     * 必填
     */
    @JsonProperty("service_item_org")
    private String serviceItemOrg;
    /**
     * 业务流水号
     * 必填
     */
    @JsonProperty("biz_num")
    private String bizNum;

    public LicenseGetTokenAuditRequest(String authCode, String itemCode, String itemName, String implOrgName, String serialNumber, String licenseCode, CurrentAccountAndInfoDto currentAccountAndInfoDto) {
        this.authCode = authCode;
        this.serviceItemCode = itemCode;
        this.serviceItemName = itemName;
        this.serviceItemOrg = implOrgName;
        this.licenseCode = licenseCode;
        CurrentAccountDto currentAccountDto = currentAccountAndInfoDto.getCurrentAccountDto();
        AccountInfoDto accountInfoDto = currentAccountAndInfoDto.getAccountInfoDto();

        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setAccount(currentAccountDto.getAccount().getAccount());
        operatorDto.setName(currentAccountDto.getAccount().getUserName());
        operatorDto.setIdentityNum(accountInfoDto.getIdentityNumber());
        operatorDto.setDivisionCode(accountInfoDto.getDivisionCode());
        operatorDto.setServiceOrg(accountInfoDto.getOrgName());
        operatorDto.setServiceOrgCode(accountInfoDto.getOrgCode());
        this.operator = operatorDto;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public OperatorDto getOperator() {
        return operator;
    }

    public void setOperator(OperatorDto operator) {
        this.operator = operator;
    }

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getServiceItemOrg() {
        return serviceItemOrg;
    }

    public void setServiceItemOrg(String serviceItemOrg) {
        this.serviceItemOrg = serviceItemOrg;
    }

    public String getBizNum() {
        return bizNum;
    }

    public void setBizNum(String bizNum) {
        this.bizNum = bizNum;
    }

    public String toJson() throws JsonProcessingException {
        return JacksonUtil.toJsonStr(this);
    }

    @Override
    public String toString() {
        return "LicenseGetTokenAuditRequest{" +
                "authCode='" + authCode + '\'' +
                ", licenseCode='" + licenseCode + '\'' +
                ", operator=" + operator +
                ", serviceItemCode='" + serviceItemCode + '\'' +
                ", serviceItemName='" + serviceItemName + '\'' +
                ", serviceItemOrg='" + serviceItemOrg + '\'' +
                ", bizNum='" + bizNum + '\'' +
                '}';
    }
}
