package gov.derate.proof.license.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.license.dto.OperatorDto;
import gov.license.common.api.req.BaseRequest;

/**
 * 电子证明用证接口请求传输类 v1/license/auth
 *
 * <AUTHOR>
 * @date 2023/04/10.
 */
public class LicenseAuthCodeRequest extends BaseRequest {
    private static final long serialVersionUID = 1280164437612943039L;

    /**
     * 事项编码
     */
    @JsonProperty("service_item_code")
    private String serviceItemCode;

    /**
     * 事项名称
     */
    @JsonProperty("service_item_name")
    private String serviceItemName;

    /**
     * 操作人对象
     */
    @JsonProperty("operator")
    private OperatorDto operator;

    /**
     * 持有人身份证号码
     */
    @JsonProperty("identity_number")
    private String identityNumber;

    /**
     * 证照号码
     */
    @JsonProperty("id_code")
    private String idCode;

    /**
     * 基本码
     */
    @JsonProperty("license_item_code")
    private String licenseItemCode;

    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public OperatorDto getOperator() {
        return operator;
    }

    public void setOperator(OperatorDto operator) {
        this.operator = operator;
    }

    public String getIdentityNumber() {
        return identityNumber;
    }

    public void setIdentityNumber(String identityNumber) {
        this.identityNumber = identityNumber;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getLicenseItemCode() {
        return licenseItemCode;
    }

    public void setLicenseItemCode(String licenseItemCode) {
        this.licenseItemCode = licenseItemCode;
    }
}
