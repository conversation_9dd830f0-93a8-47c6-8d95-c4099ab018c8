package gov.derate.proof.license.enums;

/**
 * <p>
 * 电子证照状态
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public enum LicenseStatus {
    /**
     * 草案
     */
    DRAFT(0, "草案"),
    /**
     * 已注册
     */
    REGISTER(1, "已注册"),
    /**
     * 已废止
     */
    ISSUED(2, "已废止"),
    /**
     * 已签发
     */
    ABOLISHED(3, "已签发"),

    ;
    /**
     * 描述
     */
    private final String describe;
    /**
     * 下标
     */
    private final Integer value;

    LicenseStatus(Integer value, String describe) {
        this.value = value;
        this.describe = describe;
    }

    public String getDescribe() {
        return describe;
    }

    public Integer getValue() {
        return value;
    }
}
