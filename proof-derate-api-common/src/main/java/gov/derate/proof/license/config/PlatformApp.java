package gov.derate.proof.license.config;

/**
 * <p>
 * 基础框架App应用
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-03-29
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-03-29;
 */
public interface PlatformApp {

    /**
     * 获取app account
     *
     * @return app account
     */
    String getAccount();

    /**
     * 获取app pwd
     *
     * @return app pwd
     */
    String getPassword();

    /**
     * 获取 app key
     *
     * @return app key
     */
    String getAppKey();

    /**
     * 获取 app secret
     *
     * @return app secret
     */
    String getAppSecret();

    /**
     * 获取 app url
     *
     * @return app url
     */
    String getUrl();

    /**
     * 获取字典配置
     * @return 字典配置对象
     */
    PlatformApp getDictConfig();

}
