package gov.derate.proof.license.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.license.enums.HolderType;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 证照目录
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-19
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-19;
 */
public class LicenseItemDTO implements Serializable {
    private static final long serialVersionUID = -4652300924717912595L;

    /**
     * 证照中文名称。对应数据证照目录中文名称。
     */
    @JsonProperty("name")
    private String name;
    /**
     * 电子证照目录版本号
     */
    @JsonProperty("version")
    private String version;
    /**
     * 证照目录状态（代码值）。参照LicenseItemStatus代码
     */
    @JsonProperty("status")
    private String status;
    /**
     * 证照类型代码（代码值）。参照LicenseType代码
     */
    @JsonProperty("type")
    private String type;
    /**
     * 备注说明
     */
    @JsonProperty("description")
    private String description;
    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;
    /**
     * 签发机构所属行政区划
     */
    @JsonProperty("division")
    private String division;
    /**
     * 管理属性：制证操作人
     */
    @JsonProperty("creator")
    private String creator;
    /**
     * 电子证照目录编码
     */
    @JsonProperty("license_item_code")
    private String licenseItemCode;
    /**
     * 证照中文简称
     */
    @JsonProperty("short_name")
    private String shortName;
    /**
     * 共享使用类型（代码值）。参照UseConstraintType代码
     */
    @JsonProperty("use_constraint_type")
    private String useConstraintType;
    /**
     * 缩略图（base64字符串）
     */
    @JsonProperty("icon_image")
    private String iconImage;
    /**
     * 证照缩略图
     */
    @JsonProperty("issue_rank")
    private String issueRank;
    /**
     * 目录编制部门名称
     */
    @JsonProperty("admin_org")
    private String adminOrg;
    /**
     * 目录编制部门组织机构代码
     */
    @JsonProperty("admin_org_code")
    private String adminOrgCode;
    /**
     * 目录编制部门行政区划代码
     */
    @JsonProperty("division_code")
    private String divisionCode;
    /**
     * 发布时间
     */
    @JsonProperty("publish_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishDate;
    /**
     * 数据结构描述
     */
    @JsonProperty("data_items")
    private Object dataItems;
    /**
     * 持证者类型（代码值）。参照HolderType代码
     */
    @JsonProperty("holder_type")
    private HolderType holderType;
    /**
     * 签发机构所属的行业部门名称
     */
    @JsonProperty("resp_org")
    private String respOrg;
    /**
     * 创建时间
     */
    @JsonProperty("creation_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creationTime;
    /**
     * 最后修改者
     */
    @JsonProperty("last_modificator")
    private String lastModificator;
    /**
     * 最后修改时间
     */
    @JsonProperty("last_modification_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastModificationTime;

    public LicenseItemDTO() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDivision() {
        return division;
    }

    public void setDivision(String division) {
        this.division = division;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getLicenseItemCode() {
        return licenseItemCode;
    }

    public void setLicenseItemCode(String licenseItemCode) {
        this.licenseItemCode = licenseItemCode;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getUseConstraintType() {
        return useConstraintType;
    }

    public void setUseConstraintType(String useConstraintType) {
        this.useConstraintType = useConstraintType;
    }

    public String getIconImage() {
        return iconImage;
    }

    public void setIconImage(String iconImage) {
        this.iconImage = iconImage;
    }

    public String getIssueRank() {
        return issueRank;
    }

    public void setIssueRank(String issueRank) {
        this.issueRank = issueRank;
    }

    public String getAdminOrg() {
        return adminOrg;
    }

    public void setAdminOrg(String adminOrg) {
        this.adminOrg = adminOrg;
    }

    public String getAdminOrgCode() {
        return adminOrgCode;
    }

    public void setAdminOrgCode(String adminOrgCode) {
        this.adminOrgCode = adminOrgCode;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public Date getPublishDate() {
        return publishDate;
    }

    public void setPublishDate(Date publishDate) {
        this.publishDate = publishDate;
    }

    public Object getDataItems() {
        return dataItems;
    }

    public void setDataItems(Object dataItems) {
        this.dataItems = dataItems;
    }

    public HolderType getHolderType() {
        return holderType;
    }

    public void setHolderType(HolderType holderType) {
        this.holderType = holderType;
    }

    public String getRespOrg() {
        return respOrg;
    }

    public void setRespOrg(String respOrg) {
        this.respOrg = respOrg;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public String getLastModificator() {
        return lastModificator;
    }

    public void setLastModificator(String lastModificator) {
        this.lastModificator = lastModificator;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    @Override
    public String toString() {
        return "LicenseItemDTO{" +
                "name='" + name + '\'' +
                ", version='" + version + '\'' +
                ", status=" + status +
                ", type=" + type +
                ", description='" + description + '\'' +
                ", remark='" + remark + '\'' +
                ", division='" + division + '\'' +
                ", creator='" + creator + '\'' +
                ", licenseItemCode='" + licenseItemCode + '\'' +
                ", shortName='" + shortName + '\'' +
                ", useConstraintType=" + useConstraintType +
                ", adminOrg='" + adminOrg + '\'' +
                ", adminOrgCode='" + adminOrgCode + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", publishDate=" + publishDate +
                ", dataItems=" + dataItems +
                ", holderType=" + holderType +
                ", respOrg='" + respOrg + '\'' +
                ", creationTime=" + creationTime +
                ", lastModificator='" + lastModificator + '\'' +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
