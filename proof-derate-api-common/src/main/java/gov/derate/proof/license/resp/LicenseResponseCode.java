package gov.derate.proof.license.resp;

import gov.license.common.api.resp.BaseResponseCode;


/**
 * 电子证照 异常编码类
 * Company: Zsoft
 * CreateDate:2024/5/24
 *
 * <AUTHOR>
 */
public class LicenseResponseCode extends BaseResponseCode {
    /**
     * 参数报错序号
     */
    private static final String PARAM_PREFIX_NUM = "3";
    /**
     * 系统报错序号
     */
    private static final String BIZ_PREFIX_NUM = "2";
    /**
     * 无证明城市序号
     */
    private static final String PROOF_DERATE_SYSTEM_PREFIX_NUM = "C02";
    /**
     * 电子证照模块序号
     */
    private static final String SYNCHRONOUS_PREFIX_NUM = "09";
    /**
     * 模块序号
     */
    private static final String MODULE_PREFIX_NUM = "01";
    /**
     * 格式。6位字符的拼接
     */
    private static final String FORMAT_STR = "%s%s%s%s%s";


    public static final LicenseResponseCode SERVICE_SYNCHRONOUS_ERROR = new LicenseResponseCode(String.format(FORMAT_STR, BIZ_PREFIX_NUM, PROOF_DERATE_SYSTEM_PREFIX_NUM, SYNCHRONOUS_PREFIX_NUM, MODULE_PREFIX_NUM, "000"), "服务未知异常");
    public static final LicenseResponseCode CALL_INTERFACE_ERROR_ERROR = new LicenseResponseCode(String.format(FORMAT_STR, BIZ_PREFIX_NUM, PROOF_DERATE_SYSTEM_PREFIX_NUM, SYNCHRONOUS_PREFIX_NUM, MODULE_PREFIX_NUM, "001"), "请求错误-%s");
    public static final LicenseResponseCode CALL_INTERFACE_BIZ_ERROR_ERROR = new LicenseResponseCode(String.format(FORMAT_STR, BIZ_PREFIX_NUM, PROOF_DERATE_SYSTEM_PREFIX_NUM, SYNCHRONOUS_PREFIX_NUM, MODULE_PREFIX_NUM, "002"), "请求业务错误-%s");
    public static final LicenseResponseCode CALL_ISSUE_BIZ_ERROR_ERROR = new LicenseResponseCode(String.format(FORMAT_STR, BIZ_PREFIX_NUM, PROOF_DERATE_SYSTEM_PREFIX_NUM, SYNCHRONOUS_PREFIX_NUM, MODULE_PREFIX_NUM, "003"), "制证签发失败-%s", true);


    public LicenseResponseCode(String code, String message) {
        super(code, message);
    }

    public LicenseResponseCode(String code, String message, boolean replaceFlag) {
        super(code, message, replaceFlag);
    }

    public BaseResponseCode formatByReplaceFlag(Object... args) {
        String msg = String.format(this.getMessage(), args);
        return new BaseResponseCode(this.getCode(), msg, this.isReplaceFlag());
    }

}
