package gov.derate.proof.license.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.license.common.api.req.BaseRequest;

/**
 * <p>
 * 组合目录请求体
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-19
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-19;
 */
public class LicenseItemUnionPageRequest extends BaseRequest {

    /**
     * 组合目录名称
     */
    @JsonProperty("union_name")
    private String unionName;
    /**
     * 组合目录短码
     */
    @JsonProperty("union_code")
    private String unionCode;
    /**
     * 组合目录所属行业部门
     */
    @JsonProperty("union_resp_org")
    private String unionRespOrg;
    /**
     * 页面大小（min:10,max:150）
     */
    @JsonProperty("page_size")
    private Integer pageSize;
    /**
     * 页码（min：1）
     */
    @JsonProperty("page_index")
    private Integer pageIndex;

    public LicenseItemUnionPageRequest() {

    }

    public LicenseItemUnionPageRequest(Integer pageSize, Integer pageIndex) {
        this.pageSize = pageSize;
        this.pageIndex = pageIndex;
    }

    public String getUnionName() {
        return unionName;
    }

    public void setUnionName(String unionName) {
        this.unionName = unionName;
    }

    public String getUnionCode() {
        return unionCode;
    }

    public void setUnionCode(String unionCode) {
        this.unionCode = unionCode;
    }

    public String getUnionRespOrg() {
        return unionRespOrg;
    }

    public void setUnionRespOrg(String unionRespOrg) {
        this.unionRespOrg = unionRespOrg;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    @Override
    public String toString() {
        return "LicenseItemUnionPageRequest{" +
                "unionName='" + unionName + '\'' +
                ", unionCode='" + unionCode + '\'' +
                ", unionRespOrg='" + unionRespOrg + '\'' +
                ", pageSize=" + pageSize +
                ", pageIndex=" + pageIndex +
                '}';
    }
}
