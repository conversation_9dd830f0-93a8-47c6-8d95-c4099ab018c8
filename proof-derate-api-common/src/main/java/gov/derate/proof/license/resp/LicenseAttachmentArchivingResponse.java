package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * 证照归档响应类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class LicenseAttachmentArchivingResponse extends LicenseBaseResponse {
    /**
     * 数据
     */
    @JsonProperty("data")
    private LicenseAttachmentArchivingDataResponse data;


    public LicenseAttachmentArchivingDataResponse getData() {
        return data;
    }

    public void setData(LicenseAttachmentArchivingDataResponse data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "LicenseAttachmentArchivingResponse{" +
                "data=" + data +
                '}';
    }
}
