package gov.derate.proof.license.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.license.common.tools.jackson.JacksonUtil;

import java.util.Map;

/**
 * 证照目录请求体
 * Company: Zsoft
 * CreateDate:2024/8/19
 *
 * <AUTHOR>
 */
public class LicenseItemImplInfoListRequest extends ListRequest {

    /**
     * 授权token
     */
    @JsonProperty("access_token")
    private String accessToken;
    /**
     * 实施部门机构部门
     */
    @JsonProperty("org_code")
    private String orgCode;
    /**
     * 实施部门行政区划代码
     */
    @JsonProperty("division_code")
    private String divisionCode;
    /**
     * 基本码
     */
    @JsonProperty("license_item_code")
    private String licenseItemCode;
    /**
     * 实施目录发布状态
     */
    @JsonProperty("implement_service_status")
    private String implementServiceStatus;
    /**
     * 分页大小
     */
    @JsonProperty("page_size")
    private Integer pageSize;
    /**
     * 分页页数
     */
    @JsonProperty("page_index")
    private Integer pageIndex;

    /**
     * 构造请求参数map
     *
     * @return Map
     */
    public Map<String, Object> buildJsonMap() {
        return JacksonUtil.toMap(JacksonUtil.toJsonStr(this), String.class, Object.class);
    }

    public LicenseItemImplInfoListRequest() {
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public String getLicenseItemCode() {
        return licenseItemCode;
    }

    public void setLicenseItemCode(String licenseItemCode) {
        this.licenseItemCode = licenseItemCode;
    }

    public String getImplementServiceStatus() {
        return implementServiceStatus;
    }

    public void setImplementServiceStatus(String implementServiceStatus) {
        this.implementServiceStatus = implementServiceStatus;
    }

    @Override
    public Integer getPageSize() {
        return pageSize;
    }

    @Override
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public Integer getPageIndex() {
        return pageIndex;
    }

    @Override
    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    @Override
    public String toString() {
        return "LicenseItemImplInfoListRequest{" +
                "accessToken='" + accessToken + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", licenseItemCode='" + licenseItemCode + '\'' +
                ", implementServiceStatus='" + implementServiceStatus + '\'' +
                ", pageSize=" + pageSize +
                ", pageIndex=" + pageIndex +
                '}';
    }
}
