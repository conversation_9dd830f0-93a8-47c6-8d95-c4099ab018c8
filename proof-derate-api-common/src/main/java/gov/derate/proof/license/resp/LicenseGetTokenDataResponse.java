package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 电子证明token接口响应传输类 v1/license/token
 *
 * <AUTHOR>
 * @date 2023/04/10.
 */
public class LicenseGetTokenDataResponse implements Serializable {
    private static final long serialVersionUID = 4781745703613462135L;

    /**
     * 电子证照访问令牌
     */
    @JsonProperty("license_access_token")
    private String licenseAccessToken;

    /**
     * 有效期
     */
    @JsonProperty("expiry_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiryDate;


    public String getLicenseAccessToken() {
        return licenseAccessToken;
    }

    public void setLicenseAccessToken(String licenseAccessToken) {
        this.licenseAccessToken = licenseAccessToken;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    @Override
    public String toString() {
        return "LicenseGetTokenDataResponse{" +
                "licenseAccessToken='" + licenseAccessToken + '\'' +
                ", expiryDate=" + expiryDate +
                '}';
    }
}
