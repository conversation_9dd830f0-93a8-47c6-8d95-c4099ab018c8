package gov.derate.proof.license.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * <p>
 * 证照摘要信息
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class LicenseIndexHolderDto extends LicenseIndexDto implements Serializable {
    /**
     * 当前证照的授权码
     */
    @JsonProperty("auth_code")
    private String authCode;

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    @Override
    public String toString() {
        return "LicenseIndexHolderDto{" +
                "authCode='" + authCode + '\'' +
                '}';
    }
}
