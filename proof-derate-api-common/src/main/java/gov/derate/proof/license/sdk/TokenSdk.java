package gov.derate.proof.license.sdk;

import gov.derate.proof.license.config.PlatformApp;
import gov.derate.proof.license.exception.LicenseException;

/**
 * <p>
 * 基础框架令牌服务包
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2020-08-10
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2020-08-10;
 */
public interface TokenSdk {

    /**
     * 获取基础框架的接口访问令牌 默认一周内自动刷新接口令牌
     *
     * @param platformApp platformApp 基础框架App应用
     * @return 接口访问令牌
     * @throws LicenseException 异常
     */
    String getAccessToken(PlatformApp platformApp) throws LicenseException;

}
