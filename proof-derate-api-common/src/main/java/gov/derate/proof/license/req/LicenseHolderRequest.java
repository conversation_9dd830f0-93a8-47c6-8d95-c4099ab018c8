package gov.derate.proof.license.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.license.dto.OperatorDto;
import gov.license.common.api.req.BaseRequest;


/**
 * 电子证照-持有人授权用证接口(p/v1/license/holder接口地址)  请求类
 * Company: Zsoft
 * CreateDate:2024/5/13
 *
 * <AUTHOR>
 */
public class LicenseHolderRequest extends BaseRequest {
    /**
     * 事项编码
     * 必填
     */
    @JsonProperty("service_item_code")
    private String serviceItemCode;
    /**
     * 事项名称
     * 必填
     */
    @JsonProperty("service_item_name")
    private String serviceItemName;
    /**
     * 免证办-江西政务服务码-解码接口ywxx.code字段
     */
    @JsonProperty("credential")
    private String credential;
    /**
     * 免证办-该单的身份证号码
     */
    @JsonProperty("identity_number")
    private String identityNumber;
    /**
     * 全省目录基本码
     */
    @JsonProperty("license_item_code")
    private String licenseItemCode;
    /**
     * 验证码 ：默认 033
     */
    @JsonProperty("cas_no")
    private String casNo = "033";
    /**
     * 窗口人员信息(详附录)
     * 必填
     */
    @JsonProperty("operator")
    private OperatorDto operator;


    public String getServiceItemCode() {
        return serviceItemCode;
    }

    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    public String getServiceItemName() {
        return serviceItemName;
    }

    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    public String getCredential() {
        return credential;
    }

    public void setCredential(String credential) {
        this.credential = credential;
    }

    public String getIdentityNumber() {
        return identityNumber;
    }

    public void setIdentityNumber(String identityNumber) {
        this.identityNumber = identityNumber;
    }

    public String getLicenseItemCode() {
        return licenseItemCode;
    }

    public void setLicenseItemCode(String licenseItemCode) {
        this.licenseItemCode = licenseItemCode;
    }

    public String getCasNo() {
        return casNo;
    }

    public void setCasNo(String casNo) {
        this.casNo = casNo;
    }

    public OperatorDto getOperator() {
        return operator;
    }

    public void setOperator(OperatorDto operator) {
        this.operator = operator;
    }

    @Override
    public String toString() {
        return "LicenseHolderRequest{" +
                "serviceItemCode='" + serviceItemCode + '\'' +
                ", serviceItemName='" + serviceItemName + '\'' +
                ", credential='" + credential + '\'' +
                ", identityNumber='" + identityNumber + '\'' +
                ", licenseItemCode='" + licenseItemCode + '\'' +
                ", casNo='" + casNo + '\'' +
                ", operator=" + operator +
                '}';
    }
}
