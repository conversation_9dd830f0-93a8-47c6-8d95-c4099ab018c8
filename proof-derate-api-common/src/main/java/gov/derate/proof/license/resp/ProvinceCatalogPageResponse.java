package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <p>
 * 省一网共享平台目录接口分页出参
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/11/20
 * </p>
 *
 * <AUTHOR>
 */
public class ProvinceCatalogPageResponse<T> {
    /**
     * 列表数据
     */
    @JsonProperty("records")
    private List<T> records;
    /**
     * 分页元素总数
     */
    @JsonProperty("total")
    private Integer total;
    /**
     * 分页元素当前页
     */
    @JsonProperty("current")
    private Integer current;
    /**
     * 分页元素页
     */
    @JsonProperty("pages")
    private Integer pages;

    /**
     * 分页元素size
     */
    @JsonProperty("size")
    private Integer size;

    public List<T> getRecords() {
        return records;
    }

    public void setRecords(List<T> records) {
        this.records = records;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getCurrent() {
        return current;
    }

    public void setCurrent(Integer current) {
        this.current = current;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    @Override
    public String toString() {
        return "ProvinceCatalogPageResponse{" +
                "records=" + records +
                ", total=" + total +
                ", current=" + current +
                ", pages=" + pages +
                ", size=" + size +
                '}';
    }
}
