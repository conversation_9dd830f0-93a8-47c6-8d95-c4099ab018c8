package gov.derate.proof.license.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * <p>
 * 组合目录分页DTO
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-19
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-19;
 */
public class LicenseItemUnionPageDTO implements Serializable {

    /**
     * 组合目录名称
     */
    @JsonProperty("union_name")
    private String unionName;
    /**
     * 组合目录短码
     */
    @JsonProperty("union_code")
    private String unionCode;
    /**
     * 组合名称所属行业部门
     */
    @JsonProperty("union_resp_org")
    private String unionRespOrg;
    /**
     * 关联证照名称
     */
    @JsonProperty("relation_license_name")
    private String relationLicenseName;
    /**
     * （多个用英文逗号隔开）
     */
    @JsonProperty("relation_license_resp_org")
    private String relationLicenseRespOrg;
    /**
     * 关联证照所属行业部门
     */
    @JsonProperty("relation_license_basic_code")
    private String relationLicenseBasicCode;

    public String getUnionName() {
        return unionName;
    }

    public void setUnionName(String unionName) {
        this.unionName = unionName;
    }

    public String getUnionCode() {
        return unionCode;
    }

    public void setUnionCode(String unionCode) {
        this.unionCode = unionCode;
    }

    public String getUnionRespOrg() {
        return unionRespOrg;
    }

    public void setUnionRespOrg(String unionRespOrg) {
        this.unionRespOrg = unionRespOrg;
    }

    public String getRelationLicenseName() {
        return relationLicenseName;
    }

    public void setRelationLicenseName(String relationLicenseName) {
        this.relationLicenseName = relationLicenseName;
    }

    public String getRelationLicenseRespOrg() {
        return relationLicenseRespOrg;
    }

    public void setRelationLicenseRespOrg(String relationLicenseRespOrg) {
        this.relationLicenseRespOrg = relationLicenseRespOrg;
    }

    public String getRelationLicenseBasicCode() {
        return relationLicenseBasicCode;
    }

    public void setRelationLicenseBasicCode(String relationLicenseBasicCode) {
        this.relationLicenseBasicCode = relationLicenseBasicCode;
    }

    @Override
    public String toString() {
        return "LicenseItemUnionPageDTO{" +
                "unionName='" + unionName + '\'' +
                ", unionCode='" + unionCode + '\'' +
                ", unionRespOrg='" + unionRespOrg + '\'' +
                ", relationLicenseName='" + relationLicenseName + '\'' +
                ", relationLicenseRespOrg='" + relationLicenseRespOrg + '\'' +
                ", relationLicenseBasicCode='" + relationLicenseBasicCode + '\'' +
                '}';
    }
}
