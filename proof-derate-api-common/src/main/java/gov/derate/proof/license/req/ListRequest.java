package gov.derate.proof.license.req;

import java.io.Serializable;

/**
 * <p>
 * 列表请求参数
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-20
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-20;
 */
public class ListRequest implements Serializable {

    protected static final int MAX_PAGE_SIZE = 150;
    protected static final int DEFAULT_PAGE_SIZE = 10;
    protected static final int DEFAULT_PAGE_INDEX = 1;

    /**
     * 页面大小
     */
    protected Integer pageSize;
    /**
     * 页码
     */
    protected Integer pageIndex;

    /**
     * Gets the value of 页面大小.
     *
     * @return the value of 页面大小
     */
    public Integer getPageSize() {
        return pageSize == null ? DEFAULT_PAGE_SIZE : pageSize >= MAX_PAGE_SIZE ? MAX_PAGE_SIZE : pageSize;
    }

    /**
     * Sets the 页面大小.
     *
     * @param pageSize 页面大小
     */
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * Gets the value of 页码.
     *
     * @return the value of 页码
     */
    public Integer getPageIndex() {
        return pageIndex == null ? DEFAULT_PAGE_INDEX : pageIndex;
    }

    /**
     * Sets the 页码.
     *
     * @param pageIndex 页码
     */
    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }
}
