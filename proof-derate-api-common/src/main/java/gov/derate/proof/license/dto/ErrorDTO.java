package gov.derate.proof.license.dto;

import gov.derate.proof.license.enums.Severity;

import java.io.Serializable;

/**
 * <p>
 * 错误信息
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2020-08-11
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2020-08-11;
 */
public class ErrorDTO implements Serializable {

    public ErrorDTO() {

    }

    public ErrorDTO(String message) {
        this.message = message;
        this.severity = Severity.ERROR;
    }

    public ErrorDTO(String code, String name, String message) {
        this.name = name;
        this.code = code;
        this.message = message;
        this.severity = Severity.ERROR;
    }

    public ErrorDTO(String code, String name, String message, String innerCode) {
        this.name = name;
        this.code = code;
        this.message = message;
        this.severity = Severity.ERROR;
        this.innerCode = innerCode;
    }

    private String name;

    /**
     * 获取属性：错误名称
     *
     * @return name 错误名称
     */
    public String getName() {
        return this.name;
    }

    /**
     * 属性设置：错误名称
     *
     * @param name 错误名称
     */
    public void setName(String name) {
        this.name = name;
    }

    private String code;

    /**
     * 获取属性：错误代码
     *
     * @return code 错误代码
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 属性设置：错误代码
     *
     * @param code 错误代码
     */
    public void setCode(String code) {
        this.code = code;
    }

    private Severity severity;

    /**
     * 获取属性：错误级别
     *
     * @return severity 错误级别
     */
    public Severity getSeverity() {
        return this.severity;
    }

    /**
     * 属性设置：错误级别
     *
     * @param severity 错误级别
     */
    public void setSeverity(Severity severity) {
        this.severity = severity;
    }

    private String message;

    /**
     * 获取属性：错误信息
     *
     * @return message 错误信息
     */
    public String getMessage() {
        return this.message;
    }

    /**
     * 属性设置：错误信息
     *
     * @param message 错误信息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    private String innerCode;

    /**
     * 获取属性：内部错误码 (innerCode)
     *
     * @return innerCode 内部错误码 (innerCode)
     */
    public String getInnerCode() {
        return this.innerCode;
    }

    /**
     * 设置属性：内部错误码 (innerCode)
     *
     * @param innerCode 内部错误码 (innerCode)
     */
    public void setInnerCode(String innerCode) {
        this.innerCode = innerCode;
    }

    @Override
    public String toString() {
        return "ErrorDTO{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", severity=" + severity +
                ", message='" + message + '\'' +
                ", innerCode='" + innerCode + '\'' +
                '}';
    }
}
