package gov.derate.proof.license.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 组合目录
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-19
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-19;
 */
public class LicenseItemUnionDTO implements Serializable {

    /**
     * 中文名称
     */
    private String name;
    /**
     * 拼音缩写
     */
    private String code;
    /**
     * 包含类型
     */
    private String contain;
    /**
     * 行业部门
     */
    @JsonProperty("resp_org")
    private String respOrg;
    /**
     * 基本码
     */
    @JsonProperty("basic_code")
    private String basicCode;
    /**
     * 数据项
     */
    private List<LicenseItemDataUnionDTO> items;

    /**
     * Gets the value of 中文名称.
     *
     * @return the value of 中文名称
     */
    public String getName() {
        return name;
    }

    /**
     * Sets the 中文名称.
     *
     * @param name 中文名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Gets the value of 拼音缩写.
     *
     * @return the value of 拼音缩写
     */
    public String getCode() {
        return code;
    }

    /**
     * Sets the 拼音缩写.
     *
     * @param code 拼音缩写
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * Gets the value of 包含类型.
     *
     * @return the value of 包含类型
     */
    public String getContain() {
        return contain;
    }

    /**
     * Sets the 包含类型.
     *
     * @param contain 包含类型
     */
    public void setContain(String contain) {
        this.contain = contain;
    }

    /**
     * Gets the value of 行业部门.
     *
     * @return the value of 行业部门
     */
    public String getRespOrg() {
        return respOrg;
    }

    /**
     * Sets the 行业部门.
     *
     * @param respOrg 行业部门
     */
    public void setRespOrg(String respOrg) {
        this.respOrg = respOrg;
    }

    /**
     * Gets the value of 基本码.
     *
     * @return the value of 基本码
     */
    public String getBasicCode() {
        return basicCode;
    }

    /**
     * Sets the 基本码.
     *
     * @param basicCode 基本码
     */
    public void setBasicCode(String basicCode) {
        this.basicCode = basicCode;
    }

    /**
     * Gets the value of 数据项.
     *
     * @return the value of 数据项
     */
    public List<LicenseItemDataUnionDTO> getItems() {
        return items;
    }

    /**
     * Sets the 数据项.
     *
     * @param items 数据项
     */
    public void setItems(List<LicenseItemDataUnionDTO> items) {
        this.items = items;
    }

    @Override
    public String toString() {
        return "LicenseItemUnionDTO{" +
                "name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", contain='" + contain + '\'' +
                ", respOrg='" + respOrg + '\'' +
                ", basicCode='" + basicCode + '\'' +
                ", items=" + items +
                '}';
    }
}
