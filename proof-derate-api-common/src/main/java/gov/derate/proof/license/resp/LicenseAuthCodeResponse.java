package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 电子证明用证接口响应传输类 v1/license/auth
 *
 * <AUTHOR>
 * @date 2023/04/10.
 */
public class LicenseAuthCodeResponse extends LicenseBaseResponse {
    private static final long serialVersionUID = -6487618329632891247L;

    /**
     * 数据
     */
    @JsonProperty("data")
    private List<LicenseAuthCodeDataResponse> data;

    /**
     * 用证码
     */
    @JsonProperty("auth_codes")
    private List<String> authCodes;

    public List<LicenseAuthCodeDataResponse> getData() {
        return data;
    }

    public void setData(List<LicenseAuthCodeDataResponse> data) {
        this.data = data;
    }

    public List<String> getAuthCodes() {
        return authCodes;
    }

    public void setAuthCodes(List<String> authCodes) {
        this.authCodes = authCodes;
    }
}
