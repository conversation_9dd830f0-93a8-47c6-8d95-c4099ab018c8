package gov.derate.proof.license.exception;

import gov.license.common.api.exception.ApplicationServiceException;
import gov.license.common.api.resp.BaseResponseCode;

/**
 * <p>
 * 电子证照异常
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2020-08-10
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2020-08-10;
 */
public class LicenseException extends ApplicationServiceException {
    private final BaseResponseCode responseCode;

    public LicenseException(BaseResponseCode causeResponseCode, Throwable throwable) {
        super(throwable);
        responseCode = causeResponseCode;
    }

    public LicenseException(BaseResponseCode causeResponseCode) {
        super();
        responseCode = causeResponseCode;
    }

    @Override
    public BaseResponseCode getResponseCode() {
        return responseCode;
    }

}
