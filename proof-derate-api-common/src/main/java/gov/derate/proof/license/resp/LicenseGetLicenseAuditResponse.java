package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * 证照提取受理审批证照数据响应类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class LicenseGetLicenseAuditResponse extends LicenseBaseResponse {
    /**
     * 数据
     */
    @JsonProperty("data")
    private LicenseGetLicenseAuditItemResponse data;


    public LicenseGetLicenseAuditItemResponse getData() {
        return data;
    }

    public void setData(LicenseGetLicenseAuditItemResponse data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "LicenseGetLicenseAuditResponse{" +
                "data=" + data +
                '}';
    }
}
