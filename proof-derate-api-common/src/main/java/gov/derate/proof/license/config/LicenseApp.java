package gov.derate.proof.license.config;

import gov.derate.proof.dictionary.entity.DictionaryTypeItemConstant;
import gov.licc.func.api.amp.service.DictPublicService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <p>
 * 电子证照接入应用信息
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2020-08-10
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2020-08-10;
 */
@Component
public class LicenseApp implements PlatformApp, InitializingBean {
    /**
     * 字典服务类
     */
    @Autowired
    private DictPublicService dictPublicService;
    /**
     * 应用账号
     */
    private String account;
    /**
     * 应用账号密码
     */
    private String password;
    /**
     * 应用app key
     */
    private String appKey;
    /**
     * 应用app 密码
     */
    private String appSecret;
    /**
     * 应用url
     */
    private String url;

    @Override
    public void afterPropertiesSet() throws Exception {
        getDictConfig();
    }

    /**
     * 获取字典配置值
     *
     * @return 配置对象
     */
    @Override
    public LicenseApp getDictConfig() {
        Optional<String> appKeyOptional = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_APP_KEY.getDictType().name(), DictionaryTypeItemConstant.LICENSE_APP_KEY.name());
        appKeyOptional.ifPresent(item -> this.appKey = item);
        Optional<String> secretOptional = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_APP_SECRET.getDictType().name(), DictionaryTypeItemConstant.LICENSE_APP_SECRET.name());
        secretOptional.ifPresent(item -> this.appSecret = item);
        Optional<String> passwordOptional = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_APP_PASSWORD.getDictType().name(), DictionaryTypeItemConstant.LICENSE_APP_PASSWORD.name());
        passwordOptional.ifPresent(item -> this.password = item);
        Optional<String> accountOptional = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_APP_ACCOUNT.getDictType().name(), DictionaryTypeItemConstant.LICENSE_APP_ACCOUNT.name());
        accountOptional.ifPresent(item -> this.account = item);
        Optional<String> urlOptional = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_APP_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_APP_URL.name());
        urlOptional.ifPresent(item -> this.url = item);
        return this;
    }

    /**
     * Gets the value of account.
     *
     * @return the value of account
     */
    @Override
    public String getAccount() {
        return account;
    }

    /**
     * Sets the account.
     *
     * @param account account
     */
    public void setAccount(String account) {
        this.account = account;
    }

    /**
     * Gets the value of password.
     *
     * @return the value of password
     */
    @Override
    public String getPassword() {
        return password;
    }

    /**
     * Sets the password.
     *
     * @param password password
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * Gets the value of appKey.
     *
     * @return the value of appKey
     */
    @Override
    public String getAppKey() {
        return appKey;
    }

    /**
     * Sets the appKey.
     *
     * @param appKey appKey
     */
    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    /**
     * Gets the value of appSecret.
     *
     * @return the value of appSecret
     */
    @Override
    public String getAppSecret() {
        return appSecret;
    }

    /**
     * Sets the appSecret.
     *
     * @param appSecret appSecret
     */
    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    /**
     * Gets the value of url.
     *
     * @return the value of url
     */
    @Override
    public String getUrl() {
        return url;
    }

    /**
     * Sets the url.
     *
     * @param url url
     */
    public void setUrl(String url) {
        this.url = url;
    }


}
