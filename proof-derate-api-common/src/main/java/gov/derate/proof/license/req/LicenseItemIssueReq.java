package gov.derate.proof.license.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.license.dto.OperatorDto;
import gov.license.common.tools.jackson.JacksonUtil;

import java.util.Map;

/**
 * 电子证明-issue接口请求类
 * Company: Zsoft
 * CreateDate:2024/9/20
 *
 * <AUTHOR>
 */
public class LicenseItemIssueReq {
    @JsonProperty("data")
    private DataBean data;

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public String toJsonParam() {
        return JacksonUtil.toJsonStr(this);
    }

    /**
     * 参数对象静态类
     */
    public static class DataBean {
        @JsonProperty("service_item_code")
        private String serviceItemCode;
        @JsonProperty("service_item_name")
        private String serviceItemName;
        @JsonProperty("license_group")
        private String licenseGroup;
        @JsonProperty("biz_num")
        private String bizNum;
        @JsonProperty("seal_code")
        private String sealCode;
        @JsonProperty("operator")
        private OperatorDto operator;
        @JsonProperty("data_fields")
        private Map<String, String> dataFields;

        public String getServiceItemCode() {
            return serviceItemCode;
        }

        public void setServiceItemCode(String serviceItemCode) {
            this.serviceItemCode = serviceItemCode;
        }

        public String getServiceItemName() {
            return serviceItemName;
        }

        public void setServiceItemName(String serviceItemName) {
            this.serviceItemName = serviceItemName;
        }

        public String getLicenseGroup() {
            return licenseGroup;
        }

        public void setLicenseGroup(String licenseGroup) {
            this.licenseGroup = licenseGroup;
        }

        public String getBizNum() {
            return bizNum;
        }

        public void setBizNum(String bizNum) {
            this.bizNum = bizNum;
        }

        public String getSealCode() {
            return sealCode;
        }

        public void setSealCode(String sealCode) {
            this.sealCode = sealCode;
        }

        public OperatorDto getOperator() {
            return operator;
        }

        public void setOperator(OperatorDto operator) {
            this.operator = operator;
        }

        public Map<String, String> getDataFields() {
            return dataFields;
        }

        public void setDataFields(Map<String, String> dataFields) {
            this.dataFields = dataFields;
        }

        @Override
        public String toString() {
            return "DataBean{" +
                    "serviceItemCode='" + serviceItemCode + '\'' +
                    ", serviceItemName='" + serviceItemName + '\'' +
                    ", licenseGroup='" + licenseGroup + '\'' +
                    ", bizNum='" + bizNum + '\'' +
                    ", sealCode='" + sealCode + '\'' +
                    ", operator=" + operator +
                    ", dataFields=" + dataFields +
                    '}';
        }
    }
}
