package gov.derate.proof.license.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import gov.license.common.tools.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * 证明目录-电子证明-实施码信息dto类
 * <p>
 * Company: Zsoft
 * CreateDate:2024/8/8
 *
 * <AUTHOR>
 */
public class ImplementLicenseItemInfoDto {
    /**
     * 证照中文名称。对应数据证照目录中文名称
     */
    @JsonProperty("name")
    private String name;
    /**
     * 实施机构名称
     */
    @JsonProperty("implement_org")
    private String implementOrg;
    /**
     * 目录实施码
     */
    @JsonProperty("implement_code")
    private String implementCode;
    /**
     * 服务上线状态 OFFLINE :未上线 ONLINE :已上线 STOP :已暂停 CANCEL :已撤销 DEP_CANCEL :单位注销
     */
    @JsonProperty("implement_service_status")
    private String implementServiceStatus;
    /**
     * 上线/撤销时间
     */
    @JsonProperty("open_or_cancel_time")
    private String openOrCancelTime;
    /**
     * 开通目录数据标准
     */
    @JsonProperty("data_items")
    private String dataItems;
    /**
     * 证照缩略图
     */
    @JsonProperty("icon_image")
    @JsonIgnore
    private String iconImage;

    /**
     * 获取basicCode
     *
     * @return 根据实施码截取的basicCode
     */
    public String buildBasicCode() {
        return this.implementCode.length() >= 9 ? this.implementCode.substring(0, 9) : this.implementCode;
    }

    /**
     * 获取实施部门编码
     *
     * @return 根据实施码截取的basicCode
     */
    public String buildImplementOrgCode() {
        return this.implementCode.length() >= 18 ? this.implementCode.substring(9, 18) : this.implementCode.substring(9);
    }

    /**
     * 获取获取实施部门行政区划代码
     *
     * @return 根据实施码截取的basicCode
     */
    public String getImplementDivisionCode() {
        return this.implementCode.length() >= 18 ? this.implementCode.substring(18) : this.implementCode;
    }

    /**
     * 获取数据项对象
     *
     * @return 数据项对象
     */
    public Optional<LicenseItemDataItemInfoDto> buildDataItemDto() {
        if (StringUtils.isNotBlank(this.dataItems)) {
            return Optional.ofNullable(JacksonUtil.toBean(this.dataItems, LicenseItemDataItemInfoDto.class));
        }
        return Optional.empty();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImplementOrg() {
        return implementOrg;
    }

    public void setImplementOrg(String implementOrg) {
        this.implementOrg = implementOrg;
    }

    public String getImplementCode() {
        return implementCode;
    }

    public void setImplementCode(String implementCode) {
        this.implementCode = implementCode;
    }

    public String getImplementServiceStatus() {
        return implementServiceStatus;
    }

    public void setImplementServiceStatus(String implementServiceStatus) {
        this.implementServiceStatus = implementServiceStatus;
    }

    public String getOpenOrCancelTime() {
        return openOrCancelTime;
    }

    public void setOpenOrCancelTime(String openOrCancelTime) {
        this.openOrCancelTime = openOrCancelTime;
    }

    public String getDataItems() {
        return dataItems;
    }

    public void setDataItems(String dataItems) {
        this.dataItems = dataItems;
    }

    public String getIconImage() {
        return iconImage;
    }

    public void setIconImage(String iconImage) {
        this.iconImage = iconImage;
    }

    @Override
    public String toString() {
        return "ImplementLicenseItemInfoDto{" +
                "name='" + name + '\'' +
                ", implementOrg='" + implementOrg + '\'' +
                ", implementCode='" + implementCode + '\'' +
                ", implementServiceStatus='" + implementServiceStatus + '\'' +
                ", openOrCancelTime='" + openOrCancelTime + '\'' +
                ", dataItems='" + dataItems + '\'' +
                ", iconImage='" + iconImage + '\'' +
                '}';
    }

    /**
     * 电子证照-dataItem解析对象
     */
    public static class LicenseItemDataItemInfoDto {
        @JsonProperty("columns")
        private String columns;
        @JsonProperty("form_prefix")
        private String formPrefix;
        @JsonProperty("items")
        private List<LicenseItemDataItemDetailDto> items;

        public String getColumns() {
            return columns;
        }

        public void setColumns(String columns) {
            this.columns = columns;
        }

        public String getFormPrefix() {
            return formPrefix;
        }

        public void setFormPrefix(String formPrefix) {
            this.formPrefix = formPrefix;
        }

        public List<LicenseItemDataItemDetailDto> getItems() {
            return items;
        }

        public void setItems(List<LicenseItemDataItemDetailDto> items) {
            this.items = items;
        }

        @Override
        public String toString() {
            return "LicenseItemDataItemInfoDto{" +
                    "columns='" + columns + '\'' +
                    ", formPrefix='" + formPrefix + '\'' +
                    ", items=" + items +
                    '}';
        }
    }

    /**
     * 电子证照-具体数据项值对象
     */
    public static class LicenseItemDataItemDetailDto {
        @JsonProperty("item_id")
        private String itemId;
        @JsonProperty("key")
        private String key;
        @JsonProperty("label")
        private String label;
        @JsonProperty("type_code")
        private String typeCode;
        @JsonProperty("index")
        private String index;
        @JsonProperty("colspan")
        private String colspan;
        @JsonProperty("placeholder")
        private String placeholder;
        @JsonProperty("data_type")
        private String dataType;
        @JsonProperty("length")
        private String length;
        @JsonProperty("precision")
        private String precision;
        @JsonProperty("is_allow_null")
        private String isAllowNull;
        @JsonProperty("example")
        private String example;
        @JsonProperty("file_suffix")
        private String fileSuffix;
        @JsonProperty("file_data")
        private String fileData;
        @JsonProperty("addon")
        private String addon;
        @JsonProperty("rows")
        private String rows;
        @JsonProperty("options")
        private String options;
        @JsonProperty("head")
        private String head;
        @JsonProperty("is_init_item")
        private boolean isInitItem;
        @JsonProperty("cols")
        private String cols;
        @JsonProperty("date_view_format")
        private String dateViewFormat;
        @JsonProperty("is_show")
        private String isShow;
        @JsonProperty("is_sensitive")
        private String isSensitive;
        @JsonProperty("is_sensitive_rule")
        private String isSensitiveRule;
        @JsonProperty("default_value")
        private String defaultValue;

        public String getItemId() {
            return itemId;
        }

        public void setItemId(String itemId) {
            this.itemId = itemId;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getTypeCode() {
            return typeCode;
        }

        public void setTypeCode(String typeCode) {
            this.typeCode = typeCode;
        }

        public String getIndex() {
            return index;
        }

        public void setIndex(String index) {
            this.index = index;
        }

        public String getColspan() {
            return colspan;
        }

        public void setColspan(String colspan) {
            this.colspan = colspan;
        }

        public String getPlaceholder() {
            return placeholder;
        }

        public void setPlaceholder(String placeholder) {
            this.placeholder = placeholder;
        }

        public String getDataType() {
            return dataType;
        }

        public void setDataType(String dataType) {
            this.dataType = dataType;
        }

        public String getLength() {
            return length;
        }

        public void setLength(String length) {
            this.length = length;
        }

        public String getPrecision() {
            return precision;
        }

        public void setPrecision(String precision) {
            this.precision = precision;
        }

        public String getIsAllowNull() {
            return isAllowNull;
        }

        public void setIsAllowNull(String isAllowNull) {
            this.isAllowNull = isAllowNull;
        }

        public String getExample() {
            return example;
        }

        public void setExample(String example) {
            this.example = example;
        }

        public String getFileSuffix() {
            return fileSuffix;
        }

        public void setFileSuffix(String fileSuffix) {
            this.fileSuffix = fileSuffix;
        }

        public String getFileData() {
            return fileData;
        }

        public void setFileData(String fileData) {
            this.fileData = fileData;
        }

        public String getAddon() {
            return addon;
        }

        public void setAddon(String addon) {
            this.addon = addon;
        }

        public String getRows() {
            return rows;
        }

        public void setRows(String rows) {
            this.rows = rows;
        }

        public String getOptions() {
            return options;
        }

        public void setOptions(String options) {
            this.options = options;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public boolean isInitItem() {
            return isInitItem;
        }

        public void setInitItem(boolean initItem) {
            isInitItem = initItem;
        }

        public String getCols() {
            return cols;
        }

        public void setCols(String cols) {
            this.cols = cols;
        }

        public String getDateViewFormat() {
            return dateViewFormat;
        }

        public void setDateViewFormat(String dateViewFormat) {
            this.dateViewFormat = dateViewFormat;
        }

        public String getIsShow() {
            return isShow;
        }

        public void setIsShow(String isShow) {
            this.isShow = isShow;
        }

        public String getIsSensitive() {
            return isSensitive;
        }

        public void setIsSensitive(String isSensitive) {
            this.isSensitive = isSensitive;
        }

        public String getIsSensitiveRule() {
            return isSensitiveRule;
        }

        public void setIsSensitiveRule(String isSensitiveRule) {
            this.isSensitiveRule = isSensitiveRule;
        }

        public String getDefaultValue() {
            return defaultValue;
        }

        public void setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
        }

        @Override
        public String toString() {
            return "LicenseItemDataItemDetailDto{" +
                    "itemId='" + itemId + '\'' +
                    ", key='" + key + '\'' +
                    ", label='" + label + '\'' +
                    ", typeCode='" + typeCode + '\'' +
                    ", index='" + index + '\'' +
                    ", colspan='" + colspan + '\'' +
                    ", placeholder='" + placeholder + '\'' +
                    ", dataType='" + dataType + '\'' +
                    ", length='" + length + '\'' +
                    ", precision='" + precision + '\'' +
                    ", isAllowNull='" + isAllowNull + '\'' +
                    ", example='" + example + '\'' +
                    ", fileSuffix='" + fileSuffix + '\'' +
                    ", fileData='" + fileData + '\'' +
                    ", addon='" + addon + '\'' +
                    ", rows='" + rows + '\'' +
                    ", options='" + options + '\'' +
                    ", head='" + head + '\'' +
                    ", isInitItem=" + isInitItem +
                    ", cols='" + cols + '\'' +
                    ", dateViewFormat='" + dateViewFormat + '\'' +
                    ", isShow='" + isShow + '\'' +
                    ", isSensitive='" + isSensitive + '\'' +
                    ", isSensitiveRule='" + isSensitiveRule + '\'' +
                    ", defaultValue='" + defaultValue + '\'' +
                    '}';
        }
    }
}
