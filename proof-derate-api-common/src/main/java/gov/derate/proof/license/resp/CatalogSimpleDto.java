package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * 省一网共享平台目录接口出参
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/11/20
 * </p>
 *
 * <AUTHOR>
 */
public class CatalogSimpleDto {
    /**
     * 目录编码；基本码9位
     */
    @JsonProperty("catalogCode")
    private String catalogCode;
    /**
     * 目录名称
     */
    @JsonProperty("catalogName")
    private String catalogName;

    public String getCatalogCode() {
        return catalogCode;
    }

    public void setCatalogCode(String catalogCode) {
        this.catalogCode = catalogCode;
    }

    public String getCatalogName() {
        return catalogName;
    }

    public void setCatalogName(String catalogName) {
        this.catalogName = catalogName;
    }

    @Override
    public String toString() {
        return "CatalogSimpleDto{" +
                "catalogCode='" + catalogCode + '\'' +
                ", catalogName='" + catalogName + '\'' +
                '}';
    }
}
