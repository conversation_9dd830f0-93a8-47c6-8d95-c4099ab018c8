package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * 用户出示码;获取上下文接口(show/context_code/get) 请求类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class LicenseGetTokenAuditResponse extends LicenseBaseResponse {
    /**
     * 数据
     */
    @JsonProperty("data")
    private LicenseGetTokenAuditDataResponse data;


    public LicenseGetTokenAuditDataResponse getData() {
        return data;
    }

    public void setData(LicenseGetTokenAuditDataResponse data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "LicenseGetTokenAuditResponse{" +
                "data=" + data +
                '}';
    }
}
