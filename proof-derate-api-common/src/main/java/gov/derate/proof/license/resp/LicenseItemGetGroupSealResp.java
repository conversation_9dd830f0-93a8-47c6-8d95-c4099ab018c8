package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * 电子证明-查询组别印章接口响应类
 * Company: Zsoft
 * CreateDate:2024/9/20
 *
 * <AUTHOR>
 */
public class LicenseItemGetGroupSealResp {

    /**
     * 印章组别
     */
    @JsonProperty("license_group")
    private String licenseGroup;
    /**
     * 印章列表
     */
    @JsonProperty("seal_list")
    private List<SealListResp> sealList;

    /**
     * 获取当前组别第一个印章编码
     *
     * @return 第一个印章编码Optional
     */
    public Optional<String> getFirstSealCode() {
        if (CollectionUtils.isNotEmpty(this.sealList)) {
            return this.sealList.stream().filter(item -> CollectionUtils.isNotEmpty(item.electronicSeal))
                    .flatMap(item -> item.electronicSeal.stream())
                    .map(item -> item.sealCode)
                    .findFirst();
        }
        return Optional.empty();
    }

    /**
     * 印章列表对象
     */
    public static class SealListResp {
        /**
         * 印章位置
         */
        @JsonProperty("seal_position")
        private String sealPosition;
        /**
         * 印章信息位置
         */
        @JsonProperty("electronic_seal")
        private List<ElectronicSealResp> electronicSeal;

        public String getSealPosition() {
            return sealPosition;
        }

        public void setSealPosition(String sealPosition) {
            this.sealPosition = sealPosition;
        }

        public List<ElectronicSealResp> getElectronicSeal() {
            return electronicSeal;
        }

        public void setElectronicSeal(List<ElectronicSealResp> electronicSeal) {
            this.electronicSeal = electronicSeal;
        }

        @Override
        public String toString() {
            return "SealListResp{" +
                    "sealPosition='" + sealPosition + '\'' +
                    ", electronicSeal=" + electronicSeal +
                    '}';
        }

        /**
         * 印章信息信息对象
         */
        public static class ElectronicSealResp {
            /**
             * 印章名称
             */
            @JsonProperty("seal_name")
            private String sealName;
            /**
             * 印章编码
             */
            @JsonProperty("seal_code")
            private String sealCode;
            /**
             * 印章部门
             * 组织机构代码，9位
             */
            @JsonProperty("seal_org")
            private String sealOrg;

            public String getSealName() {
                return sealName;
            }

            public void setSealName(String sealName) {
                this.sealName = sealName;
            }

            public String getSealCode() {
                return sealCode;
            }

            public void setSealCode(String sealCode) {
                this.sealCode = sealCode;
            }

            public String getSealOrg() {
                return sealOrg;
            }

            public void setSealOrg(String sealOrg) {
                this.sealOrg = sealOrg;
            }

            @Override
            public String toString() {
                return "ElectronicSealResp{" +
                        "sealName='" + sealName + '\'' +
                        ", sealCode='" + sealCode + '\'' +
                        ", sealOrg='" + sealOrg + '\'' +
                        '}';
            }
        }
    }

    public String getLicenseGroup() {
        return licenseGroup;
    }

    public void setLicenseGroup(String licenseGroup) {
        this.licenseGroup = licenseGroup;
    }

    public List<SealListResp> getSealList() {
        return sealList;
    }

    public void setSealList(List<SealListResp> sealList) {
        this.sealList = sealList;
    }

    @Override
    public String toString() {
        return "LicenseItemGetGroupSealResp{" +
                "licenseGroup='" + licenseGroup + '\'' +
                ", sealList=" + sealList +
                '}';
    }
}
