package gov.derate.proof.license.sdk.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import gov.derate.proof.account.dto.AccountInfoDto;
import gov.derate.proof.common.dto.CurrentAccountAndInfoDto;
import gov.derate.proof.common.response.ResponseCode;
import gov.derate.proof.dictionary.entity.DictionaryDicTypeConstant;
import gov.derate.proof.dictionary.entity.DictionaryTypeItemConstant;
import gov.derate.proof.item.exception.ItemServiceException;
import gov.derate.proof.license.config.LicenseProofApp;
import gov.derate.proof.license.dto.ImplementLicenseItemInfoDto;
import gov.derate.proof.license.dto.LicenseItemDTO;
import gov.derate.proof.license.dto.OperatorDto;
import gov.derate.proof.license.enums.AckCode;
import gov.derate.proof.license.exception.LicenseException;
import gov.derate.proof.license.req.LicenseAuthCodeRequest;
import gov.derate.proof.license.req.LicenseItemIssueReq;
import gov.derate.proof.license.req.LicenseItemListRequest;
import gov.derate.proof.license.resp.*;
import gov.derate.proof.license.sdk.LicenseProofSdk;
import gov.derate.proof.license.sdk.TokenSdk;
import gov.licc.func.api.amp.service.DictPublicService;
import gov.licc.func.api.auth.dto.CurrentAccountDto;
import gov.license.common.api.resp.BaseResponseCode;
import gov.license.common.tools.http.HttpUtil;
import gov.license.common.tools.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 远程调用电子证明服务包
 *
 * <AUTHOR>
 * @date 2023/04/11.
 */
@Component
public class LicenseProofSdkImpl implements LicenseProofSdk {

    private static final Logger LOGGER = LoggerFactory.getLogger(LicenseProofSdkImpl.class);
    /**
     * sdk封装服务类
     */
    @Autowired
    private TokenSdk tokenSdk;
    /**
     * 电子证明配置属性类
     */
    @Autowired
    private LicenseProofApp licenseProofApp;
    /**
     * 字典
     */
    @Autowired
    private DictPublicService dictPublicService;

    @Override
    public List<LicenseItemDTO> queryLicenseItemList(LicenseItemListRequest request) throws LicenseException {
        if (request == null) {
            return Collections.emptyList();
        }

        String accessToken = tokenSdk.getAccessToken(licenseProofApp);
        Map<String, Object> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("page_index", String.valueOf(request.getPageIndex()));
        param.put("page_size", String.valueOf(request.getPageSize()));
//        param.put("keyword", request.getKeyword());
        param.put("status", StringUtils.join(request.getStatus(), ","));
        param.put("is_open", "all");
//        param.put("rtn_icon", String.valueOf(request.getRtnIcon()));
        LicenseItemResponse response;
        String url = licenseProofApp.getUrl() + "/license_item/list";
        HttpUtil.HttpResp httpCallerResp;
        try {
            LOGGER.debug("queryLicenseItemList request param [{}],url[{}]", param, url);
            httpCallerResp = HttpUtil.doGet(url, param);
//            LOGGER.debug("queryLicenseItemList request response [{}]", httpCallerResp.getResponseString());
        } catch (Exception e) {
            throw new LicenseException(ResponseCode.LICENSE_SERVICE_ERROR);
        }
        if (HttpStatus.OK != httpCallerResp.getStatusCode()) {
            throw new LicenseException(ResponseCode.LICENSE_SERVICE_ERROR);
        }
        String responseString = httpCallerResp.getResponseString();
        try {
            response = JacksonUtil.toBean(responseString, LicenseItemResponse.class);
        } catch (Exception e) {
            throw new LicenseException(new BaseResponseCode("", e.getMessage()));
        }
        if (AckCode.SUCCESS != response.getAckCode()) {
            throw new LicenseException(new BaseResponseCode("", response.getErrorMessage()));
        }
        return response.getData();
    }

    @Override
    public LicenseAuthCodeResponse getLicenseAuthCodes(String itemCode, String itemName, String identityNumber,
                                                       CurrentAccountAndInfoDto currentAccountAndInfoDto, String idCode, String licenseItemCode) throws Exception {
        CurrentAccountDto currentAccountDto = currentAccountAndInfoDto.getCurrentAccountDto();
        AccountInfoDto accountInfoDto = currentAccountAndInfoDto.getAccountInfoDto();
        Optional<String> appKeyOptional = dictPublicService.getDetailValue(DictionaryDicTypeConstant.LICENSE_PROOF.name(), DictionaryTypeItemConstant.LICENSE_PROOF_AUTH_URL.name());

        String authUrl = appKeyOptional.orElse(StringUtils.EMPTY);

        String accessToken = tokenSdk.getAccessToken(licenseProofApp);
        authUrl = authUrl + "?access_token=" + accessToken;
        LicenseAuthCodeRequest request = new LicenseAuthCodeRequest();

        //事项
        request.setServiceItemCode(itemCode);
        request.setServiceItemName(itemName);

        //持有人身份证号码
        request.setIdentityNumber(identityNumber);

        if (StringUtils.isNotBlank(idCode)) {
            request.setIdCode(idCode);
        }
        if (StringUtils.isNotBlank(idCode)) {
            request.setLicenseItemCode(licenseItemCode);
        }
        // todo 注意info
        //操作人
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setAccount(currentAccountDto.getAccount().getAccount());
        operatorDto.setName(currentAccountDto.getAccount().getUserName());
        operatorDto.setIdentityNum(accountInfoDto.getIdentityNumber());
        operatorDto.setDivision(accountInfoDto.getDivisionName());
        operatorDto.setDivisionCode(accountInfoDto.getDivisionCode());
        operatorDto.setServiceOrg(accountInfoDto.getOrgName());
        operatorDto.setServiceOrgCode(accountInfoDto.getOrgCode());
        request.setOperator(operatorDto);

        String jsonRequest = JacksonUtil.toJsonStr(request);
        LOGGER.debug("getAuthCodes request url[{}],param[{}]", authUrl, jsonRequest);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doPostJson(authUrl, jsonRequest);
        LOGGER.debug("getAuthCodes response [{}],status[{}] [{}]", httpCallerResp.getResponseString(),
                httpCallerResp.getStatusCode(), httpCallerResp);
        if (httpCallerResp.getStatusCode() != HttpStatus.OK) {
            throw new LicenseException(ResponseCode.LICENSE_SERVICE_ERROR);
        }
        return JacksonUtil.toBean(httpCallerResp.getResponseString(),
                LicenseAuthCodeResponse.class);
    }

    @Override
    public LicenseGetTokenResponse getLicenseTokenResponse(String authCode) throws Exception {
        String accessToken = tokenSdk.getAccessToken(licenseProofApp);
        Optional<String> appKeyOptional = dictPublicService.getDetailValue(DictionaryDicTypeConstant.LICENSE_PROOF.name(), DictionaryTypeItemConstant.LICENSE_PROOF_TOKEN_URL.name());

        String getTokenUrl = appKeyOptional.orElse(StringUtils.EMPTY);

        String url = getTokenUrl + "?access_token=" + accessToken + "&auth_code=" + authCode;
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doGet(url);
        if (httpCallerResp.getStatusCode() != HttpStatus.OK) {
            throw new LicenseException(ResponseCode.LICENSE_SERVICE_ERROR);
        }
        return JacksonUtil.toBean(httpCallerResp.getResponseString(),
                LicenseGetTokenResponse.class);
    }


    /**
     * 电子证明归档
     *
     * @param authCode 授权码
     * @return 电子证明归档
     * @throws Exception 异常
     */
    @Override
    public LicenseAttachmentArchivingResponse licenseAttachmentArchiving(String authCode) throws Exception {
        String accessToken = tokenSdk.getAccessToken(licenseProofApp);
        // 电子证照，获取受理审批证照访问令牌
        Optional<String> urlConfig = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_PROOF_ARCHIVE_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_PROOF_ARCHIVE_URL.name());
        String attachmentArchivingUrl = urlConfig.orElse("");
        String url = String.format(attachmentArchivingUrl, authCode, accessToken);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doGet(url, null, null, 30 * 1000, 30 * 1000);
        String responseString = httpCallerResp.getResponseString();
        LicenseAttachmentArchivingResponse response = JacksonUtil.toBean(responseString, new TypeReference<LicenseAttachmentArchivingResponse>() {
        });
        if (response.getAckCode() == AckCode.SUCCESS) {
            return response;
        } else {
            LOGGER.error("licenseAttachmentArchiving request error url [{}] resp[{}]", url, responseString);
            throw new LicenseException(ResponseCode.SERVICE_ERROR.format(response.getErrorMessage()));
        }
    }

    @Override
    public List<ImplementLicenseItemInfoDto> getImplementLicenseItemInfoByBasicCode(String basicCode) {
        String accessToken = tokenSdk.getAccessToken(licenseProofApp);
        Optional<String> implementItemUrl = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_PROOF_ISSUE_SERV_LIST_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_PROOF_ISSUE_SERV_LIST_URL.name());
        String url = implementItemUrl.orElseThrow(() -> new ItemServiceException(ResponseCode.PARAM_ERROR));
//        LicenseItemImplInfoListRequest req = new LicenseItemImplInfoListRequest();
//        req.setAccessToken(accessToken);
//        req.setLicenseItemCode(basicCode);
//        req.setImplementServiceStatus(null);
//        req.setDivisionCode("");
//        Map<String, Object> param = req.buildJsonMap();
        Map<String, Object> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("license_item_code", basicCode);
        param.put("division_code", "");
        param.put("page_index", 1);
        param.put("page_size", 100);
        LOGGER.debug("getImplementLicenseItemInfoByBasicCode req url [{}] param is [{}]", url, param);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doGet(url, param);
        String responseString = httpCallerResp.getResponseString();
        LOGGER.debug("getImplementLicenseItemInfoByBasicCode resp url [{}] param [{}] resp [{}]", url, param, responseString);
        LicenseItemImplItemInfoResponse response = JacksonUtil.toBean(responseString, new TypeReference<LicenseItemImplItemInfoResponse>() {
        });
        if (response.getAckCode() == AckCode.SUCCESS) {
            return response.getData();
        } else {
            LOGGER.error("licenseAttachmentArchiving request error url [{}] resp[{}]", url, responseString);
            throw new LicenseException(ResponseCode.SERVICE_ERROR.format(response.getErrorMessage()));
        }
    }

    @Override
    public LicenseItemGetGroupSealResponse getSealInfo(String implementCode, String basicCode, String orgCode) {
        String accessToken = tokenSdk.getAccessToken(licenseProofApp);
        // 电子证照，获取受理审批证照访问令牌
        Optional<String> urlConfig = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_PROOF_SEAL_INFO_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_PROOF_SEAL_INFO_URL.name());
        String url = urlConfig.orElse("");
        Map<String, Object> param = Maps.newHashMap();
        param.put("access_token", accessToken);
        param.put("implement_code", implementCode);
        LOGGER.debug("getSealInfo req url [{}] param [{}]", url, param);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doGet(url, param);
        String responseString = httpCallerResp.getResponseString();
        LOGGER.debug("getSealInfo resp url [{}] param [{}],resp [{}]", url, param, responseString);
        LicenseItemGetGroupSealResponse response = JacksonUtil.toBean(responseString, new TypeReference<LicenseItemGetGroupSealResponse>() {
        });
        if (response.getAckCode() == AckCode.SUCCESS) {
            return response;
        } else {
            LOGGER.error("getSealInfo request error url [{}] resp[{}]", url, httpCallerResp);
            throw new LicenseException(LicenseResponseCode.CALL_INTERFACE_BIZ_ERROR_ERROR.format("查看电子证照接口失败"));
        }
    }

    @Override
    public String issueLicense(String itemName, String itemCode, String licenseGroup, String sealCode, String bizNum, String implementCode, OperatorDto operatorDto, Map<String, String> dataItemMap) {
        String accessToken = tokenSdk.getAccessToken(licenseProofApp);
        // 电子证照，获取受理审批证照访问令牌
        Optional<String> urlConfig = dictPublicService.getDetailValue(DictionaryTypeItemConstant.LICENSE_PROOF_ISSUE_URL.getDictType().name(), DictionaryTypeItemConstant.LICENSE_PROOF_ISSUE_URL.name());
        String url = urlConfig.orElse("");
        url = String.format(url + "?access_token=%s", implementCode, accessToken);
        LicenseItemIssueReq req = new LicenseItemIssueReq();
        LicenseItemIssueReq.DataBean dataBean = new LicenseItemIssueReq.DataBean();
        dataBean.setServiceItemCode(itemCode);
        dataBean.setServiceItemName(itemName);
        dataBean.setLicenseGroup(licenseGroup);
        dataBean.setBizNum(bizNum);
        dataBean.setSealCode(sealCode);
        dataBean.setOperator(operatorDto);
        dataBean.setDataFields(dataItemMap);
        req.setData(dataBean);
        String jsonParam = req.toJsonParam();
        LOGGER.debug("issueLicense req url [{}] param [{}]", url, jsonParam);
        HttpUtil.HttpResp httpCallerResp = HttpUtil.doPostJson(url, jsonParam, null, 30 * 1000, 30 * 1000);
        String responseString = httpCallerResp.getResponseString();
        LOGGER.debug("issueLicense resp url [{}] param [{}],resp [{}]", url, jsonParam, responseString);
        LicenseItemIssueResponse response = JacksonUtil.toBean(responseString, new TypeReference<LicenseItemIssueResponse>() {
        });
        if (response.getAckCode() == AckCode.SUCCESS) {
            return response.getData().getAuthCode();
        } else {
            LOGGER.error("issueLicense request error url [{}] resp[{}]", url, responseString);
            throw new LicenseException(LicenseResponseCode.CALL_ISSUE_BIZ_ERROR_ERROR.formatByReplaceFlag(response.getErrorMessage()));
        }
    }

}
