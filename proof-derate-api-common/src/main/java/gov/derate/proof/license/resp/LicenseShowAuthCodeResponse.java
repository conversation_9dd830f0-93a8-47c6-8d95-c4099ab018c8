package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.license.dto.ErrorDTO;
import gov.derate.proof.license.enums.AckCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * 用户出示码;获取上下文接口(show/context_code/get) 请求类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class LicenseShowAuthCodeResponse extends LicenseBaseResponse {
    /**
     * 数据
     */
    @JsonProperty("data")
    private LicenseShowAuthCodeDataResponse data;


    public LicenseShowAuthCodeDataResponse getData() {
        return data;
    }

    public void setData(LicenseShowAuthCodeDataResponse data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "LicenseShowAuthCodeResponse{" +
                "data=" + data +
                '}';
    }

    public boolean isSuccessRequest(){
        return this.getAckCode() == AckCode.SUCCESS;
    }
    public boolean isNotSuccessRequest(){
        return !isSuccessRequest();
    }
    public String getErrorCode(){
        if (isNotSuccessRequest()) {
            ErrorDTO errorDTO = this.getErrors().get(0);
            return errorDTO.getCode();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 判断-用户授权中
     * @return true/false
     */
    public boolean isUserValidating(){
        return "11518".equals(getErrorCode());
    }
    /**
     * 获取-用户授权中信息
     * @return true/false
     */
    public String getUserValidatingMsg(){
        return "用户授权中，请耐心等待用户授权" ;
    }
    /**
     * 判断-用户已取消授权
     * @return true/false
     */
    public boolean isUserCancelValidate(){
        return "11519".equals(getErrorCode());
    }
    /**
     * 获取-用户已取消授权信息
     * @return true/false
     */
    public String getUserCancelValidate(){
        return "用户已取消授权，如需继续办理业务，请提醒用户重新出示授权二维码";
    }

    /**
     * 获取异常信息
     * @return 异常信息
     */
    public String getErrorMsg(){
        if (isNotSuccessRequest() && isUserValidating()) {
            return getUserValidatingMsg();
        }
        if (isNotSuccessRequest() && isUserCancelValidate()) {
            return getUserCancelValidate();
        }
        return this.getErrorMessage();
    }

}
