package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * <p>
 * 用户出示码;获取上下文接口(show/context_code/get) 请求类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class LicenseShowContextCodeDataResponse implements Serializable {
    /**
     * 数据
     */
    @JsonProperty("context_code")
    private String contextCode;


    public String getContextCode() {
        return contextCode;
    }

    public void setContextCode(String contextCode) {
        this.contextCode = contextCode;
    }

    @Override
    public String toString() {
        return "LicenseShowContextCodeDataResponse{" +
                "contextCode='" + contextCode + '\'' +
                '}';
    }
}
