package gov.derate.proof.license.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.exempt.entity.ExemptAuthLicenseIndexDo;
import gov.derate.proof.license.enums.LicenseStatus;
import gov.derate.proof.license.enums.LicenseType;

import java.io.Serializable;

/**
 * <p>
 * 证照摘要信息
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class LicenseIndexDto implements Serializable {
    /**
     * 电子证照唯一标识码;由电子证照系统统一编码规则生成
     */
    @JsonProperty("license_code")
    private String licenseCode;
    /**
     * 证照中文名称;对应数据证照目录中文名称
     */
    @JsonProperty("name")
    private String name;
    /**
     * 证照类型(详附录)
     */
    @JsonProperty("license_type")
    private LicenseType licenseType;
    /**
     * 证照号码;签发机构编制的证照文号或编号
     */
    @JsonProperty("id_code")
    private String idCode;
    /**
     * 持证者名称.若有多个,中间以逗号(英文标点)隔开
     */
    @JsonProperty("holder_name")
    private String holderName;
    /**
     * 持证者身份证件号码.若有多个,中间以逗号(英文标点)隔开
     */
    @JsonProperty("holder_identity_num")
    private String holderIdentityNum;
    /**
     * 签发机构名称
     */
    @JsonProperty("issue_org_name")
    private String issueOrgName;
    /**
     * 签发机构所属行政区划代码
     */
    @JsonProperty("division_code")
    private String divisionCode;
    /**
     * 管理属性(详附录)
     */
    @JsonProperty("license_status")
    private LicenseStatus licenseStatus;
    /**
     * 目录编码。对应的电子证照目录编码
     */
    @JsonProperty("license_item_code")
    private String licenseItemCode;
    /**
     * 最后修改者
     */
    @JsonProperty("last_modificator")
    private String lastModificator;

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LicenseType getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(LicenseType licenseType) {
        this.licenseType = licenseType;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getHolderIdentityNum() {
        return holderIdentityNum;
    }

    public void setHolderIdentityNum(String holderIdentityNum) {
        this.holderIdentityNum = holderIdentityNum;
    }

    public String getIssueOrgName() {
        return issueOrgName;
    }

    public void setIssueOrgName(String issueOrgName) {
        this.issueOrgName = issueOrgName;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public LicenseStatus getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(LicenseStatus licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    public String getLicenseItemCode() {
        return licenseItemCode;
    }

    public void setLicenseItemCode(String licenseItemCode) {
        this.licenseItemCode = licenseItemCode;
    }

    public String getLastModificator() {
        return lastModificator;
    }

    public void setLastModificator(String lastModificator) {
        this.lastModificator = lastModificator;
    }


    @Override
    public String toString() {
        return "LicenseIndexDto{" +
                "licenseCode='" + licenseCode + '\'' +
                ", name='" + name + '\'' +
                ", licenseType=" + licenseType +
                ", idCode='" + idCode + '\'' +
                ", holderName='" + holderName + '\'' +
                ", holderIdentityNum='" + holderIdentityNum + '\'' +
                ", issueOrgName='" + issueOrgName + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", licenseStatus=" + licenseStatus +
                ", licenseItemCode='" + licenseItemCode + '\'' +
                ", lastModificator='" + lastModificator + '\'' +
                '}';
    }

    /**
     * 粤省事粤商通授权信息回写证照摘要信息 转换证照索引对象
     *
     * @param exemptAuthLicenseIndexDo 粤省事粤商通授权信息回写证照摘要信息
     * @return 证照索引对象
     */
    public static LicenseIndexDto convertByExemptAuthLicenseIndex(ExemptAuthLicenseIndexDo exemptAuthLicenseIndexDo) {
        LicenseIndexDto licenseIndexDto = new LicenseIndexDto();
        licenseIndexDto.setLicenseCode(exemptAuthLicenseIndexDo.getLicenseCode());
        licenseIndexDto.setName(exemptAuthLicenseIndexDo.getLicenseName());
        licenseIndexDto.setLicenseItemCode(exemptAuthLicenseIndexDo.getCatalogCode());
        licenseIndexDto.setDivisionCode(exemptAuthLicenseIndexDo.getRegionCode());
        licenseIndexDto.setHolderIdentityNum(exemptAuthLicenseIndexDo.getHolderIdentityNumber());
        licenseIndexDto.setHolderName(exemptAuthLicenseIndexDo.getHolderName());
        licenseIndexDto.setIssueOrgName(exemptAuthLicenseIndexDo.getOrgName());
        return licenseIndexDto;
    }
}
