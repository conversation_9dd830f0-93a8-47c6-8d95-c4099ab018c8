package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.license.enums.LicenseStatus;
import gov.derate.proof.license.enums.LicenseType;

/**
 * 电子证明-部门目录接口响应类
 * Company: Zsoft
 * CreateDate:2024/8/14
 *
 * <AUTHOR>
 */
public class ImplementLicenseItemInfoResponse extends LicenseBaseResponse {
    private static final long serialVersionUID = 6415790228956855450L;

    /**
     * 证照中文名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 电子证照标识码
     */
    @JsonProperty("license_code")
    private String licenseCode;

    /**
     * 证照类型
     */
    @JsonProperty("license_type")
    private LicenseType licenseType;

    /**
     * 证照号码，发证机构编制的证照文号
     */
    @JsonProperty("id_code")
    private String idCode;

    /**
     * 持有人名称。如有多个，中间以分号（,）分割。
     */
    @JsonProperty("holder_name")
    private String holderName;

    /**
     * 持有人证件类型。
     */
    @JsonProperty("holder_identity_type")
    private String holderIdentityType;

    /**
     * 持有者身份证件号码。如有多个，中间以分号（,）分割。
     */
    @JsonProperty("holder_identity_num")
    private String holderIdentityNum;

    /**
     * 发证机构名称
     */
    @JsonProperty("issue_org_name")
    private String issueOrgName;

    /**
     * 发证机构的组织机构代码
     */
    @JsonProperty("issue_org_code")
    private String issueOrgCode;

    /**
     * 发证机构所属行政区划
     */
    @JsonProperty("division")
    private String division;

    /**
     * 发证机构所属行政区划代码
     */
    @JsonProperty("division_code")
    private String divisionCode;

    /**
     * 证照业务数据（结构化数据），采用json格式记录。
     */
    @JsonProperty("data_fields")
    private String dataFields;

    /**
     * 签发证照对应办件的业务流水号
     */
    @JsonProperty("biz_num")
    private String bizNum;

    /**
     * 目录编码。对应的电子证照目录编码 <br>
     * 权责清单证照类型编码不为空时替换成权责清单证照类型编码
     */
    @JsonProperty("license_item_code")
    private String licenseItemCode;

    /**
     * 证照状态（代码值）参照LicenseStatus代码。
     */
    @JsonProperty("license_status")
    private LicenseStatus licenseStatus;

    /**
     * 证照扩展信息。
     */
    @JsonProperty("extend_props")
    private String extendProps;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public LicenseType getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(LicenseType licenseType) {
        this.licenseType = licenseType;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getHolderIdentityType() {
        return holderIdentityType;
    }

    public void setHolderIdentityType(String holderIdentityType) {
        this.holderIdentityType = holderIdentityType;
    }

    public String getHolderIdentityNum() {
        return holderIdentityNum;
    }

    public void setHolderIdentityNum(String holderIdentityNum) {
        this.holderIdentityNum = holderIdentityNum;
    }

    public String getIssueOrgName() {
        return issueOrgName;
    }

    public void setIssueOrgName(String issueOrgName) {
        this.issueOrgName = issueOrgName;
    }

    public String getIssueOrgCode() {
        return issueOrgCode;
    }

    public void setIssueOrgCode(String issueOrgCode) {
        this.issueOrgCode = issueOrgCode;
    }

    public String getDivision() {
        return division;
    }

    public void setDivision(String division) {
        this.division = division;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public String getDataFields() {
        return dataFields;
    }

    public void setDataFields(String dataFields) {
        this.dataFields = dataFields;
    }

    public String getBizNum() {
        return bizNum;
    }

    public void setBizNum(String bizNum) {
        this.bizNum = bizNum;
    }

    public String getLicenseItemCode() {
        return licenseItemCode;
    }

    public void setLicenseItemCode(String licenseItemCode) {
        this.licenseItemCode = licenseItemCode;
    }

    public LicenseStatus getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(LicenseStatus licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    public String getExtendProps() {
        return extendProps;
    }

    public void setExtendProps(String extendProps) {
        this.extendProps = extendProps;
    }
}
