package gov.derate.proof.license.enums;

/**
 * <p>
 * 应答代码
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2020-08-10
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2020-08-10;
 */
public enum AckCode {

    /**
     * 预留内部使用
     */
    INTERNAL,

    /**
     * 成功。对请求的处理成功
     */
    SUCCESS,

    /**
     * 失败。对请求的处理失败
     */
    FAILURE,

    /**
     * 警告。对请求的处理完成，警告信息包含在返回消息
     */
    WARNING,

    ;
}
