package gov.derate.proof.license.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * 窗口人员信息
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class OperatorDto {
    /**
     * 操作人名称
     */
    @JsonProperty("account")
    private String account;
    /**
     * 操作人姓名
     */
    @JsonProperty("name")
    private String name;
    /**
     * 操作人身份证件号码
     */
    @JsonProperty("identity_num")
    private String identityNum;
    /**
     * 操作人身份证件类型
     */
    @JsonProperty("identity_type")
    private String identityType;
    /**
     * 操作人角色;如:"窗口受理人员"
     */
    @JsonProperty("role")
    private String role;
    /**
     * 行政区划名称
     */
    @JsonProperty("division")
    private String division;
    /**
     * 行政区划代码
     */
    @JsonProperty("division_code")
    private String divisionCode;
    /**
     * 所属地址
     */
    @JsonProperty("address")
    private String address;
    /**
     * 所属机构名称
     */
    @JsonProperty("service_org")
    private String serviceOrg;
    /**
     * 所属机构编码
     */
    @JsonProperty("service_org_code")
    private String serviceOrgCode;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdentityNum() {
        return identityNum;
    }

    public void setIdentityNum(String identityNum) {
        this.identityNum = identityNum;
    }

    public String getIdentityType() {
        return identityType;
    }

    public void setIdentityType(String identityType) {
        this.identityType = identityType;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getDivision() {
        return division;
    }

    public void setDivision(String division) {
        this.division = division;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getServiceOrg() {
        return serviceOrg;
    }

    public void setServiceOrg(String serviceOrg) {
        this.serviceOrg = serviceOrg;
    }

    public String getServiceOrgCode() {
        return serviceOrgCode;
    }

    public void setServiceOrgCode(String serviceOrgCode) {
        this.serviceOrgCode = serviceOrgCode;
    }

    @Override
    public String toString() {
        return "OperatorDto{" +
                "account='" + account + '\'' +
                ", Name='" + name + '\'' +
                ", identity_num='" + identityNum + '\'' +
                ", identity_type='" + identityType + '\'' +
                ", role='" + role + '\'' +
                ", division='" + division + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", Address='" + address + '\'' +
                ", serviceOrg='" + serviceOrg + '\'' +
                ", serviceOrgCode='" + serviceOrgCode + '\'' +
                '}';
    }
}
