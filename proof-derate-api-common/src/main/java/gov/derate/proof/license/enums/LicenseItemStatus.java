package gov.derate.proof.license.enums;

/**
 * <p>
 * 证照目录状态
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-19
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-19;
 */
public enum LicenseItemStatus {

    /**
     * 草案
     */
    DRAFT("草案"),
    /**
     * 已注册
     */
    REGISTER("已注册"),
    /**
     * 已发布
     */
    PUBLISH("已发布"),
    /**
     * 已撤销
     */
    CANCLED("已撤销");

    private String desc;

    LicenseItemStatus(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
