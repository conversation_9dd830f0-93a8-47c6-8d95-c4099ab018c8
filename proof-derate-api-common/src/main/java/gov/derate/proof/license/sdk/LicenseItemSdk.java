package gov.derate.proof.license.sdk;

import gov.derate.proof.common.dto.CurrentAccountAndInfoDto;
import gov.derate.proof.license.dto.LicenseItemDTO;
import gov.derate.proof.license.dto.LicenseItemUnionDTO;
import gov.derate.proof.license.dto.LicenseItemUnionPageDTO;
import gov.derate.proof.license.dto.OperatorDto;
import gov.derate.proof.license.exception.LicenseException;
import gov.derate.proof.license.req.LicenseItemListRequest;
import gov.derate.proof.license.req.LicenseItemUnionListRequest;
import gov.derate.proof.license.req.LicenseItemUnionPageRequest;
import gov.derate.proof.license.resp.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 远程调用证照服务包
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-19
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-19;
 */
public interface LicenseItemSdk {

    /**
     * 查询证照目录
     *
     * @param licenseItemListRequest 证照目录请求体
     * @return 证照目录列表
     * @throws LicenseException 证照异常
     */
    List<LicenseItemDTO> queryLicenseItemList(LicenseItemListRequest licenseItemListRequest) throws LicenseException;

    /**
     * 查询组合目录
     *
     * @param licenseItemUnionListRequest 组合目录请求体
     * @return 组合目录列表
     * @throws LicenseException 证照异常
     */
    List<LicenseItemUnionDTO> queryLicenseItemUnion(LicenseItemUnionListRequest licenseItemUnionListRequest) throws LicenseException;


    /**
     * 查询新版组合目录
     *
     * @param licenseItemUnionPageRequest 组合目录请求体
     * @return 组合目录列表
     * @throws LicenseException 证照异常
     */
    List<LicenseItemUnionPageDTO> queryLicenseItemUnion(LicenseItemUnionPageRequest licenseItemUnionPageRequest) throws LicenseException;

    /**
     * 调用根据业务信息换取上下文编码（license/holder_auth/show/context_code/get）”，根据业务流水号、事项信息、当前操作人员信息、办事人员身份核验码获取上下文编码（context_code）；
     *
     * @param itemCode                 事项编码
     * @param itemName                 事项名称
     * @param serviceItemOrg           事项部门
     * @param serialNumber             业务流水号
     * @param authorizationCode        实名授权码
     * @param currentAccountAndInfoDto 当前用户
     * @return 响应值
     * @throws Exception 异常
     */
    LicenseShowContextCodeResponse getHolderAuthShowContextCode(String itemCode, String itemName, String serviceItemOrg, String serialNumber, String authorizationCode, CurrentAccountAndInfoDto currentAccountAndInfoDto) throws Exception;

    /**
     * 用户出示码;获取用证码和证照摘要接口(show/auth_code/get) 获取授权码与授权信息
     *
     * @param contextCode 上下文编码
     * @return 用证码与证照索引
     * @throws Exception 异常
     */
    LicenseShowAuthCodeResponse getHolderAuthShowAuthCode(String contextCode) throws Exception;

    /**
     * 归档证照
     *
     * @param authCode                 核验码
     * @param itemCode                 事项编码
     * @param itemName                 事项名称
     * @param implOrgName              事项部门
     * @param serialNumber             办件号码
     * @param currentAccountAndInfoDto 当前用户
     * @param licenseCode              归档LicenseCode
     * @return 文件数据
     * @throws Exception 异常
     */
    LicenseAttachmentArchivingResponse licenseAttachmentArchiving(String authCode, String itemCode, String itemName, String implOrgName, String serialNumber, CurrentAccountAndInfoDto currentAccountAndInfoDto, String licenseCode) throws Exception;

    /**
     * 提取受理审批证照数据
     *
     * @param authCode                 核验码
     * @param itemCode                 事项编码
     * @param itemName                 事项名称
     * @param implOrgName              事项部门
     * @param serialNumber             办件号码
     * @param currentAccountAndInfoDto 当前用户
     * @param licenseCode              归档LicenseCode
     * @return 文件数据
     * @throws Exception 异常
     */
    LicenseGetLicenseAuditResponse licenseGetLicenseAudit(String authCode, String itemCode, String itemName, String implOrgName, String serialNumber, CurrentAccountAndInfoDto currentAccountAndInfoDto, String licenseCode) throws Exception;

    /**
     * 获取授权码信息
     *
     * @param authCode                 核验码
     * @param itemCode                 事项编码
     * @param itemName                 事项名称
     * @param implOrgName              事项部门
     * @param serialNumber             办件号码
     * @param licenseCode              电子证照licenseCode
     * @param currentAccountAndInfoDto 当前用户
     * @return 数据
     * @throws Exception 异常
     */
    LicenseGetTokenAuditResponse getTokenAuditResponse(String authCode, String itemCode, String itemName, String implOrgName, String serialNumber, String licenseCode, CurrentAccountAndInfoDto currentAccountAndInfoDto) throws Exception;

    /**
     * 电子证照-持有人授权用证
     *
     * @param itemCode                 事项编码
     * @param itemName                 事项名称
     * @param idNumber                 免证办-单据办事人身份证号码
     * @param licenseItemCode          全省目录基本码
     * @param credential               免证办-江西政务服务码-解码接口ywxx.code字段
     * @param currentAccountAndInfoDto 当前用户信息
     * @return 带授权码的证照
     * @throws Exception 请求错误抛出错误
     */
    LicenseHolderResponse getLicenseByHolderAuthCodeInterface(String itemCode, String itemName, String idNumber, String licenseItemCode, String credential, CurrentAccountAndInfoDto currentAccountAndInfoDto) throws Exception;

    /**
     * 归档证照-license/archive接口
     *
     * @param authCode 核验码
     * @return 文件数据
     */
    LicenseAttachmentArchivingResponse licenseArchiveInterface(String authCode) throws Exception;

    /**
     * 查看电子证照-license/get_license接口
     *
     * @param authCode 核验码
     * @return 文件数据
     */
    LicenseGetLicenseResponse licenseGetLicenseInterface(String authCode) throws Exception;

    /**
     * 查看电子证明-印章信息
     *
     * @param implementCode 实施码
     * @param basicCode     基本码
     * @param orgCode       部门
     * @return 印章信息
     */
    LicenseItemGetGroupSealResponse getSealInfo(String implementCode, String basicCode, String orgCode);

    /**
     * 签发接口
     *
     * @param itemName      事项名称
     * @param itemCode      事项编码
     * @param licenseGroup  实施码印章组别
     * @param implementCode 实施码
     * @param sealCode      印章编码
     * @param bizNum        业务编码
     * @param operatorDto   操作人
     * @param dataItemMap   数据项 key = 实施码配置key，value = assistItemInfo的value
     * @return authCode
     */
    String issueLicense(String itemName, String itemCode, String licenseGroup, String sealCode, String bizNum, String implementCode, OperatorDto operatorDto, Map<String, String> dataItemMap);

}
