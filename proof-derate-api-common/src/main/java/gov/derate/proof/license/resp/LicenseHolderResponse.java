package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.license.dto.ErrorDTO;
import gov.derate.proof.license.dto.LicenseIndexHolderDto;
import gov.derate.proof.license.enums.AckCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 电子证照-持有人授权用证接口(p/v1/license/holder接口地址) 请求类
 * Company: Zsoft
 * CreateDate:2024/5/13
 *
 * <AUTHOR>
 */
public class LicenseHolderResponse extends LicenseBaseResponse {
    /**
     * 数据
     */
    @JsonProperty("data")
    private List<LicenseIndexHolderDto> data;

    /**
     * 用证码
     */
    @JsonProperty("auth_codes")
    private List<String> authCodes;
    /**
     * 数据总量
     */
    @JsonProperty("total_count")
    private Long totalCount;


    public boolean isSuccessRequest() {
        return this.getAckCode() == AckCode.SUCCESS;
    }

    public boolean isNotSuccessRequest() {
        return !isSuccessRequest();
    }

    public String getErrorCode() {
        if (isNotSuccessRequest()) {
            ErrorDTO errorDTO = this.getErrors().get(0);
            return errorDTO.getCode();
        }
        return StringUtils.EMPTY;
    }


    /**
     * 获取异常信息
     *
     * @return 异常信息
     */
    public String getErrorMsg() {
        if (isNotSuccessRequest()) {
            this.getErrorMessage();
        }
        return "";
    }

    /**
     * 获取data包含authCode的数据
     * 若authCodes为空，则返回data数据
     *
     * @return data数据
     */
    public List<LicenseIndexHolderDto> getDataHasAuthCode() {
        if (CollectionUtils.isNotEmpty(this.authCodes)) {
            for (int i = 0; i < this.data.size(); i++) {
                LicenseIndexHolderDto licenseIndexHolderDto = this.data.get(0);
                String authCode = this.authCodes.get(i);
                licenseIndexHolderDto.setAuthCode(authCode);
            }
        }
        return this.data;
    }

    public List<LicenseIndexHolderDto> getData() {
        return data;
    }

    public void setData(List<LicenseIndexHolderDto> data) {
        this.data = data;
    }

    public List<String> getAuthCodes() {
        return authCodes;
    }

    public void setAuthCodes(List<String> authCodes) {
        this.authCodes = authCodes;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    @Override
    public String toString() {
        return "LicenseHolderResponse{" +
                "data=" + data +
                ", authCodes=" + authCodes +
                ", totalCount=" + totalCount +
                '}';
    }
}
