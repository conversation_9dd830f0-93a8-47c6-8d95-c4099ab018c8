package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户出示码;获取上下文接口(show/context_code/get) 请求类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class LicenseGetTokenAuditDataResponse implements Serializable {
    /**
     * 电子证照访问令牌
     */
    @JsonProperty("license_access_token")
    private String licenseAccessToken;
    /**
     * 有效期
     */
    @JsonProperty("expiry_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiryDate;


    public String getLicenseAccessToken() {
        return licenseAccessToken;
    }

    public void setLicenseAccessToken(String licenseAccessToken) {
        this.licenseAccessToken = licenseAccessToken;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    @Override
    public String toString() {
        return "LicenseGetTokenAuditDataResponse{" +
                "licenseAccessToken='" + licenseAccessToken + '\'' +
                ", expiryDate=" + expiryDate +
                '}';
    }
}
