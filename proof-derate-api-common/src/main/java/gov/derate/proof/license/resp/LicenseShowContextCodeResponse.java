package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.license.dto.ErrorDTO;
import gov.derate.proof.license.enums.AckCode;

/**
 * <p>
 * 用户出示码;获取上下文接口(show/context_code/get) 请求类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/20
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/20；
 */
public class LicenseShowContextCodeResponse extends LicenseBaseResponse {
    /**
     * 数据
     */
    @JsonProperty("data")
    private LicenseShowContextCodeDataResponse data;


    public LicenseShowContextCodeDataResponse getData() {
        return data;
    }

    public void setData(LicenseShowContextCodeDataResponse data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "LicenseShowContextCodeResponse{" +
                "data=" + data +
                '}';
    }
    public boolean isSuccessRequest(){
        return this.getAckCode() == AckCode.SUCCESS;
    }
    public boolean isNotSuccessRequest(){
        return !isSuccessRequest();
    }

    public String getErrorCode(){
        if (isNotSuccessRequest()) {
            ErrorDTO errorDTO = this.getErrors().get(0);
            return errorDTO.getCode();
        }
        return "";
    }

    /**
     * 办理人暂无可用电子证照
     * @return true/false
     */
    public boolean isNotHaveValidLicenseError(){
        return "12215".equals(getErrorCode());
    }
    /**
     * 授权二维码已过期
     * @return true/false
     */
    public boolean isQrCodeValidatedError(){
        return "19008".equals(getErrorCode());
    }
    /**
     * 不支持使用此二维码过期
     * @return true/false
     */
    public boolean isNotSupportQrCodeValidatedError(){
        return "19007".equals(getErrorCode());
    }
    /**
     * 网络/服务器异常
     * @return true/false
     */
    public boolean isInternetOrServiceErrorError(){
        return "19005".equals(getErrorCode());
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getErrorCodeMsg(){
        if (isNotSuccessRequest() && isNotHaveValidLicenseError()) {
            return "办理人暂无可用电子证照，请办理人提交其他材料（如纸质材料、扫 描件等）。";
        }
        if (isNotSuccessRequest() && isQrCodeValidatedError()) {
            return "授权二维码已过期，请办事人重新出示。";
        }
        if (isNotSuccessRequest() && isNotSupportQrCodeValidatedError()) {
            return "不支持使用此二维码，请办事人检查出示的二维码是否正确。";
        }
        if (isNotSuccessRequest() && isInternetOrServiceErrorError()) {
            return "网络/服务器异常，请重试。若尝试多次仍失败，请与系统管理员联系。";
        }
        if (isNotSuccessRequest()) {
            return this.getErrorMessage();
        }
        return "";
    }
}
