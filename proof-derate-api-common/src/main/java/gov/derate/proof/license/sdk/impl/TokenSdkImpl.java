package gov.derate.proof.license.sdk.impl;

import com.google.common.collect.Maps;
import gov.derate.proof.common.constant.CacheKeyBuilder;
import gov.derate.proof.common.response.ResponseCode;
import gov.derate.proof.license.config.PlatformApp;
import gov.derate.proof.license.enums.AckCode;
import gov.derate.proof.license.exception.LicenseException;
import gov.derate.proof.license.sdk.TokenSdk;
import gov.license.cache.service.CacheService;
import gov.license.common.tools.http.HttpUtil;
import gov.license.common.tools.jackson.JacksonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 基础框架令牌服务包实现类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2020-08-10
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2020-08-10;
 */
@Component
public class TokenSdkImpl implements TokenSdk {

    private static final Logger LOGGER = LoggerFactory.getLogger(TokenSdkImpl.class);
    private static final String ACK_CODE = "ack_code";

    @Autowired
    private CacheService cacheService;

    @Override
    public String getAccessToken(PlatformApp platformApp) throws LicenseException {
        platformApp = platformApp.getDictConfig();
        TokenInfo tokenInfo = getCacheByAppKey(platformApp.getAppKey());

        try {
            if (tokenInfo == null || tokenInfo.getAccessToken() == null || tokenInfo.getCreateDate() == null
                    || tokenInfo.getExpiryDate() == null) {
                login(platformApp);
                tokenInfo = getCacheByAppKey(platformApp.getAppKey());

            } else {
                if (!verifyAccessToken(platformApp)) {
                    logout(platformApp);
                    login(platformApp);
                    tokenInfo = getCacheByAppKey(platformApp.getAppKey());
                }
            }
        } catch (Exception e) {
            LOGGER.warn("TokenSdkImpl getAccessToken error", e);
            throw new LicenseException(ResponseCode.LICENSE_SERVICE_ERROR);
        }
        return tokenInfo.getAccessToken();
    }

    /**
     * 获取登录缓存
     *
     * @param appKey appKey
     * @return 登录token信息
     */
    public TokenInfo getCacheByAppKey(String appKey) {
        String tokenInfoJson = cacheService.get(CacheKeyBuilder.LICENSE_PLATFORM_TOKEN.getCacheKey(appKey));
        TokenInfo tokenInfo = null;
        try {
            tokenInfo = JacksonUtil.toBean(tokenInfoJson, TokenInfo.class);
        } catch (Exception e) {
            LOGGER.warn("getAccessToken getCacheError", e);
        }
        return tokenInfo;
    }

    /**
     * 调用接口登陆 <br>
     * 存储全局使用
     *
     * @throws Exception
     */
    private void login(PlatformApp platformApp) throws Exception {

        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put("account", platformApp.getAccount());
        dataMap.put("password", platformApp.getPassword());
        dataMap.put("app_key", platformApp.getAppKey());
        dataMap.put("app_secret", platformApp.getAppSecret());

        String uri = platformApp.getUrl() + "/security/login";
        HttpUtil.HttpResp httpCallerResp = null;
        try {
            String jsonParam = JacksonUtil.toJsonStr(dataMap);
            LOGGER.debug("login url[{}],param[{}]", uri, jsonParam);
            httpCallerResp = HttpUtil.doPostJson(uri, jsonParam);
            LOGGER.debug("login response [{}],status [{}] [{}]", httpCallerResp.getResponseString(), httpCallerResp.getStatusCode(), httpCallerResp);

        } catch (Exception e) {
            LOGGER.error("接口登陆失败", e);
            throw new LicenseException(ResponseCode.LICENSE_LOGIN_SERVICE_ERROR);
        }
        if (HttpStatus.OK != httpCallerResp.getStatusCode()) {
            LOGGER.error("接口登陆失败 response [{}],status [{}] [{}]", httpCallerResp.getResponseString(), httpCallerResp.getStatusCode(), httpCallerResp);
            throw new LicenseException(ResponseCode.LICENSE_LOGIN_SERVICE_ERROR);
        }

        Map<String, Object> resultMap = JacksonUtil.toMap(httpCallerResp.getResponseString(), Object.class);
        if (AckCode.SUCCESS.name().equals(resultMap.get(ACK_CODE))) {
            LOGGER.debug("登陆成功,本次登陆访问令牌为：[{}]", resultMap.get("access_token"));

            TokenInfo tokenInfo = new TokenInfo();
            tokenInfo.setAccessToken(resultMap.get("access_token").toString());
            tokenInfo.setCreateDate(new Date());
            // 需要改成可配置，超时时间
            tokenInfo.setExpiryDate(new Date(tokenInfo.getCreateDate().getTime() + 7 * 24 * 60 * 60 * 1000));
            cacheService.set(CacheKeyBuilder.LICENSE_PLATFORM_TOKEN.getCacheKey(platformApp.getAppKey()), JacksonUtil.toJsonStr(tokenInfo));
        } else {
            clearAccessToken(platformApp);
            throw new LicenseException(ResponseCode.LICENSE_LOGIN_SERVICE_ERROR);
        }
    }

    /**
     * 接口退出 <br>
     * 接口令牌accessToken失效时调用
     *
     * @throws Exception
     */
    private void logout(PlatformApp platformApp) throws Exception {
        TokenInfo tokenInfo = getCacheByAppKey(platformApp.getAppKey());
        String url = platformApp.getUrl() + "/security/logout";
        Map<String, String> param = Maps.newHashMap();
        param.put("access_token", tokenInfo.getAccessToken());
        HttpUtil.HttpResp httpCallerResp = null;
        try {
            String jsonParam = JacksonUtil.toJsonStr(param);
            LOGGER.debug("logout url[{}],param[{}]", url, jsonParam);
            httpCallerResp = HttpUtil.doPostJson(url, jsonParam);
            LOGGER.debug("logout response [{}],status [{}] [{}]", httpCallerResp.getResponseString(), httpCallerResp.getStatusCode(), httpCallerResp);
        } catch (Exception e) {
            LOGGER.error("接口退出失败", e);
            throw new LicenseException(ResponseCode.LICENSE_EXISTS_SERVICE_ERROR);
        }
        if (HttpStatus.OK != httpCallerResp.getStatusCode()) {
            LOGGER.error("接口退出失败 response [{}],status [{}] [{}]", httpCallerResp.getResponseString(), httpCallerResp.getStatusCode(), httpCallerResp);

            throw new LicenseException(ResponseCode.LICENSE_EXISTS_SERVICE_ERROR);
        }

        Map<String, Object> resultMap = JacksonUtil.toMap(httpCallerResp.getResponseString(), Object.class);
        if (AckCode.SUCCESS.name().equals(resultMap.get(ACK_CODE))) {
            LOGGER.debug("退出成功");
        } else {
            clearAccessToken(platformApp);
            throw new LicenseException(ResponseCode.LICENSE_EXISTS_SERVICE_ERROR);
        }
    }

    /**
     * 清除接口令牌信息
     *
     * @param platformApp App应用
     */
    private void clearAccessToken(PlatformApp platformApp) {
        cacheService.del(CacheKeyBuilder.LICENSE_PLATFORM_TOKEN.getCacheKey(platformApp.getAppKey()));
    }

    /**
     * 判断当前接口令牌是否有效（7天有效）
     *
     * @param platformApp App应用
     * @return 当前接口令牌是否有效
     */
    private boolean verifyAccessToken(PlatformApp platformApp) {
        TokenInfo tokenInfo = getCacheByAppKey(platformApp.getAppKey());
        return tokenInfo.getExpiryDate().after(new Date());
    }

    /**
     * 令牌信息
     */
    static class TokenInfo {
        /**
         * 接口令牌
         */
        private String accessToken;
        /**
         * 创建时间
         */
        private Date createDate;
        /**
         * 失效时间
         */
        private Date expiryDate;

        /**
         * 获取 接口令牌
         *
         * @return accessToken 接口令牌
         */
        public String getAccessToken() {
            return accessToken;
        }

        /**
         * 设置 接口令牌
         *
         * @param accessToken 接口令牌
         */
        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        /**
         * 获取 创建时间
         *
         * @return createDate 创建时间
         */
        public Date getCreateDate() {
            return createDate;
        }

        /**
         * 设置 创建时间
         *
         * @param createDate 创建时间
         */
        public void setCreateDate(Date createDate) {
            this.createDate = createDate;
        }

        /**
         * 获取 失效时间
         *
         * @return expiryDate 失效时间
         */
        public Date getExpiryDate() {
            return expiryDate;
        }

        /**
         * 设置 失效时间
         *
         * @param expiryDate 失效时间
         */
        public void setExpiryDate(Date expiryDate) {
            this.expiryDate = expiryDate;
        }
    }
}
