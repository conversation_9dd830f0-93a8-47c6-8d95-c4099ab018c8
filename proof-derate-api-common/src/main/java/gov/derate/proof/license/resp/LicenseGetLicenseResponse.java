package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * 查看电子证照-license/get_license接口-响应类
 * Company: Zsoft
 * CreateDate:2024/5/13
 *
 * <AUTHOR>
 */
public class LicenseGetLicenseResponse extends LicenseBaseResponse {
    /**
     * 数据
     */
    @JsonProperty("data")
    private LicenseGetLicenseItemResponse data;


    public LicenseGetLicenseItemResponse getData() {
        return data;
    }

    public void setData(LicenseGetLicenseItemResponse data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "LicenseGetLicenseResponse{" +
                "data=" + data +
                '}';
    }
}
