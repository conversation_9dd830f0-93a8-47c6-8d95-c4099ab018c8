package gov.derate.proof.license.req;

import gov.derate.proof.license.enums.LicenseItemStatus;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 证照目录请求体
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-19
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-19;
 */
public class LicenseItemListRequest extends ListRequest {

    /**
     * 查询关键字（证照名称，目录编制部门，电子证照目录编码）
     */
    private String keyword = "";
    /**
     * 证照状态（默认查询已发布，多个时用逗号（,）分隔）
     */
    private List<LicenseItemStatus> status;
    /**
     * 开通状态（默认已开通）
     * true,false,all
     */
    private String isOpen;
    /**
     * 忽略缩略图，default : true
     */
    private Boolean rtnIcon = false;

    public LicenseItemListRequest() {
    }

    public LicenseItemListRequest(Integer pageSize, Integer pageIndex) {
        this.pageSize = pageSize;
        this.pageIndex = pageIndex;
    }

    /**
     * 查询 关键字
     *
     * @return 查询关键字
     */
    public String getKeyword() {
        return StringUtils.isNotBlank(keyword) ? keyword : "";
    }

    /**
     * 设置 关键字
     *
     * @param keyword 关键字
     */
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    /**
     * Gets the value of 证照状态.
     *
     * @return the value of 证照状态
     */
    public List<LicenseItemStatus> getStatus() {
        return null == status ? new ArrayList<>() : status;
    }

    /**
     * Sets the 证照状态.
     *
     * @param status 证照状态
     */
    public void setStatus(List<LicenseItemStatus> status) {
        this.status = status;
    }

    public String getIsOpen() {
        return StringUtils.isBlank(isOpen) ? "all" : isOpen;
    }

    public void setIsOpen(String isOpen) {
        this.isOpen = isOpen;
    }

    /**
     * Gets the value of 忽略缩略图.
     *
     * @return the value of 忽略缩略图
     */
    public Boolean getRtnIcon() {
        return rtnIcon != null && rtnIcon;
    }

    /**
     * Sets the 忽略缩略图.
     *
     * @param rtnIcon 忽略缩略图
     */
    public void setRtnIcon(Boolean rtnIcon) {
        this.rtnIcon = rtnIcon;
    }
}
