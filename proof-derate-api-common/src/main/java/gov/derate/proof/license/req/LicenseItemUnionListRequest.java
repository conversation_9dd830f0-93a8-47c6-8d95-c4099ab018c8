package gov.derate.proof.license.req;

/**
 * <p>
 * 组合目录请求体
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-19
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-19;
 */
public class LicenseItemUnionListRequest extends ListRequest {

    /**
     * 证照名称
     */
    private String name;
    /**
     * 证照编码
     */
    private String code;
    /**
     * 是否返回详情
     */
    private Boolean returnField;

    public LicenseItemUnionListRequest() {

    }

    public LicenseItemUnionListRequest(Integer pageSize, Integer pageIndex) {
        this.pageSize = pageSize;
        this.pageIndex = pageIndex;
    }

    /**
     * Gets the value of 证照名称.
     *
     * @return the value of 证照名称
     */
    public String getName() {
        return name == null ? "" : name;
    }

    /**
     * Sets the 证照名称.
     *
     * @param name 证照名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Gets the value of 证照编码.
     *
     * @return the value of 证照编码
     */
    public String getCode() {
        return code == null ? "" : code;
    }

    /**
     * Sets the 证照编码.
     *
     * @param code 证照编码
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * Gets the value of 是否返回详情.
     *
     * @return the value of 是否返回详情
     */
    public Boolean getReturnField() {
        return returnField == null ? false : returnField;
    }

    /**
     * Sets the 是否返回详情.
     *
     * @param returnField 是否返回详情
     */
    public void setReturnField(Boolean returnField) {
        this.returnField = returnField;
    }

}
