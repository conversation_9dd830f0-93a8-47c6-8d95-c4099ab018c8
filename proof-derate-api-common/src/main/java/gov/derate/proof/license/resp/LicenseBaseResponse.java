package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.license.dto.ErrorDTO;
import gov.derate.proof.license.enums.AckCode;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * <p>
 * 响应基础类
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2020-08-11
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2020-08-11;
 */
public class LicenseBaseResponse implements Serializable {

    /**
     * 应答代码
     */
    @JsonProperty("ack_code")
    private AckCode ackCode;
    /**
     * 错误信息
     */
    private ArrayList<ErrorDTO> errors;

    public boolean getIsSuccessStatus() {
        return AckCode.SUCCESS == ackCode;
    }

    public String getErrorMessage() {
        if (CollectionUtils.isEmpty(errors)) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (ErrorDTO error : errors) {
            sb.append(error.getMessage()).append("\r\n");
        }

        return sb.toString();
    }

    /**
     * Gets the value of ackCode.
     *
     * @return the value of ackCode
     */
    public AckCode getAckCode() {
        return ackCode;
    }

    /**
     * Sets the ackCode.
     *
     * @param ackCode ackCode
     */
    public void setAckCode(AckCode ackCode) {
        this.ackCode = ackCode;
    }

    /**
     * Gets the value of errors.
     *
     * @return the value of errors
     */
    public ArrayList<ErrorDTO> getErrors() {
        return errors;
    }

    /**
     * Sets the errors.
     *
     * @param errors errors
     */
    public void setErrors(ArrayList<ErrorDTO> errors) {
        this.errors = errors;
    }
}
