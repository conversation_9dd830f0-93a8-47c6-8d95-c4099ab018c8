package gov.derate.proof.license.resp;

/**
 * 电子证明-issue接口响应对象
 * Company: Zsoft
 * CreateDate:2024/9/20
 *
 * <AUTHOR>
 */
public class LicenseItemIssueResponse extends LicenseBaseResponse {

    private static final long serialVersionUID = 2941870990138432663L;

    /**
     * 证照目录
     */
    private LicenseItemIssueResp data;

    public LicenseItemIssueResp getData() {
        return data;
    }

    public void setData(LicenseItemIssueResp data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "LicenseItemIssueResponse{" +
                "data=" + data +
                '}';
    }
}
