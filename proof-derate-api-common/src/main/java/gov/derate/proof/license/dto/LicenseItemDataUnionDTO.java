package gov.derate.proof.license.dto;

import java.io.Serializable;

/**
 * <p>
 * 组合目录 - 数据项
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021-08-19
 * </p>
 *
 * <AUTHOR>
 * @history create document;Mender:Liangguiming;Date:2021-08-19;
 */
public class LicenseItemDataUnionDTO implements Serializable {

    /**
     * 中文名称
     */
    private String name;
    /**
     * 拼音缩写
     */
    private String key;
    /**
     * 数据来源
     */
    private String source;

    /**
     * 获取 中文名称
     *
     * @return name 中文名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置 中文名称
     *
     * @param name 中文名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取 拼音缩写
     *
     * @return key 拼音缩写
     */
    public String getKey() {
        return key;
    }

    /**
     * 设置 拼音缩写
     *
     * @param key 拼音缩写
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 获取 数据来源
     *
     * @return source 数据来源
     */
    public String getSource() {
        return source;
    }

    /**
     * 设置 数据来源
     *
     * @param source 数据来源
     */
    public void setSource(String source) {
        this.source = source;
    }

    @Override
    public String toString() {
        return "LicenseItemDataUnionDTO{" +
                "name='" + name + '\'' +
                ", key='" + key + '\'' +
                ", source='" + source + '\'' +
                '}';
    }
}
