package gov.derate.proof.license.req;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * 省一网共享平台目录接口入参
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/11/20
 * </p>
 *
 * <AUTHOR>
 */
public class ProvinceCatalogRequest {
    /**
     * 证照名称
     */
    @JsonProperty("catalogName")
    private String catalogName;
    /**
     * 分页，从1开始
     */
    @JsonProperty("pageIndex")
    private String pageIndex;
    /**
     * 分页size，从1开始
     */
    @JsonProperty("pageSize")
    private String pageSize;

    public String getCatalogName() {
        return catalogName;
    }

    public void setCatalogName(String catalogName) {
        this.catalogName = catalogName;
    }

    public String getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(String pageIndex) {
        this.pageIndex = pageIndex;
    }

    public String getPageSize() {
        return pageSize;
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "ProvinceCatalogRequest{" +
                "catalogName='" + catalogName + '\'' +
                ", pageIndex='" + pageIndex + '\'' +
                ", pageSize='" + pageSize + '\'' +
                '}';
    }
}
