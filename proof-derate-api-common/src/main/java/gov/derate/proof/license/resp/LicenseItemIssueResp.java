package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 电子证明-issue接口响应类
 * Company: Zsoft
 * CreateDate:2024/9/20
 *
 * <AUTHOR>
 */
public class LicenseItemIssueResp {
    /**
     * 证照编码
     */
    @JsonProperty("license_code")
    private String licenseCode;

    /**
     * 制证授权码
     */
    @JsonProperty("auth_code")
    private String authCode;

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    @Override
    public String toString() {
        return "LicenseItemIssueResp{" +
                "licenseCode='" + licenseCode + '\'' +
                ", authCode='" + authCode + '\'' +
                '}';
    }
}
