package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

/**
 * <p>
 * 省一网共享平台目录接口出参
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2023/11/20
 * </p>
 *
 * <AUTHOR>
 */
public class ProvinceCatalogCommonResponse<T> {
    /**
     * 业务请求状态
     */
    @JsonProperty("status")
    private Integer status;
    @JsonProperty("result")
    private T result;
    /**
     * 异常响应信息
     */
    @JsonProperty("message")
    private String message;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "ProvinceCatalogCommonResponse{" +
                "status=" + status +
                ", result=" + result +
                ", message='" + message + '\'' +
                '}';
    }

    /**
     * 是否成功请求。
     *
     * @return true，为成功。
     */
    public boolean isSuccess() {
        return Objects.nonNull(this.status) && this.status == 200;
    }

}
