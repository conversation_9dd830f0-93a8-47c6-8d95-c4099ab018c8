package gov.derate.proof.license.resp;

import gov.derate.proof.license.dto.ImplementLicenseItemInfoDto;

import java.util.List;


/**
 * 获取电子证明实施目录信息响应类
 * Company: Zsoft
 * CreateDate:2024/8/8
 *
 * <AUTHOR>
 */
public class LicenseItemImplItemInfoResponse extends LicenseBaseResponse {
    private List<ImplementLicenseItemInfoDto> data;

    public List<ImplementLicenseItemInfoDto> getData() {
        return data;
    }

    public void setData(List<ImplementLicenseItemInfoDto> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "LicenseItemImplItemInfoResponse{" +
                "data=" + data +
                '}';
    }
}
