package gov.derate.proof.license.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import gov.derate.proof.license.enums.LicenseStatus;
import gov.derate.proof.license.enums.LicenseType;

import java.util.Date;

/**
 * 查看电子证照-license/get_license接口-响应类
 * Company: Zsoft
 * CreateDate:2024/5/13
 *
 * <AUTHOR>
 */
public class LicenseGetLicenseItemResponse {

    /**
     * 电子证照唯一标识码
     * 由电子证照系统按统一编码规则自动编码。
     */
    @JsonProperty("license_code")
    private String licenseCode;

    /**
     * 证照中文名称
     * 对应数据证照目录中文名称。
     */
    @JsonProperty("name")
    private String name;

    /**
     * 证照类型（证照执照，证明文件，批文批复，鉴定报告，办事结果）
     * 参照LicenseType代码
     */
    @JsonProperty("license_type")
    private LicenseType licenseType;

    /**
     * 证照号码，签发机构编制的证照文号或编号。
     */
    @JsonProperty("id_code")
    private String idCode;

    /**
     * 文书标题。证照类型为批文批复时填写。
     */
    @JsonProperty("doc_name")
    private String docName;

    /**
     * 文书内容概要。证照类型为批文批复时填写。
     */
    @JsonProperty("doc_summary")
    private String docSummary;

    /**
     * 文书主题词。证照类型为批文批复时填写。
     */
    @JsonProperty("doc_keyword")
    private String docKeyword;

    /**
     * 持证者名称。如有多个，中间以逗号（,）分割。
     */
    @JsonProperty("holder_name")
    private String holderName;

    /**
     * 持证者身份证件类型代码。如有多个，中间以逗号（,）分割。
     */
    @JsonProperty("holder_identity_type")
    private String holderIdentityType;

    /**
     * 持证者身份证件号码。如有多个，中间以逗号（,）分割。
     */
    @JsonProperty("holder_identity_num")
    private String holderIdentityNum;

    /**
     * 签发机构名称
     */
    @JsonProperty("issue_org_name")
    private String issueOrgName;

    /**
     * 签发机构的组织机构代码
     */
    @JsonProperty("issue_org_code")
    private String issueOrgCode;

    /**
     * 签发机构所属行政区划
     */
    @JsonProperty("division")
    private String division;

    /**
     * 签发机构所属行政区划代码
     */
    @JsonProperty("division_code")
    private String divisionCode;

    /**
     * 签发日期YYYYMMDD，证照签发的业务时间
     */
    @JsonProperty("issue_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueDate;

    /**
     * 证照有效期开始日期
     * YYYYMMDD
     */
    @JsonProperty("begin_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginDate;

    /**
     * 证照有效期结束日期
     * YYYYMMDD，没有截止有效日期时，为空
     */
    @JsonProperty("expiry_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiryDate;

    /**
     * 证照业务数据（结构化数据）
     * ，采用json格式记录。
     */
    @JsonProperty("data_fields")
    private String dataFields;

    /**
     * 证照附件数据
     * 采用json格式记录。
     */
    @JsonProperty("attachment_fields")
    private String attachmentFields;

    /**
     * 证照信息的可信等级
     * 参照TrustLevel代码。
     */
    @JsonProperty("trust_level")
    private String trustLevel;

    /**
     * 证照扩展信息。
     */
    @JsonProperty("extend_props")
    private String extendProps;

    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;

    /**
     * 签发证照对应办件的业务流水号
     */
    @JsonProperty("biz_num")
    private String bizNum;

    /**
     * 目录编码。对应的电子证照目录编码
     */
    @JsonProperty("license_item_code")
    private String licenseItemCode;

    /**
     * 证照状态（代码值）
     * 参照LicenseStatus代码。
     */
    @JsonProperty("license_status")
    private LicenseStatus licenseStatus;

    /**
     * 制证操作人
     */
    @JsonProperty("creator")
    private String creator;

    /**
     * 制证时间
     */
    @JsonProperty("creation_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creationTime;

    /**
     * 签发操作人
     */
    @JsonProperty("issuer")
    private String issuer;

    /**
     * 签发时间
     */
    @JsonProperty("issue_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTime;

    /**
     * 废止操作人
     */
    @JsonProperty("abolisher")
    private String abolisher;

    /**
     * 废止时间
     */
    @JsonProperty("abolish_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date abolishTime;

    /**
     * 废止原因
     */
    @JsonProperty("abolish_reason")
    private String abolishReason;

    /**
     * 关联的证照标识
     * 如有多个，中间以逗号（,）分割。
     */
    @JsonProperty("correlative_license")
    private String correlativeLicense;

    /**
     * 公钥值
     */
    @JsonProperty("public_key")
    private String publicKey;

    /**
     * 签发机构密钥标识符
     */
    @JsonProperty("s_sign_cert")
    private String sSignCert;

    /**
     * 签名算法
     */
    @JsonProperty("algorithm")
    private String algorithm;

    /**
     * 证照数据电文经过签名得出的签名值
     */
    @JsonProperty("s_sign_data")
    private String sSignData;

    /**
     * 最后修改者
     */
    @JsonProperty("last_modificator")
    private String lastModificator;

    /**
     * 最后修改时间
     */
    @JsonProperty("last_modification_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastModificationTime;

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LicenseType getLicenseType() {
        return licenseType;
    }

    public void setLicenseType(LicenseType licenseType) {
        this.licenseType = licenseType;
    }

    public String getIdCode() {
        return idCode;
    }

    public void setIdCode(String idCode) {
        this.idCode = idCode;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public String getDocSummary() {
        return docSummary;
    }

    public void setDocSummary(String docSummary) {
        this.docSummary = docSummary;
    }

    public String getDocKeyword() {
        return docKeyword;
    }

    public void setDocKeyword(String docKeyword) {
        this.docKeyword = docKeyword;
    }

    public String getHolderName() {
        return holderName;
    }

    public void setHolderName(String holderName) {
        this.holderName = holderName;
    }

    public String getHolderIdentityType() {
        return holderIdentityType;
    }

    public void setHolderIdentityType(String holderIdentityType) {
        this.holderIdentityType = holderIdentityType;
    }

    public String getHolderIdentityNum() {
        return holderIdentityNum;
    }

    public void setHolderIdentityNum(String holderIdentityNum) {
        this.holderIdentityNum = holderIdentityNum;
    }

    public String getIssueOrgName() {
        return issueOrgName;
    }

    public void setIssueOrgName(String issueOrgName) {
        this.issueOrgName = issueOrgName;
    }

    public String getIssueOrgCode() {
        return issueOrgCode;
    }

    public void setIssueOrgCode(String issueOrgCode) {
        this.issueOrgCode = issueOrgCode;
    }

    public String getDivision() {
        return division;
    }

    public void setDivision(String division) {
        this.division = division;
    }

    public String getDivisionCode() {
        return divisionCode;
    }

    public void setDivisionCode(String divisionCode) {
        this.divisionCode = divisionCode;
    }

    public Date getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getDataFields() {
        return dataFields;
    }

    public void setDataFields(String dataFields) {
        this.dataFields = dataFields;
    }

    public String getAttachmentFields() {
        return attachmentFields;
    }

    public void setAttachmentFields(String attachmentFields) {
        this.attachmentFields = attachmentFields;
    }

    public String getTrustLevel() {
        return trustLevel;
    }

    public void setTrustLevel(String trustLevel) {
        this.trustLevel = trustLevel;
    }

    public String getExtendProps() {
        return extendProps;
    }

    public void setExtendProps(String extendProps) {
        this.extendProps = extendProps;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBizNum() {
        return bizNum;
    }

    public void setBizNum(String bizNum) {
        this.bizNum = bizNum;
    }

    public String getLicenseItemCode() {
        return licenseItemCode;
    }

    public void setLicenseItemCode(String licenseItemCode) {
        this.licenseItemCode = licenseItemCode;
    }

    public LicenseStatus getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(LicenseStatus licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public String getIssuer() {
        return issuer;
    }

    public void setIssuer(String issuer) {
        this.issuer = issuer;
    }

    public Date getIssueTime() {
        return issueTime;
    }

    public void setIssueTime(Date issueTime) {
        this.issueTime = issueTime;
    }

    public String getAbolisher() {
        return abolisher;
    }

    public void setAbolisher(String abolisher) {
        this.abolisher = abolisher;
    }

    public Date getAbolishTime() {
        return abolishTime;
    }

    public void setAbolishTime(Date abolishTime) {
        this.abolishTime = abolishTime;
    }

    public String getAbolishReason() {
        return abolishReason;
    }

    public void setAbolishReason(String abolishReason) {
        this.abolishReason = abolishReason;
    }

    public String getCorrelativeLicense() {
        return correlativeLicense;
    }

    public void setCorrelativeLicense(String correlativeLicense) {
        this.correlativeLicense = correlativeLicense;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getsSignCert() {
        return sSignCert;
    }

    public void setsSignCert(String sSignCert) {
        this.sSignCert = sSignCert;
    }

    public String getAlgorithm() {
        return algorithm;
    }

    public void setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }

    public String getsSignData() {
        return sSignData;
    }

    public void setsSignData(String sSignData) {
        this.sSignData = sSignData;
    }

    public String getLastModificator() {
        return lastModificator;
    }

    public void setLastModificator(String lastModificator) {
        this.lastModificator = lastModificator;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    @Override
    public String toString() {
        return "LicenseGetLicenseAuditItemResponse{" +
                "licenseCode='" + licenseCode + '\'' +
                ", name='" + name + '\'' +
                ", licenseType=" + licenseType +
                ", idCode='" + idCode + '\'' +
                ", docName='" + docName + '\'' +
                ", docSummary='" + docSummary + '\'' +
                ", docKeyword='" + docKeyword + '\'' +
                ", holderName='" + holderName + '\'' +
                ", holderIdentityType='" + holderIdentityType + '\'' +
                ", issueOrgName='" + issueOrgName + '\'' +
                ", issueOrgCode='" + issueOrgCode + '\'' +
                ", division='" + division + '\'' +
                ", divisionCode='" + divisionCode + '\'' +
                ", issueDate=" + issueDate +
                ", beginDate=" + beginDate +
                ", expiryDate=" + expiryDate +
                ", dataFields='" + dataFields + '\'' +
                ", attachmentFields='" + attachmentFields + '\'' +
                ", trustLevel='" + trustLevel + '\'' +
                ", extendProps='" + extendProps + '\'' +
                ", remark='" + remark + '\'' +
                ", bizNum='" + bizNum + '\'' +
                ", licenseItemCode='" + licenseItemCode + '\'' +
                ", licenseStatus=" + licenseStatus +
                ", creator='" + creator + '\'' +
                ", creationTime=" + creationTime +
                ", issuer='" + issuer + '\'' +
                ", issueTime=" + issueTime +
                ", abolisher='" + abolisher + '\'' +
                ", abolishTime=" + abolishTime +
                ", abolishReason='" + abolishReason + '\'' +
                ", correlativeLicense='" + correlativeLicense + '\'' +
                ", publicKey='" + publicKey + '\'' +
                ", sSignCert='" + sSignCert + '\'' +
                ", algorithm='" + algorithm + '\'' +
                ", sSignData='" + sSignData + '\'' +
                ", lastModificator='" + lastModificator + '\'' +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}