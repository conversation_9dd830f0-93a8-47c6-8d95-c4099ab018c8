package gov.derate.proof.exempt.repository;

import gov.derate.proof.exempt.entity.ExemptOrderWriteBackDo;
import gov.license.common.api.repository.BaseRepository;
import org.springframework.stereotype.Repository;

/**
 * 粤商通工单信息回写 持久化
 *
 * <AUTHOR>
 * @date 2023/4/17
 */
@Repository
public interface ExemptOrderWriteBackDoRepository extends BaseRepository<ExemptOrderWriteBackDo, String> {

    /**
     * 根据工单号查找
     *
     * @param orderNo 授权工单号
     * @return 粤商通工单信息回写
     **/
    ExemptOrderWriteBackDo findByOrOrderNo(String orderNo);
}
