package gov.derate.proof.exempt.repository;

import gov.derate.proof.exempt.entity.ExemptAuthWriteBackDo;
import gov.license.common.api.repository.BaseRepository;
import org.springframework.stereotype.Repository;

/**
 * 粤商通授权信息回写 持久化
 *
 * <AUTHOR>
 * @date 2023/4/17
 */
@Repository
public interface ExemptAuthWriteBackDoRepository extends BaseRepository<ExemptAuthWriteBackDo, String> {

    /**
     * 根据授权工单号查找
     *
     * @param orderNo 授权工单号
     * @return 粤商通授权信息
     **/
    ExemptAuthWriteBackDo findByOrderNo(String orderNo);
}
