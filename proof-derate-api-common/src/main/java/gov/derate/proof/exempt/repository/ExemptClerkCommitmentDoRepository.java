package gov.derate.proof.exempt.repository;

import gov.derate.proof.exempt.entity.ExemptClerkCommitmentDo;
import gov.license.common.api.repository.BaseRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 免证办替代方式之办事人承诺表
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/13
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/13；
 */
@Repository
public interface ExemptClerkCommitmentDoRepository extends BaseRepository<ExemptClerkCommitmentDo, String> {
    /**
     * 根据免证办-办理号查询替代方式的协查号
     * @param serialNumberSet 办理号list
     * @return 数据
     */
    List<ExemptClerkCommitmentDo> findAllBySerialNumberIn(Collection<String> serialNumberSet);

    /**
     * 删除数据
     * @param serialNumber 办理号
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    void deleteAllBySerialNumber(String serialNumber);

    /**
     * 根据id和文件id，定位唯一一条数据
     * @param replaceWayId id
     * @param fileId 文件id
     * @return 替代方式数据
     */
    ExemptClerkCommitmentDo findByIdAndCommitAttachmentId(String replaceWayId, String fileId);

    /**
     * 统计事项下，有多少免证办材料
     * @param itemCodeSet 事项编码集合
     * @return 统计数据
     */
    @Query("select distinct o.proofListId from ExemptClerkCommitmentDo  o where o.itemCode in (:itemCodeSet)")
    Set<String> statExemptMaterial(@Param("itemCodeSet") Collection<String> itemCodeSet);
}
