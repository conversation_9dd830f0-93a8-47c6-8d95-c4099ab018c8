package gov.derate.proof.exempt.repository;

import gov.derate.proof.exempt.entity.ExemptLicenseItemApplyDo;
import gov.license.common.api.repository.BaseRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 免证办材料电子证照清理方式-开具证明申请
 *
 * <AUTHOR>
 * @date 2023/03/29.
 */
@Repository
public interface ExemptLicenseItemApplyRepository extends BaseRepository<ExemptLicenseItemApplyDo, String> {

    /**
     * 根据 免证办材料电子证明清理方式Id查询开具证明申请
     *
     * @param exemptLicenseItemId 免证办材料电子证明清理方式Id
     * @return 开具证明申请对象
     */
    ExemptLicenseItemApplyDo findByExemptLicenseItemId(String exemptLicenseItemId);
    /**
     * 根据推送状态查询开具电子证明申请
     *
     * @param pushed   是否已推送
     * @param pageable 分页对象
     * @return 开具电子证明申请集合
     */
    List<ExemptLicenseItemApplyDo> findAllByPushedOrderByLastModificationTimeDesc(boolean pushed, Pageable pageable);

}
