package gov.derate.proof.exempt.repository;

import gov.derate.proof.exempt.entity.ExemptAuthLicenseIndexDo;
import gov.license.common.api.repository.BaseRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 粤省事粤商通授权信息回写证照摘要信息 持久化
 *
 * <AUTHOR>
 * @date 2023/4/17
 */
@Repository
public interface ExemptAuthLicenseIndexDoRepository extends BaseRepository<ExemptAuthLicenseIndexDo, String> {

    /**
     * 根据令牌查询证照摘要信息
     *
     * @param authToken 令牌
     * @return 证照摘要信息
     */
    List<ExemptAuthLicenseIndexDo> findAllByAuthToken(String authToken);
}
