package gov.derate.proof.exempt.repository;

import gov.derate.proof.common.enums.VerificationAndInspectionEnum;
import gov.derate.proof.exempt.entity.ExemptCertificatesDo;
import gov.derate.proof.exempt.query.ExemptCertificatesManagerPageQuery;
import gov.license.common.api.repository.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 免证办清单表
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/13
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/13；
 */
@Repository
public interface ExemptCertificatesDoRepository extends BaseRepository<ExemptCertificatesDo, String> {
    /**
     * 根据免证办-办理号，查询唯一数据
     * @param serialNumber 办理号
     * @return 唯一的免证办数据
     */
    ExemptCertificatesDo findBySerialNumber(String serialNumber);

    /**
     * 删除数据
     * @param serialNumber 办理号
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    void deleteAllBySerialNumber(String serialNumber);

    /**
     * 根据状态，查询对象
     * @param enums 枚举状态
     * @return 对象List
     */
    List<ExemptCertificatesDo> findAllByProcessResultIn(Collection<VerificationAndInspectionEnum> enums);
    @Query(value =
            "select t from ExemptCertificatesDo t " +
                    " where " +
                    " (t.serialNumber =  :#{#queryObj.serialNumber} or  :#{#queryObj.serialNumber} is null ) " +
                    " and ((t.handleAffairsName  = :#{#queryObj.handleAffairsNameOrBizOrgName} or t.bizOrgName  = :#{#queryObj.handleAffairsNameOrBizOrgName} ) or :#{#queryObj.handleAffairsNameOrBizOrgName} is null ) " +
                    " and ((t.identityNumber  like :#{#queryObj.identityNumberOrOrgCode} or t.bizOrgIdentityNum  like :#{#queryObj.identityNumberOrOrgCode}) or :#{#queryObj.identityNumberOrOrgCode} is null ) " +
                    " and (t.toUserOrgCode  in :#{#queryObj.creditCodeList} or :#{#queryObj.creditCodeList==null?null:'-1'} is null ) " +
                    " and (t.processResult  in :#{#queryObj.processResultList} or :#{#queryObj.processResultList==null?null:'-1'} is null ) " +
                    " and (t.operateTime >= :#{#queryObj.operateBeginTime} or :#{#queryObj.operateBeginTime} is null ) " +
                    " and (t.operateTime <= :#{#queryObj.operateEndTime} or :#{#queryObj.operateEndTime} is null ) " +
                    " and (t.itemCode like :#{#queryObj.itemCodeLike} or :#{#queryObj.itemCodeLike} is null ) " +
                    " and (t.itemCode in( select p.itemCode from ItemDo p where p.itemCode = t.itemCode and p.itemName like  :#{#queryObj.itemNameLike} )  or :#{#queryObj.itemNameLike} is null ) " +
                    " and (t.itemCode in( select p.itemCode from ItemDo p where p.itemCode = t.itemCode and p.divisionCode =  :#{#queryObj.itemDivisionCode} )  or :#{#queryObj.itemDivisionCode} is null ) " +
                    // " and (t.operateId in( select p.id from AccountViewDo p where p.id = t.operateId and p.userName like  :#{#queryObj.getOperatorLike} )  or :#{#queryObj.getOperatorLike} is null ) " +
                   "",
            countQuery =
                    "select count(t) from ExemptCertificatesDo t " +
                            " where " +
                            " (t.serialNumber =  :#{#queryObj.serialNumber} or  :#{#queryObj.serialNumber} is null ) " +
                            " and ((t.handleAffairsName  = :#{#queryObj.handleAffairsNameOrBizOrgName} or t.bizOrgName  = :#{#queryObj.handleAffairsNameOrBizOrgName} ) or :#{#queryObj.handleAffairsNameOrBizOrgName} is null ) " +
                            " and ((t.identityNumber  like :#{#queryObj.identityNumberOrOrgCode} or t.bizOrgIdentityNum  like :#{#queryObj.identityNumberOrOrgCode}) or :#{#queryObj.identityNumberOrOrgCode} is null ) " +
                            " and (t.toUserOrgCode  in :#{#queryObj.creditCodeList} or :#{#queryObj.creditCodeList==null?null:'-1'} is null ) " +
                            " and (t.processResult  in :#{#queryObj.processResultList} or :#{#queryObj.processResultList==null?null:'-1'} is null ) " +
                            " and (t.operateTime >= :#{#queryObj.operateBeginTime} or :#{#queryObj.operateBeginTime} is null ) " +
                            " and (t.operateTime <= :#{#queryObj.operateEndTime} or :#{#queryObj.operateEndTime} is null ) " +
                            " and (t.itemCode like :#{#queryObj.itemCodeLike} or :#{#queryObj.itemCodeLike} is null ) " +
                            " and (t.itemCode in( select p.itemCode from ItemDo p where p.itemCode = t.itemCode and p.itemName like  :#{#queryObj.itemNameLike} )  or :#{#queryObj.itemNameLike} is null ) " +
                            " and (t.itemCode in( select p.itemCode from ItemDo p where p.itemCode = t.itemCode and p.divisionCode =  :#{#queryObj.itemDivisionCode} )  or :#{#queryObj.itemDivisionCode} is null ) " +
                            //     " and (t.operateId in( select p.id from AccountViewDo p where p.id = t.operateId and p.userName like  :#{#queryObj.getOperatorLike} )  or :#{#queryObj.getOperatorLike} is null ) " +
                            ""
    )
    Page<ExemptCertificatesDo> managerPage(@Param("queryObj") ExemptCertificatesManagerPageQuery query, Pageable pageable);
}
