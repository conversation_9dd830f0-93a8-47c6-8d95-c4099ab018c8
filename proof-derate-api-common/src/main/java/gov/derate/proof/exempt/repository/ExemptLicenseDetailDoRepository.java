package gov.derate.proof.exempt.repository;

import gov.derate.proof.exempt.entity.ExemptLicenseDetailDo;
import gov.license.common.api.repository.BaseRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 免证办材料电子证照清理方式
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2022/6/13
 * </p>
 *
 * <AUTHOR>
 * @history 创建文档；Mender:lym；Date:2022/6/13；
 */
@Repository
public interface ExemptLicenseDetailDoRepository extends BaseRepository<ExemptLicenseDetailDo, String> {

    /**
     * 根据免证办-办理号查询替代方式的协查号
     *
     * @param serialNumberSet 办理号list
     * @return 数据
     */
    List<ExemptLicenseDetailDo> findAllBySerialNumberIn(Collection<String> serialNumberSet);

    /**
     * 删除数据
     *
     * @param serialNumber 办理号
     */
    @Transactional(rollbackFor = Exception.class)
    @Modifying
    void deleteAllBySerialNumber(String serialNumber);

    /**
     * 根据id和替代方式id，定位唯一一条数据
     *
     * @param licenseCode  电子证照号码唯一标识
     * @param replaceWayId 替代方式id
     * @return 替代方式数据
     */
    ExemptLicenseDetailDo findByLicenseCodeAndExemptLicenseId(String licenseCode, String replaceWayId);

    /**
     * 根据id和替代方式id，定位唯一一条数据
     *
     * @param archivingId  归档id
     * @param replaceWayId 替代方式id
     * @return 替代方式数据
     */
    ExemptLicenseDetailDo findByArchivingIdAndExemptLicenseId(String archivingId, String replaceWayId);

    /**
     * 根据id定位唯一一条数据
     *
     * @param replaceWayId 替代方式id
     * @return 替代方式数据
     */
    List<ExemptLicenseDetailDo> findAllByExemptLicenseId(String replaceWayId);
}
