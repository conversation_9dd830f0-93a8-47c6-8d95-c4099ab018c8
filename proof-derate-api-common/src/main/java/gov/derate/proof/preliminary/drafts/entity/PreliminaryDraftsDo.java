package gov.derate.proof.preliminary.drafts.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import gov.derate.proof.common.entity.AbstractDomainEntity;
import gov.derate.proof.common.enums.ItemMaterialSourceEnum;
import gov.derate.proof.common.enums.ItemProofStatusEnum;
import gov.derate.proof.common.enums.ProofClearTypeEnum;
import gov.derate.proof.common.enums.ProofProvideTypeEnum;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.util.Arrays;

/**
 * <p>
 * 初稿表
 * </p>
 * rights reserved.
 * </p>
 * <p>
 * Company: Zsoft
 * </p>
 * <p>
 * CreateDate:2021/8/20 16:04
 * </p>
 *
 * <AUTHOR>
 */
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "tbl_preliminary_drafts")
public class PreliminaryDraftsDo extends AbstractDomainEntity {


    /**
     * 事项清单ID
     */
    @Column(name = "proof_list_id")
    private String proofListId;
    /**
     * 事项编码
     */
    @Column(name = "item_code")
    private String itemCode;
    /**
     * 事项材料id
     */
    @Column(name = "material_id")
    private String materialId;
    /**
     * 证明名称（材料名称）
     */
    @Column(name = "proof_name")
    private String proofName;
    /**
     * 事项材料来源
     * 人工添加(非标准材料)、对接获取（标准材料）
     */
    @Column(name = "item_material_source")
    private ItemMaterialSourceEnum itemMaterialSource;
    /**
     * 事项证明状态：0待梳理确认、1已梳理确认、2审核通过、3审核不通过
     */
    @Column(name = "item_proof_status")
    private ItemProofStatusEnum itemProofStatus;
    /**
     * 证明清理类型:0替代取消、1直接取消
     */
    @Column(name = "proof_clear_type")
    private ProofClearTypeEnum proofClearType;
    /**
     * 直接取消说明
     */
    @Column(name = "direct_description")
    private String directDescription;
    /**
     * 替代取消方式 ：0转化为电子证照/其他证件、1办事人承诺、2数据共享（证明开具部门）、
     * 3人工协查（证明开具部门）、4部门自行调查、5其它
     */
    @Column(name = "replace_cancel_way")
    private String replaceCancelWay;
    /**
     * 转换为电子证照说明
     */
    @Column(name = "license_description")
    private String licenseDescription;
    /**
     * 该材料关联的电子证照的目录编码(逗号隔开)
     */
    @Column(name = "license_code")
    private String licenseCode;
    /**
     * 该材料关联的电子证照的名称(逗号隔开)
     */
    @Column(name = "license_name")
    private String licenseName;
    /**
     * 转换为电子证明说明
     */
    @Column(name = "license_item_description")
    private String licenseItemDescription;

    /**
     * 该材料关联的电子证明的目录编码(逗号隔开)
     */
    @Column(name = "license_item_code")
    private String licenseItemCode;
    /**
     * 该材料关联的电子证明的名称(逗号隔开)
     */
    @Column(name = "license_item_name")
    private String licenseItemName;
    /**
     * 承诺书说明
     */
    @Column(name = "commit_book_description")
    private String commitBookDescription;
    /**
     * 承诺书模板附件ID
     */
    @Column(name = "commit_attachment_id")
    private String commitAttachmentId;
    /**
     * 承诺书模板文件名称
     */
    @Column(name = "commit_attachment_name")
    private String commitAttachmentName;
    /**
     * 承诺书模板附件数据
     */
    @JsonIgnore
    @Lob
    @Column(name = "FILE_DATA")
    private byte[] fileData;
    /**
     * 所属行业部门名称之数据共享
     */
    @Column(name = "INDUSTRY_DEPT_NAME_DATA_SHARED")
    private String industryDeptNameByDataShared;
    /**
     * 所属行业部门代码之数据共享
     */
    @Column(name = "INDUSTRY_DEPT_CODE_DATA_SHARED")
    private String industryDeptCodeByDataShared;

    /**
     * 证明开具单位类型之数据共享
     */
    @Column(name = "PROOF_PROVIDE_TYPE_DATA_SHARED")
    private ProofProvideTypeEnum proofProvideTypeByDataShared;
    /**
     * 数据共享说明
     */
    @Column(name = "data_shared_description")
    private String dataSharedDescription;
    /**
     * 所属行业部门名称之人工协查部门
     */
    @Column(name = "INDUSTRY_DEPT_NAME_INVESTI")
    private String industryDeptNameByInvestigation;

    /**
     * 所属行业部门代码之人工协查部门
     */
    @Column(name = "INDUSTRY_DEPT_CODE_INVESTI")
    private String industryDeptCodeByInvestigation;
    /**
     * 证明开具单位类型之人工协查部门
     */
    @Column(name = "PROOF_PROVIDE_TYPE_INVESTI")
    private ProofProvideTypeEnum proofProvideTypeByInvestigation;

    /**
     * 人工协查说明
     */
    @Column(name = "investigation_description")
    private String investigationDescription;

    /**
     * 部门名称 （部门自行调查）
     */
    @Column(name = "dept_name")
    private String deptName;
    /**
     * 部门代码 （部门自行调查）
     */
    @Column(name = "dept_code")
    private String deptCode;
    /**
     * 自行调查说明 （部门自行调查）
     */
    @Column(name = "dept_cancel_description")
    private String deptCancelDescription;
    /**
     * 其它说明（其他替代取消方式的说明）
     */
    @Column(name = "other_clear_description")
    private String otherClearDescription;
    /**
     * 证明目录id
     */
    @Column(name = "proof_catalog_id")
    private String proofCatalogId;

    public String getProofListId() {
        return proofListId;
    }

    public void setProofListId(String proofListId) {
        this.proofListId = proofListId;
    }

    public String getItemCode() {
        return itemCode;
    }

    public void setItemCode(String itemCode) {
        this.itemCode = itemCode;
    }

    public String getMaterialId() {
        return materialId;
    }

    public void setMaterialId(String materialId) {
        this.materialId = materialId;
    }

    public String getProofName() {
        return proofName;
    }

    public void setProofName(String proofName) {
        this.proofName = proofName;
    }

    public ItemMaterialSourceEnum getItemMaterialSource() {
        return itemMaterialSource;
    }

    public void setItemMaterialSource(ItemMaterialSourceEnum itemMaterialSource) {
        this.itemMaterialSource = itemMaterialSource;
    }

    public ItemProofStatusEnum getItemProofStatus() {
        return itemProofStatus;
    }

    public void setItemProofStatus(ItemProofStatusEnum itemProofStatus) {
        this.itemProofStatus = itemProofStatus;
    }

    public ProofClearTypeEnum getProofClearType() {
        return proofClearType;
    }

    public void setProofClearType(ProofClearTypeEnum proofClearType) {
        this.proofClearType = proofClearType;
    }

    public String getDirectDescription() {
        return directDescription;
    }

    public void setDirectDescription(String directDescription) {
        this.directDescription = directDescription;
    }

    public String getReplaceCancelWay() {
        return replaceCancelWay;
    }

    public void setReplaceCancelWay(String replaceCancelWay) {
        this.replaceCancelWay = replaceCancelWay;
    }

    public String getLicenseDescription() {
        return licenseDescription;
    }

    public void setLicenseDescription(String licenseDescription) {
        this.licenseDescription = licenseDescription;
    }

    public String getLicenseCode() {
        return licenseCode;
    }

    public void setLicenseCode(String licenseCode) {
        this.licenseCode = licenseCode;
    }

    public String getLicenseName() {
        return licenseName;
    }

    public void setLicenseName(String licenseName) {
        this.licenseName = licenseName;
    }

    public String getLicenseItemDescription() {
        return licenseItemDescription;
    }

    public void setLicenseItemDescription(String licenseItemDescription) {
        this.licenseItemDescription = licenseItemDescription;
    }

    public String getLicenseItemCode() {
        return licenseItemCode;
    }

    public void setLicenseItemCode(String licenseItemCode) {
        this.licenseItemCode = licenseItemCode;
    }

    public String getLicenseItemName() {
        return licenseItemName;
    }

    public void setLicenseItemName(String licenseItemName) {
        this.licenseItemName = licenseItemName;
    }

    public String getCommitBookDescription() {
        return commitBookDescription;
    }

    public void setCommitBookDescription(String commitBookDescription) {
        this.commitBookDescription = commitBookDescription;
    }

    public String getCommitAttachmentId() {
        return commitAttachmentId;
    }

    public void setCommitAttachmentId(String commitAttachmentId) {
        this.commitAttachmentId = commitAttachmentId;
    }

    public String getCommitAttachmentName() {
        return commitAttachmentName;
    }

    public void setCommitAttachmentName(String commitAttachmentName) {
        this.commitAttachmentName = commitAttachmentName;
    }

    public byte[] getFileData() {
        return fileData;
    }

    public void setFileData(byte[] fileData) {
        this.fileData = fileData;
    }

    public String getIndustryDeptNameByDataShared() {
        return industryDeptNameByDataShared;
    }

    public void setIndustryDeptNameByDataShared(String industryDeptNameByDataShared) {
        this.industryDeptNameByDataShared = industryDeptNameByDataShared;
    }

    public String getIndustryDeptCodeByDataShared() {
        return industryDeptCodeByDataShared;
    }

    public void setIndustryDeptCodeByDataShared(String industryDeptCodeByDataShared) {
        this.industryDeptCodeByDataShared = industryDeptCodeByDataShared;
    }

    public ProofProvideTypeEnum getProofProvideTypeByDataShared() {
        return proofProvideTypeByDataShared;
    }

    public void setProofProvideTypeByDataShared(ProofProvideTypeEnum proofProvideTypeByDataShared) {
        this.proofProvideTypeByDataShared = proofProvideTypeByDataShared;
    }

    public String getDataSharedDescription() {
        return dataSharedDescription;
    }

    public void setDataSharedDescription(String dataSharedDescription) {
        this.dataSharedDescription = dataSharedDescription;
    }

    public String getIndustryDeptNameByInvestigation() {
        return industryDeptNameByInvestigation;
    }

    public void setIndustryDeptNameByInvestigation(String industryDeptNameByInvestigation) {
        this.industryDeptNameByInvestigation = industryDeptNameByInvestigation;
    }

    public String getIndustryDeptCodeByInvestigation() {
        return industryDeptCodeByInvestigation;
    }

    public void setIndustryDeptCodeByInvestigation(String industryDeptCodeByInvestigation) {
        this.industryDeptCodeByInvestigation = industryDeptCodeByInvestigation;
    }

    public ProofProvideTypeEnum getProofProvideTypeByInvestigation() {
        return proofProvideTypeByInvestigation;
    }

    public void setProofProvideTypeByInvestigation(ProofProvideTypeEnum proofProvideTypeByInvestigation) {
        this.proofProvideTypeByInvestigation = proofProvideTypeByInvestigation;
    }

    public String getInvestigationDescription() {
        return investigationDescription;
    }

    public void setInvestigationDescription(String investigationDescription) {
        this.investigationDescription = investigationDescription;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptCancelDescription() {
        return deptCancelDescription;
    }

    public void setDeptCancelDescription(String deptCancelDescription) {
        this.deptCancelDescription = deptCancelDescription;
    }

    public String getOtherClearDescription() {
        return otherClearDescription;
    }

    public void setOtherClearDescription(String otherClearDescription) {
        this.otherClearDescription = otherClearDescription;
    }

    public String getProofCatalogId() {
        return proofCatalogId;
    }

    public void setProofCatalogId(String proofCatalogId) {
        this.proofCatalogId = proofCatalogId;
    }

    @Override
    public String toString() {
        return "PreliminaryDraftsDo{" +
                "proofListId='" + proofListId + '\'' +
                ", itemCode='" + itemCode + '\'' +
                ", materialId='" + materialId + '\'' +
                ", proofName='" + proofName + '\'' +
                ", itemMaterialSource=" + itemMaterialSource +
                ", itemProofStatus=" + itemProofStatus +
                ", proofClearType=" + proofClearType +
                ", directDescription='" + directDescription + '\'' +
                ", replaceCancelWay='" + replaceCancelWay + '\'' +
                ", licenseDescription='" + licenseDescription + '\'' +
                ", licenseCode='" + licenseCode + '\'' +
                ", licenseName='" + licenseName + '\'' +
                ", licenseItemDescription='" + licenseItemDescription + '\'' +
                ", licenseItemCode='" + licenseItemCode + '\'' +
                ", licenseItemName='" + licenseItemName + '\'' +
                ", commitBookDescription='" + commitBookDescription + '\'' +
                ", commitAttachmentId='" + commitAttachmentId + '\'' +
                ", commitAttachmentName='" + commitAttachmentName + '\'' +
                ", fileData=" + Arrays.toString(fileData) +
                ", industryDeptNameByDataShared='" + industryDeptNameByDataShared + '\'' +
                ", industryDeptCodeByDataShared='" + industryDeptCodeByDataShared + '\'' +
                ", proofProvideTypeByDataShared=" + proofProvideTypeByDataShared +
                ", dataSharedDescription='" + dataSharedDescription + '\'' +
                ", industryDeptNameByInvestigation='" + industryDeptNameByInvestigation + '\'' +
                ", industryDeptCodeByInvestigation='" + industryDeptCodeByInvestigation + '\'' +
                ", proofProvideTypeByInvestigation=" + proofProvideTypeByInvestigation +
                ", investigationDescription='" + investigationDescription + '\'' +
                ", deptName='" + deptName + '\'' +
                ", deptCode='" + deptCode + '\'' +
                ", deptCancelDescription='" + deptCancelDescription + '\'' +
                ", otherClearDescription='" + otherClearDescription + '\'' +
                ", proofCatalogId='" + proofCatalogId + '\'' +
                '}';
    }
}
