spring.datasource.druid.url=jdbc:dm://IP:PORT/proof_derate
spring.datasource.druid.username=ENC()
spring.datasource.druid.password=ENC()
# 链接有效性参数
spring.datasource.druid.validation-query=select 1
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
# 连接池参数
spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=205
spring.datasource.druid.max-active=300
spring.datasource.druid.max-wait=5000
spring.datasource.druid.poolPreparedStatements=true
spring.datasource.druid.maxOpenPreparedStatements=100
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.database=oracle