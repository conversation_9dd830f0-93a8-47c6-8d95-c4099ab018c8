<config xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
        xmlns='http://www.ehcache.org/v3'
        xsi:schemaLocation="http://www.ehcache.org/v3
            http://www.ehcache.org/schema/ehcache-core-3.7.xsd">

    <!-- 一个缓存模板。如果应用程序有多个缓存，但缓存的配置基本相同，那么这将特别有利。  -->

    <!-- 持久缓存目录 persistence(持久性标记) 在持久性标记中，我们为硬盘(磁盘存储)上基于文件的缓存定义了目录。这只是文件夹的定义。-->
    <persistence directory="proof-derate-api/cache"/>

    <!-- Default cache template -->
    <cache-template name="default">
        <expiry>
            <!-- 在expires标签中，我们定义了30秒的生存时间(ttl)。生存时间指定缓存条目独立于访问可以在缓存中保留多长时间。指定的时间过期后，将从缓存中删除该值。 -->
            <ttl unit="seconds">60</ttl>
        </expiry>
        <!-- 配置缓存的层和容量。 -->
        <resources>
            <!-- 对于堆上存储，我们配置了1,000个缓存条目的容量。这是开始删除缓存之前的最大条目数。 -->
            <heap unit="entries">1000</heap>
            <!-- 对于堆外存储，我们配置了10mb的容量。 -->
            <offheap unit="MB">10</offheap>
            <!-- 作为磁盘缓存，我们配置了20mb 磁盘缓存必须始终具有比堆缓存更高的内存容量，否则应用程序在解析XML文件时在应用程序启动时抛出异常。 -->
            <disk persistent="true" unit="MB">20</disk>
        </resources>
    </cache-template>

    <cache alias="minute" uses-template="default"></cache>

    <cache alias="hour" uses-template="default">
        <expiry>
            <ttl unit="hours">1</ttl>
        </expiry>
    </cache>

    <cache alias="day" uses-template="default">
        <expiry>
            <ttl unit="days">1</ttl>
        </expiry>
    </cache>

    <cache alias="week" uses-template="default">
        <expiry>
            <ttl unit="days">7</ttl>
        </expiry>
    </cache>

    <cache alias="month" uses-template="default">
        <expiry>
            <ttl unit="days">30</ttl>
        </expiry>
    </cache>

</config>