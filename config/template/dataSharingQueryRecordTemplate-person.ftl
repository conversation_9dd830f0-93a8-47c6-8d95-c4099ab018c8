<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?mso-application progid="Word.Document"?>
<w:wordDocument xmlns:w="http://schemas.microsoft.com/office/word/2003/wordml" xmlns:v="urn:schemas-microsoft-com:vml"
                xmlns:wx="http://schemas.microsoft.com/office/word/2003/auxHint"
                xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882"
                w:macrosPresent="no" w:embeddedObjPresent="no" w:ocxPresent="no" xml:space="preserve"
><o:DocumentProperties>
        <o:Author>july mai</o:Author>
        <o:LastAuthor>zz-computer</o:LastAuthor>
        <o:Revision>46</o:Revision>
        <o:Created>2023-04-26T12:05:00Z</o:Created>
        <o:LastSaved>2023-11-21T04:06:04Z</o:LastSaved>
        <o:TotalTime>0</o:TotalTime>
        <o:Pages>1</o:Pages>
        <o:Words>59</o:Words>
        <o:Characters>340</o:Characters>
        <o:Lines>2</o:Lines>
        <o:Paragraphs>1</o:Paragraphs>
        <o:CharactersWithSpaces>398</o:CharactersWithSpaces>
        <o:Version>14</o:Version>
    </o:DocumentProperties>
    <o:CustomDocumentProperties>
        <o:KSOProductBuildVer dt:dt="string">2052-11.8.2.12085</o:KSOProductBuildVer>
        <o:ICV dt:dt="string">FAB1EA842B3C4A1883A0B80127004FDD</o:ICV>
    </o:CustomDocumentProperties>
    <w:fonts>
        <w:defaultFonts w:ascii="Times New Roman" w:fareast="宋体" w:h-ansi="Times New Roman" w:cs="Times New Roman"/>
        <w:font w:name="Times New Roman">
            <w:panose-1 w:val="02020603050405020304"/>
            <w:charset w:val="00"/>
            <w:family w:val="Auto"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="E0002EFF" w:usb-1="C000785B" w:usb-2="00000009" w:usb-3="00000000" w:csb-0="400001FF"
                   w:csb-1="FFFF0000"/>
        </w:font>
        <w:font w:name="宋体">
            <w:panose-1 w:val="02010600030101010101"/>
            <w:charset w:val="86"/>
            <w:family w:val="Auto"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="00000203" w:usb-1="288F0000" w:usb-2="00000006" w:usb-3="00000000" w:csb-0="00040001"
                   w:csb-1="00000000"/>
        </w:font>
        <w:font w:name="Wingdings">
            <w:panose-1 w:val="05000000000000000000"/>
            <w:charset w:val="02"/>
            <w:family w:val="Auto"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="00000000" w:usb-1="00000000" w:usb-2="00000000" w:usb-3="00000000" w:csb-0="80000000"
                   w:csb-1="00000000"/>
        </w:font>
        <w:font w:name="Arial">
            <w:panose-1 w:val="020B0604020202020204"/>
            <w:charset w:val="01"/>
            <w:family w:val="SWiss"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="E0002EFF" w:usb-1="C000785B" w:usb-2="00000009" w:usb-3="00000000" w:csb-0="400001FF"
                   w:csb-1="FFFF0000"/>
        </w:font>
        <w:font w:name="黑体">
            <w:panose-1 w:val="02010609060101010101"/>
            <w:charset w:val="86"/>
            <w:family w:val="Auto"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="800002BF" w:usb-1="38CF7CFA" w:usb-2="00000016" w:usb-3="00000000" w:csb-0="00040001"
                   w:csb-1="00000000"/>
        </w:font>
        <w:font w:name="Courier New">
            <w:panose-1 w:val="02070309020205020404"/>
            <w:charset w:val="01"/>
            <w:family w:val="Modern"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="E0002EFF" w:usb-1="C0007843" w:usb-2="00000009" w:usb-3="00000000" w:csb-0="400001FF"
                   w:csb-1="FFFF0000"/>
        </w:font>
        <w:font w:name="Symbol">
            <w:panose-1 w:val="05050102010706020507"/>
            <w:charset w:val="02"/>
            <w:family w:val="Roman"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="00000000" w:usb-1="00000000" w:usb-2="00000000" w:usb-3="00000000" w:csb-0="80000000"
                   w:csb-1="00000000"/>
        </w:font>
        <w:font w:name="Calibri">
            <w:panose-1 w:val="020F0502020204030204"/>
            <w:charset w:val="00"/>
            <w:family w:val="SWiss"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="E4002EFF" w:usb-1="C200247B" w:usb-2="00000009" w:usb-3="00000000" w:csb-0="200001FF"
                   w:csb-1="00000000"/>
        </w:font>
        <w:font w:name="Wingdings">
            <w:panose-1 w:val="05000000000000000000"/>
            <w:charset w:val="00"/>
            <w:family w:val="Auto"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="00000000" w:usb-1="00000000" w:usb-2="00000000" w:usb-3="00000000" w:csb-0="80000000"
                   w:csb-1="00000000"/>
        </w:font>
        <w:font w:name="Arial">
            <w:panose-1 w:val="020B0604020202020204"/>
            <w:charset w:val="00"/>
            <w:family w:val="Auto"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="E0002EFF" w:usb-1="C000785B" w:usb-2="00000009" w:usb-3="00000000" w:csb-0="400001FF"
                   w:csb-1="FFFF0000"/>
        </w:font>
        <w:font w:name="Courier New">
            <w:panose-1 w:val="02070309020205020404"/>
            <w:charset w:val="00"/>
            <w:family w:val="Auto"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="E0002EFF" w:usb-1="C0007843" w:usb-2="00000009" w:usb-3="00000000" w:csb-0="400001FF"
                   w:csb-1="FFFF0000"/>
        </w:font>
        <w:font w:name="Symbol">
            <w:panose-1 w:val="05050102010706020507"/>
            <w:charset w:val="00"/>
            <w:family w:val="Auto"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="00000000" w:usb-1="00000000" w:usb-2="00000000" w:usb-3="00000000" w:csb-0="80000000"
                   w:csb-1="00000000"/>
        </w:font>
        <w:font w:name="等线">
            <w:panose-1 w:val="02010600030101010101"/>
            <w:charset w:val="86"/>
            <w:family w:val="Auto"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="A00002BF" w:usb-1="38CF7CFA" w:usb-2="00000016" w:usb-3="00000000" w:csb-0="0004000F"
                   w:csb-1="00000000"/>
        </w:font>
        <w:font w:name="微软雅黑">
            <w:panose-1 w:val="020B0503020204020204"/>
            <w:charset w:val="86"/>
            <w:family w:val="Auto"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="80000287" w:usb-1="2ACF3C50" w:usb-2="00000016" w:usb-3="00000000" w:csb-0="0004001F"
                   w:csb-1="00000000"/>
        </w:font>
        <w:font w:name="Tahoma">
            <w:panose-1 w:val="020B0604030504040204"/>
            <w:charset w:val="00"/>
            <w:family w:val="Auto"/>
            <w:pitch w:val="Default"/>
            <w:sig w:usb-0="E1002EFF" w:usb-1="C000605B" w:usb-2="00000029" w:usb-3="00000000" w:csb-0="200101FF"
                   w:csb-1="20280000"/>
        </w:font>
    </w:fonts>
    <w:styles>
        <w:latentStyles w:defLockedState="off" w:latentStyleCount="260">
            <w:lsdException w:name="Normal"/>
            <w:lsdException w:name="heading 1"/>
            <w:lsdException w:name="heading 2"/>
            <w:lsdException w:name="heading 3"/>
            <w:lsdException w:name="heading 4"/>
            <w:lsdException w:name="heading 5"/>
            <w:lsdException w:name="heading 6"/>
            <w:lsdException w:name="heading 7"/>
            <w:lsdException w:name="heading 8"/>
            <w:lsdException w:name="heading 9"/>
            <w:lsdException w:name="index 1"/>
            <w:lsdException w:name="index 2"/>
            <w:lsdException w:name="index 3"/>
            <w:lsdException w:name="index 4"/>
            <w:lsdException w:name="index 5"/>
            <w:lsdException w:name="index 6"/>
            <w:lsdException w:name="index 7"/>
            <w:lsdException w:name="index 8"/>
            <w:lsdException w:name="index 9"/>
            <w:lsdException w:name="toc 1"/>
            <w:lsdException w:name="toc 2"/>
            <w:lsdException w:name="toc 3"/>
            <w:lsdException w:name="toc 4"/>
            <w:lsdException w:name="toc 5"/>
            <w:lsdException w:name="toc 6"/>
            <w:lsdException w:name="toc 7"/>
            <w:lsdException w:name="toc 8"/>
            <w:lsdException w:name="toc 9"/>
            <w:lsdException w:name="Normal Indent"/>
            <w:lsdException w:name="footnote text"/>
            <w:lsdException w:name="annotation text"/>
            <w:lsdException w:name="header"/>
            <w:lsdException w:name="footer"/>
            <w:lsdException w:name="index heading"/>
            <w:lsdException w:name="caption"/>
            <w:lsdException w:name="table of figures"/>
            <w:lsdException w:name="envelope address"/>
            <w:lsdException w:name="envelope return"/>
            <w:lsdException w:name="footnote reference"/>
            <w:lsdException w:name="annotation reference"/>
            <w:lsdException w:name="line number"/>
            <w:lsdException w:name="page number"/>
            <w:lsdException w:name="endnote reference"/>
            <w:lsdException w:name="endnote text"/>
            <w:lsdException w:name="table of authorities"/>
            <w:lsdException w:name="macro"/>
            <w:lsdException w:name="toa heading"/>
            <w:lsdException w:name="List"/>
            <w:lsdException w:name="List Bullet"/>
            <w:lsdException w:name="List Number"/>
            <w:lsdException w:name="List 2"/>
            <w:lsdException w:name="List 3"/>
            <w:lsdException w:name="List 4"/>
            <w:lsdException w:name="List 5"/>
            <w:lsdException w:name="List Bullet 2"/>
            <w:lsdException w:name="List Bullet 3"/>
            <w:lsdException w:name="List Bullet 4"/>
            <w:lsdException w:name="List Bullet 5"/>
            <w:lsdException w:name="List Number 2"/>
            <w:lsdException w:name="List Number 3"/>
            <w:lsdException w:name="List Number 4"/>
            <w:lsdException w:name="List Number 5"/>
            <w:lsdException w:name="Title"/>
            <w:lsdException w:name="Closing"/>
            <w:lsdException w:name="Signature"/>
            <w:lsdException w:name="Default Paragraph Font"/>
            <w:lsdException w:name="Body Text"/>
            <w:lsdException w:name="Body Text Indent"/>
            <w:lsdException w:name="List Continue"/>
            <w:lsdException w:name="List Continue 2"/>
            <w:lsdException w:name="List Continue 3"/>
            <w:lsdException w:name="List Continue 4"/>
            <w:lsdException w:name="List Continue 5"/>
            <w:lsdException w:name="Message Header"/>
            <w:lsdException w:name="Subtitle"/>
            <w:lsdException w:name="Salutation"/>
            <w:lsdException w:name="Date"/>
            <w:lsdException w:name="Body Text First Indent"/>
            <w:lsdException w:name="Body Text First Indent 2"/>
            <w:lsdException w:name="Note Heading"/>
            <w:lsdException w:name="Body Text 2"/>
            <w:lsdException w:name="Body Text 3"/>
            <w:lsdException w:name="Body Text Indent 2"/>
            <w:lsdException w:name="Body Text Indent 3"/>
            <w:lsdException w:name="Block Text"/>
            <w:lsdException w:name="Hyperlink"/>
            <w:lsdException w:name="FollowedHyperlink"/>
            <w:lsdException w:name="Strong"/>
            <w:lsdException w:name="Emphasis"/>
            <w:lsdException w:name="Document Map"/>
            <w:lsdException w:name="Plain Text"/>
            <w:lsdException w:name="E-mail Signature"/>
            <w:lsdException w:name="Normal (Web)"/>
            <w:lsdException w:name="HTML Acronym"/>
            <w:lsdException w:name="HTML Address"/>
            <w:lsdException w:name="HTML Cite"/>
            <w:lsdException w:name="HTML Code"/>
            <w:lsdException w:name="HTML Definition"/>
            <w:lsdException w:name="HTML Keyboard"/>
            <w:lsdException w:name="HTML Preformatted"/>
            <w:lsdException w:name="HTML Sample"/>
            <w:lsdException w:name="HTML Typewriter"/>
            <w:lsdException w:name="HTML Variable"/>
            <w:lsdException w:name="Normal Table"/>
            <w:lsdException w:name="annotation subject"/>
            <w:lsdException w:name="Table Simple 1"/>
            <w:lsdException w:name="Table Simple 2"/>
            <w:lsdException w:name="Table Simple 3"/>
            <w:lsdException w:name="Table Classic 1"/>
            <w:lsdException w:name="Table Classic 2"/>
            <w:lsdException w:name="Table Classic 3"/>
            <w:lsdException w:name="Table Classic 4"/>
            <w:lsdException w:name="Table Colorful 1"/>
            <w:lsdException w:name="Table Colorful 2"/>
            <w:lsdException w:name="Table Colorful 3"/>
            <w:lsdException w:name="Table Columns 1"/>
            <w:lsdException w:name="Table Columns 2"/>
            <w:lsdException w:name="Table Columns 3"/>
            <w:lsdException w:name="Table Columns 4"/>
            <w:lsdException w:name="Table Columns 5"/>
            <w:lsdException w:name="Table Grid 1"/>
            <w:lsdException w:name="Table Grid 2"/>
            <w:lsdException w:name="Table Grid 3"/>
            <w:lsdException w:name="Table Grid 4"/>
            <w:lsdException w:name="Table Grid 5"/>
            <w:lsdException w:name="Table Grid 6"/>
            <w:lsdException w:name="Table Grid 7"/>
            <w:lsdException w:name="Table Grid 8"/>
            <w:lsdException w:name="Table List 1"/>
            <w:lsdException w:name="Table List 2"/>
            <w:lsdException w:name="Table List 3"/>
            <w:lsdException w:name="Table List 4"/>
            <w:lsdException w:name="Table List 5"/>
            <w:lsdException w:name="Table List 6"/>
            <w:lsdException w:name="Table List 7"/>
            <w:lsdException w:name="Table List 8"/>
            <w:lsdException w:name="Table 3D effects 1"/>
            <w:lsdException w:name="Table 3D effects 2"/>
            <w:lsdException w:name="Table 3D effects 3"/>
            <w:lsdException w:name="Table Contemporary"/>
            <w:lsdException w:name="Table Elegant"/>
            <w:lsdException w:name="Table Professional"/>
            <w:lsdException w:name="Table Subtle 1"/>
            <w:lsdException w:name="Table Subtle 2"/>
            <w:lsdException w:name="Table Web 1"/>
            <w:lsdException w:name="Table Web 2"/>
            <w:lsdException w:name="Table Web 3"/>
            <w:lsdException w:name="Balloon Text"/>
            <w:lsdException w:name="Table Grid"/>
            <w:lsdException w:name="Table Theme"/>
            <w:lsdException w:name="Light Shading"/>
            <w:lsdException w:name="Light List"/>
            <w:lsdException w:name="Light Grid"/>
            <w:lsdException w:name="Medium Shading 1"/>
            <w:lsdException w:name="Medium Shading 2"/>
            <w:lsdException w:name="Medium List 1"/>
            <w:lsdException w:name="Medium List 2"/>
            <w:lsdException w:name="Medium Grid 1"/>
            <w:lsdException w:name="Medium Grid 2"/>
            <w:lsdException w:name="Medium Grid 3"/>
            <w:lsdException w:name="Dark List"/>
            <w:lsdException w:name="Colorful Shading"/>
            <w:lsdException w:name="Colorful List"/>
            <w:lsdException w:name="Colorful Grid"/>
            <w:lsdException w:name="Light Shading Accent 1"/>
            <w:lsdException w:name="Light List Accent 1"/>
            <w:lsdException w:name="Light Grid Accent 1"/>
            <w:lsdException w:name="Medium Shading 1 Accent 1"/>
            <w:lsdException w:name="Medium Shading 2 Accent 1"/>
            <w:lsdException w:name="Medium List 1 Accent 1"/>
            <w:lsdException w:name="Medium List 2 Accent 1"/>
            <w:lsdException w:name="Medium Grid 1 Accent 1"/>
            <w:lsdException w:name="Medium Grid 2 Accent 1"/>
            <w:lsdException w:name="Medium Grid 3 Accent 1"/>
            <w:lsdException w:name="Dark List Accent 1"/>
            <w:lsdException w:name="Colorful Shading Accent 1"/>
            <w:lsdException w:name="Colorful List Accent 1"/>
            <w:lsdException w:name="Colorful Grid Accent 1"/>
            <w:lsdException w:name="Light Shading Accent 2"/>
            <w:lsdException w:name="Light List Accent 2"/>
            <w:lsdException w:name="Light Grid Accent 2"/>
            <w:lsdException w:name="Medium Shading 1 Accent 2"/>
            <w:lsdException w:name="Medium Shading 2 Accent 2"/>
            <w:lsdException w:name="Medium List 1 Accent 2"/>
            <w:lsdException w:name="Medium List 2 Accent 2"/>
            <w:lsdException w:name="Medium Grid 1 Accent 2"/>
            <w:lsdException w:name="Medium Grid 2 Accent 2"/>
            <w:lsdException w:name="Medium Grid 3 Accent 2"/>
            <w:lsdException w:name="Dark List Accent 2"/>
            <w:lsdException w:name="Colorful Shading Accent 2"/>
            <w:lsdException w:name="Colorful List Accent 2"/>
            <w:lsdException w:name="Colorful Grid Accent 2"/>
            <w:lsdException w:name="Light Shading Accent 3"/>
            <w:lsdException w:name="Light List Accent 3"/>
            <w:lsdException w:name="Light Grid Accent 3"/>
            <w:lsdException w:name="Medium Shading 1 Accent 3"/>
            <w:lsdException w:name="Medium Shading 2 Accent 3"/>
            <w:lsdException w:name="Medium List 1 Accent 3"/>
            <w:lsdException w:name="Medium List 2 Accent 3"/>
            <w:lsdException w:name="Medium Grid 1 Accent 3"/>
            <w:lsdException w:name="Medium Grid 2 Accent 3"/>
            <w:lsdException w:name="Medium Grid 3 Accent 3"/>
            <w:lsdException w:name="Dark List Accent 3"/>
            <w:lsdException w:name="Colorful Shading Accent 3"/>
            <w:lsdException w:name="Colorful List Accent 3"/>
            <w:lsdException w:name="Colorful Grid Accent 3"/>
            <w:lsdException w:name="Light Shading Accent 4"/>
            <w:lsdException w:name="Light List Accent 4"/>
            <w:lsdException w:name="Light Grid Accent 4"/>
            <w:lsdException w:name="Medium Shading 1 Accent 4"/>
            <w:lsdException w:name="Medium Shading 2 Accent 4"/>
            <w:lsdException w:name="Medium List 1 Accent 4"/>
            <w:lsdException w:name="Medium List 2 Accent 4"/>
            <w:lsdException w:name="Medium Grid 1 Accent 4"/>
            <w:lsdException w:name="Medium Grid 2 Accent 4"/>
            <w:lsdException w:name="Medium Grid 3 Accent 4"/>
            <w:lsdException w:name="Dark List Accent 4"/>
            <w:lsdException w:name="Colorful Shading Accent 4"/>
            <w:lsdException w:name="Colorful List Accent 4"/>
            <w:lsdException w:name="Colorful Grid Accent 4"/>
            <w:lsdException w:name="Light Shading Accent 5"/>
            <w:lsdException w:name="Light List Accent 5"/>
            <w:lsdException w:name="Light Grid Accent 5"/>
            <w:lsdException w:name="Medium Shading 1 Accent 5"/>
            <w:lsdException w:name="Medium Shading 2 Accent 5"/>
            <w:lsdException w:name="Medium List 1 Accent 5"/>
            <w:lsdException w:name="Medium List 2 Accent 5"/>
            <w:lsdException w:name="Medium Grid 1 Accent 5"/>
            <w:lsdException w:name="Medium Grid 2 Accent 5"/>
            <w:lsdException w:name="Medium Grid 3 Accent 5"/>
            <w:lsdException w:name="Dark List Accent 5"/>
            <w:lsdException w:name="Colorful Shading Accent 5"/>
            <w:lsdException w:name="Colorful List Accent 5"/>
            <w:lsdException w:name="Colorful Grid Accent 5"/>
            <w:lsdException w:name="Light Shading Accent 6"/>
            <w:lsdException w:name="Light List Accent 6"/>
            <w:lsdException w:name="Light Grid Accent 6"/>
            <w:lsdException w:name="Medium Shading 1 Accent 6"/>
            <w:lsdException w:name="Medium Shading 2 Accent 6"/>
            <w:lsdException w:name="Medium List 1 Accent 6"/>
            <w:lsdException w:name="Medium List 2 Accent 6"/>
            <w:lsdException w:name="Medium Grid 1 Accent 6"/>
            <w:lsdException w:name="Medium Grid 2 Accent 6"/>
            <w:lsdException w:name="Medium Grid 3 Accent 6"/>
            <w:lsdException w:name="Dark List Accent 6"/>
            <w:lsdException w:name="Colorful Shading Accent 6"/>
            <w:lsdException w:name="Colorful List Accent 6"/>
            <w:lsdException w:name="Colorful Grid Accent 6"/>
        </w:latentStyles>
        <w:style w:type="paragraph" w:styleId="a1" w:default="on">
            <w:name w:val="Normal"/>
            <w:pPr>
                <w:widowControl w:val="off"/>
                <w:jc w:val="both"/>
            </w:pPr>
            <w:rPr>
                <w:rFonts w:ascii="等线" w:h-ansi="等线" w:fareast="等线" w:cs="Times New Roman" w:hint="default"/>
                <w:kern w:val="2"/>
                <w:sz w:val="21"/>
                <w:sz-cs w:val="22"/>
                <w:lang w:val="EN-US" w:fareast="ZH-CN" w:bidi="AR-SA"/>
            </w:rPr>
        </w:style>
        <w:style w:type="character" w:styleId="a6" w:default="on">
            <w:name w:val="Default Paragraph Font"/>
        </w:style>
        <w:style w:type="table" w:styleId="a4" w:default="on">
            <w:name w:val="Normal Table"/>
            <w:semiHidden/>
            <w:tblPr>
                <w:tblCellMar>
                    <w:top w:w="0" w:type="dxa"/>
                    <w:left w:w="108" w:type="dxa"/>
                    <w:bottom w:w="0" w:type="dxa"/>
                    <w:right w:w="108" w:type="dxa"/>
                </w:tblCellMar>
            </w:tblPr>
        </w:style>
        <w:style w:type="paragraph" w:styleId="a2">
            <w:name w:val="footer"/>
            <w:basedOn w:val="a1"/>
            <w:link w:val="a8"/>
            <w:pPr>
                <w:snapToGrid w:val="off"/>
                <w:jc w:val="left"/>
            </w:pPr>
            <w:rPr>
                <w:sz w:val="18"/>
                <w:sz-cs w:val="18"/>
            </w:rPr>
        </w:style>
        <w:style w:type="paragraph" w:styleId="a3">
            <w:name w:val="header"/>
            <w:basedOn w:val="a1"/>
            <w:link w:val="a7"/>
            <w:pPr>
                <w:pBdr>
                    <w:bottom w:val="single" w:sz="6" wx:bdrwidth="15" w:space="1" w:color="auto"/>
                </w:pBdr>
                <w:snapToGrid w:val="off"/>
                <w:jc w:val="center"/>
            </w:pPr>
            <w:rPr>
                <w:sz w:val="18"/>
                <w:sz-cs w:val="18"/>
            </w:rPr>
        </w:style>
        <w:style w:type="table" w:styleId="a5">
            <w:name w:val="Table Grid"/>
            <w:basedOn w:val="a4"/>
            <w:pPr>
                <w:pStyle w:val="a4"/>
            </w:pPr>
            <w:tblPr>
                <w:tblBorders>
                    <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                    <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                    <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                    <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                    <w:insideH w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                    <w:insideV w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                </w:tblBorders>
                <w:tblCellMar>
                    <w:top w:w="0" w:type="dxa"/>
                    <w:left w:w="108" w:type="dxa"/>
                    <w:bottom w:w="0" w:type="dxa"/>
                    <w:right w:w="108" w:type="dxa"/>
                </w:tblCellMar>
            </w:tblPr>
        </w:style>
        <w:style w:type="character" w:styleId="a7">
            <w:name w:val="页眉 字符"/>
            <w:basedOn w:val="a6"/>
            <w:link w:val="a3"/>
            <w:rPr>
                <w:sz w:val="18"/>
                <w:sz-cs w:val="18"/>
            </w:rPr>
        </w:style>
        <w:style w:type="character" w:styleId="a8">
            <w:name w:val="页脚 字符"/>
            <w:basedOn w:val="a6"/>
            <w:link w:val="a2"/>
            <w:rPr>
                <w:sz w:val="18"/>
                <w:sz-cs w:val="18"/>
            </w:rPr>
        </w:style>
    </w:styles>
    <w:bgPict>
        <w:background/>
        <v:background id="_x0000_s1025">
            <v:fill on="f" focussize="0,0"/>
        </v:background>
    </w:bgPict>
    <w:docPr>
        <w:view w:val="print"/>
        <w:zoom w:percent="120"/>
        <w:characterSpacingControl w:val="CompressPunctuation"/>
        <w:documentProtection w:enforcement="off"/>
        <w:doNotEmbedSystemFonts/>
        <w:bordersDontSurroundHeader/>
        <w:bordersDontSurroundFooter/>
        <w:defaultTabStop w:val="420"/>
        <w:drawingGridVerticalSpacing w:val="156"/>
        <w:displayHorizontalDrawingGridEvery w:val="1"/>
        <w:displayVerticalDrawingGridEvery w:val="1"/>
        <w:compat>
            <w:adjustLineHeightInTable/>
            <w:ulTrailSpace/>
            <w:doNotExpandShiftReturn/>
            <w:balanceSingleByteDoubleByteWidth/>
            <w:useFELayout/>
            <w:spaceForUL/>
            <w:breakWrappedTables/>
            <w:dontGrowAutofit/>
            <w:useFELayout/>
        </w:compat>
    </w:docPr>
    <w:body>
        <wx:sect>
            <w:p>
                <w:pPr>
                    <w:jc w:val="center"/>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                        <w:b/>
                        <w:sz w:val="36"/>
                        <w:sz-cs w:val="36"/>
                    </w:rPr>
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                        <w:b/>
                        <w:sz w:val="36"/>
                        <w:sz-cs w:val="36"/>
                    </w:rPr>
                    <w:t>数据共享查询记录</w:t>
                </w:r>
            </w:p>
            <w:p>
                <w:pPr>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                    </w:rPr>
                </w:pPr>
            </w:p>
            <w:p>
                <w:pPr>
                    <w:ind w:first-line="560" w:first-line-chars="200"/>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                        <w:sz w:val="28"/>
                        <w:sz-cs w:val="28"/>
                    </w:rPr>
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                        <w:sz w:val="28"/>
                        <w:sz-cs w:val="28"/>
                    </w:rPr>
                    <w:t>根据</w:t>
                </w:r>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                        <w:b/>
                        <w:sz w:val="28"/>
                        <w:sz-cs w:val="28"/>
                        <w:u w:val="single"/>
                    </w:rPr>
                    <w:t>${operatorOrgName!}${operatorName!}</w:t>
                </w:r>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                        <w:sz w:val="28"/>
                        <w:sz-cs w:val="28"/>
                    </w:rPr>
                    <w:t>申请，经</w:t>
                </w:r>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                        <w:b/>
                        <w:sz w:val="28"/>
                        <w:sz-cs w:val="28"/>
                        <w:u w:val="single"/>
                    </w:rPr>
                    <w:t>${platformName!}</w:t>
                </w:r>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                        <w:sz w:val="28"/>
                        <w:sz-cs w:val="28"/>
                    </w:rPr>
                    <w:t>查询，查询结果如下：</w:t>
                </w:r>
            </w:p>
            <w:tbl>
                <w:tblPr>
                    <w:tblStyle w:val="a5"/>
                    <w:tblW w:w="5000" w:type="pct"/>
                    <w:jc w:val="center"/>
                    <w:tblInd w:w="0" w:type="dxa"/>
                    <w:tblBorders>
                        <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                        <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                        <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                        <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                        <w:insideH w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                        <w:insideV w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                    </w:tblBorders>
                    <w:tblCellMar>
                        <w:top w:w="0" w:type="dxa"/>
                        <w:left w:w="108" w:type="dxa"/>
                        <w:bottom w:w="0" w:type="dxa"/>
                        <w:right w:w="108" w:type="dxa"/>
                    </w:tblCellMar>
                </w:tblPr>
                <w:tblGrid>
                    <w:gridCol w:w="1819"/>
                    <w:gridCol w:w="1997"/>
                    <w:gridCol w:w="2264"/>
                    <w:gridCol w:w="2442"/>
                </w:tblGrid>
                <w:tr>
                    <w:tblPrEx>
                        <w:tblBorders>
                            <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideH w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideV w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                        </w:tblBorders>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="108" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="108" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPrEx>
                    <w:trPr>
                        <w:jc w:val="center"/>
                    </w:trPr>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="908" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                    <w:lang w:val="EN-US" w:fareast="ZH-CN"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                    <w:lang w:val="EN-US" w:fareast="ZH-CN"/>
                                </w:rPr>
                                <w:t>查询时间</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="4092" w:type="pct"/>
                            <w:gridSpan w:val="3"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                    <w:lang w:val="EN-US" w:fareast="ZH-CN"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>${searchTime!}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                </w:tr>
                <w:tr>
                    <w:tblPrEx>
                        <w:tblBorders>
                            <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideH w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideV w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                        </w:tblBorders>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="108" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="108" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPrEx>
                    <w:trPr>
                        <w:jc w:val="center"/>
                    </w:trPr>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="908" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>事项名称</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="4092" w:type="pct"/>
                            <w:gridSpan w:val="3"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:color w:val="000000"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                    <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>${itemName!}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                </w:tr>
                <w:tr>
                    <w:tblPrEx>
                        <w:tblBorders>
                            <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideH w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideV w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                        </w:tblBorders>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="108" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="108" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPrEx>
                    <w:trPr>
                        <w:jc w:val="center"/>
                    </w:trPr>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="908" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>材料名称</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="4092" w:type="pct"/>
                            <w:gridSpan w:val="3"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:color w:val="000000"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                    <w:shd w:val="clear" w:color="auto" w:fill="FFFFFF"/>
                                </w:rPr>
                                <w:t>${materialName!}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                </w:tr>
                <w:tr>
                    <w:tblPrEx>
                        <w:tblBorders>
                            <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideH w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideV w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                        </w:tblBorders>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="108" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="108" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPrEx>
                    <w:trPr>
                        <w:jc w:val="center"/>
                    </w:trPr>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="908" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>办事人</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="1539" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>${name!}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="862" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>证件类型</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="1691" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>${sex!}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                </w:tr>
                <w:tr>
                    <w:tblPrEx>
                        <w:tblBorders>
                            <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideH w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideV w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                        </w:tblBorders>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="108" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="108" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPrEx>
                    <w:trPr>
                        <w:jc w:val="center"/>
                    </w:trPr>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="908" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>证件号码</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="1539" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>${identityNumber!}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="862" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="1691" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                        </w:p>
                    </w:tc>
                </w:tr>
                <#list data as item>
                <w:tr>
                    <w:tblPrEx>
                        <w:tblBorders>
                            <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideH w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideV w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                        </w:tblBorders>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="108" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="108" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPrEx>
                    <w:trPr>
                        <w:jc w:val="center"/>
                    </w:trPr>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="5000" w:type="pct"/>
                            <w:gridSpan w:val="4"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="F1F1F1"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>数据${item_index + 1}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                </w:tr>
                <#list 0..(item?size-1) as index>
                <!-- 只处理偶数索引，因为每行要取两个值 -->
                <#if (index % 2 == 0)>
                <w:tr>
                    <w:tblPrEx>
                        <w:tblBorders>
                            <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideH w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            <w:insideV w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                        </w:tblBorders>
                        <w:tblCellMar>
                            <w:top w:w="0" w:type="dxa"/>
                            <w:left w:w="108" w:type="dxa"/>
                            <w:bottom w:w="0" w:type="dxa"/>
                            <w:right w:w="108" w:type="dxa"/>
                        </w:tblCellMar>
                    </w:tblPrEx>
                    <w:trPr>
                        <w:jc w:val="center"/>
                    </w:trPr>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="908" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>${item[index].key}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="1539" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>${item[index].value}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="862" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>${(item[index+1].key)!}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                    <w:tc>
                        <w:tcPr>
                            <w:tcW w:w="1691" w:type="pct"/>
                            <w:tcBorders>
                                <w:top w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:left w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:bottom w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                                <w:right w:val="single" w:sz="4" wx:bdrwidth="10" w:space="0" w:color="auto"/>
                            </w:tcBorders>
                            <w:shd w:val="clear" w:color="auto" w:fill="auto"/>
                        </w:tcPr>
                        <w:p>
                            <w:pPr>
                                <w:widowControl w:val="off"/>
                                <w:spacing w:line="360" w:line-rule="auto"/>
                                <w:jc w:val="both"/>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                            </w:pPr>
                            <w:r>
                                <w:rPr>
                                    <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                                    <w:sz w:val="24"/>
                                    <w:sz-cs w:val="24"/>
                                </w:rPr>
                                <w:t>${(item[index+1].value)!}</w:t>
                            </w:r>
                        </w:p>
                    </w:tc>
                </w:tr>
                </#if>
                </#list>
                </#list>
            </w:tbl>
            <w:p>
                <w:pPr>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                        <w:sz w:val="28"/>
                        <w:sz-cs w:val="28"/>
                    </w:rPr>
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                        <w:sz w:val="28"/>
                        <w:sz-cs w:val="28"/>
                    </w:rPr>
                    <w:t>注：此查询结果由</w:t>
                </w:r>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                        <w:b/>
                        <w:sz w:val="28"/>
                        <w:sz-cs w:val="28"/>
                        <w:u w:val="single"/>
                    </w:rPr>
                    <w:t>${platformName!}</w:t>
                </w:r>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                        <w:sz w:val="28"/>
                        <w:sz-cs w:val="28"/>
                    </w:rPr>
                    <w:t>提供，仅供办理政务服务事项时使用。如对查询信息有异议，请</w:t>
                </w:r>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                        <w:sz w:val="28"/>
                        <w:sz-cs w:val="28"/>
                        <w:lang w:val="EN-US" w:fareast="ZH-CN"/>
                    </w:rPr>
                    <w:t>咨询</w:t>
                </w:r>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                        <w:sz w:val="28"/>
                        <w:sz-cs w:val="28"/>
                    </w:rPr>
                    <w:t>相关部门。</w:t>
                </w:r>
            </w:p>
            <w:p>
                <w:pPr>
                    <w:spacing w:before="240"/>
                    <w:jc w:val="right"/>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                    </w:rPr>
                </w:pPr>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                    </w:rPr>
                    <w:t>日期：</w:t>
                </w:r>
                <w:r>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="fareast"/>
                        <w:b/>
                        <w:u w:val="single"/>
                    </w:rPr>
                    <w:t>${archiveDate!}</w:t>
                </w:r>
            </w:p>
            <w:p>
                <w:pPr>
                    <w:rPr>
                        <w:rFonts w:ascii="宋体" w:h-ansi="宋体" w:fareast="宋体" w:hint="default"/>
                    </w:rPr>
                </w:pPr>
            </w:p>
            <w:p/>
            <w:sectPr>
                <w:ftr w:type="odd">
                    <w:p>
                        <w:pPr>
                            <w:pStyle w:val="a2"/>
                            <w:tabs>
                                <w:tab w:val="center" w:pos="4153"/>
                                <w:tab w:val="right" w:pos="8306"/>
                            </w:tabs>
                            <w:jc w:val="center"/>
                        </w:pPr>
                        <w:r>
                            <w:fldChar w:fldCharType="begin"/>
                        </w:r>
                        <w:r>
                            <w:instrText>PAGE \* MERGEFORMAT</w:instrText>
                        </w:r>
                        <w:r>
                            <w:fldChar w:fldCharType="separate"/>
                        </w:r>
                        <w:r>
                            <w:rPr>
                                <w:lang w:val="ZH-CN"/>
                            </w:rPr>
                            <w:t>2</w:t>
                        </w:r>
                        <w:r>
                            <w:fldChar w:fldCharType="end"/>
                        </w:r>
                    </w:p>
                    <w:p>
                        <w:pPr>
                            <w:pStyle w:val="a2"/>
                            <w:tabs>
                                <w:tab w:val="center" w:pos="4153"/>
                                <w:tab w:val="right" w:pos="8306"/>
                            </w:tabs>
                        </w:pPr>
                    </w:p>
                </w:ftr>
                <w:pgSz w:w="11906" w:h="16838"/>
                <w:pgMar w:top="1440" w:right="1800" w:bottom="1440" w:left="1800" w:header="851" w:footer="992"
                         w:gutter="0"/>
                <w:cols w:space="425"/>
                <w:docGrid w:type="lines" w:line-pitch="312"/>
            </w:sectPr>
        </wx:sect>
    </w:body></w:wordDocument>