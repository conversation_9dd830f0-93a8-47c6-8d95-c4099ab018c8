[{"groupName": "协查模块", "reqUrls": ["/assist_user/**", "/assist_investigate/**"], "filterValues": ["%", "&lt;", "&gt;", "&amp;", "script", "document", "<", "(", ")", ">", "eval", "CR", "LF", "&lt;script", "&lt;/script", "./bin", "./sleep", ".."], "sqlFilterValues": ["select ", "drop table", "drop ", "insert ", " count(", "truncate ", " char(", " and ", " or ", "netlocalgroup administrators", "exec master", "order by "]}, {"groupName": "免证办模块", "reqUrls": ["/exempt/**"], "filterValues": ["script", "eval", "&lt;script", "&lt;/script", "./bin", "./sleep", ".."], "sqlFilterValues": ["drop table", "drop ", "insert ", " count(", "truncate ", " char(", " and ", " or ", "netlocalgroup administrators", "exec master", "order by "]}, {"groupName": "证明模块-证明目录-数据共享", "reqUrls": ["/proof_derate/**"], "filterValues": ["script", "eval", "&lt;script", "&lt;/script", "./bin", "./sleep", ".."], "sqlFilterValues": ["drop table", "drop ", "insert ", " count(", "truncate ", " char(", " and ", " or ", "netlocalgroup administrators", "exec master", "order by "]}, {"groupName": "证明模块-证明目录", "reqUrls": ["/proof_catalog/**", "/item/**"], "filterValues": ["script", "./bin", "./sleep", "sleep", "..", ">", "&lt;", "&gt;"], "sqlFilterValues": []}, {"groupName": "证明模块-水印管理", "reqUrls": ["/license/webapi/v1/common/watermark/**"], "filterValues": ["script"], "sqlFilterValues": []}, {"groupName": "证明模块-清理流程", "reqUrls": ["/proof_list_confirm/**", "/proof_list/**", "/proof_list_audit/**"], "filterValues": ["script", "./bin", "./sleep", ".."], "sqlFilterValues": []}, {"groupName": "链接去斜杠,http组", "reqUrls": ["/auth/webapi/v1/common/**", "/catalog/webapi/v1/**"], "filterValues": ["&lt;", "&gt;", "&amp;", "script", "document", "CR", "LF", "&lt;script", "&lt;/script", "./bin", "./sleep", ".."], "sqlFilterValues": ["select ", "delete ", "drop table", "drop ", "insert ", " count(", "truncate ", " char(", " and ", " or ", "netlocalgroup administrators", "exec master", "order by "]}, {"groupName": "邮箱去@,固定电话去'-'组", "reqUrls": ["/user/webapi/v1/account_info/**"], "filterValues": ["|", "$", "'", "\"", "'", "../", "\"", "+", "\"", "\\", "http", "%", "<", ">", "&", "script", "document", "eval", "CR", "LF", "<script", "</script"], "sqlFilterValues": ["select ", "delete ", "drop table", "drop ", "insert ", " count(", "truncate ", " char(", " and ", " or ", "netlocalgroup administrators", "exec master", "order by "]}, {"groupName": "内部骨架接口配置", "reqUrls": ["/licc-func-server/inner_service/**"], "filterValues": ["|", "../", "+", "\\", "http", "<", ">", "script", "document", "eval", "CR", "LF", "<script", "</script"], "sqlFilterValues": ["select ", "delete ", "drop table", "drop ", "insert ", " count(", "truncate ", " char(", " and ", " or ", "netlocalgroup administrators", "exec master", "order by "]}]