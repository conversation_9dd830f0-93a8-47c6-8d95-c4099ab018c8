# 项目配置
server.servlet.context-path=/proof-derate-api
server.port=8080
# 最大工作线程数
server.tomcat.max-threads=300
# 最小空闲线程数
server.tomcat.min-spare-threads=50
# 最大连接数
server.tomcat.max-connections=350
# 连接队列长度
server.tomcat.accept-count=100
# 连接超时时间（毫秒）
server.tomcat.connection-timeout=30000
# 链接有效性参数
spring.datasource.druid.validation-query=select 1
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
# 连接池参数
spring.datasource.druid.initial-size=5
spring.datasource.druid.min-idle=205
spring.datasource.druid.max-active=300
spring.datasource.druid.max-wait=5000
spring.datasource.druid.poolPreparedStatements=true
spring.datasource.druid.maxOpenPreparedStatements=100
# 引入公共模块  多个引入配置文件使用【,】号分隔
#spring.profiles.include=common,mysql,clu
spring.profiles.include=common,mysql,clu
logging.config=classpath:log4j2-spring.xml
spring.main.allow-bean-definition-overriding=true
#******************系统相关配置 start******************
# 第三方授权@ApiAuth开关 true为开，false为关
gov.license.common.auth.api.start=true
#Configuration配置文件中key对应配置的文件白名单列表
system.file.allow.list=
#Configuration配置文件中key对应配置的文件上传最大值,单位mb
system.file.allow.upload.size.max=1
#******************系统相关配置 end******************
# sm2 公钥，用于前端密码加密传输
auth.secret=3059301306072a8648ce3d020106082a811ccf5501822d03420004b82c84e07aac3062970e33257e3d9e9551913abaaa4f944244e8f97d30b976c3464dfce37f80d3d78299b951d2d4c3f299b28b5a25582192d8c90998bb11a47a
# 验证码显示控制
auth.open.captcha=false
# 是否开启数据签名
auth.open.data-sign=false
# jwt算法证书文件路径
jwt.cre.file.path=D:/sm2.cer
jwt.cre.private.key.path=D:/sm2.key
#配置文件加密
platform.secret=17fd7d3154d07083a35bc34c06d4ef55
jasypt.encryptor.bean=commonSmEncryptor
# 无证明城市-江西事项同步前置库开关
proof.jx-item-service-front.sync.enable=false
# 无证明城市-事项系统同步配置
proof.derate.url=http://192.168.10.20:3000/mock/1204/test-api/ebus/sxgl/v2/getLastAuditItem?test=1
proof.derate.passid=ENC(5d258e7933c8d4d6a31fcc63e08e5ad5)
proof.derate.token=ENC(848e68df4be89928ee8c981938f519b7927e357e3c14617a4454cc9a09eb058d6c1a3e31db35d07a081b4835aa2af7c9)
proof.derate.nonce=zhongzhi
proof.derate.area.code=441500
proof.derate.isAllArea=1
proof.derate.syncSourceItemCleanWayToTargetItemCleanWayBatch=1000
proof.derate.syncMiddleItemToItemBatch=200
proof.derate.syncServiceItemToMiddleBatch=100
proof.filterChain.anno.urls=/login,/gov_easy/**,/api/**,/license_derate/api/**,/assist/api/**,/assist/detail/**,/assist/history/**,/assist/history_page/**,/assist/audit/**